const path = require("path");
const { CleanWebpackPlugin } = require("clean-webpack-plugin");

const isProduction = process.env.NODE_ENV === "production";

module.exports = {
    mode: process.env.NODE_ENV === "production" ? "production" : "development",
    entry: "./src/index.ts",
    output: {
        path: path.resolve(__dirname, "dist"),
        filename: "index.js",
        library: {
            name: "utils",
            type: "umd",
        },
        globalObject: "this",
        clean: isProduction, // 只在生产环境 clean
    },
    resolve: {
        extensions: [".ts", ".tsx", ".js", ".jsx", ".json"],
    },
    externals: {
        react: {
            commonjs: "react",
            commonjs2: "react",
            amd: "React",
            root: "React",
        },
        "react-dom": {
            commonjs: "react-dom",
            commonjs2: "react-dom",
            amd: "ReactDOM",
            root: "ReactDOM",
        },
        typescript: "commonjs typescript",
        "@hippy/react": "commonjs2 @hippy/react",
    },
    module: {
        rules: [
            {
                test: /\.(ts|tsx)$/,
                use: [
                    {
                        loader: "babel-loader",
                        options: {
                            presets: [
                                "@babel/preset-typescript",
                                "@babel/preset-react",
                                [
                                    "@babel/preset-env",
                                    {
                                        targets: { ios: 9 },
                                    },
                                ],
                            ],
                            plugins: [
                                ["@babel/plugin-proposal-decorators", { legacy: true }],
                                "@babel/plugin-proposal-class-properties",
                                "@babel/plugin-proposal-optional-chaining",
                                "@babel/plugin-proposal-nullish-coalescing-operator",
                            ],
                        },
                    },
                    "unicode-loader",
                ],
                exclude: /node_modules/,
            },
            {
                test: /\.(jsx?)$/,
                use: [
                    {
                        loader: "babel-loader",
                        options: {
                            presets: [
                                "@babel/preset-react",
                                [
                                    "@babel/preset-env",
                                    {
                                        targets: {
                                            ios: 9,
                                        },
                                    },
                                ],
                            ],
                            plugins: ["@babel/plugin-proposal-class-properties"],
                        },
                    },
                    "unicode-loader",
                ],
            },
            {
                test: /\.json$/,
                loader: "json-loader",
            },
        ],
    },
    plugins: isProduction ? [new CleanWebpackPlugin()] : [],
};
