import { Observable, of } from "rxjs";
import { delay } from "rxjs/operators";
import { Fiber } from "react-reconciler";
import { UIManagerModule } from "@hippy/react";

type AnyType = any;

// eslint-disable-next-line @typescript-eslint/no-empty-function,@typescript-eslint/no-unused-vars
export function ignore(...param: AnyType[]): void {}

export function delayed(timeInMs: number): Observable<number> {
    return of(timeInMs).pipe(delay(timeInMs));
}

export function numberWithFillZero(num: number): string {
    let _num = num.toString();
    if (num < 10) {
        _num = "0" + _num;
    }
    return _num;
}
