type AnyType = any;

export class Completer<T> {
    promise: Promise<T>;
    finish = false;

    private _resolve!: (value: T | PromiseLike<T>) => void;
    private _reject!: (reason?: any) => void;

    public resolve(value?: T | PromiseLike<T>): void {
        // 如果 value 是 undefined，传递一个 undefined 值（需要类型断言）
        if (value === undefined) {
            this._resolve(undefined as unknown as T | PromiseLike<T>);
        } else {
            this._resolve(value);
        }
        this.finish = true;
    }

    public reject(reason?: AnyType): void {
        this._reject(reason);
        this.finish = true;
    }

    constructor() {
        this.promise = new Promise<T>((resolve, reject) => {
            this._resolve = resolve;
            this._reject = reject;
        });
    }
}
