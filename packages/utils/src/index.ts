import "./time-ext";

export { default as UiUtils } from "./ui-utils";
export type { LayoutContent, RefType, RefType2 } from "./ui-utils";
export { pxToDp } from "./ui-utils";
export { default as Utils } from "./utils";
export * from "./global";

export { Const } from "./consts";
export { DeviceUtils } from "./device-utils";
export { Version } from "./version-utils";
export { applyMixins } from "./mixin-utils";
export { UniqueKey } from "./keys";
export {
    getLastMonthStartDate,
    getLastMonthEndDate,
    getLastNDaysDate,
    getMonthStartDate,
    getWeekStartDate,
    getYearStartDate,
    prevDate,
    nextDate,
    isLeapYear,
    getMaxDaysOfMonth,
    generateRange,
    getStartOfDate,
    getThisMonthFirstDay,
    getThisMonthEndDay,
    getNextMonthFirstDay,
    getFirstDayOfMonth,
    getMonthDays,
    getPrevMonthLastDays,
    parseTime,
    clearTime,
    getDateTimestamp,
    isDate,
} from "./date";

export type { IJsonMetaData, TypeConverter } from "./json-mapper";
export {
    JsonProperty,
    getClazz,
    getJsonProperty,
    JsonMapper,
    fromJsonToDate,
    dateToYyyyMMddString,
    fromJsonToNumber,
    fromJsonToMap,
    fromJsonToDateTime,
    fromBrToN,
} from "./json-mapper";
export { AccessibilityLabelType } from "./common-type";
export type { AnyType, ScrollEvent, LayoutEvent } from "./common-type";
export type { Point, Layout, Margin, Padding } from "./common-interface";
export { ignore, delayed, numberWithFillZero } from "./common-function";
export { Disposable, DisposableTracker } from "./disposable";
export { keyboardListener } from "./keyboard-listener";
export { AbcUIManagerModule } from "./ui-manager-module";
export { AbcError, ABCDeviceLoginError, UnimplementedError, errorSummary, errorToStr, errorToBuglyReportStr } from "./error";
export { Completer } from "./completer";
export { TaskQueue } from "./task-queue";
export type { Task } from "./task-queue";
export { fromAsyncIterableIgnoreError, fromPromise } from "./rxjs-ext";
export { DifferenceDate, getDateDifference } from "./time-utils";

interface optionsInterface {
    appInfo?: any;
    log?: any;
}

let _appInfo: any = null;
let _logInstance: any = null;

export function utilsInit(options: optionsInterface): void {
    if (options?.appInfo) {
        _appInfo = options.appInfo;
    }

    if (options?.log) {
        _logInstance = options.log;
    }
}

export function getLog(): any {
    return _logInstance;
}

export function getAppInfo(): any {
    return _appInfo;
}
