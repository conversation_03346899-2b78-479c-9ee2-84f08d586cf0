/**
 * 获取指定日期上个月的开始日期
 * @param date
 * @return {Date}
 */
export function getLastMonthStartDate(date?: Date) {
    date = date || new Date();
    return new Date(date.getFullYear(), date.getMonth() - 1, 1);
}

/**
 * 获取指定日期上个月的结束日期
 * @param date
 * @return {Date}
 */
export function getLastMonthEndDate(date?: Date) {
    date = date || new Date();
    // @ts-ignore
    return new Date(getMonthStartDate(date) - 1000 * 60 * 60 * 24);
}

/**
 * 获取距离 sinceWhen 的日期, 默认为当前日期
 * @return {Date}
 * @param offset 距离的天数
 * @param sinceWhen 计算的起始日期
 */
export function getLastNDaysDate(offset: number, sinceWhen?: Date) {
    const now = sinceWhen || new Date();
    const lastdate = new Date(now.getTime() - offset * 24 * 3600 * 1000);
    return lastdate;
}

/**
 * 获取指定日期所在月份的开始日期
 * @param date
 * @return {Date}
 */
export function getMonthStartDate(date?: Date): Date {
    date = date || new Date();
    return new Date(date.getFullYear(), date.getMonth(), 1);
}

/**
 * 获取传入日期的周一的日期
 * @param {Date} date - 日期
 * @return {Date} 传入日期所在周的周一
 */
export function getWeekStartDate(date?: Date) {
    date = date || new Date();
    const dayOfWeek = date.getDay() || 7; //今天本周的第几天
    return new Date(date.getFullYear(), date.getMonth(), date.getDate() - dayOfWeek + 1);
}

/**
 * 获取指定日期所在年的开始日期
 * @param date
 * @return {Date}
 */
export function getYearStartDate(date?: Date) {
    date = date || new Date();
    return new Date(date.getFullYear(), 0, 1);
}

/**
 * 获取传入日期的前 n 天
 * @param {Date} date - 日期
 * @param {number} n - n 天，默认为 1
 * @return {Date}
 */
export function prevDate(date: Date, n = 1) {
    return new Date(date.getFullYear(), date.getMonth(), date.getDate() - n);
}

/**
 * 获取传入日期的后 n 天
 * @param {Date} date - 日期
 * @param {number} n - n 天，默认为 1
 * @return {Date}
 */
export function nextDate(date: Date, n = 1) {
    return new Date(date.getFullYear(), date.getMonth(), date.getDate() + n);
}

export const isLeapYear = (year: number): boolean => {
    return (
        (year / 4 == Math.floor(year / 4) && year / 100 != Math.floor(year / 100)) ||
        (year / 400 == Math.floor(year / 400) && year / 3200 != Math.floor(year / 3200)) ||
        year / 172800 == Math.floor(year / 172800)
    );
};

export const getMaxDaysOfMonth = (year: number, mouth: number): number => {
    switch (mouth) {
        case 2: {
            if (isLeapYear(year)) {
                //闰月 二月 29 天
                return 29;
            } else {
                //平月 二月 28 天
                return 28;
            }
        }
        case 1:
        case 3:
        case 5:
        case 7:
        case 8:
        case 10:
        case 12: {
            //大月 一、三、五、七、八、十、腊月 31 天
            return 31;
        }
        case 4:
        case 6:
        case 9:
        case 11:
            //小月 四、六、九、十一月 30 天
            return 30;
    }

    throw "月份只能[1-12]，不能为:" + mouth;
};

export function generateRange(n: number) {
    // @ts-ignore
    // eslint-disable-next-line prefer-spread
    return Array.apply(null, { length: n }).map((_, index) => index);
}

export function getStartOfDate(date = new Date()): Date {
    const newDate = new Date(date.getTime());
    newDate.setHours(0);
    newDate.setMinutes(0);
    newDate.setSeconds(0);
    newDate.setMilliseconds(0);

    return newDate;
}

export function getThisMonthFirstDay(date = new Date()): Date {
    const now = new Date(date);
    now.setDate(1);
    now.setHours(0);
    now.setMinutes(0);
    now.setSeconds(0);
    now.setMilliseconds(0);
    return now;
}

export function getThisMonthEndDay(date = new Date()): Date {
    const now = new Date(date);
    const _year = now.getFullYear(),
        _month = now.getMonth() + 1;
    const maxDay = getMaxDaysOfMonth(_year, _month);
    now.setDate(maxDay);
    now.setHours(23);
    now.setMinutes(59);
    now.setSeconds(59);
    now.setMilliseconds(999);
    return now;
}

export function getNextMonthFirstDay(date = new Date()): Date {
    const now = getThisMonthEndDay(date);

    return new Date(now.getTime() + 1);
}

/**
 * 获取传入日期所在月份第一天是周几
 * @param {Date} date
 * @return {number} 周几 0 -> 6: 周日 -> 周六
 */
export function getFirstDayOfMonth(date: Date) {
    const temp = new Date(date.getTime());
    temp.setDate(1);
    return temp.getDay();
}

/**
 * 获取月份的日期数组 [1...lastDay]
 * @param date
 */
export const getMonthDays = (date: Date) => {
    const temp = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    const days = temp.getDate();
    return generateRange(days).map((_, index) => index + 1);
};

/**
 * 获取上月的最后 N 天的日期数组，例：9 月的最后 5 天：[26, 27, 28, 29, 30]
 * @param date
 * @param amount
 */
export function getPrevMonthLastDays(date: Date, amount: number) {
    if (amount <= 0) return [];
    const temp = new Date(date.getTime());
    temp.setDate(0);
    const lastDay = temp.getDate();
    return generateRange(amount).map((_, index) => lastDay - (amount - index - 1));
}

/**
 * 时间格式化
 * @param time - 时间
 * @param cFormat - 格式化结构
 * @param noSameYearOrDay - true 跳过同年和同日判断直接返回时间
 * @return {string}
 */
export function parseTime(time: any, cFormat: string, noSameYearOrDay = false) {
    if (arguments.length === 0) {
        return null;
    }
    let format = cFormat || "y-m-d h:i:s";
    let date;
    if (time && typeof time === "object") {
        date = time;
    } else {
        if (`${time}`.length === 10) time = parseInt(time) * 1000;
        date = new Date(time);
    }
    const today = new Date();
    let timeStr = "";
    if (
        today.getFullYear() === date.getFullYear() &&
        today.getMonth() === date.getMonth() &&
        today.getDate() === date.getDate() &&
        !noSameYearOrDay
    ) {
        timeStr = "今天";
    } else if (today.getFullYear() === date.getFullYear() && !noSameYearOrDay) {
        format = "m-d";
        const formatObj = {
            m: date.getMonth() + 1,
            d: date.getDate(),
        };
        timeStr = format.replace(/(y|m|d|h|i|s|a)+/g, (result, key) => {
            // @ts-ignore
            let value = formatObj[key];
            if (result.length > 0 && value < 10) {
                value = `0${value}`;
            }
            return value || 0;
        });
    } else {
        const formatObj = {
            y: date.getFullYear(),
            m: date.getMonth() + 1,
            d: date.getDate(),
            h: date.getHours(),
            i: date.getMinutes(),
            s: date.getSeconds(),
            a: date.getDay(),
        };
        timeStr = format.replace(/(y|m|d|h|i|s|a)+/g, (result, key) => {
            // @ts-ignore
            let value = formatObj[key];
            if (result.length > 0 && value < 10) {
                value = `0${value}`;
            }
            return value || 0;
        });
    }

    return timeStr;
}

export function clearTime(date: Date) {
    return new Date(date.getFullYear(), date.getMonth(), date.getDate());
}

export function getDateTimestamp(time: number | string | Date) {
    if (typeof time === "number" || typeof time === "string") {
        return clearTime(new Date(time)).getTime();
    }
    if (time instanceof Date) {
        return clearTime(time).getTime();
    }
    return NaN;
}

export function isDate(date: any) {
    if (date === null || date === undefined) return false;
    if (isNaN(new Date(date).getTime())) return false;
    if (Array.isArray(date)) return false; // deal with `new Date([ new Date() ]) -> new Date()`
    return true;
}
