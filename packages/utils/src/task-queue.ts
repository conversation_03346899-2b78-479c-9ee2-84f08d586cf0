import { AbcError, errorToStr } from "./error";
import { getLog } from "./index";
import { Completer } from "./completer";
import { Disposable } from "./disposable";

export interface Task {
    (): Promise<any> | void | undefined;
}

export class TaskQueue extends Disposable {
    private lock?: Completer<boolean>;

    private queue: Task[] = [];
    private _disposed = false;
    private currentProcessTask?: Task;

    constructor() {
        super();
        this._doWork();
    }

    public schedule(task: Task): void {
        getLog()?.d("TaskQueue.schedule");
        if (this._disposed) throw new AbcError(new Error("TaskQueue.schedule but scheduler has disposed"));
        this.queue.push(task);
        this.lock?.resolve(true);
    }

    private async _doWork() {
        getLog()?.d("TaskQueue._doWork");
        do {
            if (this.queue.length == 0) {
                this.lock = new Completer();
                await this.lock.promise;
            }

            if (this._disposed) break;

            const task = this.queue.shift();

            // getLog()?.d("TaskQueue._doWork process task:" + task);
            try {
                this.currentProcessTask = task;
                const taskResult = task!();
                if (taskResult != undefined) await taskResult;
            } catch (e) {
                getLog()?.e("TaskQueue, error = " + errorToStr(e));
            }

            this.currentProcessTask = undefined;
            // getLog()?.d("TaskQueue._doWork process finish task:" + task);
        } while (!this._disposed);
    }

    dispose(): void {
        this._disposed = true;
        this.lock?.resolve(true);
    }
}
