import _ from "lodash";

export class DifferenceDate {
    differenceTime: number;

    constructor(differenceTime: number) {
        this.differenceTime = differenceTime;
    }

    get inDays(): number {
        return Math.floor(this.differenceTime / (24 * 3600 * 1000));
    }

    get inHours(): number {
        // let leave1 = this.differenceTime % (24 * 3600 * 1000)    //计算天数后剩余的毫秒数
        return Math.floor(this.differenceTime / (3600 * 1000));
    }

    get inMinutes(): number {
        // let leave2 = this.inHours % (3600 * 1000)        //计算小时数后剩余的毫秒数
        return Math.floor(this.differenceTime / (60 * 1000));
    }

    get inSeconds(): number {
        // let leave3 = this.inMinutes % (60 * 1000)      //计算分钟数后剩余的毫秒数
        return Math.round(this.differenceTime / 1000);
    }

    get inMillisecond(): number {
        return Math.round(this.differenceTime);
    }
}

export function getDateDifference(date1?: Date, date2?: Date): DifferenceDate {
    if (_.isUndefined(date1) || _.isUndefined(date2)) return new DifferenceDate(0);
    const date3 = new Date(date2).getTime() - new Date(date1).getTime(); //时间差的毫秒数
    return new DifferenceDate(date3);
}