/**
 * create by dengjie
 * desc:
 * create date 2020/4/27
 */

import _ from "lodash";

export class Version {
    major = 0; //主版本号
    minor = 0; //子版本号
    revision = 0; //修证版本号
    build = 0; //编译build号

    constructor(version?: Partial<Version> | string) {
        if (version == undefined) return;
        if (_.isString(version)) {
            const tokens = version.split(".");
            if (tokens.length >= 1) {
                this.major = parseInt(tokens[0]);
            }
            if (tokens.length >= 2) {
                this.minor = parseInt(tokens[1]);
            }

            if (tokens.length >= 3) {
                this.revision = parseInt(tokens[2]);
            }

            if (tokens.length >= 4) {
                this.build = parseInt(tokens[3]);
            }
        } else {
            Object.assign(this, version);
        }
    }

    public compareTo(version: Version): number {
        if (this.major !== version.major) {
            return this.major - version.major;
        }

        if (this.minor !== version.minor) {
            return this.minor - version.minor;
        }

        if (this.revision !== version.revision) {
            return this.revision - version.revision;
        }

        return this.build - version.build;
    }
}
