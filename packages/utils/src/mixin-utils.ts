type AnyType = any;

export function applyMixins(derivedCtor: AnyType, baseCtors: AnyType[], exclude?: string[]): void {
    const derivedPropertyKeys = Object.getOwnPropertyNames(derivedCtor.prototype);

    for (let i = 0, len = baseCtors.length; i < len; i++) {
        const baseCtor = baseCtors[i];
        const propertyKeys = Object.getOwnPropertyNames(baseCtor.prototype);
        for (let j = 0, len2 = propertyKeys.length; j < len2; j++) {
            const name = propertyKeys[j];
            if (derivedPropertyKeys.includes(name) || (exclude && exclude.includes(name))) continue;

            // LogUtils.d("applyMixins derivedCtor.name = " + derivedCtor.name + ", method= " + name);
            derivedCtor.prototype[name] = baseCtor.prototype[name];
        }
    }
}
