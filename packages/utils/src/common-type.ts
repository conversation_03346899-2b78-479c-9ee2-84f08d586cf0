import { Layout } from "./common-interface";

export enum AccessibilityLabelType {
    PAGE = "abc-page",
    DIALOG = "abc-dialog",
    OVERLAY = "abc-overlay",
    TOAST = "abc-toast",
    INPUT = "abc-input",
    LISTVIEW = "abc-list-view",
}

export type AnyType = any;

export type ScrollEvent = {
    contentOffset: { x: number; y: number };
    contentSize?: {
        width: number;
        height: number;
    };
};

export type LayoutEvent = {
    layout: Layout;
};
