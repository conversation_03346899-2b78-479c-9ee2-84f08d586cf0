import { Platform } from "@hippy/react";

/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/5/30
 *
 * @description
 */

export class DeviceUtils {
    static _isIOS: boolean | null = null;
    static _isAndroid: boolean | null = null;
    static _isOhos: boolean | null = null;

    static isOhos(): boolean {
        if (this._isOhos == null) this._isOhos = Platform.OS === "ohos";
        return this._isOhos;
    }

    static isAndroid(): boolean {
        if (this._isAndroid == null) this._isAndroid = !this.isIOS() && !this.isOhos();
        return this._isAndroid;
    }

    static isIOS(): boolean {
        if (this._isIOS == null) this._isIOS = Platform.OS === "ios";

        return this._isIOS;
    }
}
