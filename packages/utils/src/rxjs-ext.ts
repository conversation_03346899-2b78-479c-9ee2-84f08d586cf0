/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-20
 *
 * @description
 */

import { Observable } from "rxjs";
import { AbcError } from "./error";

export function fromAsyncIterableIgnoreError<T>(gen: AsyncGenerator<T>): Observable<T | AbcError> {
    return new Observable<T | AbcError>((subscriber) => {
        (async () => {
            try {
                let result = await gen.next();
                while (!result.done) {
                    subscriber.next(result.value);
                    result = await gen.next();
                }
            } catch (e) {
                subscriber.next(new AbcError(e));
            }
            subscriber.complete();
        })();

        return subscriber;
    });
}

export function fromPromise<T>(promise: Promise<T>): Observable<T> {
    return new Observable<T>((subscriber) => {
        (async () => {
            try {
                const result = await promise;
                subscriber.next(result);
            } catch (e) {
                subscriber.error(e);
            }
            subscriber.complete();
        })();

        return subscriber;
    });
}