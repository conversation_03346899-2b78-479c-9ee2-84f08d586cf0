type AnyType = any;

export class AbcError extends Error {
    detailError: any;

    constructor(detailError: AnyType) {
        super(detailError instanceof Error ? `${detailError.message}` : "");
        this.detailError = detailError;
    }

    toString(): string {
        return this.detailError.toString();
    }
}

export class ABCDeviceLoginError extends Error {
    detailError: any;
    url?: string;
    method?: string;

    constructor(detailError?: AnyType, url?: string, method?: string) {
        super(`社备案注册失败`);
        this.detailError = detailError;
        this.url = url;
        this.method = method;
    }
}

export class UnimplementedError {
    message: string;

    constructor(message: string) {
        this.message = message;
    }
}

/**
 * 获取错误的摘要信息
 * @param error 错误对象
 * @returns 错误信息字符串
 */
export function errorSummary(error: AnyType): string {
    if (error instanceof AbcError || error instanceof ABCDeviceLoginError) {
        error = error.detailError;
    }

    // iOS native调用返回的异常
    if (error.domain === "HippyInvokeError" && error.userInfo?.message) {
        return error.userInfo!.message;
    }

    console.error("errorSummary: ", error);
    if (error instanceof Error) {
        console.error("errorSummary stack: ", error.stack);
    }

    if (error instanceof Error) {
        return `${error.name}: ${error.message}`;
    } else if (typeof error === "string") {
        return error;
    }

    try {
        return JSON.stringify(error);
    } catch (e) {
        return String(error);
    }
}

/**
 * 将错误转换为字符串表示
 * @param error 错误对象
 * @returns 错误信息字符串
 */
export function errorToStr(error: AnyType): string {
    if (error instanceof AbcError || error instanceof ABCDeviceLoginError) {
        error = error.detailError;
    }

    console.error("errorToStr: ", error);
    if (error instanceof Error) {
        console.error("errorToStr stack: ", error.stack);
    }

    if (error instanceof Error) {
        return `${error.name}: ${error.message}`;
    }

    try {
        return error + JSON.stringify(error);
    } catch (e) {
        return String(error);
    }
}

/**
 * 用于bugly异常上报
 * @param error 错误对象
 * @returns 错误信息字符串
 */
export function errorToBuglyReportStr(error: AnyType): string {
    if (error instanceof AbcError) {
        error = error.detailError;
    }

    if (error instanceof Error) {
        return `${error.name}: ${error.message}`;
    }

    try {
        return error + JSON.stringify(error);
    } catch (e) {
        return String(error);
    }
}
