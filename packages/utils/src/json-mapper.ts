/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020-04-22
 *
 * @description
 *
 */

import "reflect-metadata";
import { getLog } from "./index";
import _ from "lodash";

type AnyType = any;

export interface IJsonMetaData<T> {
    name?: string;
    type?: { new (): T };
    clazz?: { new (): any }; //如果是数组，这里为子元素类型

    fromJson?: (json: any, parent: any) => any;

    toJson?: (obj: any) => any;

    ignore?: boolean; //忽略此属性
}

const jsonMetadataKey = "jsonProperty";

export function JsonProperty<T>(metadata?: IJsonMetaData<T> | string): any {
    if (metadata instanceof String || typeof metadata === "string") {
        return Reflect.metadata(jsonMetadataKey, {
            name: metadata,
            clazz: undefined,
        });
    } else {
        const metadataObj = <IJsonMetaData<T>>metadata;
        return Reflect.metadata(jsonMetadataKey, metadataObj);
    }
}

export function getClazz(target: AnyType, propertyKey: string): AnyType {
    const type = Reflect.getMetadata("design:type", target, propertyKey);

    if (type) return type;

    const meta = getJsonProperty(target, propertyKey);
    if (meta && meta.type) return meta.type;
}

export function getJsonProperty<T>(target: AnyType, propertyKey: string): IJsonMetaData<T> {
    return Reflect.getMetadata(jsonMetadataKey, target, propertyKey);
}

export interface TypeConverter {
    dateConverter?: (date: Date) => any;
}

export class JsonMapper {
    static isPrimitive(obj: AnyType): boolean {
        switch (typeof obj) {
            case "string":
            case "number":
            case "boolean":
                return true;
        }

        return false;
    }

    static isArray(object: AnyType): boolean {
        if (object === Array) {
            return true;
        } else if (typeof Array.isArray === "function") {
            return Array.isArray(object);
        } else {
            return object instanceof Array;
        }
    }

    static deserialize<T>(clazz: { new (): T }, jsonObject: Partial<T> | undefined, withNullAttr = true): T {
        const obj: any = new clazz();
        Object.keys(obj).forEach((key) => {
            JsonMapper.deserializeObjKey(obj, key, jsonObject);
        });
        if (!withNullAttr) {
            // @ts-ignore
            _.pickBy(obj, _.identity);
        }
        return obj;
    }

    static serialize(obj: AnyType, typeConverter?: TypeConverter): AnyType {
        const result = {};
        Object.keys(obj).forEach((key) => {
            JsonMapper.serializeObjKey(result, obj, key, typeConverter);
        });
        return result;
    }

    static deserializeObjKey(obj: AnyType, key: AnyType, json: AnyType): void {
        const metadata = getJsonProperty(obj, key);
        let propertyName = key;
        let subClazz: any;
        let fromJson: ((json: any, parent: any) => any) | undefined;
        let ignore = false;

        if (metadata) {
            if (metadata.name) {
                propertyName = metadata.name;
            }
            subClazz = metadata.clazz;
            ignore = metadata.ignore != undefined ? metadata.ignore : false;

            fromJson = metadata.fromJson;
        }

        if (ignore) return;

        const innerJson = json ? json[propertyName] : undefined;
        if (fromJson) {
            if (innerJson === null) {
                obj[key] = null;
            } else if (innerJson !== undefined) {
                obj[key] = fromJson(innerJson, json);
            }
            return;
        }

        const clazz = getClazz(obj, key);
        if (JsonMapper.isArray(clazz)) {
            if (innerJson) {
                if (JsonMapper.isArray(innerJson)) {
                    obj[key] = innerJson.map((item: any) => (subClazz ? JsonMapper.deserialize(subClazz, item) : item));
                } else {
                    getLog?.()?.e(obj.constructor.name + "的属性：" + key + ",声明为数组，但json为:" + JSON.stringify(innerJson));
                }
            }
        } else if (clazz != undefined && !JsonMapper.isPrimitive(clazz) && !_.isNil(innerJson)) {
            obj[key] = JsonMapper.deserialize(clazz, innerJson);
        } else {
            obj[key] = innerJson;
        }
    }

    static serializeObjKey(target: AnyType, obj: AnyType, key: AnyType, typeConverter?: TypeConverter): void {
        const metadata = getJsonProperty(obj, key);
        let propertyName = key;
        let toJson: ((obj: any) => any) | undefined;
        let ignore = false;

        if (metadata) {
            if (metadata.name) {
                propertyName = metadata.name;
            }
            ignore = metadata.ignore != undefined ? metadata.ignore : false;

            toJson = metadata.toJson;
        }

        if (ignore) return;

        const objValue = obj ? obj[propertyName] : undefined;

        if (toJson) {
            if (objValue === null) {
                target[key] = null;
            } else if (objValue !== undefined) {
                target[key] = toJson(objValue);
            }
            return;
        }

        const clazz = getClazz(obj, key);
        if (JsonMapper.isArray(clazz)) {
            if (objValue) {
                if (JsonMapper.isArray(objValue)) {
                    target[key] = objValue.map((item: any) => JsonMapper.serialize(item));
                } else {
                    getLog?.()?.e(obj.constructor.name + "的属性：" + key + ",声明为数组，但json为:" + JSON.stringify(objValue));
                }
            }
        } else if (clazz != undefined && !JsonMapper.isPrimitive(clazz) && !_.isNil(objValue)) {
            target[key] = JsonMapper.serialize(objValue);
        } else {
            if (_.isDate(objValue) && typeConverter?.dateConverter) {
                target[key] = typeConverter.dateConverter(objValue);
            } else {
                target[key] = objValue;
            }
        }
    }
}

function fromJsonToDate(date: AnyType): Date | null {
    switch (typeof date) {
        case "string":
            if (!date.length) return null;
            const d = new Date(date);
            if (!_.isNaN(d.getTime())) {
                return d;
            }
            if (_.findLastIndex(".0") > -1) {
                //兼容门诊搜索返回的 2020-09-06 14:57:15.0 格式
                //返回值已经转换时区
                return new Date(new Date(date.split(".")[0].replace(/T/g, " ").replace(/-/g, "/")).getTime());
            }
            //加入时区转化
            //2020-07-29 13:50:14
            //2020/07/29 13:50:14
            //2020-07-29T13:50:14.609+0000
            //Wed Jul 29 2020 13:50:14 GMT+0800
            return new Date(new Date(date.substr(0, 19).replace(/T/g, " ").replace(/-/g, "/")).getTime() + 8 * 60 * 60 * 1000);
    }

    return date;
}

function dateToYyyyMMddString(date?: Date): string | undefined {
    if (!date) return undefined;

    return date.format("yyyy-MM-dd");
}

function fromJsonToNumber(json: AnyType): number {
    if (_.isNumber(json)) {
        return json;
    }

    if (_.isString(json)) {
        return Number(json);
    }

    return json;
}

function fromJsonToMap(obj: AnyType): Map<AnyType, AnyType> | undefined {
    if (!obj) return undefined;

    const keys = Object.keys(obj);
    if (!keys.length) return undefined;

    const map = new Map();
    keys.forEach((key) => {
        map.set(key, obj[key]);
    });
    return map;
}

function fromJsonToDateTime(data: AnyType): Date | null {
    if (!data) return new Date(`2000/01/01 00:00:00`);
    return new Date(`2000/01/01 ${data}`);
}

function fromBrToN(str: AnyType): string | undefined {
    if (_.isString(str)) {
        return str?.replace(/<br[^>]*>/g, "\n") ?? str;
    }
    return str;
}

export { fromJsonToDate, dateToYyyyMMddString, fromJsonToNumber, fromJsonToMap, fromJsonToDateTime, fromBrToN };
