type AnyType = any;
interface ComponentErrorHandler {
    (error: AnyType, errorInfo: AnyType): void;
}

type ComponentErrorHandlerFn = () => ComponentErrorHandler | null;

interface optionsInterface {
    log?: any;
    languageObserver?: any;
    getComponentErrorHandler?: ComponentErrorHandlerFn;
    addressInfoCache?: any;
}

let _logInstance: any = null;
let _languageObserver: any = null;
let _getComponentErrorHandler: ComponentErrorHandlerFn | null = null;
let _addressInfoCache: any = null;

export function init(options: optionsInterface): void {
    if (options?.log) {
        _logInstance = options.log;
    }

    if (options?.languageObserver) {
        _languageObserver = options.languageObserver;
    }

    if (options?.getComponentErrorHandler) {
        _getComponentErrorHandler = options.getComponentErrorHandler;
    }

    if(options?.addressInfoCache) {
        _addressInfoCache = options.addressInfoCache;
    }
}

export function getLog(): any {
    return _logInstance;
}

export function getLanguageObserver(): any {
    return _languageObserver;
}

export function getErrorHandler(): ComponentErrorHandlerFn | null {
    return _getComponentErrorHandler;
}

export { _addressInfoCache }

export * from "./components/index";
export * from "./components/component-demo";
export { default as icons } from "./components/abc-iconfont/icons";
