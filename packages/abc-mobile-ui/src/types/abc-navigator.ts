import type { ReactNode } from "react";

export interface BottomSheetOptions {
    backgroundColor?: number;
    justifyContent?: JustifyContent;
    dismissWhenTouchOutside?: boolean;
    enableBackgroundAnimation?: boolean;
}

export type JustifyContent =
    | "start"
    | "center"
    | "end"
    | "flex-start"
    | "flex-end"
    | "left"
    | "right"
    | "normal"
    | "space-between"
    | "space-around"
    | "space-evenly"
    | "stretch";

export type AlignItems = "stretch" | "center" | "flex-start" | "flex-end" | "baseline";

export interface DialogOptions {
    routeName?: string;
    alignItems?: AlignItems;
    autoBlurText?: boolean;
    dialogMaskBg?: string;
    borderRadius?: number;
    isHasCloseBtn?: boolean;
    marginVertical?: number;
    contentPadding?: any;
    titleToContentSpace?: number;
    titleLineHeight?: number;
    dismissWhenTouchOutside?: boolean;
}

export interface Route {
    routeName: string;
    component: ReactNode;
    initProps?: any;
    transitionType?: TransitionType;
    backgroundColor?: number | string;
    justifyContent?: JustifyContent;
    alignItems?: AlignItems;
    dismissWhenTouchOutside?: boolean;
    enableBackgroundAnimation?: boolean;
    enableGestureRecognizer?: boolean;
    animated?: false;
    replace?: boolean;
    clearStack?: boolean;
    _innerKey?: string;
    autoBlurText?: boolean;
}

export enum TransitionType {
    none,
    inFromRight,
    inFromBottom,
}

export interface ABCNavigator {
    pop: (options?: any, animation?: boolean, fromEdgeGesture?: boolean) => void;
    showBottomSheet: (node: ReactNode, options?: BottomSheetOptions) => Promise<any>;
    // ...可根据实际补充其它方法签名
}
