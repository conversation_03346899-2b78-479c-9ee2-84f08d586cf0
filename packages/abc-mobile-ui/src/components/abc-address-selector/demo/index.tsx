import React, { useState } from "react";
import { View } from "@hippy/react";
import { AbcAddressSelector } from "../index";
import { AbcText} from "../../abc-text";
import { Colors, Sizes } from "@app/theme";
import { AbcButton } from "../../abc-button";
import { AddressPickerAddressInfo } from "../address-selector";

export function AbcAddressSelectorDemo() {
    const [addressInfo, setAddressInfo] = useState<AddressPickerAddressInfo | undefined | null>(null);
    async function handleSelectAddress() {
        const selectedAddressInfo = await AbcAddressSelector();
        setAddressInfo(selectedAddressInfo)
    }


    return (
        <View>
            <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>基础用法</AbcText>
            <AbcButton onClick={handleSelectAddress}>选择地址</AbcButton>

            <View style={{ marginTop: Sizes.dp16 }}>
                <AbcText style={{ color: Colors.T1 }}>address: </AbcText>
                <View style={{ color: Colors.T2 }}>
                    <AbcText>{ addressInfo?.displayString || ""}</AbcText>
                </View>
            </View>
        </View>
    );
}
