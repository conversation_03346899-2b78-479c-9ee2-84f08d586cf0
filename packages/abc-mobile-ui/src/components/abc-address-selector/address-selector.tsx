import { CityInfo, DistrictInfo, ProvinceInfo } from "./address-info";
import { AddressInfoCache } from "./address-info-cache";
import { View } from "@hippy/react";
import { Colors, Sizes } from "@app/theme";
import { AbcNavigator } from "../abc-navigator";
import { JsonMapper, JsonProperty, pxToDp } from "@app/utils";
import { AbcWheel, wheelHelper } from "../abc-wheel";
import * as React from "react";
import _ from "lodash";

export class AddressPickerAddressInfo {
    @JsonProperty({type: ProvinceInfo})
    provinceInfo?: ProvinceInfo;

    @JsonProperty({type: CityInfo})
    cityInfo?: CityInfo;

    @JsonProperty({type: DistrictInfo})
    districtInfo?: DistrictInfo;

    get displayString(): string {
        const _arr = [];
        if (this.provinceInfo?.name) _arr.push(this.provinceInfo.name);
        if (this.cityInfo?.name) _arr.push(this.cityInfo.name);
        if (this.districtInfo?.name) _arr.push(this.districtInfo.name);

        return _arr.join("/");
    }

    //如果省市区的id都为空，则采用门店设置地址
    get isUseStoreAddress(): boolean {
        return !this.provinceInfo?.id && !this.cityInfo?.id && !this.districtInfo?.id;
    }
}

export async function AbcAddressSelector(initialInfo?: AddressPickerAddressInfo): Promise<AddressPickerAddressInfo | undefined> {
    const view = <AbcAddressPicker initialInfo={initialInfo}/>;
    return AbcNavigator.showBottomSheet(view);
}

interface AddressPickerProps {
    initialInfo?: AddressPickerAddressInfo;
}

const kDefaultProvinceId = "510000"; //默认四川省

class AbcAddressPicker extends React.Component<AddressPickerProps, any> {
    provincesList: Array<ProvinceInfo>;
    citiesList: Array<CityInfo>;
    districtsList: Array<DistrictInfo>;

    provincesInit?: number;
    cityInit?: number;
    districtsInit?: number;

    constructor(props: any) {
        super(props);

        this.provincesList = [];
        this.citiesList = [];
        this.districtsList = [];
    }

    componentDidMount() {
        this._initLoadAddressInfo().then();
    }

    /**
     * 初始化地址列表，首次加载地址信息，可能比较久
     * @private
     */
    async _initLoadAddressInfo() {
        this.provincesList = await AddressInfoCache.getProvincesInfoList();
        const initialInfo = this.props.initialInfo;

        //设置初始选中的省
        let provinceId = initialInfo?.provinceInfo?.id ?? kDefaultProvinceId;
        this.provincesInit = this.provincesList.findIndex((item) => item.id === provinceId);
        if (this.provincesInit < 0) this.provincesInit = 0;

        //设置初始选中的市
        provinceId = this.provincesList[this.provincesInit].id!;
        this.citiesList = await AddressInfoCache.getCityList(provinceId);
        if (_.isEmpty(this.citiesList)) {
            this.cityInit = undefined;
            this.setState({});
        }

        this.cityInit = this.citiesList.findIndex((city) => city.id === initialInfo?.cityInfo?.id);
        if (this.cityInit < 0) this.cityInit = 0;

        this.districtsList = await AddressInfoCache.getDistrictInfoList(this.citiesList[this.cityInit].id!);
        if (_.isEmpty(this.districtsList)) {
            this.districtsInit = undefined;
            this.setState({});
        }
        this.districtsInit = this.districtsList.findIndex((district) => district.id === initialInfo?.districtInfo?.id);
        if (this.districtsInit <= 0) this.districtsInit = 0;

        this.setState({});
    }

    async onChangeProvince(index: number) {
        this.provincesInit = index;
        AddressInfoCache.getCityList(this.provincesList[index].id)
            .then((res) => {
                this.citiesList = res;
                this.districtsList = [];
                this.forceUpdate();
            })
            .then(() => {
                this.onChangeCity(0);
            });
    }

    async onChangeCity(index: number) {
        this.cityInit = index;
        if (this.citiesList.length == 0) {
            this.cityInit = 0;
            this.districtsList = [];
            this.forceUpdate();
            return;
        }

        AddressInfoCache.getDistrictInfoList(this.citiesList[index].id)
            .then((res) => {
                this.districtsList = res;
                this.forceUpdate();
            })
            .then(() => {
                this.onChangeDistricts(0);
            });
    }

    onChangeDistricts(index: number) {
        this.districtsInit = index;
    }

    render() {
        return (
            <View
                style={{
                    backgroundColor: Colors.white,
                    height: pxToDp(358),
                    borderTopLeftRadius: Sizes.dp6,
                    borderTopRightRadius: Sizes.dp6,
                }}
            >
                {wheelHelper.creatTitleBar(() => {
                    const provinceInfo = this.provincesList[this.provincesInit!];
                    const cityInfo = this.citiesList[this.cityInit!];
                    let districtInfo: DistrictInfo | undefined;
                    if (!_.isEmpty(this.districtsList)) {
                        districtInfo = this.districtsList[this.districtsInit!];
                    }

                    AbcNavigator.pop(
                        JsonMapper.deserialize(AddressPickerAddressInfo, {
                            provinceInfo: provinceInfo,
                            cityInfo: cityInfo,
                            districtInfo: districtInfo,
                        })
                    );
                })}
                <View
                    style={{
                        flexGrow: 1,
                        flexDirection: "row",
                        backgroundColor: Colors.white,
                    }}
                >
                    <AbcWheel
                        style={{flex: 1}}
                        initialIndex={this.provincesInit}
                        items={this.provincesList.map((item) => item.name ?? "")}
                        onSelectChanged={this.onChangeProvince.bind(this)}
                    />
                    <AbcWheel
                        style={{flex: 1}}
                        initialIndex={this.cityInit}
                        items={this.citiesList.map((item) => item.name ?? "")}
                        onSelectChanged={this.onChangeCity.bind(this)}
                    />
                    <AbcWheel
                        style={{flex: 1}}
                        initialIndex={this.districtsInit}
                        items={this.districtsList.map((item) => item.name ?? "")}
                        onSelectChanged={this.onChangeDistricts.bind(this)}
                    />
                </View>
            </View>
        );
    }
}
