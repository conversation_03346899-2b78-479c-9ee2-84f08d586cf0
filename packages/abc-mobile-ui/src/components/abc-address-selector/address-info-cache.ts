import { JsonMapper } from "@app/utils";
import { CityInfo, DistrictInfo, ProvinceInfo } from "./address-info";
import { addressData } from "./area";
import { _addressInfoCache } from '../../index'

type AnyType = any;
export class AddressInfoCache {
    static _addressInfoCache: AnyType;
    static async getProvincesInfoList(): Promise<Array<ProvinceInfo>> {
        let data = null
        // 优先读取配置
        data = await _addressInfoCache?.getAddressInfo();
        if (!data) {
            data = addressData
        }
        this._addressInfoCache = data;
        console.log("_addressInfoCache-----",this._addressInfoCache)
        const provinces = this._addressInfoCache["100000"];
        console.log("provinces-------", provinces)
        const provinceInfoList: Array<ProvinceInfo> = [];

        console.log("ProvinceInfo------", ProvinceInfo)
        for (const key in provinces) {
            provinceInfoList.push(JsonMapper.deserialize(ProvinceInfo, { id: key, name: provinces[key] }));
        }
        console.log("provinceInfoList-----", provinceInfoList)

        return provinceInfoList;
    }

    static async getCityList(provinceId?: string): Promise<Array<CityInfo>> {
        if (!provinceId) return [{ name: "请选择" }];
        const cities = this._addressInfoCache[provinceId];
        const cityList: Array<CityInfo> = [];

        for (const key in cities) {
            cityList.push(JsonMapper.deserialize(CityInfo, { id: key, name: cities[key] }));
        }
        return cityList;
    }

    static async getDistrictInfoList(cityId?: string): Promise<Array<DistrictInfo>> {
        if (!cityId) return [{ name: "请选择" }];
        const districts = this._addressInfoCache[cityId];
        const districtList: Array<DistrictInfo> = [];

        for (const key in districts) {
            districtList.push(JsonMapper.deserialize(DistrictInfo, { id: key, name: districts[key] }));
        }

        return districtList;
    }
}
