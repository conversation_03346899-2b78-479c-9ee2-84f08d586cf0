import React, { useState } from "react";
import { View } from "@hippy/react";
import { AbcCircle } from "../circle";
import { AbcFlex } from "../../abc-flex";
import {ABCStyles, Colors, FontWeights, Sizes} from "@app/theme";
import { AbcText } from "../../abc-text";
import { AbcButton } from "../../abc-button";
import { AbcScroll } from "../../abc-scroll";

export function AbcCircleDemo() {
    const [rate, setRate] = useState(0);
    const [size, setSize] = useState(64);
    const [strokeWidth, setStrokeWidth] = useState(4);
    const [theme, setTheme] = useState("green");
    const [showSuccessIcon, setShowSuccessIcon] = useState(false);

    const format = (rate: number) => Math.min(Math.max(rate, 0), 100);

    const handleClick = (type: string) => {
        const isAdd = type === 'add';
        setRate((r) => isAdd ? format(r + 20) : format(r - 20));
    };

    const themes = [
        { name: "green", label: "green", value: "green" },
        { name: "blue", label: "blue", value: "blue" },
        { name: "yellow", label: "yellow", value: "yellow" },
        { name: "red", label: "red", value: "red" },
    ];

    const sizes = [
        { name: "small", label: "48px", value: 48 },
        { name: "normal", label: "64px", value: 64 },
        { name: "large", label: "80px", value: 80 },
        { name: "xlarge", label: "100px", value: 100 },
    ];

    const strokeWidths = [
        { name: "thin", label: "2px", value: 2 },
        { name: "normal", label: "4px", value: 4 },
        { name: "thick", label: "6px", value: 6 },
        { name: "thickest", label: "8px", value: 8 },
    ];

    return (
        <AbcScroll style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
            <View style={{ marginBottom: 16 }}>
                <AbcText size="large" fontWeight={FontWeights.bold} style={{ marginBottom: 8 }}>
                    AbcCircle 组件演示
                </AbcText>
                <AbcText size="tiny" style={{ marginBottom: 4 }}>
                    圆形进度条组件，支持自定义尺寸、颜色、进度值等属性
                </AbcText>
                <AbcText size="tiny" style={{ marginBottom: 4 }}>
                    进度达到100%时可以通过showSuccessIcon设置显示成功图标并触发动画效果
                </AbcText>
            </View>

            <View style={{ marginBottom: 16 }}>
                <AbcText size="large" fontWeight={FontWeights.bold} style={{ marginBottom: 8 }}>
                    基础用法
                </AbcText>
                <AbcText size="tiny" style={{ marginBottom: 4 }}>
                    通过 rate 属性控制进度，通过 theme 控制颜色主题
                </AbcText>
                <AbcText size="tiny" style={{ marginBottom: 4 }}>
                    通过 strokeWidth 控制边框宽度
                </AbcText>
            </View>

            <View style={{ marginBottom: 16 }}>
                <AbcText size="normal" style={{ marginBottom: 8 }}>
                    不同进度值展示：
                </AbcText>
                <AbcFlex gap={8} wrap="wrap">
                    <AbcCircle rate={0} />
                    <AbcCircle rate={25} />
                    <AbcCircle rate={50} />
                    <AbcCircle rate={75} />
                    <AbcCircle rate={100} />
                    <AbcCircle rate={100} showSuccessIcon={true} />
                </AbcFlex>
            </View>

            <View style={{ marginBottom: 16 }}>
                <AbcText size="normal" style={{ marginBottom: 8 }}>
                    不同主题色展示：
                </AbcText>
                <AbcFlex gap={8} wrap="wrap">
                    <AbcCircle rate={75} theme="green" />
                    <AbcCircle rate={75} theme="blue" />
                    <AbcCircle rate={75} theme="yellow" />
                    <AbcCircle rate={75} theme="red" />
                </AbcFlex>
            </View>

            <View style={{ marginBottom: 16 }}>
                <AbcText size="normal" style={{ marginBottom: 8 }}>
                    不同边框宽度展示：
                </AbcText>
                <AbcFlex gap={8} wrap="wrap">
                    <AbcCircle rate={60} strokeWidth={2} />
                    <AbcCircle rate={60} strokeWidth={4} />
                    <AbcCircle rate={60} strokeWidth={6} />
                    <AbcCircle rate={60} strokeWidth={8} />
                </AbcFlex>
            </View>

            {/* 交互式配置区域 */}
            <AbcText size="large" fontWeight={FontWeights.bold} style={{ marginBottom: 12 }}>
                交互式配置
            </AbcText>

            {/* 尺寸选择 */}
            <AbcText size="normal" style={{ marginBottom: 8 }}>
                尺寸选择：
            </AbcText>
            <View style={{ flexDirection: "row", flexWrap: "wrap", marginBottom: 16 }}>
                {sizes.map((sizeOption) => (
                    <View
                        key={sizeOption.name}
                        style={{
                            flexDirection: "row",
                            alignItems: "center",
                            marginRight: 16,
                            marginBottom: 8,
                        }}
                    >
                        <View
                            style={{
                                width: 16,
                                height: 16,
                                borderRadius: 8,
                                borderWidth: 2,
                                borderColor: size === sizeOption.value ? "#1890ff" : "#d9d9d9",
                                backgroundColor: size === sizeOption.value ? "#1890ff" : "transparent",
                                marginRight: 8,
                            }}
                            onClick={() => setSize(sizeOption.value)}
                        />
                        <AbcText size="mini" style={{ color: "#666" }} onClick={() => setSize(sizeOption.value)}>
                            {sizeOption.label}
                        </AbcText>
                    </View>
                ))}
            </View>

            {/* 边框宽度选择 */}
            <AbcText size="normal" style={{ marginBottom: 8 }}>
                边框宽度：
            </AbcText>
            <View style={{ flexDirection: "row", flexWrap: "wrap", marginBottom: 16 }}>
                {strokeWidths.map((width) => (
                    <View
                        key={width.name}
                        style={{
                            flexDirection: "row",
                            alignItems: "center",
                            marginRight: 16,
                            marginBottom: 8,
                        }}
                    >
                        <View
                            style={{
                                width: 16,
                                height: 16,
                                borderRadius: 8,
                                borderWidth: 2,
                                borderColor: strokeWidth === width.value ? "#1890ff" : "#d9d9d9",
                                backgroundColor: strokeWidth === width.value ? "#1890ff" : "transparent",
                                marginRight: 8,
                            }}
                            onClick={() => setStrokeWidth(width.value)}
                        />
                        <AbcText size="mini" style={{ color: "#666" }} onClick={() => setStrokeWidth(width.value)}>
                            {width.label}
                        </AbcText>
                    </View>
                ))}
            </View>

            {/* 主题选择 */}
            <AbcText size="normal" style={{ marginBottom: 8 }}>
                主题选择：
            </AbcText>
            <View style={{ flexDirection: "row", flexWrap: "wrap", marginBottom: 16 }}>
                {themes.map((themeOption) => (
                    <View
                        key={themeOption.name}
                        style={{
                            flexDirection: "row",
                            alignItems: "center",
                            marginRight: 16,
                            marginBottom: 8,
                        }}
                    >
                        <View
                            style={{
                                width: 16,
                                height: 16,
                                borderRadius: 8,
                                borderWidth: 2,
                                borderColor: theme === themeOption.value ? "#1890ff" : "#d9d9d9",
                                backgroundColor: theme === themeOption.value ? "#1890ff" : "transparent",
                                marginRight: 8,
                            }}
                            onClick={() => setTheme(themeOption.value)}
                        />
                        <AbcText size="mini" style={{ color: "#666" }} onClick={() => setTheme(themeOption.value)}>
                            {themeOption.label}
                        </AbcText>
                    </View>
                ))}
            </View>

            {/* 成功图标开关 */}
            <View style={{ flexDirection: "row", alignItems: "center", marginBottom: 16 }}>
                <AbcText size="normal" style={{ marginRight: 8 }}>
                    显示成功图标：
                </AbcText>
                <View
                    style={{
                        width: 44,
                        height: 24,
                        borderRadius: 12,
                        backgroundColor: showSuccessIcon ? "#1890ff" : "#d9d9d9",
                        justifyContent: "center",
                        alignItems: showSuccessIcon ? "flex-end" : "flex-start",
                        paddingHorizontal: 2,
                    }}
                    onClick={() => setShowSuccessIcon(!showSuccessIcon)}
                >
                    <View
                        style={{
                            width: 20,
                            height: 20,
                            borderRadius: 10,
                            backgroundColor: "#fff",
                        }}
                    />
                </View>
            </View>

            {/* 预览区域 */}
            <View
                style={{
                    backgroundColor: "#f5f5f5",
                    padding: 16,
                    borderRadius: 8,
                    marginBottom: 24,
                    alignItems: "center",
                }}
            >
                <AbcText size="normal" style={{ marginBottom: 12, color: "#666" }}>
                    当前效果预览：
                </AbcText>
                <AbcCircle
                    rate={rate}
                    size={size}
                    strokeWidth={strokeWidth}
                    theme={theme}
                    showSuccessIcon={showSuccessIcon}
                    style={{ marginBottom: 8 }}
                />
                <AbcText size="mini" style={{ color: "#999" }}>
                    {`进度: ${Math.round(rate)}% | 尺寸: ${size}px | 边框: ${strokeWidth}px | 主题: ${theme}`}
                </AbcText>
            </View>

            {/* 控制按钮 */}
            <View style={[ABCStyles.centerChild, { marginBottom: 24 }]}>
                <AbcFlex gap={8}>
                    <AbcButton onClick={() => handleClick('add')}>增加进度</AbcButton>
                    <AbcButton onClick={() => handleClick('reduce')}>减少进度</AbcButton>
                    <AbcButton onClick={() => setRate(100)}>设为100%</AbcButton>
                    <AbcButton onClick={() => setRate(0)}>重置</AbcButton>
                </AbcFlex>
            </View>

            {/* 自定义文本 */}
            <View style={{ marginBottom: 16 }}>
                <AbcText size="normal" style={{ marginBottom: 8 }}>
                    自定义文本：
                </AbcText>
                <AbcCircle rate={25}>
                    <AbcText size="tiny" style={{ color: Colors.t3, fontSize: Sizes.dp20 }}>
                        进行中
                    </AbcText>
                </AbcCircle>
                <AbcText size="tiny" style={{ color: "#999" }}>
                    提示：自定义文本会覆盖默认的百分比显示
                </AbcText>
            </View>

            {/* 属性详情表格 */}
            <AbcText size="large" fontWeight={FontWeights.bold} style={{ marginTop: 24, marginBottom: 12 }}>
                属性详情：
            </AbcText>
            <View style={{ backgroundColor: "#f9f9f9", padding: 16, borderRadius: 8 }}>
                {/* 表格头部 */}
                <View
                    style={{
                        flexDirection: "row",
                        borderBottomWidth: 1,
                        borderBottomColor: "#e8e8e8",
                        paddingBottom: 8,
                        marginBottom: 8,
                    }}
                >
                    <AbcText size="mini" fontWeight={FontWeights.bold} style={{ color: "#333", flex: 1, paddingRight: 12 }}>
                        属性名
                    </AbcText>
                    <AbcText size="mini" fontWeight={FontWeights.bold} style={{ color: "#333", flex: 2, paddingRight: 12 }}>
                        描述
                    </AbcText>
                    <AbcText size="mini" fontWeight={FontWeights.bold} style={{ color: "#333", flex: 2, paddingRight: 12 }}>
                        类型
                    </AbcText>
                    <AbcText size="mini" fontWeight={FontWeights.bold} style={{ color: "#333", flex: 1 }}>
                        默认值
                    </AbcText>
                </View>

                {/* 表格内容 */}
                <View style={{ flexDirection: "row", paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: "#f0f0f0" }}>
                    <AbcText size="mini" style={{ color: "#333", flex: 1, paddingRight: 12 }}>
                        size
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#666", flex: 2, paddingRight: 12 }}>
                        圆形进度条尺寸
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#999", flex: 2, paddingRight: 12 }}>
                        number | string
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#999", flex: 1 }}>
                        64
                    </AbcText>
                </View>

                <View style={{ flexDirection: "row", paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: "#f0f0f0" }}>
                    <AbcText size="mini" style={{ color: "#333", flex: 1, paddingRight: 12 }}>
                        rate
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#666", flex: 2, paddingRight: 12 }}>
                        进度值，控制进度条显示的百分比
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#999", flex: 2, paddingRight: 12 }}>
                        number
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#999", flex: 1 }}>
                        0
                    </AbcText>
                </View>

                <View style={{ flexDirection: "row", paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: "#f0f0f0" }}>
                    <AbcText size="mini" style={{ color: "#333", flex: 1, paddingRight: 12 }}>
                        circleTotal
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#666", flex: 2, paddingRight: 12 }}>
                        进度总值，用于计算完成状态
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#999", flex: 2, paddingRight: 12 }}>
                        number
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#999", flex: 1 }}>
                        100
                    </AbcText>
                </View>

                <View style={{ flexDirection: "row", paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: "#f0f0f0" }}>
                    <AbcText size="mini" style={{ color: "#333", flex: 1, paddingRight: 12 }}>
                        showSuccessIcon
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#666", flex: 2, paddingRight: 12 }}>
                        是否在进度完成时显示成功图标
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#999", flex: 2, paddingRight: 12 }}>
                        boolean
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#999", flex: 1 }}>
                        false
                    </AbcText>
                </View>

                <View style={{ flexDirection: "row", paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: "#f0f0f0" }}>
                    <AbcText size="mini" style={{ color: "#333", flex: 1, paddingRight: 12 }}>
                        theme
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#666", flex: 2, paddingRight: 12 }}>
                        进度条主题色
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#999", flex: 2, paddingRight: 12 }}>
                        {`"green" | "blue" | "yellow" | "red"`}
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#999", flex: 1 }}>
                        green
                    </AbcText>
                </View>

                <View style={{ flexDirection: "row", paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: "#f0f0f0" }}>
                    <AbcText size="mini" style={{ color: "#333", flex: 1, paddingRight: 12 }}>
                        strokeWidth
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#666", flex: 2, paddingRight: 12 }}>
                        进度条边框宽度
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#999", flex: 2, paddingRight: 12 }}>
                        number
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#999", flex: 1 }}>
                        4
                    </AbcText>
                </View>

                <View style={{ flexDirection: "row", paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: "#f0f0f0" }}>
                    <AbcText size="mini" style={{ color: "#333", flex: 1, paddingRight: 12 }}>
                        children
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#666", flex: 2, paddingRight: 12 }}>
                        自定义文本内容，会覆盖默认的百分比显示
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#999", flex: 2, paddingRight: 12 }}>
                        React.ReactNode
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#999", flex: 1 }}>
                        -
                    </AbcText>
                </View>

                <View style={{ flexDirection: "row", paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: "#f0f0f0" }}>
                    <AbcText size="mini" style={{ color: "#333", flex: 1, paddingRight: 12 }}>
                        style
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#666", flex: 2, paddingRight: 12 }}>
                        自定义样式
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#999", flex: 2, paddingRight: 12 }}>
                        CSSProperties
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#999", flex: 1 }}>
                        -
                    </AbcText>
                </View>

                <View style={{ flexDirection: "row", paddingVertical: 8 }}>
                    <AbcText size="mini" style={{ color: "#333", flex: 1, paddingRight: 12 }}>
                        onChange
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#666", flex: 2, paddingRight: 12 }}>
                        进度变化时的回调函数
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#999", flex: 2, paddingRight: 12 }}>
                        {`(rate: number) => void`}
                    </AbcText>
                    <AbcText size="mini" style={{ color: "#999", flex: 1 }}>
                        -
                    </AbcText>
                </View>
            </View>
        </AbcScroll>
    );
}
