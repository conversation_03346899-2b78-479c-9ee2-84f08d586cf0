import React, { CSSProperties, useEffect, useMemo, useState, useRef } from "react";
import { View, Text } from "@hippy/react";
import { AbcSvg } from "../abc-svg";
import { pxToDp } from "@app/utils";
import { Colors } from "@app/theme";

export interface CircleProps {
    size?: number | string;
    rate?: number; // 受控值 (0-100范围或自定义)
    circleTotal?: number; // 自定义受控总值
    showSuccessIcon?: boolean;
    theme?: string;
    layerColor?: string; // 背景环颜色
    fill?: string; // 填充色
    strokeWidth?: number; // 线宽
    style?: CSSProperties;
    children?: React.ReactNode;
    onChange?: (rate: number) => void;
}

export const enum CircleTheme {
    Green = "green",
    Blue = "blue",
    Yellow = "yellow",
    Red = "red",
}

const COLOR_MAP: Record<string, string> = {
    [CircleTheme.Green]: Colors.border_color_success1,
    [CircleTheme.Blue]: Colors.border_color_theme1,
    [CircleTheme.Yellow]: Colors.border_color_warning1,
    [CircleTheme.Red]: Colors.border_color_error1,
    default: Colors.border_color_success1, // 默认颜色
};

function formatRate(rate: number | undefined) {
    if (rate == null) return 0;
    const n = +rate;
    if (Number.isNaN(n)) return 0;
    return Math.min(Math.max(n, 0), 100);
}

function getPath(viewBoxSize: number, radius: number) {
    const centerX = viewBoxSize / 2;
    const centerY = viewBoxSize / 2;
    // 标准圆形路径：从12点钟开始，顺时针绘制
    return `M ${centerX} ${centerY - radius} A ${radius} ${radius} 0 1 1 ${centerX} ${centerY + radius} A ${radius} ${radius} 0 1 1 ${centerX} ${centerY - radius}`;
}

export const AbcCircle: React.FC<CircleProps> = (circleProps) => {
    const props = {
        rate: 0,
        circleTotal: 100,
        fill: "none",
        size: 64,
        strokeWidth: 4,
        layerColor: "#ffffff",
        theme: CircleTheme.Green,
        ...circleProps,
    };

    // 动画态值
    const [currentRate, setCurrentRate] = useState(formatRate(props.rate));

    const radius = useMemo(() => {
        const actualSize = pxToDp(+props.size);
        return (actualSize - props.strokeWidth) / 2;
    }, [props.size, props.strokeWidth]);

    const viewBoxSize = useMemo(() => radius * 2 + props.strokeWidth, [radius, props.strokeWidth]);
    const iconWidth = viewBoxSize * 24 / 64;
    const iconHeight = viewBoxSize * 20 / 64;

    const rafIdRef = useRef<number | null>(null);

    // 动画：当目标值或速度变化时触发
    useEffect(() => {
        const speed = 100;
        const startTime = Date.now();
        const startRate = currentRate;
        const endRate = formatRate(props.rate);
        const duration = Math.abs(((startRate - endRate) * 1000) / speed);

        const animate = () => {
            const now = Date.now();
            const progress = Math.min((now - startTime) / duration, 1);
            const rate = progress * (endRate - startRate) + startRate;
            const crate = formatRate(parseFloat(rate.toFixed(1)));
            setCurrentRate(crate);
            if (endRate > startRate ? rate < endRate : rate > endRate) {
                rafIdRef.current = requestAnimationFrame(animate);
            } else {
                props.onChange?.(crate);
            }
        };

        if (rafIdRef.current) {
            cancelAnimationFrame(rafIdRef.current);
        }
        rafIdRef.current = requestAnimationFrame(animate);
    }, [props.rate]);

    const path = useMemo(() => getPath(viewBoxSize, radius), [viewBoxSize, radius]);

    const renderLayer = () => {
        return `<path fill="${props.fill}" stroke="${props.layerColor}" stroke-width="${pxToDp(props.strokeWidth)}" d="${path}" />`;
    };

    const renderHover = () => {
        const PERIMETER = Math.PI * 2 * radius;
        const offset = (PERIMETER * currentRate) / 100;
        const color = COLOR_MAP[props.theme] || COLOR_MAP.default;

        return `<path fill="none" stroke="${color}" stroke-width="${pxToDp(
            props.strokeWidth
        )}" stroke-linecap="round" stroke-dasharray="${pxToDp(offset)} ${pxToDp(PERIMETER)}" d="${path}" />`;
    };

    const renderText = () => {
        if (props.children) {
            return props.children;
        }
        const content = `${Math.round(currentRate)}%`;
        return <Text style={{ textAlign: "center" }}>{content as any}</Text>;
    };

    const getSizeStyle = (originSize?: string | number) => {
        if (originSize !== undefined && originSize !== null) {
            const size = pxToDp(+originSize);
            return {
                width: size,
                height: size,
            };
        }
        return {};
    };

    const boxStyle: CSSProperties = {
        position: "relative",
        alignItems: "center",
        justifyContent: "center",
        ...getSizeStyle(props.size),
        ...(props.style || {}),
    };

    const svgContent = `
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${viewBoxSize} ${viewBoxSize}">
            ${renderLayer()}
            ${renderHover()}
        </svg>
    `.trim();

    const successSvgContent = `
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${viewBoxSize} ${viewBoxSize}">
            <path fill="${Colors.success}" d="${path}" />
        </svg>
    `.trim();

    const successIconSvgContent = `
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${iconWidth} ${iconHeight}">
            <path d="M${(iconWidth * 2) / 24} ${(iconHeight * 10.3333) / 20}L${(iconWidth * 9.77778) / 24} ${(iconHeight * 18) / 20}L${(iconWidth * 22) / 24} ${(iconHeight * 2) / 20}" stroke="white" stroke-width="${pxToDp(iconWidth * 4 / 24)}" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    `.trim();

    const { showSuccessIcon, circleTotal } = props;
    const [scale, setScale] = useState(1);
    const animationRef = useRef<number>();
    const finished = showSuccessIcon && currentRate >= circleTotal;

    // 监听 finished 状态变化
    useEffect(() => {
        if (finished) {
            // 重置缩放状态
            setScale(0.7);

            // 开始动画
            const duration = 200; // 动画总时长(ms)
            const startScale = 0.7; // 起始缩放比例
            const peakScale = 1.1;  // 峰值缩放比例
            const endScale = 1;     // 结束缩放比例
            const startTime = Date.now();

            const animate = () => {
                const now = Date.now();
                const elapsed = now - startTime;
                const progress = Math.min(elapsed / duration, 1);

                if (progress < 0.5) {
                    // 从0.7缩放到1.1
                    const scaleProgress = progress * 2; // 0-1
                    setScale(startScale + (peakScale - startScale) * scaleProgress);
                } else {
                    // 从1.1缩放到1.0
                    const scaleProgress = (progress - 0.5) * 2; // 0-1
                    setScale(peakScale - (peakScale - endScale) * scaleProgress);
                }

                if (progress < 1) {
                    animationRef.current = requestAnimationFrame(animate);
                }
            };

            animationRef.current = requestAnimationFrame(animate);
        } else {
            setScale(1);
        }

        return () => {
            if (animationRef.current) {
                cancelAnimationFrame(animationRef.current);
            }
        };
    }, [finished]);

    return (
        <View style={boxStyle}>
            {!finished ? (
                <>
                    <AbcSvg content={svgContent} style={{ ...getSizeStyle(props.size) }} />
                    <View
                        style={{
                            position: "absolute",
                            right: 0,
                            left: 0,
                            top: 0,
                            bottom: 0,
                            alignItems: "center",
                            justifyContent: "center",
                        }}
                    >
                        {renderText()}
                    </View>
                </>
            ) : (
                <>
                    <AbcSvg
                        content={successSvgContent}
                        style={{
                            ...getSizeStyle(props.size),
                            transform: [{ scale }],
                            transformOrigin: 'center',
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0
                        }}
                    />
                    <View
                        style={{
                            position: "absolute",
                            right: 0,
                            left: 0,
                            top: 0,
                            bottom: 0,
                            alignItems: "center",
                            justifyContent: "center",
                        }}
                    >
                        <AbcSvg
                            content={successIconSvgContent}
                            style={{
                                width: pxToDp(iconWidth),
                                height: pxToDp(iconHeight),
                                transform: [{ scale }],
                                transformOrigin: 'center'
                            }}
                        />
                    </View>
                </>
            )}
        </View>
    );
};

export default AbcCircle;
