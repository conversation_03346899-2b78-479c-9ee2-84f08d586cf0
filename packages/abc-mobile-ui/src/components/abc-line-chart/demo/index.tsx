import React from "react";
import { View, Text } from "@hippy/react";
import { Abc<PERSON><PERSON><PERSON><PERSON>, LineChartDataSet } from "../index";
import { Sizes, TextStyles, Colors } from "@app/theme";
import _ from "lodash";
import { prevDate } from "@app/utils";
import { AbcText } from "../../abc-text";

export function AbcLineChartDemo() {
    const data: LineChartDataSet = {
        values: [],
        lineWidth: 1.5,
        lineColor: Colors.mainColor,
        circleColor: Colors.mainColor,
        drawCircles: true,
        circleRadius: 1.5,
    };

    const list = [
        {
            date: prevDate(new Date(), 2),
            value: 22,
        },
        {
            date: prevDate(new Date(), 1),
            value: 12,
        },
        {
            date: new Date(),
            value: 8,
        },
    ];

    let maxPrice = 0;
    data.values = list.map((item) => {
        maxPrice = Math.max(maxPrice, item.value);
        return {
            x: item.date.getTime(),
            y: item.value,
        };
    });

    // @ts-ignore
    const axisMinimum = _.first(list)?.date.getTime() ?? 0;
    // @ts-ignore
    const axisMaximum = _.last(list)?.date.getTime() ?? 0;

    return (
        <View>
            <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>基础用法</AbcText>
            <AbcLineChart
                style={{ height: Sizes.dp140, marginBottom: Sizes.dp5 }}
                xAxis={{
                    labelPosition: "bottom",
                    labelFontStyle: TextStyles.t10NT2,
                    axisMinimum: axisMinimum,
                    axisMaximum: axisMaximum,
                    valueFormatter: { type: "time", format: "MM.dd" },
                }}
                leftAxis={{ labelPosition: "outside", labelFontStyle: TextStyles.t10NT2, axisMinimum: 0, axisMaximum: maxPrice }}
                data={[data]}
            />
        </View>
    );
}
