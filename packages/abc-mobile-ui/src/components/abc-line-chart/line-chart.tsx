/* eslint-disable no-underscore-dangle */

import React from "react";
import { colorParse, Style } from "@hippy/react";
import { TextStyle } from "@app/theme";

interface Axis {
    labelFontStyle?: TextStyle;

    axisMinimum?: number; //最小值
    axisMaximum?: number; //最大值
    granularity?: number; //轴上最小间隔
}

interface LineChartViewProps {
    style?: Style | Style[];
    xAxis?: XAxis;
    leftAxis?: YAxis;
    data: LineChartDataSet[];
}

interface XAxis extends Axis {
    labelPosition?: "topInside" | "bottomInside" | "top" | "bottom";
    valueFormatter?: {
        type: "time";
        format: string; // "MM.dd"
    };
}

interface YAxis extends Axis {
    labelPosition?: "inside" | "outside";
    labelFontStyle?: TextStyle;
}

export interface LineChartDataSet {
    label?: string;
    axisDependency?: "left" | "right";

    lineWidth?: number; //1.0
    lineColor?: string;
    circleColor?: string;
    drawCircles?: boolean;
    circleRadius?: number;
    values: { x: number; y: number }[];
}

export class AbcLineChart extends React.Component<LineChartViewProps, {}> {
    static defaultProps = {
        items: [],
        initialIndex: 0,
    };

    private instance: HTMLDivElement | null = null;

    /**
     * @ignore
     */
    constructor(props: LineChartViewProps) {
        super(props);
    }

    /**
     * @ignore
     */
    public render(): JSX.Element {
        const { xAxis, leftAxis, data, ...nativeProps } = this.props;

        this._transformAxis(xAxis);
        this._transformAxis(leftAxis);
        /**
         * 解决axisMaximum = 0 时，图标显示异常问题
         */
        leftAxis!.axisMaximum = Math.max(10, leftAxis?.axisMaximum ?? 0);

        this._transformData(data);

        return (
            <div
                // @ts-ignore
                nativeName="LineChartView"
                ref={(ref: HTMLDivElement) => {
                    this.instance = ref;
                }}
                xAxis={xAxis}
                leftAxis={leftAxis}
                data={data}
                {...nativeProps}
            />
        );
    }

    private _transformAxis(axis?: XAxis | YAxis) {
        if (axis?.labelFontStyle) {
            const labelFontStyle: any = {};
            Object.assign(labelFontStyle, axis.labelFontStyle);
            labelFontStyle.color = colorParse(labelFontStyle.color);
            axis.labelFontStyle = labelFontStyle;
        }
    }

    private _transformData(data: LineChartDataSet[]) {
        data.forEach((data) => {
            if (data.lineColor != undefined) (data as any).lineColor = colorParse(data.lineColor);

            if (data.circleColor) (data as any).circleColor = colorParse(data.circleColor);
        });
    }
}
