import { DescribeItem } from "./calender-interface";

export type DateType = "date" | "daterange";
export interface PickerOptions {
    // 具体结构请根据实际补充
    [key: string]: any;
}

type DateValue = Date | string;
type DateRangeValue = [DateValue, DateValue];

export interface CalendarProps {
    type?: DateType;
    describeList?: DescribeItem[];
    pickerOptions?: PickerOptions;
    date?: Date | string;
    rangeDate?: DateValue | DateRangeValue;
    beginYear?: number;
    endYear?: number;
    displayDateRange?: boolean; // 是否展示时间范围
    shortcutStyle?: Object; // 自定义快捷操作区样式
    initToast?: boolean; // 初始化是否toast提示
    changeDateToast?: boolean; // 选择了时间是否toast提示
    selectionDateClose?: boolean; // 选择日期是否关闭
    // 日期单选
    onChange?(date: Date): void;
    // 日期范围
    onConfirm?: (params: { dateRange: Date[]; label?: string }) => void;
    onCancel?(): void;
}

export interface DatePanelProps {
    type?: DateType;
    date?: Date | string;
    selectionDateClose?: boolean; // 选择日期是否关闭
    describeList?: DescribeItem[];
    pickerOptions?: PickerOptions;
    beginYear: number;
    endYear: number;
    // 日期单选
    onChange?(date: Date): void;
}
export interface DateRangePanelProps {
    type?: DateType;
    date?: DateValue | DateRangeValue;
    describeList?: DescribeItem[];
    pickerOptions?: PickerOptions;
    beginYear: number;
    endYear: number;
    displayDateRange?: boolean;
    shortcutStyle?: Object; // 自定义快捷操作区样式
    initToast?: boolean; // 初始化是否toast提示
    changeDateToast?: boolean; // 选择了时间是否toast提示
    // 日期范围
    onConfirm?: (params: { dateRange: Date[]; label?: string }) => void;
    onCancel?(): void;
}

export interface RangeStateInterface {
    startDate: Date | null;
    endDate: Date | null;
    selecting: boolean;
}
