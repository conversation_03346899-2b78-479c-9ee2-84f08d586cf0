import React, { useRef, useState } from "react";
import { Text, View } from "@hippy/react";
import { DatePanelProps } from "./date-constants";

import { AbcDateTable } from "./date-table";
import { ABCStyles, Colors, Sizes, TextStyles } from "@app/theme";
import { AbcView } from "../abc-view/index";
import { AbcIconfont } from "../abc-iconfont/index";
import { AbcSizedBox } from "../abc-size-box/index";
import { getNextMonthFirstDay, getThisMonthFirstDay } from "@app/utils";
import { AbcFlex } from "../abc-flex";
import { AbcDatePicker } from "../abc-date-picker";

// 定义暴露给父组件的方法类型
interface AbcDateTableRef {
    setMonthPage: (forceSetPage?: boolean) => void;
}

const AbcDatePanel: React.FC<DatePanelProps> = (props: DatePanelProps) => {
    const { type = "date", pickerOptions, describeList, date, beginYear, endYear, selectionDateClose = false, onChange } = props || {};

    const time = date ? new Date(date) : new Date();
    const currentValue = date ? new Date(date) : undefined;
    const [currentMonthDate, setCurrentMonthDate] = useState(getThisMonthFirstDay(time));
    // 监听 props.date 变化
    React.useEffect(() => {
        if (date) {
            setCurrentMonthDate(getThisMonthFirstDay(new Date(date)));
        }
    }, [date]);
    const abcDateTable = useRef<AbcDateTableRef>(null);
    const pre = currentMonthDate > new Date(beginYear, 0, 1);
    const next = currentMonthDate < new Date(endYear, 11, 1);

    const preMouth = (forceSetPage = false) => {
        if (!pre) return;
        const _currentMonthDate = getThisMonthFirstDay(new Date(currentMonthDate.getTime() - 1));
        setCurrentMonthDate(_currentMonthDate);
        abcDateTable.current?.setMonthPage?.(forceSetPage);
    };

    const nextMouth = (forceSetPage = false) => {
        if (!next) return;
        const _currentMonthDate = getNextMonthFirstDay(currentMonthDate);
        setCurrentMonthDate(_currentMonthDate);
        abcDateTable.current?.setMonthPage?.(forceSetPage);
    };

    function handleChangeDate(date: Date) {
        onChange?.(date);
    }

    async function handleClick() {
        const minDate = new Date(beginYear, 0, 1);
        const maxDate = new Date(endYear, 11, 1);
        const select = await AbcDatePicker.show(new Date(currentMonthDate), { minDate, maxDate, showDay: false });
        if (select) {
            const _currentMonthDate = getThisMonthFirstDay(new Date(select));
            setCurrentMonthDate(_currentMonthDate);
            abcDateTable.current?.setMonthPage?.(true);
        }
    }

    return (
        <View style={{ backgroundColor: Colors.bg_white }}>
            <View style={[ABCStyles.rowAlignCenterSpaceBetween, { height: Sizes.dp56, padding: Sizes.dp16 }]}>
                <AbcFlex align="center" style={{ lineHeight: Sizes.dp24 }} onClick={handleClick}>
                    <Text style={[TextStyles.t18MT1.copyWith({ lineHeight: Sizes.dp24 })]}>
                        {`${currentMonthDate.format("yyyy 年 MM 月")}`}
                    </Text>
                    <AbcIconfont
                        style={[
                            ABCStyles.centerChild,
                            {
                                width: Sizes.dp24,
                                height: Sizes.dp24,
                                lineHeight: Sizes.dp24,
                            },
                        ]}
                        name={"Dropdown_Triangle"}
                        size={Sizes.dp18}
                        color={Colors.t1}
                    />
                </AbcFlex>
                <View style={[ABCStyles.rowAlignCenterSpaceBetween]}>
                    <AbcView
                        style={[
                            ABCStyles.centerChild,
                            {
                                width: Sizes.dp24,
                                height: Sizes.dp24,
                                borderWidth: Sizes.dp1,
                                borderColor: Colors.border_color_light,
                                borderRadius: Sizes.dp6,
                            },
                            { transform: [{ rotateY: "180deg" }] },
                        ]}
                        onClick={() => {
                            preMouth(true);
                        }}
                    >
                        <AbcIconfont name={"Arrow_Right"} size={Sizes.dp16} color={pre ? Colors.T1 : Colors.T4} />
                    </AbcView>
                    <AbcSizedBox width={Sizes.dp32} />
                    <AbcView
                        style={[
                            ABCStyles.centerChild,
                            {
                                width: Sizes.dp24,
                                height: Sizes.dp24,
                                borderWidth: Sizes.dp1,
                                borderColor: Colors.border_color_light,
                                borderRadius: Sizes.dp6,
                            },
                        ]}
                        onClick={() => {
                            nextMouth(true);
                        }}
                    >
                        <AbcIconfont
                            style={{ width: Sizes.dp16, height: Sizes.dp16, lineHeight: Sizes.dp16 }}
                            name={"Arrow_Right"}
                            size={Sizes.dp16}
                            color={next ? Colors.T1 : Colors.T4}
                        />
                    </AbcView>
                </View>
            </View>
            <AbcDateTable
                ref={abcDateTable}
                selectionMode={type}
                value={currentValue}
                date={currentMonthDate}
                beginYear={beginYear}
                endYear={endYear}
                describeList={describeList}
                pickerOptions={pickerOptions}
                selectionDateClose={selectionDateClose}
                onPreMouth={preMouth}
                onNextMouth={nextMouth}
                onChange={handleChangeDate}
            ></AbcDateTable>
        </View>
    );
};

export { AbcDatePanel };
