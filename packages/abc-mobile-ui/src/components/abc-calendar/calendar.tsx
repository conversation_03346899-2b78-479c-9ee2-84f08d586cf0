import React from "react";
import { View } from "@hippy/react";
import { CalendarProps } from "./date-constants";
import { AbcDatePanel } from "./date-panel";
import { AbcDateRangePanel } from "./date-range-panel";

const AbcCalendar: React.FC<CalendarProps> = (props: CalendarProps) => {
    const {
        type = "date",
        date,
        rangeDate,
        describeList,
        pickerOptions,
        beginYear = 1990,
        endYear = 2099,
        initToast ,
        changeDateToast ,
        selectionDateClose ,
        shortcutStyle,
        displayDateRange,
        onChange,
        onConfirm,
        onCancel,
    } = props || {};
    const dateRange = type === "daterange";
    return (
        <View>
            {dateRange ? (
                <AbcDateRangePanel
                    type={type}
                    date={rangeDate}
                    describeList={describeList}
                    pickerOptions={pickerOptions}
                    beginYear={beginYear}
                    endYear={endYear}
                    initToast={initToast}
                    changeDateToast={changeDateToast}
                    displayDateRange={displayDateRange}
                    shortcutStyle={shortcutStyle}
                    onConfirm={onConfirm}
                    onCancel={onCancel}
                ></AbcDateRangePanel>
            ) : (
                <AbcDatePanel
                    type={type}
                    date={date}
                    describeList={describeList}
                    pickerOptions={pickerOptions}
                    selectionDateClose={selectionDateClose}
                    beginYear={beginYear}
                    endYear={endYear}
                    onChange={onChange}
                ></AbcDatePanel>
            )}
        </View>
    );
};

export { AbcCalendar };
