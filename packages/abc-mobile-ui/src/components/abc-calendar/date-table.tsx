import React, { useRef, useState, useImperativeHandle, forwardRef } from "react";
import { Dimensions, Text, View, ViewPager } from "@hippy/react";
import { ABCStyles, Sizes, Colors, TextStyles } from "@app/theme";
import { AbcView } from "../abc-view/index";
import { pxToDp } from "@app/utils";
import { AbcGrid } from "../abc-grid";
import _, { range } from "lodash";
import { _CalendarDayItem, DescribeItem } from "./calender-interface";

import {
    parseTime,
    getFirstDayOfMonth,
    getPrevMonthLastDays,
    getMonthDays,
    getDateTimestamp,
    getThisMonthFirstDay,
    getThisMonthEndDay,
} from "@app/utils";

import { AbcNavigator } from "../abc-navigator";
import { RangeStateInterface } from "./date-constants";

type SelectionMode = "date" | "daterange";

interface PickerOptions {
    [key: string]: any;
}

export interface CalendarProps {
    selectionMode?: SelectionMode; // 'date' | 'daterange'
    selectionDateClose?: boolean; // 单选是否关闭
    beginYear?: number; // 指定范围的开始年
    endYear?: number; // 指定范围的结束年
    minDate?: Date;
    maxDate?: Date;
    // selectionMode 为 date 时
    value?: Date;
    date: Date; // required
    rangeState?: RangeStateInterface;
    describeList?: DescribeItem[];
    firstDayOfWeek?: number;
    pickerOptions?: PickerOptions;
    showWeeks?: boolean;

    onPreMouth?(): void;
    onNextMouth?(): void;
    onChange?(date: Date): void;
    onChangeRangeState?(rangeState: RangeStateInterface): void;
    onDateChange?(params: { minDate: Date | string | null; maxDate: Date | string | null }): void;
}

const defaultRangeState: RangeStateInterface = {
    startDate: null,
    endDate: null,
    selecting: false,
};

const todayStr = parseTime(new Date(), "y-m-d", true);

const rangeArr = (n: number) => {
    // @ts-ignore
    // eslint-disable-next-line prefer-spread
    return Array.apply(null, { length: n }).map((_, it) => it);
};

function WeekHeader() {
    const dateArray = ["日", "一", "二", "三", "四", "五", "六"];

    return (
        <View style={[Sizes.paddingLTRB(Sizes.dp10, 0)]}>
            <AbcGrid crossAxisCount={7} itemWidth={Sizes.dp40} itemHeight={Sizes.dp22}>
                {dateArray.map((item) => (
                    <View key={item}>
                        <View style={[ABCStyles.centerChild, { width: Sizes.dp40, height: Sizes.dp22 }]}>
                            <Text style={[TextStyles.t14NT1.copyWith({ lineHeight: Sizes.dp22, color: Colors.t2 })]}>{item}</Text>
                        </View>
                    </View>
                ))}
            </AbcGrid>
        </View>
    );
}

const AbcDateTable = forwardRef<any, CalendarProps>((props: CalendarProps, ref) => {
    const {
        selectionMode = "date",
        selectionDateClose = false,
        minDate,
        maxDate,
        date = new Date(),
        value = new Date(),
        rangeState = defaultRangeState,
        pickerOptions = {},
        showWeeks = true,
        onPreMouth,
        onNextMouth,
    } = props || {};

    const isDateRangeMode = selectionMode === "daterange";

    const [initCalendar, setInitCalendar] = useState(false);
    const [checkDate, setCheckDate] = useState(value);
    const viewPageRef = useRef<ViewPager>(null);
    const [dataArray, setDataArray] = useState<_CalendarDayItem[][]>([]);
    const getDateDescribe = (date: string) => {
        const { describeList = [] } = props || {};
        return describeList.find((item: DescribeItem) => {
            return item?.date === date;
        });
    };
    const prevMonthDatePrefix = (date: Date) => {
        const temp = new Date(date.getTime());
        temp.setDate(0);
        return parseTime(temp, "y-m", true);
    };

    const curMonthDatePrefix = (date: Date) => {
        return parseTime(date, "y-m", true);
    };

    const nextMonthDatePrefix = (date: Date) => {
        const temp = new Date(date.getFullYear(), date.getMonth() + 1, 1);
        return parseTime(temp, "y-m", true);
    };

    const disabledDate = (dateStr: string) => {
        const { disabledDate } = pickerOptions;
        if (!disabledDate || typeof disabledDate !== "function") return false;
        return disabledDate(new Date(dateStr.replace(/-/g, "/")));
    };

    const isInRange = (dateStr: string) => {
        const date = getDateTimestamp(new Date(dateStr.replace(/-/g, "/")));
        const minRange = minDate ? date > getDateTimestamp(minDate) : false;
        const maxRange = maxDate ? date < getDateTimestamp(maxDate) : false;
        return minRange && maxRange;
    };

    const getFormatDate = (date: Date, day: number | string, type: string) => {
        if (!day || ["prev", "current", "next"].indexOf(type) === -1) {
            throw new Error("invalid day or type");
        }
        let prefix = curMonthDatePrefix(date);
        if (type === "prev") {
            prefix = prevMonthDatePrefix(date);
        } else if (type === "next") {
            prefix = nextMonthDatePrefix(date);
        }
        day = `00${day}`.slice(-2);
        return `${prefix}-${day}`;
    };
    const getDateList = (date: Date) => {
        const firstDay = getFirstDayOfMonth(date);
        // 上个月剩余天数
        const lastMonthRemainingDays = firstDay;
        const prevMonthDays = getPrevMonthLastDays(date, lastMonthRemainingDays).map((day: number | string) => {
            const d = getFormatDate(date, day, "prev");
            return {
                date: new Date(d),
                dateStr: d,
                text: day,
                type: "prev",
                describe: getDateDescribe(d),
            };
        });

        const currentMonthDays = getMonthDays(date).map((day: any) => {
            const d = getFormatDate(date, day, "current");
            return {
                date: new Date(d),
                dateStr: d,
                text: day,
                type: "current",
                describe: getDateDescribe(d),
            };
        });
        let _dates = [];
        _dates = [...prevMonthDays, ...currentMonthDays];
        const nextMonthDays = rangeArr(42 - _dates.length).map((_, index) => {
            const d = getFormatDate(date, index + 1, "next");
            return {
                date: new Date(d),
                dateStr: d,
                text: index + 1,
                type: "next",
                describe: getDateDescribe(d),
            };
        });
        _dates = _dates.concat(nextMonthDays);
        _dates = _dates.filter((d) => d.type !== "next");
        return _dates;
    };

    //生成日历数组
    if (!initCalendar) {
        const now = new Date();
        const { beginYear, endYear } = props;
        let maxYear = now.getFullYear() + 10;
        let minYear = now.getFullYear() - 10;
        const maxMonth = 12;
        const minMonth = 0;
        if (beginYear) {
            minYear = beginYear ?? minYear;
        }
        if (endYear) {
            maxYear = endYear + 1;
        }
        const list: _CalendarDayItem[][] = [];

        range(minYear, maxYear).map((year) => {
            range(minMonth, maxMonth).map((month) => {
                const tempDate = new Date(year, month);
                const dateList = getDateList(tempDate);
                list.push(dateList);
            });
        });
        setDataArray(list);
        setInitCalendar(true);
    }

    //查找当前视口月份在viewpage中的索引
    const findCurrentMonthIndex = (date = props.date): number => {
        for (let index = 0; index < dataArray.length; index++) {
            for (const day of dataArray[index]) {
                if (day.type === "current") {
                    if (date.getTime() == getThisMonthFirstDay(day.date).getTime()) {
                        return index;
                    }
                }
            }
        }
        return 1;
    };

    const setMonthPage = (forceSetPage = false) => {
        const _currentMonthDate = props.date;
        if (forceSetPage) {
            viewPageRef.current?.setPage(findCurrentMonthIndex(_currentMonthDate));
        }
    };

    function updateRangeState(state: RangeStateInterface) {
        const { onChangeRangeState, onDateChange, pickerOptions } = props || {};
        const { onPick } = pickerOptions || {};
        const newState = {
            ...rangeState,
            ...state,
        };
        onChangeRangeState?.(newState);

        const { startDate, endDate } = newState;
        onPick?.({ minDate: startDate, maxDate: endDate });
        onDateChange?.({ minDate: startDate, maxDate: endDate });
    }

    const handleClickDate = (date: Date) => {
        const { rangeState, onChange, onDateChange } = props || {};
        if (selectionMode === "date") {
            setCheckDate(date);
            onChange?.(date);
            selectionDateClose && AbcNavigator.pop(date);
            return;
        } else if (isDateRangeMode) {
            const newRangeState = _.clone(rangeState);
            if (newRangeState?.startDate && newRangeState?.endDate) {
                onDateChange?.({ minDate: null, maxDate: null });
            }
            let startTime = "";
            const currentSelectedDate = parseTime(new Date(date), "y-m-d", true);
            if (newRangeState?.startDate) {
                startTime = parseTime(new Date(newRangeState.startDate), "y-m-d", true) ?? "";
                if (currentSelectedDate && currentSelectedDate < startTime) {
                    newRangeState.selecting = false;
                }
            }

            if (!newRangeState?.selecting) {
                updateRangeState?.({
                    selecting: true,
                    startDate: date,
                    endDate: null,
                });
            } else {
                updateRangeState?.({
                    selecting: false,
                    startDate: newRangeState.startDate,
                    endDate: date,
                });
            }
        }
    };

    const createDayView = (day: _CalendarDayItem, index: number) => {
        let checked = false;
        if (selectionMode === "date") {
            checked = checkDate.getTime() == day.date.getTime();
        }
        const isRowStart = index % 7 === 0;
        const isRowLast = index % 7 === 6;
        const disabled = disabledDate(day.dateStr);
        const currentDate = parseTime(new Date(day.date), "y-m-d", true);
        const isCurrentMonthFirstDay = currentDate === parseTime(getThisMonthFirstDay(day.date), "y-m-d", true);
        const isCurrentMonthEndDay = currentDate === parseTime(getThisMonthEndDay(day.date), "y-m-d", true);
        const isToday = currentDate === todayStr;
        const minDateStr = minDate && parseTime(minDate, "y-m-d", true);
        const maxDateStr = maxDate && parseTime(maxDate, "y-m-d", true);
        const isStart = currentDate === minDateStr;
        const isEnd = currentDate === maxDateStr;
        const isSelectStatus = checked || isStart || isEnd;
        const isSelectedRange = isInRange(day.dateStr);
        const isSelectedFinish = minDate && maxDate;
        const startAndEndNotEqual = minDateStr !== maxDateStr;
        const isNeedFillGap =
            isDateRangeMode &&
            isSelectedFinish &&
            startAndEndNotEqual &&
            (isSelectedRange || isStart) &&
            !isRowLast &&
            !isCurrentMonthEndDay;
        const showBorderLeftRadius =
            (isSelectedRange && (isRowStart || isCurrentMonthFirstDay)) || isStart || (isEnd && (isRowStart || isCurrentMonthFirstDay));
        const showBorderRightRadius =
            (isSelectedRange && (isRowLast || isCurrentMonthEndDay)) || isEnd || (isStart && (isRowLast || isCurrentMonthEndDay));
        const { width: screenWidth } = Dimensions.get("screen");
        // 计算日期列之间的间隙 (屏幕宽度 - 7天 - 左右padding)
        const remainedSpace = screenWidth - 7 * Sizes.dp40 - 2 * Sizes.dp10;
        const colGap = Math.ceil(remainedSpace / 6);
        return (
            <AbcView
                key={day.date.getTime()}
                style={{
                    position: "relative",
                    justifyContent: "flex-start",
                    alignItems: "flex-start",
                    backgroundColor: Colors.transparent,
                    width: !isNeedFillGap ? Sizes.dp40 : Sizes.dp40 + pxToDp(colGap + 1),
                    height: Sizes.dp52,
                }}
            >
                {day.type === "current" && isNeedFillGap && (
                    <View
                        style={[
                            ABCStyles.centerChild,
                            {
                                position: "absolute",
                                top: 0,
                                left: Sizes.dp20,
                                backgroundColor: Colors.theme4,
                                width: Sizes.dp20 + pxToDp(colGap + 1),
                                height: Sizes.dp40,
                            },
                        ]}
                    ></View>
                )}
                {day.type === "current" ? (
                    <View>
                        <View
                            style={[
                                ABCStyles.centerChild,
                                {
                                    backgroundColor: isSelectStatus || isSelectedRange ? Colors.theme4 : Colors.transparent,
                                    padding: Sizes.dp4,
                                    width: Sizes.dp40,
                                    height: Sizes.dp40,
                                    ...((!isDateRangeMode || !isSelectedRange) && {
                                        borderTopLeftRadius: Sizes.dp20,
                                        borderBottomLeftRadius: Sizes.dp20,
                                        borderTopRightRadius: Sizes.dp20,
                                        borderBottomRightRadius: Sizes.dp20,
                                    }),
                                    ...(isDateRangeMode &&
                                        isSelectedFinish && {
                                            borderTopLeftRadius: showBorderLeftRadius ? Sizes.dp20 : 0,
                                            borderBottomLeftRadius: showBorderLeftRadius ? Sizes.dp20 : 0,
                                            borderTopRightRadius: showBorderRightRadius ? Sizes.dp20 : 0,
                                            borderBottomRightRadius: showBorderRightRadius ? Sizes.dp20 : 0,
                                        }),
                                },
                            ]}
                            onClick={() => {
                                if (disabled) return;
                                handleClickDate(day.date);
                            }}
                        >
                            <View
                                style={[
                                    ABCStyles.centerChild,
                                    {
                                        backgroundColor: isSelectStatus ? Colors.B1 : Colors.transparent,
                                        width: Sizes.dp32,
                                        height: Sizes.dp32,
                                        borderRadius: !isSelectedRange ? Sizes.dp16 : 0,
                                    },
                                ]}
                            >
                                <Text
                                    style={[
                                        TextStyles.t16MT1,
                                        {
                                            color: Colors.t1,
                                        },
                                        isToday
                                            ? {
                                                  color: Colors.B1,
                                              }
                                            : {},
                                        disabled
                                            ? {
                                                  color: Colors.bdColor,
                                              }
                                            : {},
                                        isSelectStatus ? { color: Colors.t5 } : {},
                                    ]}
                                >
                                    {isToday && pickerOptions.todayText ? pickerOptions.todayText : day.date.getDate()}
                                </Text>
                            </View>
                        </View>

                        <View
                            style={{
                                position: "absolute",
                                top: Sizes.dp34,
                                flexDirection: "row",
                                justifyContent: "center",
                                alignItems: "center",
                                width: Sizes.dp40,
                                height: Sizes.dp18,
                                backgroundColor: Colors.transparent,
                            }}
                        >
                            <Text
                                style={[
                                    TextStyles.t11MT1,
                                    {
                                        color: Colors.B1,
                                    },
                                ]}
                            >
                                {day.describe?.describe || ""}
                            </Text>
                        </View>
                    </View>
                ) : (
                    <View />
                )}
            </AbcView>
        );
    };

    const createDaysView = (): JSX.Element => {
        const currentIndex = findCurrentMonthIndex();
        return (
            <ViewPager
                ref={viewPageRef}
                initialPage={currentIndex}
                style={[{ minHeight: pxToDp(280) }]}
                onPageSelected={(event) => {
                    if (event.position == currentIndex) return;
                    if (event.position < currentIndex) {
                        onPreMouth?.();
                    } else {
                        onNextMouth?.();
                    }
                }}
            >
                {dataArray.map((list, index) => {
                    if (Math.abs(currentIndex - index) > 1) {
                        return <View key={index} />;
                    }

                    return (
                        <View key={index} style={{ flex: 1, ...Sizes.paddingLTRB(Sizes.dp10, Sizes.dp16) }}>
                            <AbcGrid
                                itemWidth={Sizes.dp40}
                                itemHeight={Sizes.dp52}
                                crossAxisCount={7}
                                style={{ flex: 1, backgroundColor: Colors.transparent }}
                            >
                                {list.map((item, index) => createDayView(item, index))}
                            </AbcGrid>
                        </View>
                    );
                })}
            </ViewPager>
        );
    };

    useImperativeHandle(ref, () => ({
        setMonthPage,
    }));

    return (
        <View style={{ flex: 1, backgroundColor: Colors.bg_white }}>
            {showWeeks && <WeekHeader />}
            {createDaysView()}
        </View>
    );
});

export { AbcDateTable };
