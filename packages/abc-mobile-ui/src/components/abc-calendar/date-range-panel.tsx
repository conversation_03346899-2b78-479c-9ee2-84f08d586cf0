import React, { useRef, useState, useEffect } from "react";
import { Text, View } from "@hippy/react";
import { DateRangePanelProps, RangeStateInterface } from "./date-constants";

import { AbcDateTable } from "./date-table";
import { ABCStyles, Colors, Sizes, TextStyles, FontWeights } from "@app/theme";
import { AbcView } from "../abc-view/index";
import { AbcIconfont } from "../abc-iconfont/index";
import { AbcSizedBox } from "../abc-size-box/index";
import { getNextMonthFirstDay, getThisMonthFirstDay, isDate, parseTime } from "@app/utils";
import { AbcFlex } from "../abc-flex";
import { AbcDatePicker } from "../abc-date-picker";
import { AbcGrid } from "../index";
import { AbcToast } from "../abc-toast";
import { AbcDivider } from "../abc-divider";

// 定义暴露给父组件的方法类型
interface AbcDateTableRef {
    setMonthPage: (forceSetPage?: boolean) => void;
}

const AbcDateRangePanel: React.FC<DateRangePanelProps> = (props: DateRangePanelProps) => {
    const {
        type = "daterange",
        displayDateRange = true,
        pickerOptions,
        describeList,
        date,
        beginYear,
        endYear,
        initToast = true,
        changeDateToast = true,
        shortcutStyle,
        onConfirm,
        onCancel,
    } = props || {};
    // 初始化时根据 props.date 设置 dateRange.minDate/dateRange.maxDate
    const [dateRange, setDateRange] = useState<{ minDate: Date | null; maxDate: Date | null }>(() => {
        if (Array.isArray(date) && date.length === 2) {
            return {
                minDate: date[0] ? new Date(date[0]) : null,
                maxDate: date[1] ? new Date(date[1]) : null,
            };
        }
        return { minDate: null, maxDate: null };
    });
    const [rangeState, setRangeState] = useState<{
        startDate: Date | null;
        endDate: Date | null;
        selecting: boolean;
    }>({
        startDate: null,
        endDate: null,
        selecting: false,
    });
    // 监听 props.date 变化
    React.useEffect(() => {
        if (Array.isArray(date) && date.length === 2) {
            const start = date[0] ? new Date(date[0]) : null;
            const end = date[1] ? new Date(date[1]) : null;
            setDateRange({
                minDate: start,
                maxDate: end,
            });

            setRangeState({
                startDate: start,
                endDate: end,
                selecting: false,
            });
            // 自动高亮快捷项
            const { shortcuts } = pickerOptions || {};
            if (shortcuts && shortcuts.length) {
                let matchedIdx = -1;
                shortcuts.forEach((shortcut: ShortcutItem, idx: number) => {
                    shortcut.onClick((val: [Date, Date]) => {
                        if (
                            Array.isArray(val) &&
                            isSameRange([date[0] ? new Date(date[0]) : null, date[1] ? new Date(date[1]) : null], [val[0], val[1]])
                        ) {
                            matchedIdx = idx;
                        }
                    });
                });
                setSelectedShortcutIndex(matchedIdx >= 0 ? matchedIdx : null);
            } else {
                setSelectedShortcutIndex(null);
            }
        } else {
            setDateRange({ minDate: null, maxDate: null });
            setSelectedShortcutIndex(null);
        }
    }, [date, pickerOptions]);
    const time = dateRange.minDate ? new Date(dateRange.minDate) : new Date();
    // 首次挂载时检查 minDate
    useEffect(() => {
        if (initToast && !dateRange.minDate) {
            async function handleToastShow() {
                await AbcToast.show("请选择开始时间");
            }
            handleToastShow().then();
        }
    }, []);
    const [currentMonthDate, setCurrentMonthDate] = useState(getThisMonthFirstDay(time));
    const abcDateTable = useRef<AbcDateTableRef>(null);
    const pre = currentMonthDate > new Date(beginYear, 0, 1);
    const next = currentMonthDate < new Date(endYear, 11, 1);
    const [selectedShortcutIndex, setSelectedShortcutIndex] = useState<number | null>(null);

    function isSameDay(a: Date | null, b: Date | null): boolean {
        if (!a || !b) return false;
        return a.getFullYear() === b.getFullYear() && a.getMonth() === b.getMonth() && a.getDate() === b.getDate();
    }

    function isSameRange(a: [Date | null, Date | null], b: [Date | null, Date | null]): boolean {
        return isSameDay(a[0], b[0]) && isSameDay(a[1], b[1]);
    }

    const preMouth = (forceSetPage = false) => {
        if (!pre) return;
        const _currentMonthDate = getThisMonthFirstDay(new Date(currentMonthDate.getTime() - 1));
        setCurrentMonthDate(_currentMonthDate);
        abcDateTable.current?.setMonthPage?.(forceSetPage);
    };

    const nextMouth = (forceSetPage = false) => {
        if (!next) return;
        const _currentMonthDate = getNextMonthFirstDay(currentMonthDate);
        setCurrentMonthDate(_currentMonthDate);
        abcDateTable.current?.setMonthPage?.(forceSetPage);
    };

    async function handleConfirm() {
        if (!dateRange.minDate || !dateRange.maxDate) {
            await AbcToast.show(`请选择${!dateRange.minDate ? "开始" : "结束"}时间`);
            return;
        }

        let label = "";
        const { shortcuts } = props?.pickerOptions || {};
        if (selectedShortcutIndex !== null && shortcuts?.length) {
            label = shortcuts[selectedShortcutIndex]?.text || "";
        }
        if (dateRange.minDate && dateRange.maxDate) {
            onConfirm?.({
                label,
                dateRange: [dateRange.minDate, dateRange.maxDate],
            });
        }
    }

    function handleCancel() {
        onCancel?.();
    }

    function getShortcutRanges(shortcuts: ShortcutItem[]): [Date | null, Date | null][] {
        return shortcuts.map((shortcut) => {
            let range: [Date | null, Date | null] = [null, null];
            if (typeof shortcut.onClick === "function") {
                shortcut.onClick((val: [Date, Date]) => {
                    if (Array.isArray(val)) {
                        range = [val[0], val[1]];
                    }
                });
            }
            return range;
        });
    }

    function handleChangeRangeState(rangeState: RangeStateInterface, resetShortcutIndex = true) {
        const newRangeState = {
            startDate: rangeState?.startDate,
            endDate: rangeState.endDate,
            selecting: rangeState.selecting,
        };
        setRangeState(newRangeState);
        if (!resetShortcutIndex) {
            return;
        }

        // 用户手动点日历时，判断是否与某个shortcut区间一致
        const { shortcuts = [] } = props?.pickerOptions || {};
        const shortcutRanges = getShortcutRanges(shortcuts);
        if (shortcutRanges.length && newRangeState.startDate && newRangeState.endDate) {
            const idx = shortcutRanges.findIndex(([s, e]) => isSameRange([newRangeState.startDate, newRangeState.endDate], [s, e]));
            setSelectedShortcutIndex(idx >= 0 ? idx : null);
        } else {
            setSelectedShortcutIndex(null);
        }
    }

    function handleDateTableChange({ minDate, maxDate }: { minDate: Date | null; maxDate: Date | null }) {
        handleChangeDate({ minDate, maxDate });
        // 匹配快捷项高亮
        const { shortcuts } = props?.pickerOptions || {};
        if (shortcuts && shortcuts.length) {
            let priorityMatchedIndex = -1;
            let priority = -1;
            shortcuts.forEach((shortcut: ShortcutItem, idx: number) => {
                shortcut.onClick((val: [Date, Date]) => {
                    if (isSameRange([minDate, maxDate], val)) {
                        if (shortcut.priority && shortcut.priority > priority) {
                            priorityMatchedIndex = idx;
                            priority = shortcut.priority;
                        } else if (!shortcut.priority && priorityMatchedIndex === -1) {
                            priorityMatchedIndex = idx;
                        }
                    }
                });
            });
            if (priorityMatchedIndex >= 0) {
                setSelectedShortcutIndex(priorityMatchedIndex);
            } else {
                setSelectedShortcutIndex(null);
            }
        }
    }

    async function handleChangeDate({ minDate, maxDate }: { minDate: Date | null; maxDate: Date | null }) {
        setDateRange({ minDate, maxDate });
        if (changeDateToast && (!minDate || !maxDate)) {
            await AbcToast.show(`请选择${!minDate ? "开始" : "结束"}时间`);
            return;
        }
        if (maxDate) {
            const _currentMonthDate = getThisMonthFirstDay(maxDate);
            setCurrentMonthDate(_currentMonthDate);
            abcDateTable.current?.setMonthPage?.(true);
        }
    }

    async function handleClick() {
        const minDate = new Date(beginYear, 0, 1);
        const maxDate = new Date(endYear, 11, 1);
        const select = await AbcDatePicker.show(new Date(currentMonthDate), { minDate, maxDate, showDay: false });
        if (select) {
            const _currentMonthDate = getThisMonthFirstDay(new Date(select));
            setCurrentMonthDate(_currentMonthDate);
            abcDateTable.current?.setMonthPage?.(true);
        }
    }

    let startTime = "";
    let endTime = "";
    if (rangeState.startDate) {
        const parsed = parseTime(new Date(rangeState.startDate), "y-m-d", true);
        startTime = parsed ? parsed.replace(/-/g, "/") : "";
    }

    if (rangeState.endDate) {
        const parsed = parseTime(new Date(rangeState.endDate), "y-m-d", true);
        endTime = parsed ? parsed.replace(/-/g, "/") : "";
    }

    interface ShortcutItem {
        text: string;
        onClick: (cb: (val: any) => void) => void;
        priority?: number; // 可选，越大优先级越高
    }

    const createShortcutsView = () => {
        const { shortcuts } = props?.pickerOptions || {};
        if (!shortcuts?.length) return;

        function handleShortcutClick(shortcut: ShortcutItem, index: number) {
            if (shortcut.onClick && typeof shortcut.onClick === "function") {
                shortcut.onClick((newVal) => {
                    let minDate = null;
                    let maxDate = null;
                    if (Array.isArray(newVal)) {
                        minDate = isDate(newVal[0]) ? new Date(newVal[0]) : null;
                        maxDate = isDate(newVal[1]) ? new Date(newVal[1]) : null;
                    }
                    setDateRange({ minDate: null, maxDate: null });
                    setSelectedShortcutIndex(index);
                    handleChangeDate({ minDate, maxDate });
                    handleChangeRangeState({ startDate: minDate, endDate: maxDate, selecting: false }, false);
                });
            }
        }

        return (
            <View
                style={{
                    padding: Sizes.dp16,
                    borderBottomWidth: Sizes.dpHalf,
                    borderColor: Colors.border_color_light,
                    ...shortcutStyle,
                }}
            >
                <AbcGrid
                    itemHeight={Sizes.dp34}
                    crossAxisCount={4}
                    crossAxisSpacing={Sizes.dp8}
                    mainAxisSpacing={Sizes.dp8}
                    style={{ backgroundColor: Colors.transparent }}
                >
                    {shortcuts.map((item: ShortcutItem, index: number) => (
                        <View
                            key={index}
                            style={[
                                ABCStyles.centerChild,
                                {
                                    height: Sizes.dp34,
                                    borderRadius: Sizes.dp8,
                                    borderWidth: Sizes.dp1,
                                    color: selectedShortcutIndex === index ? Colors.B1 : Colors.T2,
                                    borderColor: selectedShortcutIndex === index ? Colors.border_color_theme1 : Colors.border_color_light,
                                    backgroundColor: selectedShortcutIndex === index ? Colors.theme4 : Colors.cp_white,
                                },
                            ]}
                            onClick={() => handleShortcutClick(item, index)}
                        >
                            <Text style={{ color: selectedShortcutIndex === index ? Colors.theme1 : Colors.t2 }}>{item.text}</Text>
                        </View>
                    ))}
                </AbcGrid>
            </View>
        );
    };

    return (
        <View style={{ backgroundColor: Colors.bg_white }}>
            {props?.pickerOptions?.shortcuts?.length && createShortcutsView()}
            <View style={[ABCStyles.rowAlignCenterSpaceBetween, { height: Sizes.dp56, padding: Sizes.dp16 }]}>
                <AbcFlex align="center" style={{ lineHeight: Sizes.dp24 }} onClick={handleClick}>
                    <Text style={[TextStyles.t16MT1.copyWith({ lineHeight: Sizes.dp24 })]}>
                        {`${currentMonthDate.format("yyyy 年 MM 月")}`}
                    </Text>
                    <AbcIconfont
                        style={[
                            ABCStyles.centerChild,
                            {
                                width: Sizes.dp24,
                                height: Sizes.dp24,
                                lineHeight: Sizes.dp24,
                            },
                        ]}
                        name={"Dropdown_Triangle"}
                        size={Sizes.dp18}
                        color={Colors.t1}
                    />
                </AbcFlex>
                <View style={[ABCStyles.rowAlignCenterSpaceBetween]}>
                    <AbcView
                        style={[
                            ABCStyles.centerChild,
                            {
                                width: Sizes.dp24,
                                height: Sizes.dp24,
                                borderWidth: Sizes.dp1,
                                borderColor: Colors.border_color_light,
                                borderRadius: Sizes.dp6,
                            },
                        ]}
                        onClick={() => {
                            preMouth(true);
                        }}
                    >
                        <AbcIconfont name={"s-arrowleft-line-medium"} size={Sizes.dp16} color={pre ? Colors.T1 : Colors.T4} />
                    </AbcView>
                    <AbcSizedBox width={Sizes.dp32} />
                    <AbcView
                        style={[
                            ABCStyles.centerChild,
                            {
                                width: Sizes.dp24,
                                height: Sizes.dp24,
                                borderWidth: Sizes.dp1,
                                borderColor: Colors.border_color_light,
                                borderRadius: Sizes.dp6,
                            },
                        ]}
                        onClick={() => {
                            nextMouth(true);
                        }}
                    >
                        <AbcIconfont name={"s-arrowright-middle-line-medium"} size={Sizes.dp16} color={next ? Colors.T1 : Colors.T4} />
                    </AbcView>
                </View>
            </View>
            <View>
                <AbcDateTable
                    ref={abcDateTable}
                    selectionMode={type}
                    date={currentMonthDate}
                    beginYear={beginYear}
                    endYear={endYear}
                    minDate={dateRange.minDate ?? undefined}
                    maxDate={dateRange.maxDate ?? undefined}
                    rangeState={rangeState}
                    describeList={describeList}
                    pickerOptions={pickerOptions}
                    onPreMouth={preMouth}
                    onNextMouth={nextMouth}
                    onDateChange={handleDateTableChange}
                    onChangeRangeState={handleChangeRangeState}
                ></AbcDateTable>
            </View>
            {displayDateRange && (
                <View>
                    <AbcDivider lineHeight={Sizes.dpHalf} color={Colors.border_color_light} />
                    <View
                        style={{
                            flex: 1,
                            height: Sizes.dp44,
                            ...Sizes.paddingLTRB(Sizes.dp16, Sizes.dp16, Sizes.dp16, Sizes.dp4),
                        }}
                    >
                        <AbcFlex style={{ height: Sizes.dp24 }} align={"center"} justify={"center"}>
                            <AbcFlex style={{ width: Sizes.dp168 }} justify={"center"}>
                                {startTime ? (
                                    <>
                                        <Text style={[TextStyles.t16NT1, { fontWeight: FontWeights.medium }]}>开始时间: </Text>
                                        <Text style={[TextStyles.t16NT1, { fontWeight: FontWeights.medium }]}>{startTime}</Text>
                                    </>
                                ) : (
                                    <Text style={[TextStyles.t16NT3, { fontWeight: FontWeights.normal }]}>请选择开始时间</Text>
                                )}
                            </AbcFlex>
                            <Text style={{ fontWeight: FontWeights.medium }}>~</Text>
                            <AbcFlex style={{ width: Sizes.dp168 }} justify={"center"}>
                                {endTime ? (
                                    <>
                                        <Text style={[TextStyles.t16NT1, { fontWeight: FontWeights.medium }]}>结束时间: </Text>
                                        <Text style={[TextStyles.t16NT1, { fontWeight: FontWeights.medium }]}>{endTime}</Text>
                                    </>
                                ) : (
                                    <Text style={[TextStyles.t16NT3, { fontWeight: FontWeights.normal }]}>请选择结束时间</Text>
                                )}
                            </AbcFlex>
                        </AbcFlex>
                    </View>
                </View>
            )}

            <AbcFlex
                style={[
                    ABCStyles.rowAlignCenterSpaceBetween,
                    {
                        flex: 1,
                        height: Sizes.dp64,
                        ...Sizes.paddingLTRB(Sizes.dp16, Sizes.dp12),
                    },
                ]}
            >
                <View
                    style={[
                        ABCStyles.centerChild,
                        {
                            height: Sizes.dp40,
                            color: Colors.theme1,
                            borderRadius: Sizes.border_radius_small,
                            backgroundColor: Colors.theme4,
                        },
                    ]}
                    onClick={handleCancel}
                >
                    <Text style={{ fontSize: Sizes.dp16, color: Colors.theme1 }}>取消</Text>
                </View>
                <View
                    style={[
                        ABCStyles.centerChild,
                        {
                            marginLeft: Sizes.dp8,
                            height: Sizes.dp40,
                            color: Colors.t5,
                            borderRadius: Sizes.border_radius_small,
                            backgroundColor: Colors.theme1,
                        },
                    ]}
                    onClick={handleConfirm}
                >
                    <Text style={{ fontSize: Sizes.dp16, color: Colors.t5 }}>确定</Text>
                </View>
            </AbcFlex>
        </View>
    );
};

export { AbcDateRangePanel };
