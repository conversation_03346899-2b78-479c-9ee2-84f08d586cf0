import React from "react";
import { View } from "@hippy/react";
import { AbcCalendar } from "../index";
import { AbcText } from "../../abc-text";
import { Colors, Sizes } from "@app/theme";
import { parseTime } from "@app/utils";

const now = new Date();
const nowDayOfWeek = now.getDay();
const nowDay = now.getDate();
const nowMonth = now.getMonth();
const nowYear = now.getFullYear();
now.setDate(1);
now.setMonth(now.getMonth() - 1);

const pickerOptions = {
    disabledDate(date: Date) {
        return date > new Date();
    },
};

const describeList = [
    {
        date: parseTime(new Date(), "y-m-d", true) || "",
        describe: "今",
    },
];

type ShortcutCallback = (val: [Date, Date]) => void;

const dateRangePickerOptions = {
    shortcuts: [
        {
            text: "今天",

            onClick(cb: ShortcutCallback) {
                const start = new Date();
                const end = new Date();
                cb([start, end]);
            },
        },
        {
            text: "昨天",

            onClick(cb: ShortcutCallback) {
                const end = new Date(nowYear, nowMonth, nowDay - 1);
                const start = new Date(nowYear, nowMonth, nowDay - 1);
                cb([start, end]);
            },
        },
        {
            text: "本周",

            onClick(cb: ShortcutCallback) {
                let start = new Date(nowYear, nowMonth, nowDay - 6);

                if (nowDayOfWeek) {
                    start = new Date(nowYear, nowMonth, nowDay - nowDayOfWeek + 1);
                }

                const end = new Date();
                cb([start, end]);
            },
        },
        {
            text: "本月",

            onClick(cb: ShortcutCallback) {
                const start = new Date(nowYear, nowMonth, 1);
                const end = new Date();
                cb([start, end]);
            },
        },
    ],
};
interface AbcCalendarDemoProps {
    calendarType?: string;
}
export function AbcCalendarDemo(props: AbcCalendarDemoProps) {
    const { calendarType = "" } = props || {};
    // const date = "2025-06-02";
    // const dateRange: [string, string] = ["2025-06-01", "2025-06-17"];
    const date = "";
    const dateRange: [string, string] = ["", ""];

    const calendarRange = calendarType === "daterange";
    function handleChange(date: Date) {
        console.log("handleChange------>", date);
    }

    function handleConfirm({ dateRange, label }: { dateRange: Date[]; label?: string }) {
        console.log("handleConfirm------>", dateRange, label);
    }

    function handleCancel() {
        console.log("handleCancel------>");
    }

    return (
        <View style={{ marginBottom: Sizes.dp20 }}>
            <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>基础用法</AbcText>
            {!calendarRange ? (
                <View>
                    <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp8 }}>date日期组件</AbcText>
                    <AbcCalendar
                        date={date}
                        describeList={describeList}
                        pickerOptions={pickerOptions}
                        onChange={handleChange}
                    ></AbcCalendar>
                </View>
            ) : (
                <View style={{ marginTop: Sizes.dp16 }}>
                    <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp8 }}>daterange日期范围组件</AbcText>
                    <AbcCalendar
                        type={"daterange"}
                        rangeDate={dateRange}
                        describeList={describeList}
                        pickerOptions={dateRangePickerOptions}
                        onConfirm={handleConfirm}
                        onCancel={handleCancel}
                    ></AbcCalendar>
                </View>
            )}
        </View>
    );
}
