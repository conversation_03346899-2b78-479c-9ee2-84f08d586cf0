import React from "react";
import { View } from "@hippy/react";
import { Sizes, Colors, ABCStyles } from "@app/theme";
import { AbcText } from "../../abc-text/index";
import { AbcGrid } from "../index";

const list = [
    {
        label: "grid1",
        value: "grid1",
    },
    {
        label: "grid2",
        value: "grid2",
    },
    {
        label: "grid3",
        value: "grid3",
    },
    {
        label: "grid4",
        value: "grid4",
    },
    {
        label: "grid5",
        value: "grid5",
    },
    {
        label: "grid6",
        value: "grid6",
    },
];
export function AbcGridDemo() {
    const viewList = list.map((item) => {
        return (
            <View key={item.value} style={[ABCStyles.centerChild, { backgroundColor: Colors.B3 }]}>
                {item.label}
            </View>
        );
    });

    function getViewList(num: number): any[] {
        return viewList.slice(0, num);
    }

    return (
        <View>
            <View>
                <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>基础用法</AbcText>
                <AbcGrid crossAxisCount={1} crossAxisSpacing={Sizes.dp16} itemHeight={Sizes.dp24}>
                    {getViewList(1)}
                </AbcGrid>

                <AbcGrid style={{ marginTop: Sizes.dp16 }} crossAxisCount={2} crossAxisSpacing={Sizes.dp16} itemHeight={Sizes.dp24}>
                    {getViewList(2)}
                </AbcGrid>
                <AbcGrid style={{ marginTop: Sizes.dp16 }} crossAxisCount={3} crossAxisSpacing={Sizes.dp16} itemHeight={Sizes.dp24}>
                    {getViewList(3)}
                </AbcGrid>

                <AbcGrid
                    style={{ marginTop: Sizes.dp24 }}
                    crossAxisCount={4}
                    mainAxisSpacing={Sizes.dp8}
                    itemWidth={Sizes.dp40}
                    itemHeight={Sizes.dp24}
                >
                    {getViewList(6)}
                </AbcGrid>
            </View>
        </View>
    );
}
