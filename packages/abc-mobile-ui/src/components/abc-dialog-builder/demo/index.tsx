import React from "react";
import { View } from "@hippy/react";
import { AbcText } from "../../abc-text";
import { Colors, Sizes } from "@app/theme";
import { AbcButton } from "../../abc-button";
import {
    AbcShowConfirmDialog,
    AbcShowQueryDialog,
    AbcShowDialog,
    AbcShowInputDialog,
} from "../index";
import {AbcNavigator} from "../../abc-navigator";

export function AbcDialogDemo() {
    async function showQueryDialog() {
        const select = await AbcShowQueryDialog("是否删除该商品？", "");
        console.log("select--", select)
    }

    function showConfirmDialog() {
        AbcShowConfirmDialog("提示", `商品库存不足`).then(res => {
            console.log("res--", res)
        });
    }

    async function showInputDialog() {
        const text = await AbcShowInputDialog.show({ title: "保存为随访结果模板", placeholder: "输入模板名称" });
        console.log("text--", text)
    }

    function showDialog() {
        const content = (
            <View style={{
                    justifyContent: "center",
                    alignItems: "center",
                    width: Sizes.dp300,
                    height: Sizes.dp180,
                    borderRadius: Sizes.dp8,
                    backgroundColor: Colors.white
                }}
            >
                <View style={{
                        flex: 1,
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                >
                    <AbcText style={{ color: Colors.T1 }}>展示showDialog</AbcText>
                </View>
                <AbcButton style={{ width: Sizes.dp100, marginBottom: Sizes.dp12 }} onClick={() => AbcNavigator.pop()}>关闭</AbcButton>
            </View>
        )
        AbcShowDialog(content, {
            routeName: "Toast",
            alignItems: "center",
        }).then(res => {
            console.log("showDialog res--", res)
        }) ;
    }

    return (
        <View>
            <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>基础用法</AbcText>
            <AbcButton style={{ marginBottom: Sizes.dp12 }} onClick={showQueryDialog}>显示询问对话框 (showQueryDialog)</AbcButton>
            <AbcButton style={{ marginBottom: Sizes.dp12 }} onClick={showConfirmDialog}>显示确认对话框 (showConfirmDialog)</AbcButton>
            <AbcButton style={{ marginBottom: Sizes.dp12 }} onClick={showInputDialog}>显示输入对话框 (showInputDialog)</AbcButton>
            <AbcButton style={{ marginBottom: Sizes.dp12 }} onClick={showDialog}>显示对话框 (showDialog)</AbcButton>
        </View>
    );
}
