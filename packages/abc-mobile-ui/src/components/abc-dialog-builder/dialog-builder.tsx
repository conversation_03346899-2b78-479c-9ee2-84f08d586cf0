import React, { useState } from "react";
import { Dimensions, Style, Text, View } from "@hippy/react";
import { ABCStyles, Colors, Sizes, TextStyles, TextStyle } from "@app/theme";
import _ from "lodash";
import { AbcNavigator } from "../abc-navigator";
import { AbcIconfont } from "../abc-iconfont";
import { AbcSizedBox } from "../abc-size-box";
import { AbcView } from "../abc-view";
import { AbcTextInput } from "../abc-text-input";
import { AbcBaseComponent } from "../abc-base-component";
import { ignore, AccessibilityLabelType } from "@app/utils";
import { DialogOptions } from "../../utils/index";

export enum DialogIndex {
    negative, //取消
    positive, //确认
}

type DialogAlignment = "left" | "center" | "right";

export class AbcDialogBuilder {
    positive = "";
    negative = "";
    title = "";
    content?: JSX.Element | string;
    button?: AbcDialogButtonBuilder;
    contentMinHeight?: number;
    contentMaxHeight?: number;

    contentPadding?: Style = Sizes.paddingLTRB(Sizes.dp24);
    contentHorizontalPadding?: number;
    contentVerticalPadding?: number;

    negativeTextStyle: TextStyle = TextStyles.t16NT1.copyWith({ color: Colors.t1 }); //default = TextStyles.t16NT6
    positiveTextStyle?: TextStyle; //default = TextStyles.t18MM

    //添加这个属性是Android上有个框架bug,没有找到原因，默认hidden跳到下个页面，与编辑框使用时，容易出现标题栏展示不出来的问题
    containerOverflowStyle: "hidden" | "scroll" | undefined = "hidden";

    onClick?: (index: number) => void;

    routerName?: string; //在前进后退栈中名称

    //标题对齐方式
    alignment?: DialogAlignment;
    marginVertical?: number; // 弹窗上下外间距 解决安卓长文本时边框可能会超出
    isShowBtn = true; //是否显示按钮

    show<T>(options?: DialogOptions): Promise<T> {
        const buttonBuilder = this.button ?? new AbcDialogButtonBuilder();
        if (this.negative) {
            buttonBuilder.appendButton({
                text: this.negative,
                id: DialogIndex.negative,
                onClick: this.onClick ? () => this.onClick!(DialogIndex.negative) : undefined,
                textStyle: this.negativeTextStyle,
            });
        }

        if (this.positive) {
            buttonBuilder.appendButton({
                text: this.positive,
                id: DialogIndex.positive,
                onClick: this.onClick ? () => this.onClick!(DialogIndex.positive) : undefined,
                textStyle: this.positiveTextStyle,
            });
        }

        const _showTitle = !_.isNil(this.title) && this.title != "";
        const _showContent = !_.isNil(this.content) && this.content != "";

        let contentWidget: JSX.Element | undefined;
        if (_.isString(this.content) && this.content.length > 0) {
            contentWidget = (
                <Text
                    style={{
                        ...TextStyles.t14NT2.copyWith({ color: Colors.t2 }),
                        textAlign: this.alignment ?? "center",
                        lineHeight: Sizes.dp22,
                    }}
                >
                    {this.content as string}
                </Text>
            );
        } else if (this.content) {
            contentWidget = <View style={{ flex: 1 }}>{this.content as JSX.Element}</View>;
        }

        this.contentPadding = options?.contentPadding ?? this.contentPadding;

        const content = (
            <View
                accessibilityLabel={`${AccessibilityLabelType.DIALOG}-${this.title ?? ""}`}
                style={{
                    marginVertical: this.marginVertical,
                    marginHorizontal: Sizes.dp32,
                    backgroundColor: Colors.white,
                    borderRadius: options?.borderRadius ?? Sizes.dp12,
                    width: Sizes.dp311,
                    maxHeight: this.contentMaxHeight ?? (Dimensions.get("window").height * 3) / 4,
                    minHeight: this.contentMinHeight ? this.contentMinHeight : 0,
                    overflow: this.containerOverflowStyle,
                }}
            >
                {options?.isHasCloseBtn && (
                    <AbcView style={{ position: "absolute", right: Sizes.dp14, top: Sizes.dp13 }} onClick={() => AbcNavigator.pop()}>
                        <AbcIconfont name={"cross_small"} size={Sizes.dp18} color={Colors.t3} />
                    </AbcView>
                )}
                <View
                    style={{
                        flex: 1,
                        justifyContent: "center",
                        ...this.contentPadding,
                        paddingHorizontal: this.contentHorizontalPadding ?? undefined,
                        paddingVertical: this.contentVerticalPadding ?? Sizes.dp24,
                        minHeight: this.contentMinHeight ? this.contentMinHeight : Sizes.dp106,
                    }}
                >
                    {_showTitle && (
                        <Text
                            style={[
                                TextStyles.t16MB,
                                {
                                    textAlign: this.alignment ?? "center",
                                    lineHeight: Sizes.dp24,
                                },
                            ]}
                        >
                            {this.title}
                        </Text>
                    )}
                    {_showTitle && _showContent && <AbcSizedBox height={options?.titleToContentSpace ?? Sizes.dp12} />}
                    {contentWidget}
                </View>
                {this.isShowBtn && buttonBuilder.buildBar()}
            </View>
        );

        return AbcShowDialog(content, { ...options, routeName: this.routerName });
    }
}

type DialogButtonClick = () => void;

///用于辅助构造对话框底部按钮
function DialogButton(props: { text: string; onClick: DialogButtonClick; textStyle?: Style }) {
    const { text, onClick, textStyle } = props;
    const [activePress, setActivePress] = useState(false);
    return (
        <AbcView
            key={text}
            style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: activePress ? Colors.cp_pressed : "",
            }}
            onClick={onClick}
            onTouchDown={() => {
                setActivePress(true);
            }}
            onTouchEnd={() => {
                setActivePress(false);
            }}
        >
            <Text style={textStyle ?? TextStyles.t16MM}>{text}</Text>
        </AbcView>
    );
}

export class AbcDialogButtonBuilder {
    static createDialogButton(text: string, onClick: DialogButtonClick, textStyle?: Style): JSX.Element {
        return <DialogButton text={text} onClick={onClick} textStyle={textStyle} />;
    }

    private buttons: JSX.Element[] = [];

    /**
     * 添加一个按钮
     * @param params{
     *     text://button文本
     *     id: button id
     * }
     */
    appendButton(params: { text: string; id?: number; textStyle?: Style; onClick?: DialogButtonClick }): AbcDialogButtonBuilder {
        const button = AbcDialogButtonBuilder.createDialogButton(
            params.text,
            () => {
                if (params.onClick) {
                    params.onClick();
                    return;
                }

                if (!_.isNil(params.id)) AbcNavigator.pop(params.id);
            },
            params.textStyle
        );

        this.buttons.push(button);

        return this;
    }

    appendCustomButton(params: JSX.Element): AbcDialogButtonBuilder {
        this.buttons.push(params);
        return this;
    }

    appendPositiveButton(text: string): AbcDialogButtonBuilder {
        return this.appendButton({ text: text, id: DialogIndex.positive });
    }

    appendNegativeButton(text: string): AbcDialogButtonBuilder {
        return this.appendButton({ text: text, id: DialogIndex.negative });
    }

    /**
     * 构建对话框底部按钮栏
     */
    public buildBar(): JSX.Element {
        const finalButtons: JSX.Element[] = [];

        for (const button of this.buttons) {
            if (finalButtons.length > 0) {
                finalButtons.push(
                    <View
                        key={finalButtons.length.toString()}
                        style={{
                            backgroundColor: Colors.dividerLineColor,
                            width: 0.5,
                            alignSelf: "stretch",
                        }}
                    />
                );
            }

            finalButtons.push(button);
        }

        return (
            <View
                style={[
                    ABCStyles.topLine,
                    {
                        flexDirection: "row",
                        height: Sizes.listItemHeight,
                        backgroundColor: "white",
                    },
                ]}
            >
                {finalButtons}
            </View>
        );
    }
}

/**
 * 显示确认对话框（只包含一个确认按钮）
 * @param title 标题
 * @param content 内容
 * @param positive 确认按钮的文本
 * @param routeName 对话框在navigator中的名称
 * @param alignment
 */
export function AbcShowConfirmDialog(
    title: string,
    content: string | JSX.Element,
    positive = "确认",
    routeName?: string,
    alignment: DialogAlignment = "center",
    dialogMaskBg?: string,
    contentPadding?: Style,
    titleToContentSpace?: number,
    titleLineHeight?: number
): Promise<DialogIndex> {
    const builder = new AbcDialogBuilder();
    builder.positive = positive;
    builder.title = title;
    builder.content = content;
    builder.routerName = routeName;

    builder.alignment = alignment;

    return builder.show({ dialogMaskBg, contentPadding, titleToContentSpace, titleLineHeight });
}

/**
 * 显示询问对话框
 * @param title 标题
 * @param content 内容
 * @param positive 确认按钮的文本
 * @param negative 取消按钮显示文本
 * @param alignment 标题和content对齐方式
 * @param dialogMaskBg  弹窗遮罩层的背景颜色
 * @param marginVertical 弹窗上下外间距
 * @param onClick 点击确定/取消按钮的回调
 * @return Promise<DialogIndex> 对话框隐藏时返回点击按钮的index
 */
export function AbcShowQueryDialog(
    title: string,
    content: string | JSX.Element,
    positive = "确认",
    negative = "取消",
    alignment: DialogAlignment = "center",
    dialogMaskBg?: string,
    marginVertical?: number, // 安卓长文本时上下边框可能会超出屏幕
    onClick?: (index: number) => void
): Promise<DialogIndex> {
    const builder = new AbcDialogBuilder();
    builder.positive = positive;
    builder.negative = negative;
    builder.title = title;
    builder.content = content;
    builder.alignment = alignment;
    builder.marginVertical = marginVertical;
    builder.onClick = onClick;
    return builder.show({ dialogMaskBg });
}

/**
 * 显示对话框
 * @param dialog 要显示的对话框
 * @param options 对话框显示属性
 * @return Promise<T> 对话框隐藏时的返回值，由业务自己定义
 */
export function AbcShowDialog<T>(dialog: JSX.Element, options?: DialogOptions): Promise<T> {
    return AbcNavigator.showDialog(dialog, options);
}

interface ShowInputDialogProps {
    title?: string;
    content?: string;
    placeholder?: string;
    positive?: string;
    negative?: string;
    button?: AbcDialogButtonBuilder;
    onChange?: (text: string) => void;
}

interface ShowInputDialogStates {
    text: string;
}

export class AbcShowInputDialog extends AbcBaseComponent<ShowInputDialogProps, ShowInputDialogStates> {
    static show(options?: ShowInputDialogProps): Promise<string | undefined> {
        let name = "";
        return AbcShowDialog(
            <AbcShowInputDialog
                {...options}
                onChange={(text) => {
                    name = text;
                }}
            />
        ).then((select) => {
            if (select == DialogIndex.positive) {
                return name;
            } else {
                return undefined;
            }
        });
    }

    contentPadding = Sizes.paddingLTRB(16, 32, 16, 32);

    constructor(props: ShowInputDialogProps) {
        super(props);
        this.state = {
            text: props.content ?? "",
        };
    }

    static defaultProps = {
        positive: "确认",
        negative: "取消",
    };

    render(): JSX.Element {
        const { title, placeholder, positive, negative, button, onChange } = this.props;
        const buttonBuilder = button ?? new AbcDialogButtonBuilder();
        if (negative) {
            buttonBuilder.appendButton({
                text: negative,
                id: DialogIndex.negative,
            });
        }

        if (positive) {
            buttonBuilder.appendButton({
                text: positive,
                id: DialogIndex.positive,
                textStyle: TextStyles.t18MM.copyWith(this.state.text.trim().length ? {} : { color: Colors.T6 }),
                onClick: this.state.text.trim().length ? undefined : () => ignore(1),
            });
        }

        const _showTitle = !_.isNil(title) && title != "";

        const contentWidget = (
            <View style={[ABCStyles.halfDPBorder, { flex: 1, height: Sizes.dp48 }]}>
                <AbcTextInput
                    style={{ flex: 1, ...TextStyles.t16NT1, ...Sizes.paddingLTRB(Sizes.dp8, Sizes.dp12) }}
                    defaultValue={this.state.text}
                    placeholder={placeholder}
                    numberOfLines={1}
                    maxLength={20}
                    onChangeText={(t) => {
                        onChange?.(t);
                        this.setState({ text: t });
                    }}
                />
            </View>
        );

        return (
            <View
                style={{
                    marginHorizontal: 24,
                    backgroundColor: "white",
                    borderRadius: 4,
                    maxHeight: (Dimensions.get("window").height * 3) / 4,
                    minHeight: 0,
                    overflow: "hidden",
                }}
            >
                <View
                    style={{
                        flex: 1,
                        justifyContent: "center",
                        ...this.contentPadding,
                        paddingVertical: 24,
                    }}
                >
                    {_showTitle && (
                        <Text
                            style={[
                                TextStyles.t18MB,
                                {
                                    textAlign: "center",
                                    lineHeight: Sizes.dp20,
                                },
                            ]}
                        >
                            {title ?? ""}
                        </Text>
                    )}
                    {_showTitle && <AbcSizedBox height={24} />}
                    {contentWidget}
                </View>
                {buttonBuilder.buildBar()}
            </View>
        );
    }
}
