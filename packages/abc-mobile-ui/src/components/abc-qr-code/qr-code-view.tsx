/**
 * 成都字节星球科技公司
 *
 * Created by <PERSON><PERSON><PERSON> on 2020-08-13
 *
 * @description 生成二维码
 *
 */
import React from "react";
import { colorParse, Style } from "@hippy/react";
import { Color, Colors } from "@app/theme";

interface QRViewProps {
    content: string; //二维码内容
    color?: Color; //二维码颜色，默认黑色
    style?: Style | Style[];
}

export class AbcQrCode extends React.Component<QRViewProps, {}> {
    private instance: HTMLDivElement | null = null;

    /**
     * @ignore
     */
    constructor(props: QRViewProps) {
        super(props);
    }

    /**
     * @ignore
     */
    public render(): JSX.Element {
        const { color, ...nativeProps } = this.props;
        return (
            <div
                // @ts-ignore
                nativeName="QRView"
                ref={(ref: HTMLDivElement) => {
                    this.instance = ref;
                }}
                // @ts-ignore
                color={color != undefined ? colorParse(color) : colorParse(Colors.black)}
                {...nativeProps}
            />
        );
    }
}
