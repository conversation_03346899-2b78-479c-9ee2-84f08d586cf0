/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/5/21
 *
 * @description
 */
import { AbcBaseComponent } from "../abc-base-component";
import { Style, StyleSheet, Text } from "@hippy/react";
import React from "react";
import { Colors, TextStyles, Color, Sizes, createShadowStyle } from "@app/theme";
import { AbcLoading } from "../abc-loading";
import { AbcView } from "../abc-view";
import { Subject } from "rxjs";

//@ts-ignore
const styles = StyleSheet.create({
    containerStyle: {
        alignItems: "center",
        justifyContent: "center",
        borderRadius: Sizes.dp3,
        padding: Sizes.dp10,
    },

    loadingButtonContainer: {
        color: Colors.white,
        height: Sizes.dp32,
        borderRadius: Sizes.dp4,
        justifyContent: "center",
        alignItems: "center",
        paddingHorizontal: Sizes.dp3,
        fontSize: Sizes.dp18,
    },
});

interface AbcButtonProps {
    text?: string;
    textStyle?: Style; //default TextStyles.t14NW
    style?: Style | Style[];
    disableStyle?: Style;
    disableTextStyle?: Style;
    pressColor?: Color;
    continuousClick?: boolean; //允许连续
    onClick?: () => void; //当onClick为空时，为disableStyle
    showShadow?: boolean; //是否显示阴影
    onLongClick?(): void;
}

const AbcButtonPressStatusTrigger = new Subject();

export class AbcButton extends AbcBaseComponent<AbcButtonProps> {
    private activePress?: boolean;

    componentDidMount(): void {
        super.componentDidMount();
        AbcButtonPressStatusTrigger?.subscribe(() => {
            if (!this.activePress) return;
            this.activePress = false;
            this.setState({});
        });
        // TODO addToDisposableBag
        // ?.addToDisposableBag(this);
    }

    render(): JSX.Element {
        const { onClick, text, disableTextStyle, disableStyle, pressColor, continuousClick, showShadow } = this.props;
        let { textStyle, style } = this.props;
        let bgColor = Colors.mainColor;
        if (!onClick) {
            textStyle = disableTextStyle ?? textStyle ?? TextStyles.t14NW;
            style = disableStyle ?? style;
            bgColor = Colors.bdColor;
        }

        return (
            <AbcView
                style={[
                    styles.containerStyle,
                    { backgroundColor: bgColor },
                    style ?? {},
                    this.activePress && pressColor ? { backgroundColor: pressColor } : {},
                    !!showShadow
                        ? createShadowStyle({
                              shadowColor: Colors.black,
                              shadowOpacity: 0.08,
                              shadowRadius: Sizes.dp4,
                              //@ts-ignore
                              shadowOffset: { height: Sizes.dp1 },
                          })
                        : {},
                ]}
                continuousClick={continuousClick}
                onTouchDown={() => {
                    this.activePress = true;
                    this.setState({});
                }}
                onTouchEnd={() => {
                    AbcButtonPressStatusTrigger.next();
                }}
                onTouchCancel={() => {
                    AbcButtonPressStatusTrigger.next();
                }}
                onClick={() => this.props.onClick?.()}
                onLongClick={this.props.onLongClick}
            >
                {text && <Text style={[TextStyles.t14NW, textStyle ?? {}]}>{text}</Text>}
                {this.props.children}
            </AbcView>
        );
    }
}

interface ButtonProps {
    loading?: boolean;
    style?: Style;
    onClick?: any;
}

export class AbcLoadingButton extends React.Component<ButtonProps> {
    constructor(props: ButtonProps) {
        super(props);
    }

    render(): JSX.Element {
        const { style = {}, loading } = this.props;
        const _loadingHeight = (style.height ?? styles.loadingButtonContainer.height!) - 10;
        return (
            <AbcView
                style={[styles.loadingButtonContainer, { backgroundColor: Colors.mainColor }, { ...style }]}
                onClick={() => {
                    if (loading) return;
                    this.props.onClick?.();
                }}
            >
                {React.Children.map(this.props.children, (child) => {
                    return child;
                })}
                {loading && <AbcLoading name={"loading"} style={{ position: "absolute" }} size={_loadingHeight} ignoreTheme={true} />}
            </AbcView>
        );
    }
}
