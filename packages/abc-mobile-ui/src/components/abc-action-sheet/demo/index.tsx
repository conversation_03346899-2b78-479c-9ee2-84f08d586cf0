import React, {useState} from "react";
import {View} from "@hippy/react";
import {AbcActionSheet} from "../index";
import {AbcText} from "../../abc-text";
import {ABCStyles, Colors, Sizes} from "@app/theme";
import {AbcButton} from "../../abc-button";
import {AbcToast} from "../../abc-toast";

export function AbcActionSheetDemo() {
    const [actions, setActions] = useState([{
        name: "选项一",
        value: "1"
    }, {
        name: "选项二",
        value: "2"
    }, {
        name: "选项三",
        value: "3"
    }])

    const [visible, setVisible] = useState(false)
    const [visibleCancel, setVisibleCancel] = useState(false)
    const [visibleTitle, setVisibleTitle] = useState(false)

    function onSelect(item: any) {
        AbcToast.show(`选择了${item.name}`)
    }

    function onClick() {
        AbcToast.show("点击自定义内容区域")
    }

    return (
        <View>
            <AbcText style={{color: Colors.T1, marginBottom: Sizes.dp20}}>基础用法</AbcText>
            <View>
                <AbcButton onClick={() => setVisible(true)}>AbcPopUp</AbcButton>
                <AbcActionSheet
                    visible={visible}
                    minHeight={100}
                    contentPadding={0}
                    actions={actions}
                    onSelect={onSelect}
                    onClose={() => {
                        setVisible(false);
                    }}
                />
            </View>

            <View style={{marginTop: Sizes.dp16}}>
                <AbcButton onClick={() => setVisibleCancel(true)}>展示取消按钮和描述信息</AbcButton>
                <AbcActionSheet
                    visible={visibleCancel}
                    minHeight={200}
                    contentPadding={0}
                    actions={actions}
                    actionsDescription={"更多选项"}
                    onClose={() => {
                        setVisibleCancel(false);
                    }}
                    showCancel={true}
                    onSelect={onSelect}
                    onCancel={() =>{
                        setVisibleCancel(false);
                    }}
                />
            </View>

            <View style={{marginTop: Sizes.dp16}}>
                <AbcButton onClick={() => setVisibleTitle(true)}>展示标题和删除按钮，内容自定义</AbcButton>
                <AbcActionSheet
                    visible={visibleTitle}
                    minHeight={200}
                    contentPadding={0}
                    onClose={() => {
                        setVisibleTitle(false);
                    }}
                    title={"自定义标题"}
                    showCloseIcon={true}
                    onCloseIcon={() =>{
                        setVisibleTitle(false);
                    }}
                    customActionContent={<View style={[ABCStyles.centerChild]} onClick={onClick}>自定义内容</View>}
                    showCancel={true}
                    onCancel={() => {
                        setVisibleTitle(false);
                    }}
                />
            </View>
        </View>
    );
}
