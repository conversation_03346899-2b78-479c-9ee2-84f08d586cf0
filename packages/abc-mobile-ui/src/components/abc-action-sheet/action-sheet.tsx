import React, {useState} from "react";
import {View, ScrollView} from "@hippy/react";
import {AbcPopUp} from "../abc-pop-up";
import type {PopUpProps} from "../abc-pop-up/pop-up";
import {Sizes, ABCStyles, TextStyles, Colors} from "@app/theme";
import {AbcView} from "../abc-view";
import {AbcText} from "../abc-text";
import {AbcDivider} from "../abc-divider";

export interface ActionSheetProps {
    actions?: any[]; // 面板选项列表
    actionsDescription?: string; // 选项上方的描述信息
    customActionContent?: React.ReactNode; // 自定义actionSheet内容区域
    showCancel?: boolean; // 展示取消按钮
    cancelText?: string; // 取消按钮文案
    onCancel?: () => void; // 取消按钮事件
    onSelect?: (item: any) => void; // 选择了actions中的某一项
}


const ActionSheetContent: React.FC<ActionSheetProps> = (props: ActionSheetProps) => {
    const {
        actions,
        actionsDescription,
        customActionContent,
        showCancel,
        cancelText = "取消",
        onCancel,
        onSelect
    } = props;

    const contentView = () => {
        if (customActionContent) {
            return (
                <View style={{flex: 1}}>
                    {customActionContent}
                </View>
            )
        }

        const [currentIndex, setCurrentIndex] = useState(-1);
        return (
            <View style={{flex: 1}}>
                {actionsDescription && (
                    <View
                        style={[
                            ABCStyles.bottomLine,
                            ABCStyles.rowAlignCenter,
                            {
                                justifyContent: "center",
                                paddingVertical: Sizes.dp16,
                                borderColor: Colors.border_color_light,
                            },
                        ]}
                    >
                        <AbcText style={[TextStyles.t13NT2.copyWith({color: Colors.t2, lineHeight: Sizes.dp21})]}>
                            {actionsDescription}
                        </AbcText>
                    </View>
                )}
                {!!actions?.length && (
                    <ScrollView style={{flex: 1}} showsVerticalScrollIndicator={false}
                                showsHorizontalScrollIndicator={false}>
                        {actions?.map((item, index) => {
                            const isShowBottomLine = index < actions.length - 1;
                            return (
                                <View
                                    key={item.name || item.value || index}
                                    style={[
                                        isShowBottomLine ? ABCStyles.bottomLine : {},
                                        ABCStyles.rowAlignCenter,
                                        {
                                            justifyContent: "center",
                                            paddingVertical: Sizes.dp16,
                                            borderColor: Colors.border_color_light,
                                            backgroundColor: currentIndex == index ? Colors.cp_pressed : "",
                                            zIndex: 9999,
                                        },
                                    ]}
                                    onClick={() => {
                                        onSelect?.(item);
                                    }}
                                    onTouchDown={() => {
                                        setCurrentIndex(index);
                                    }}
                                    onTouchEnd={() => {
                                        setCurrentIndex(-1);
                                    }}
                                >
                                    <AbcText style={[TextStyles.t16NB, {lineHeight: Sizes.dp24}]}>
                                        {item.name}
                                    </AbcText>
                                </View>
                            );
                        })}
                    </ScrollView>
                )}
            </View>
        );
    };

    return (
        <View style={{flex: 1, position: "relative"}}>
            {contentView()}
            {showCancel && (
                <View
                    style={{
                        position: "relative",
                        bottom: 0,
                        left: 0,
                    }}
                >
                    <AbcDivider lineHeight={Sizes.dp8} color={Colors.retail_bg_grey2}></AbcDivider>
                    <AbcView
                        style={{paddingVertical: Sizes.dp16, ...ABCStyles.rowAlignCenter, justifyContent: "center"}}
                        onClick={() => onCancel?.()}
                    >
                        <AbcText style={TextStyles.t16NB}>{cancelText}</AbcText>
                    </AbcView>
                </View>
            )}
        </View>
    )

}

export function AbcActionSheet(props: ActionSheetProps & PopUpProps) {
    return (
        <View style={{backgroundColor: Colors.white, flex: 1}}>
            <AbcPopUp {...props} customView={(<ActionSheetContent {...props}/>)}/>
        </View>
    );
}
