import React, { useState } from "react";
import { AbcView } from "../abc-view/index";
import { Text } from "@hippy/react";
import { AbcIconfont } from "../abc-iconfont/index";
import { ABCStyles, Colors, Sizes, TextStyles } from "@app/theme";
const AbcCollapse: React.FC<{ defaultStatus?: boolean; showCount?: boolean }> = ({ defaultStatus, showCount = false, children }) => {
    const [showMore, setShowMore] = useState(defaultStatus ?? true);
    const childrenArray = React.Children.toArray(children);
    const childCount = childrenArray.length;
    if (childCount < 2) return <>{children}</>;
    return (
        <>
            {showMore ? childrenArray : childrenArray[0]}
            <AbcView style={[ABCStyles.rowAlignCenter, { marginTop: Sizes.dp8 }]} onClick={() => setShowMore(!showMore)}>
                <Text style={TextStyles.t14NT6}>
                    {showMore ? "收起" : `展开${showCount && childCount - 1 > 0 ? `(${childCount - 1})` : ""}`}
                </Text>
                <AbcIconfont name={showMore ? "arrow-up-big" : "arrow-down-big"} size={Sizes.dp16} color={Colors.T6} />
            </AbcView>
        </>
    );
};

export { AbcCollapse };
