/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/13.
 */

import React from "react";
import { View } from "@hippy/react";
import AbcTabs from "./tabs";

interface TabProps {
    title?: string;
    badge?: boolean;

    renderSlot?(event: AbcTabs, selected: boolean): JSX.Element;
}

export default class AbcTab extends React.PureComponent<TabProps, any> {
    constructor(props: TabProps) {
        super(props);
    }

    render(): JSX.Element {
        return <View style={{ flex: 1 }}>{this.props.children}</View>;
    }
}
