import { Style, StyleSheet, View, ViewPager } from "@hippy/react";
import { Color, Colors, flattenStyles, Sizes, TextStyles } from "@app/theme";
import { UniqueKey, AnyType, ignore, DeviceUtils } from "@app/utils";
import React from "react";
import { AbcDivider } from "../abc-divider";
import { AbcSpacer } from "../abc-spacer";
import { AbcBadge } from "../abc-badge";
import { AbcScroll } from "../abc-scroll";
import { AbcText } from "../abc-text";

// @ts-ignore
const TabsStyle = StyleSheet.create({
    title: {
        minHeight: Sizes.listItemHeight - Sizes.dp4,
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-around",
    },
    titleText: {
        ...TextStyles.t16MT2.copyWith({ color: Colors.t2 }),
    },
    currentTitle: {},
    currentTitleText: {
        ...TextStyles.t16MB,
    },
    content: {},
});

interface TabsProps {
    lazy?: boolean;

    /**
     * 自定义组件key，避免与当更新组件时，造成的key冲突
     */
    customKey?: string;

    /**
     * 懒加载的数量
     * 当前页面前后加载数量
     * @default 0
     */
    lazyCount?: number;
    enable?: boolean;
    scrollEnabled?: boolean;
    initialPage?: number;
    style?: Style | Style[];
    tabsStyle?: Style;
    tabStyle?: Style;
    currentStyle?: Style;
    lineColor?: Color;
    lineMargin?: number; //default 1
    leftSuffix?: () => JSX.Element;
    rightSuffix?: () => JSX.Element;
    scrollBeginDragInputBlur?: boolean;
    dotStyle?: Style;
    dotSize?: number;
    titleBorderStyle?: Style;

    /**
     * 是否显示tab的title
     * @default true
     */
    showTabTitle?: boolean;

    /**
     * tab切换时是否使用动画
     * @default true
     */
    animated?: boolean;

    /**
     * 是否允许手势滑动切换
     * @default true
     */
    swipeEnabled?: boolean;

    onAttachedToWindow?(): void;

    onChange?(arg1: number): void;

    onPageScroll?(): void;
}

export default class AbcTabs extends React.PureComponent<TabsProps, any> {
    private _viewPager?: ViewPager | null;
    private selectedIndex: number;
    private _tabRef: Set<number> = new Set<number>();
    private _tabHeader?: AbcScroll | null;

    private __keyScrollStart = UniqueKey();
    private __keyScrollEnd = UniqueKey();

    constructor(props: TabsProps) {
        super(props);
        this.selectedIndex = this.props.initialPage || 0;
        this._tabRef.add(this.selectedIndex);
    }

    static defaultProps = {
        tabStyle: {
            marginRight: 0,
        },
        lazy: true,
        lazyCount: 0,
        scrollEnabled: true,
        enable: true,
        showTabTitle: true,
        animated: true,
        swipeEnabled: true,
    };

    private __firstChildKey?: string;
    private __lastChildKey?: string;

    private __animated = true;

    get sheets(): AnyType {
        // eslint-disable-next-line react/prop-types
        let { children } = this.props;
        if (!(children instanceof Array)) {
            if (children) children = [children];
            else children = [];
        }
        // @ts-ignore
        // eslint-disable-next-line react/prop-types
        children = children.filter((item) => item);

        // @ts-ignore
        const _firstIndex = children?.findIndex((item) => item.props.title == this.__firstChildKey);
        // @ts-ignore
        // const _lastIndex = children?.findIndex((item) => item.props.title == this.__lastChildKey);
        if ((_firstIndex ?? 0) > 0) {
            this.selectedIndex = _firstIndex + this.selectedIndex;
            this.__animated = false;
            // @ts-ignore
        } else {
            this.__animated = true;
        }
        // @ts-ignore
        this.__firstChildKey = children[0].props.title;
        // @ts-ignore
        this.__lastChildKey = [...children].reverse()[0].props.title;

        return children;
    }

    //判断页面是否被选中
    public isSelected(index: number): boolean {
        return this.selectedIndex == index;
    }

    handlePageSelected({ position }: AnyType): void {
        this._handleScrollViewScroll(position);
        this.selectedIndex = position;
        this.props.onChange?.(position);
        this._tabRef.add(this.selectedIndex);
        this.forceUpdate();
    }

    public nextPage(): void {
        if (!this.props.enable) return;
        const { selectedIndex } = this;
        this.setPage(Math.min(selectedIndex + 1, this.sheets.length - 1));
    }

    public prePage(): void {
        if (!this.props.enable) return;
        const { selectedIndex } = this;
        this.setPage(Math.max(selectedIndex - 1, 0));
    }

    public setPage(index: number, animated = true): void {
        if (!this.props.enable) return;
        this.scrollTo(index, animated);
    }

    // 外部调用执行滑动
    public scrollTo(idx: number, animated?: boolean): void {
        const { selectedIndex } = this;
        if (!this._viewPager) return;
        if (idx !== selectedIndex) {
            this._handleScrollViewScroll(idx);
            if (animated) {
                this._viewPager.setPage(idx);
            } else {
                this._viewPager.setPageWithoutAnimation(idx);
            }
        }
    }

    render(): JSX.Element {
        const {
            customKey,
            tabsStyle,
            currentStyle,
            lineColor,
            rightSuffix,
            scrollEnabled,
            enable,
            leftSuffix,
            lineMargin = 1,
            scrollBeginDragInputBlur,
            dotStyle,
            dotSize,
            showTabTitle,
            onAttachedToWindow,
            style,
            titleBorderStyle,
            animated,
            swipeEnabled,
        } = this.props;
        const { borderBottomWidth, borderRadius, borderColor, backgroundColor, flexGrow, flexShrink, paddingHorizontal, ...others } =
            tabsStyle ?? {};
        ignore(flexGrow);
        ignore(flexShrink);
        return (
            <View
                style={{
                    flexGrow: 1,
                    backgroundColor: Colors.white,
                    position: "relative",
                    ...flattenStyles(style),
                }}
            >
                {!!showTabTitle && (
                    <View
                        accessibilityLabel={"abc-tab-bar"}
                        style={[TabsStyle.title, { borderBottomWidth, borderRadius, borderColor, backgroundColor }]}
                    >
                        <View
                            style={{
                                // alignSelf: "stretch",
                                backgroundColor,
                            }}
                        >
                            {leftSuffix?.()}
                        </View>
                        <AbcScroll
                            scrollBeginDragInputBlur={scrollBeginDragInputBlur}
                            ref={(ref) => {
                                this._tabHeader = ref;
                            }}
                            horizontal={true}
                            showsHorizontalScrollIndicator={false}
                            style={{
                                flex: 1,
                            }}
                            contentContainerStyle={flattenStyles([
                                TabsStyle.title,
                                { flex: 1 },
                                others ? { backgroundColor, ...others } : {},
                                {
                                    flexDirection: "row",
                                },
                            ])}
                        >
                            <View ref={this.__keyScrollStart} collapsable={false} />
                            {
                                // @ts-ignore
                                this.sheets.map((item, index, self) => {
                                    const { title, badge, renderSlot } = item.props;
                                    const length = self.length;
                                    // @ts-ignore
                                    const { marginRight, ...others } = this.props.tabStyle;
                                    return (
                                        <View
                                            key={index}
                                            ref={`${title}${index}`}
                                            style={{
                                                marginRight: index + 1 == length ? paddingHorizontal ?? 0 : marginRight,
                                                marginLeft: index == 0 ? paddingHorizontal : 0,
                                                alignSelf: "stretch",
                                                position: "relative",
                                            }}
                                            collapsable={false}
                                        >
                                            {/*<SizedBox height={7} />*/}
                                            <AbcSpacer />
                                            <View
                                                style={{ paddingRight: badge && DeviceUtils.isAndroid() ? Sizes.dp5 : 0 }}
                                                overflow={"visible"}
                                            >
                                                <AbcBadge dot={badge} size={dotSize} dotStyle={dotStyle}>
                                                    {renderSlot ? (
                                                        renderSlot(
                                                            this,
                                                            this.selectedIndex == index && (currentStyle || TabsStyle.currentTitleText)
                                                        )
                                                    ) : (
                                                        <AbcText
                                                            style={[
                                                                TabsStyle.titleText,
                                                                others,
                                                                this.selectedIndex == index && (currentStyle || TabsStyle.currentTitleText),
                                                            ]}
                                                            onClick={this.setPage.bind(this, index, animated)}
                                                        >
                                                            {title}
                                                        </AbcText>
                                                    )}
                                                </AbcBadge>
                                            </View>
                                            <AbcSpacer />
                                            {this.selectedIndex == index && (
                                                <AbcDivider
                                                    style={[
                                                        {
                                                            borderRadius: 0.5,
                                                            position: "absolute",
                                                            left: 0,
                                                            right: 0,
                                                            bottom: Sizes.dp2,
                                                            ...flattenStyles(titleBorderStyle),
                                                        },
                                                    ]}
                                                    color={lineColor || Colors.mainColor}
                                                    lineHeight={Sizes.dp3}
                                                    lineMargin={lineMargin}
                                                />
                                            )}
                                        </View>
                                    );
                                })
                            }
                            <View ref={this.__keyScrollEnd} collapsable={false} />
                        </AbcScroll>
                        <View
                            style={{
                                // alignSelf: "stretch",
                                backgroundColor,
                            }}
                        >
                            {rightSuffix?.()}
                        </View>
                    </View>
                )}
                <ViewPager
                    key={customKey ?? this.sheets.length}
                    ref={(ref) => {
                        this._viewPager = ref;
                    }}
                    //@ts-ignore
                    onAttachedToWindow={() => {
                        onAttachedToWindow?.();
                    }}
                    scrollEnabled={enable && scrollEnabled && swipeEnabled}
                    initialPage={this.selectedIndex}
                    style={{ flex: 1 }}
                    onPageSelected={this.handlePageSelected.bind(this)}
                    onPageScroll={this.handlePageScroll.bind(this)}
                    onPageScrollStateChanged={this.handlePageScrollStateChanged.bind(this)}
                >
                    {/* eslint-disable-next-line react/prop-types */}
                    {React.Children.map(this.props.children, (child, index) => {
                        if (!this.props.lazy) return child;
                        if (Math.abs(this.selectedIndex - index) <= (this.props.lazyCount ?? 0) || this._tabRef.has(index)) return child;
                        return <View />;
                    })}
                </ViewPager>
            </View>
        );
    }

    handlePageScroll({}: /*position, offset*/ AnyType): void {
        this.props.onPageScroll?.();
    }

    handlePageScrollStateChanged(/*pageScrollState: string*/): void {}

    private _handleScrollViewScroll(idx: number): void {
        const { selectedIndex } = this;
        let willSdx = selectedIndex,
            selectedRef;
        if (selectedIndex <= idx) {
            //选项在当前选中项的右边
            willSdx = idx + 1;
        } else if (selectedIndex > idx) {
            //选中项在当前选中项的左边
            willSdx = idx - 1;
        }
        if (willSdx == 0) selectedRef = this.__keyScrollStart;
        else if (willSdx == this.sheets.length) selectedRef = this.__keyScrollEnd;
        else selectedRef = `${this.sheets[willSdx]?.props?.title}${willSdx}`;
        this._tabHeader?.scrollChildToVisible(selectedRef, this.__keyScrollStart, this.__keyScrollEnd, this.__animated);
    }
}
