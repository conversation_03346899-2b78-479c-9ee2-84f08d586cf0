import React from "react";
import { View } from "@hippy/react";
import { AbcTabs, AbcTab } from "../index";
import { AbcText } from "../../abc-text";
import { ABCStyles, Colors, Sizes, TextStyles } from "@app/theme";

export function AbcTabsDemo() {
    const tabList = [
        {
            id: 1,
            title: "预约",
        },
        {
            id: 2,
            title: "挂号",
        },
    ];
    return (
        <View>
            <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>基础用法</AbcText>
            <AbcTabs
                tabsStyle={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "center",
                    paddingHorizontal: Sizes.listHorizontalMargin,
                    backgroundColor: Colors.white,
                    height: Sizes.dp44,
                    ...ABCStyles.bottomLine,
                }}
                tabStyle={{
                    marginRight: Sizes.dp61,
                    ...TextStyles.t16NB.copyWith({ color: Colors.t2 }),
                }}
                currentStyle={{
                    ...TextStyles.t16MT1,
                }}
            >
                {tabList.map((item) => {
                    return <AbcTab title={item.title} key={item.id} />;
                })}
            </AbcTabs>
        </View>
    );
}
