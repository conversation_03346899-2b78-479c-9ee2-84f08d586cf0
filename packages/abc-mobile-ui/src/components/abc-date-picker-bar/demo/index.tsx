import React, { useState, useMemo } from "react";
import { View } from "@hippy/react";
import { AbcDatePickerBar } from "../index";
import { DatePickerBarOptions } from "../options";
import { getLastMonthEndDate, getLastMonthStartDate, nextDate, parseTime, prevDate } from "@app/utils";
import { Colors, Sizes } from "@app/theme";
import { AbcText } from "../../abc-text";

export function AbcDatePickerBarDemo() {
    const [label, setLabel] = useState(DatePickerBarOptions.DAY.label);
    const [date, setDate] = useState<string | string[]>("");
    const options = [
        DatePickerBarOptions.DAY,
        DatePickerBarOptions.LAST_3_DAYS,
        DatePickerBarOptions.WEEK,
        DatePickerBarOptions.MONTH,
        DatePickerBarOptions.YEAR,
        {
            label: "last_month",
            name: "上月",

            getValue() {
                return [getLastMonthStartDate(), getLastMonthEndDate()];
            },
        },
    ];
    const [pickerStartDate, setPickerStartDate] = useState<number | null>(null);
    const pickerStartDateRef = React.useRef<number | null>(null);

    const describeList = [
        {
            date: parseTime(new Date(), "y-m-d", true) || "",
            describe: "今",
        },
    ];

    const pickerOptions = {
        disabledDate(date: Date) {
            const current = pickerStartDateRef.current;
            if (current) {
                let maxTime = nextDate(new Date(current), 7).getTime();
                const minTime = prevDate(new Date(current), 7).getTime();

                if (maxTime > Date.now()) {
                    maxTime = Date.now();
                }

                return date.getTime() > maxTime || date.getTime() < minTime || date.getTime() > Date.now();
            }

            return date > new Date();
        },

        onPick: ({ minDate }: { minDate: Date | null }) => {
            if (minDate) {
                setPickerStartDate(minDate.getTime());
                pickerStartDateRef.current = minDate.getTime(); // 立即同步
            }
        },
    };

    function handleChange({ date, label }: { date: string | string[]; label: string | number }) {
        setLabel(label as string);
        setDate(date);
        pickerStartDateRef.current = null;
    }

    return (
        <View>
            <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>基础用法</AbcText>
            <AbcDatePickerBar
                value={label}
                date={date}
                options={options}
                pickerOptions={pickerOptions}
                describeList={describeList}
                enableDatePicker={true}
                onChange={handleChange}
            />
        </View>
    );
}
