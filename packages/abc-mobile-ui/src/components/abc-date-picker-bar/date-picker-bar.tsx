import React, { useState, useEffect, useRef } from "react";
import { Dimensions, Text, View } from "@hippy/react";
import { Colors, Sizes, TextStyles } from "@app/theme";
import { AbcRadioButton, AbcRadioGroup2 } from "../abc-radio-button";
import { AbcDateRangePanel } from "../abc-calendar/date-range-panel";
import AbcDivider from "../abc-divider/divider";
import { parseTime, Utils, UiUtils, getLog } from "@app/utils";
import { AbcFlex } from "../abc-flex";
import { DescribeItem } from "../abc-calendar/calender-interface";
import { PickerOptions } from "../abc-calendar/date-constants";
import { AbcNavigator } from "../abc-navigator";
import { AbcSafeArea } from "../abc-safe-area/index";
import { AbcView } from "../abc-view";

export interface DatePickerBarOption {
    label: string;
    name: string;
    getValue?: () => {};
}

export interface DatePickerBarProps {
    enableDatePicker?: boolean; // 是否自定义显示日期范围选择控件
    options: DatePickerBarOption[];
    value?: string | number;
    date?: string | string[];
    describeList?: DescribeItem[];
    pickerOptions?: PickerOptions;
    beginYear?: number;
    endYear?: number;
    customStyle?: Object;
    shortcutStyle?: Object;
    initToast?: boolean; // 初始化是否toast提示
    changeDateToast?: boolean; // 选择了时间是否toast提示
    displayDateRange?: boolean; // 是否展示时间范围
    onSelected?: (label: string) => void;
    onChange?: ({ date, label }: { date: string | string[]; label: string | number }) => void;
}

const LABEL_DATE_RANGE_PICKER = "__date-range-picker";
const customOptionItem = {
    label: LABEL_DATE_RANGE_PICKER,
    name: "自定义",
};

export const AbcDatePickerBar: React.FC<DatePickerBarProps> = (props) => {
    const {
        value,
        date,
        options,
        enableDatePicker = true,
        initToast,
        changeDateToast,
        displayDateRange,
        describeList,
        pickerOptions,
        beginYear = 1990,
        endYear = 2040,
        customStyle = {},
        shortcutStyle = {},
        onChange,
    } = props || {};
    const isSelectedCustomOptionItem = value === customOptionItem.label;
    const optionsList = [...(options || [])];
    const [rangeDate, setRangeDate] = useState(() => {
        if (isSelectedCustomOptionItem && Array.isArray(date)) {
            return {
                start: date[0] || "",
                end: date[1] || "",
            };
        }
        return { start: "", end: "" };
    });
    // 用于临时保存上一个 label
    const [prevLabel, setPrevLabel] = useState<string | number | undefined>(value);
    const [currentLabel, setCurrentLabel] = useState<string | number | undefined>(value);

    useEffect(() => {
        setCurrentLabel(value);
        setPrevLabel(value);
    }, [value]);

    // 当前选中的 label，优先 currentLabel
    const selectedRadioLabel = currentLabel;
    let rangeDateStart = rangeDate.start ? parseTime(new Date(rangeDate.start), "m-d", true) || "" : "";
    rangeDateStart = rangeDateStart?.replace(/-/g, "/");
    let rangeDateEnd = rangeDate.end ? parseTime(new Date(rangeDate.end), "m-d", true) || "" : "";
    rangeDateEnd = rangeDateEnd?.replace(/-/g, "/");
    const _screenHeight = Dimensions.get("screen").height;

    // 防止异步流程未结束时重复点击
    const processingRef = useRef(false);
    const customRadioBtn = useRef(null);
    async function handleClick(label: string | number) {
        if (processingRef.current) return;
        processingRef.current = true;
        try {
            if (label === LABEL_DATE_RANGE_PICKER) {
                const layout = await UiUtils.measureInWindow(customRadioBtn.current);
                const topOffset = layout.y + layout.height - UiUtils.safeStatusHeight() + Sizes.dp16;
                setPrevLabel(currentLabel); // 保存上一个选中的 label
                setCurrentLabel(LABEL_DATE_RANGE_PICKER);
                await AbcNavigator.showRightSheet(
                    <AbcSafeArea airTestKey={"dateRangePanel"} statusBarColor={Colors.transparent} style={{ height: _screenHeight }}>
                        <AbcView
                            airTestKey={"ignore-node"}
                            style={{ flexGrow: 1 }}
                            onClick={() => {
                                AbcNavigator.pop();
                                setCurrentLabel(prevLabel);
                            }}
                        >
                            <View
                                style={{
                                    height: _screenHeight - topOffset - (Utils.isiPhoneX() ? 32 : 0),
                                    top: topOffset,
                                }}
                                onClick={() => ""}
                            >
                                <AbcDivider style={{ color: Colors.border_color_light }}></AbcDivider>
                                <AbcDateRangePanel
                                    beginYear={beginYear}
                                    endYear={endYear}
                                    date={[rangeDate.start ? new Date(rangeDate.start) : "", rangeDate.end ? new Date(rangeDate.end) : ""]}
                                    describeList={describeList}
                                    pickerOptions={pickerOptions}
                                    initToast={initToast}
                                    changeDateToast={changeDateToast}
                                    displayDateRange={displayDateRange}
                                    onConfirm={onConfirm}
                                    onCancel={onCancel}
                                ></AbcDateRangePanel>
                            </View>
                        </AbcView>
                    </AbcSafeArea>
                );
                return;
            }
            setCurrentLabel(label);
            const now = new Date();
            let start: string = parseTime(now, "y-m-d", true) || "";
            let end: string = parseTime(now, "y-m-d", true) || "";
            const option = options.find((opt) => opt.label === label);
            if (option && typeof option.getValue === "function") {
                const optionValue = option.getValue();
                if (Array.isArray(optionValue)) {
                    const [rangeStart, rangeEnd] = optionValue;
                    start = parseTime(rangeStart, "y-m-d", true) || "";
                    end = parseTime(rangeEnd, "y-m-d", true) || "";
                    setRangeDate({
                        start: "",
                        end: "",
                    });
                    onChange?.({
                        label,
                        date: [start, end],
                    });
                    return;
                }
                const date = parseTime(optionValue, "y-m-d", true) || "";
                setRangeDate({
                    start: "",
                    end: "",
                });
                onChange?.({
                    label,
                    date,
                });
            }
        } catch (e) {
            console.log("e------", e);
            getLog()?.d("err:" + e);
        } finally {
            processingRef.current = false;
        }
    }
    function onConfirm({ dateRange }: { dateRange: Date[] }) {
        const start = dateRange[0] ? parseTime(dateRange[0], "y-m-d", true) || "" : "";
        const end = dateRange[1] ? parseTime(dateRange[1], "y-m-d", true) || "" : "";
        setRangeDate({
            start,
            end,
        });
        onChange?.({
            date: [start, end],
            label: LABEL_DATE_RANGE_PICKER,
        });
        AbcNavigator.pop();
    }

    function onCancel() {
        AbcNavigator.pop();
        setCurrentLabel(prevLabel); // 恢复到上一个选中的 label
    }
    return (
        <View style={{ flex: 1, padding: Sizes.dp16, ...shortcutStyle }} collapsable={false}>
            <AbcRadioGroup2 gap={Sizes.dp8} value={selectedRadioLabel} direction={"row"} onChange={handleClick}>
                {optionsList.map((option) => (
                    <AbcRadioButton
                        key={option.label}
                        title={option.name}
                        label={option.label}
                        width={Sizes.dp79}
                        height={Sizes.dp34}
                        style={{ ...customStyle }}
                    />
                ))}
                {/* onClick={()=>{}} 不能删，否则在android上调用UiUtils.measureInWindow获取不到node */}
                {enableDatePicker && (
                    <View ref={customRadioBtn} collapsable={false} onClick={() => ({})}>
                        <AbcRadioButton
                            key={customOptionItem.label}
                            title={customOptionItem.name}
                            label={customOptionItem.label}
                            width={Sizes.dp79}
                            height={Sizes.dp34}
                            style={{ ...Sizes.paddingLTRB(Sizes.dp8, !isSelectedCustomOptionItem ? Sizes.dp6 : 0), ...customStyle }}
                        >
                            {isSelectedCustomOptionItem && rangeDateStart && rangeDateEnd ? (
                                <AbcFlex vertical={true}>
                                    <Text style={TextStyles.t11NT2.copyWith({ color: Colors.B7 })}>{rangeDateStart}</Text>
                                    <Text style={TextStyles.t11NT2.copyWith({ color: Colors.B7 })}>{rangeDateEnd}</Text>
                                </AbcFlex>
                            ) : (
                                <Text
                                    style={TextStyles.t14NT2.copyWith({
                                        color: selectedRadioLabel === customOptionItem.label ? Colors.B7 : Colors.T2,
                                    })}
                                >
                                    {customOptionItem.name}
                                </Text>
                            )}
                        </AbcRadioButton>
                    </View>
                )}
            </AbcRadioGroup2>
        </View>
    );
};
