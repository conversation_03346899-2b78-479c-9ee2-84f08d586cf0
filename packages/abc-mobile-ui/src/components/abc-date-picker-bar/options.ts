import { getLastNDaysDate, getMonthStartDate, getWeekStartDate, getYearStartDate, nextDate, prevDate } from "@app/utils";

export const DatePickerBarOptions = Object.freeze({
    YESTERDAY: {
        label: "yesterday",
        name: "昨天",
        getValue() {
            return prevDate(new Date());
        },
    },
    DAY: {
        label: "day",
        name: "今天",
        getValue() {
            return new Date();
        },
    },
    TOMORROW: {
        label: "tomorrow",
        name: "明天",
        getValue() {
            return nextDate(new Date());
        },
    },
    AFTER_TOMORROW: {
        label: "after_tomorrow",
        name: "后天",
        getValue() {
            return nextDate(new Date(), 2);
        },
    },
    LAST_3_DAYS: {
        label: "last_3_days",
        name: "最近3天",
        getValue() {
            const end = new Date();
            const start = getLastNDaysDate(3, end);
            return [start, end];
        },
    },
    WEEK: {
        label: "week",
        name: "本周",
        getValue() {
            const end = new Date();
            const start = getWeekStartDate();
            return [start, end];
        },
    },

    MONTH: {
        label: "month",
        name: "本月",
        getValue() {
            const end = new Date();
            const start = getMonthStartDate();
            return [start, end];
        },
    },

    YEAR: {
        label: "year",
        name: "今年",
        getValue() {
            const end = new Date();
            const start = getYearStartDate();
            return [start, end];
        },
    },

    LATEST_WEEK: {
        label: "latest_week",
        name: "近一周",
        getValue() {
            const end = new Date();
            const start = getLastNDaysDate(7, end);
            return [start, end];
        },
    },

    LATEST_MONTH: {
        label: "latest_month",
        name: "近一月",
        getValue() {
            const end = new Date();
            const start = getLastNDaysDate(30, end);
            return [start, end];
        },
    },

    LATEST_THREE_MONTH: {
        label: "latest_three_month",
        name: "近三月",
        getValue() {
            const end = new Date();
            const start = getLastNDaysDate(90, end);
            return [start, end];
        },
    },

    LATEST_HALF_YEAR: {
        label: "latest_half_year",
        name: "近半年",
        getValue() {
            const end = new Date();
            const start = getLastNDaysDate(180, end);
            return [start, end];
        },
    },

    MONTHLY: {
        label: "monthly",
        name: "月趋势",
        getValue() {
            const end = new Date();
            const start = getLastNDaysDate(360, end);
            return [start, end];
        },
    },
});
