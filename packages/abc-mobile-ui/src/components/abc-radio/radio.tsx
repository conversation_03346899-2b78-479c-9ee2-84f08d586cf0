import React from "react";
import { Color, Colors, flattenStyles, Sizes, TextStyle } from "@app/theme";
import { Style, Text, View } from "@hippy/react";
import { AbcSizedBox } from "../abc-size-box/index";
import { Margin, Padding } from "@app/utils";
import _ from "lodash";
import { AbcAssetImage } from "../abc-asset-image/index";
import { DeviceUtils } from "@app/utils";

interface RadioBoxProps {
    text: string;
    check: boolean;
    enable: boolean;
    onChanged?: (check: boolean) => void;
    activeColor?: Color;
    size?: number; //default 16
    style?: Style;
    textStyle?: TextStyle;
    padding?: Padding;
    shouldInverseCheck?: boolean;
    disabledBgColor?: string; //不可点击时背景颜色
}

export class AbcRadioBox extends React.Component<RadioBoxProps> {
    static defaultProps = {
        size: 16,
    };

    _checked = false;

    constructor(props: RadioBoxProps) {
        super(props);
        this._syncProps(this.props);
    }

    // eslint-disable-next-line react/no-deprecated
    public componentWillReceiveProps(nextProps: Readonly<RadioBoxProps> /*, nextContext: any*/): void {
        this._syncProps(nextProps);
    }

    private _syncProps(props: RadioBoxProps): void {
        this._checked = props.check;
    }

    public render(): JSX.Element {
        const { padding, enable, text, textStyle, style, disabledBgColor } = this.props;
        return (
            <View style={[padding ?? {}, { flexDirection: "row", alignItems: "center" }, style ?? {}]} onClick={() => this._onClick()}>
                <View
                    style={{
                        borderRadius: Sizes.dp8,
                        borderColor: Colors.P1,
                        width: Sizes.dp16,
                        height: Sizes.dp16,
                        borderWidth: 1,
                        justifyContent: "center",
                        alignItems: "center",
                        backgroundColor: enable ? undefined : disabledBgColor,
                    }}
                >
                    {this._checked && (
                        <View
                            style={{
                                width: Sizes.dp11,
                                height: Sizes.dp11,
                                backgroundColor: enable ? Colors.mainColor : Colors.T4,
                                borderRadius: Sizes.dp5,
                            }}
                        />
                    )}
                </View>

                {text && (
                    <Text
                        style={[
                            enable ? { color: Colors.T1 } : { color: Colors.T2 },
                            textStyle ?? {},
                            { marginLeft: Sizes.dp9, flexShrink: 1 },
                            DeviceUtils.isAndroid() ? { lineHeight: Sizes.dp18 } : {},
                        ]}
                    >
                        {text}
                    </Text>
                )}
            </View>
        );
    }

    private _onClick() {
        const { enable, shouldInverseCheck } = this.props;
        if (!enable || (!shouldInverseCheck && this._checked)) return;

        this._checked = !this._checked;
        this.setState({});
        this.props.onChanged?.(this._checked);
    }
}

// FIXME 这里只替换了原来的选中图标 是否需要作为通用组件图标（自行传入）有待商酌
export class TickSelectedBox extends React.Component<RadioBoxProps> {
    static defaultProps = {
        size: 16,
    };

    _checked = false;

    constructor(props: RadioBoxProps) {
        super(props);
        this._syncProps(this.props);
    }

    // eslint-disable-next-line react/no-deprecated
    public componentWillReceiveProps(nextProps: Readonly<RadioBoxProps> /*, nextContext: any*/): void {
        this._syncProps(nextProps);
    }

    private _syncProps(props: RadioBoxProps): void {
        this._checked = props.check;
    }

    public render(): JSX.Element {
        const { padding, enable, text, textStyle, style } = this.props;
        return (
            <View style={[padding ?? {}, { flexDirection: "row", alignItems: "center" }, style ?? {}]} onClick={() => this._onClick()}>
                <View
                    style={{
                        borderRadius: Sizes.dp8,
                        borderColor: Colors.P1,
                        width: Sizes.dp16,
                        height: Sizes.dp16,
                        borderWidth: 1,
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                >
                    {this._checked &&
                        (enable ? (
                            <AbcAssetImage name={"selected"} style={{ width: Sizes.dp16, height: Sizes.dp16 }} />
                        ) : (
                            <View
                                style={{
                                    width: Sizes.dp11,
                                    height: Sizes.dp11,
                                    backgroundColor: Colors.T4,
                                    borderRadius: Sizes.dp5,
                                }}
                            />
                        ))}
                </View>

                {text && (
                    <Text style={[enable ? { color: Colors.T1 } : { color: Colors.T2 }, textStyle ?? {}, { paddingLeft: Sizes.dp9 }]}>
                        {text}
                    </Text>
                )}
            </View>
        );
    }

    private _onClick() {
        const { enable, shouldInverseCheck } = this.props;
        if (!enable || (!shouldInverseCheck && this._checked)) return;

        this._checked = !this._checked;
        this.setState({});
        this.props.onChanged?.(this._checked);
    }
}

interface RadioButtonGroupProps {
    labels: string[];
    picked: string;
    disabled?: string[];
    onChanged?: (label: string, index: number) => void;
    groupStyle?: Style | Style[];
    labelStyle?: TextStyle;
    activeColor?: Color;
    itemBuilder?: () => JSX.Element;
    padding?: Padding;
    margin?: Margin;
    marginBetweenItem?: number; // 横向间距

    shouldInverseCheck?: boolean; //支持反选
    upperLowerSpace?: number; // 纵向间距
}

export class AbcRadioButtonGroup extends React.Component<RadioButtonGroupProps> {
    _selected = "";

    constructor(props: RadioButtonGroupProps) {
        super(props);
        this._syncProps(props);
    }

    // eslint-disable-next-line react/no-deprecated
    public componentWillReceiveProps(nextProps: Readonly<RadioButtonGroupProps> /*, nextContext: any*/): void {
        this._syncProps(nextProps);
    }

    private _syncProps(props: RadioButtonGroupProps): void {
        this._selected = props.picked ?? "";
    }

    render(): JSX.Element {
        const { labels, disabled, labelStyle, groupStyle, shouldInverseCheck, upperLowerSpace } = this.props;
        let { marginBetweenItem } = this.props;
        marginBetweenItem = marginBetweenItem ?? 0;

        const optionViews: JSX.Element[] = [];
        labels.forEach((item, index) => {
            const option = labels[index];
            optionViews.push(
                // <RadioBox
                //     key={item}
                //     text={item}
                //     check={this._selected == option}
                //     textStyle={labelStyle}
                //     enable={!((disabled?.indexOf(option) ?? -1) >= 0)}
                //     shouldInverseCheck={shouldInverseCheck}
                //     onChanged={(select) => this._onChanged(item, index, select)}
                // />
                <TickSelectedBox
                    key={item}
                    text={item}
                    check={this._selected == option}
                    textStyle={labelStyle}
                    enable={!((disabled?.indexOf(option) ?? -1) >= 0)}
                    shouldInverseCheck={shouldInverseCheck}
                    onChanged={(select) => this._onChanged(item, index, select)}
                    disabledBgColor={!_.isEmpty(disabled) ? "rgba(0,0,0,0.04)" : undefined}
                />
            );

            if (index != labels.length - 1 && marginBetweenItem! > 0) {
                optionViews.push(<AbcSizedBox key={"space_" + item} width={marginBetweenItem!} />);
            }
            if (index != labels.length - 1 && upperLowerSpace! > 0) {
                optionViews.push(<AbcSizedBox key={"column_" + index} height={Sizes.dp30} />);
            }
        });

        return <View style={{ flexDirection: "row", ...flattenStyles(groupStyle) }}>{optionViews}</View>;
    }

    private _onChanged(item: string, index: number, last: boolean) {
        console.log(last);
        this._selected = item;
        this.setState({});

        this.props.onChanged?.(this._selected, index);
    }
}
