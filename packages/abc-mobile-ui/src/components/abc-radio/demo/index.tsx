import React from "react";
import { View } from "@hippy/react";
import { AbcRadioBox, AbcRadioButtonGroup } from "../index";
import { Sizes, Colors } from "@app/theme";
import { AbcText } from "../../abc-text/index";

export function AbcRadioBoxDemo() {
    return (
        <View>
            <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>Radio基础用法</AbcText>
            <AbcRadioBox text={""} enable={true} check={true} />
        </View>
    );
}

export function AbcRadioButtonGroupDemo() {
    return (
        <View>
            <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>RadioButtonGroup基础用法</AbcText>
            <AbcRadioButtonGroup labels={["是", "否"]} picked={"否"} marginBetweenItem={Sizes.dp24} />
        </View>
    );
}
