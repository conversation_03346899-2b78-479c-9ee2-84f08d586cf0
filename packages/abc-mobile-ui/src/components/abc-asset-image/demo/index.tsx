import React from "react";
import { View } from "@hippy/react";
import { AbcAssetImage } from "../index";
import { Sizes, Colors } from "@app/theme";
import { AbcText } from "../../abc-text/index";

export function AbcAssetImageDemo() {
    return (
        <View>
            <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>基础用法</AbcText>
            <AbcAssetImage
                name={"home_navi_guahao"}
                ignoreTheme={false}
                style={{ width: Sizes.dp24, height: Sizes.dp24, marginBottom: Sizes.dp16 }}
            />
            <AbcAssetImage name={"self_user_bg"} ignoreTheme={false} style={{ flex: 1, height: Sizes.dp160 }} />
        </View>
    );
}
