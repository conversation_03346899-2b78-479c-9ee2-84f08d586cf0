import { Text, View } from "@hippy/react";
import { ABCStyleSheet, Colors, Sizes, TextStyles } from "@app/theme";
import { pxToDp } from "@app/utils";
import React from "react";
import _ from "lodash";
import { AbcBaseComponent } from "../abc-base-component";
import { AbcTextInput } from "../abc-text-input";
import { AbcGrid } from "../abc-grid";
import { AbcButton } from "../abc-button";
import { AbcIconfont } from "../abc-iconfont";
import { AbcSizedBox } from "../abc-size-box";
import { kKeyboardHeight } from "./constants";

interface NumberKeyboardProps {
    textInput: AbcTextInput;
    selectUnit?: string;
    unitList?: string[];
    onChangeUnit?: (unit: string) => void;
}

const styles = ABCStyleSheet.create({
    gridItemContainer: {
        height: pxToDp(44),
        backgroundColor: Colors.white,
        justifyContent: "center",
        alignItems: "center",
    },
    defaultUnit: {
        padding: 0,
        flex: 1,
        height: pxToDp(44),
        borderWidth: 0.5,
        borderColor: Colors.white,
        borderRadius: pxToDp(3),
        backgroundColor: Colors.white,
    },
    selectUnit: {
        borderColor: Colors.border_color_theme1,
    },
});

export class AbcNumberKeyboard extends AbcBaseComponent<NumberKeyboardProps> {
    private readonly _gridItems: JSX.Element[];

    constructor(props: NumberKeyboardProps) {
        super(props);

        const characters = _.range(1, 10).map((value) => value.toString());
        characters.push(".");
        characters.push("0");

        this._gridItems = characters.map((item) => this._renderNumberItem(item));

        this._gridItems.push(this._renderDeleteItem());
    }

    public render(): JSX.Element {
        const { textInput } = this.props;
        return (
            <View
                accessibilityLabel={"数字键盘"}
                style={{
                    height: kKeyboardHeight,
                    backgroundColor: Colors.keyboardBg,
                    flexDirection: "row",
                    padding: pxToDp(6),
                }}
            >
                <AbcGrid
                    itemHeight={pxToDp(44)}
                    crossAxisCount={3}
                    crossAxisSpacing={pxToDp(6)}
                    mainAxisSpacing={pxToDp(6)}
                    style={{ flex: 1 }}
                    rowStyle={{ alignItems: "stretch" }}
                >
                    {this._gridItems}
                </AbcGrid>
                <View style={{ width: pxToDp(81), marginLeft: pxToDp(6) }}>
                    {this._renderUnitList()}
                    <AbcButton
                        text={"确认"}
                        style={{ flex: 1 }}
                        textStyle={TextStyles.t18MW}
                        showShadow={true}
                        onClick={() => {
                            textInput.finishEditing();
                        }}
                    />
                </View>
            </View>
        );
    }

    private _renderNumberItem(number: string): JSX.Element {
        const { textInput } = this.props;
        return (
            <AbcButton
                style={styles.gridItemContainer}
                pressColor={Colors.buttonPressed}
                key={number}
                continuousClick={true}
                showShadow={true}
                onClick={() => textInput.insertText(number.toString())}
            >
                <Text style={TextStyles.t22NT1.copyWith({ lineHeight: Sizes.dp32 })}>{number}</Text>
            </AbcButton>
        );
    }

    private _renderDeleteItem(): JSX.Element {
        const { textInput } = this.props;
        return (
            <AbcButton
                style={styles.gridItemContainer}
                pressColor={Colors.buttonPressed}
                key={"Delete"}
                continuousClick={true}
                showShadow={true}
                onClick={() => textInput.deleteCurrentChar()}
            >
                <AbcIconfont name={"delete"} size={pxToDp(20)} color={Colors.t2} />
            </AbcButton>
        );
    }

    private _renderUnitItem(unit: string, select: boolean): JSX.Element {
        const { onChangeUnit } = this.props;
        const _style = { ...styles.defaultUnit };
        if (select) Object.assign(_style, styles.selectUnit);
        return (
            <AbcButton
                key={unit}
                text={unit}
                style={_style}
                textStyle={select ? TextStyles.t16NM : TextStyles.t16NT4}
                pressColor={Colors.buttonPressed}
                continuousClick={true}
                showShadow={true}
                onClick={() => {
                    if (select) return;
                    onChangeUnit?.(unit);
                }}
            />
        );
    }

    private _renderUnitList(): JSX.Element {
        const { unitList, selectUnit } = this.props;
        if (unitList && unitList?.length > 4) return <View />;
        const _rowFirst: JSX.Element[] = [];
        const _rowSecond: JSX.Element[] = [];
        unitList?.forEach((item, index, self) => {
            if (index < 2) {
                _rowFirst.push(this._renderUnitItem(item, selectUnit == item || item == selectUnit?.replace("/次", "")));
                if (index < self.length - 1) _rowFirst.push(<AbcSizedBox key={index.toString()} width={pxToDp(6)} />);
            } else {
                _rowSecond.push(this._renderUnitItem(item, selectUnit == item || item == selectUnit?.replace("/次", "")));
                if (index < self.length - 1) _rowSecond.push(<AbcSizedBox key={index.toString()} width={pxToDp(6)} />);
            }
        });
        return (
            <View>
                <View style={{ flexDirection: "row" }}>{_rowFirst}</View>
                {_rowFirst.length ? <AbcSizedBox height={pxToDp(6)} /> : <View />}
                <View style={{ flexDirection: "row" }}>{_rowSecond}</View>
                {_rowSecond.length ? <AbcSizedBox height={pxToDp(6)} /> : <View />}
            </View>
        );
    }
}
