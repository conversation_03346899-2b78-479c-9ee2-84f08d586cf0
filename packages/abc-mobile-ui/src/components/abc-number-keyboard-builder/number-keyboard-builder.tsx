import { AbcTextInput, KeyboardBuilder } from "../abc-text-input";
import React from "react";
import { AbcNumberKeyboard } from "./number-keyboard";
import { kKeyboardHeight } from "./constants";

export class AbcNumberKeyboardBuilder implements KeyboardBuilder {
    selectUnit?: string;
    unitList?: string[];
    onChangeUnit?: (unit: string) => void;
    startUnitList?: string;

    constructor(options?: { selectUnit?: string; unitList?: string[]; onChangeUnit?: (unit: string) => void; startUnitList?: string }) {
        const { selectUnit, unitList, onChangeUnit, startUnitList } = options ?? {};
        this.selectUnit = selectUnit;
        this.unitList = unitList;
        this.startUnitList = startUnitList;
        this.onChangeUnit = onChangeUnit;
    }

    build(textInput: AbcTextInput): JSX.Element {
        return (
            <AbcNumberKeyboard
                textInput={textInput}
                selectUnit={this.selectUnit}
                unitList={this.unitList}
                onChangeUnit={this.onChangeUnit}
            />
        );
    }

    keyboardHeight(): number {
        return kKeyboardHeight;
    }
}
