/**
 * create by deng<PERSON>e
 * desc:
 * create date 2020/9/1
 */
import React from "react";
import { AbcBaseComponent } from "../abc-base-component";
import { Animation, View } from "@hippy/react";
import { Color, Colors, Sizes } from "@app/theme";
import { AbcView } from "../abc-view";

interface AbcSwitchProps {
    checked?: boolean;
    disable?: boolean;
    onChange?(arg1: boolean): void;
    width?: number;
    height?: number;
    closeColor?: Color; // 关闭颜色
}

export class AbcSwitch extends AbcBaseComponent<AbcSwitchProps> {
    animation: Animation;
    switchHeight: number;
    switchWidth: number;

    get dotSize(): number {
        return this.switchHeight - Sizes.dp4;
    }

    private _switchStatus = false;
    set switchStatus(value: boolean) {
        if (this._switchStatus == value) return;
        const _switchStatus = this._switchStatus;
        if (_switchStatus) {
            this.animation.updateAnimation({
                startValue: this.switchWidth - Sizes.dp2 - this.dotSize,
                toValue: Sizes.dp2,
                duration: 100,
            });
        } else {
            this.animation.updateAnimation({
                startValue: Sizes.dp2,
                toValue: this.switchWidth - Sizes.dp2 - this.dotSize,
                duration: 100,
            });
        }
        this.animation.start();
        this._switchStatus = value;
    }
    get switchStatus(): boolean {
        return this._switchStatus;
    }

    constructor(props: AbcSwitchProps) {
        super(props);
        this.switchHeight = props.height ?? Sizes.dp20;
        this.switchWidth = props.width ?? Sizes.dp36;
        this.animation = new Animation({
            startValue: Sizes.dp2,
            toValue: this.switchWidth - Sizes.dp2 - this.dotSize,
            duration: 100,
            delay: 0, // 至动画真正开始的延迟时间
            mode: "timing", // 动画模式
            timingFunction: "linear", // 动画缓动函数
        });
        this.switchStatus = props.checked ?? false;
    }

    componentWillReceiveProps(nextProps: Readonly<AbcSwitchProps> /*nextContext: any*/): void {
        this.switchStatus = nextProps.checked ?? false;
    }

    render(): JSX.Element {
        const { closeColor, disable } = this.props;
        return (
            <AbcView onClick={disable ? undefined : this._changeSwitchStatus.bind(this)} airTestKey={"abc_switch_key"}>
                <View
                    style={[
                        {
                            height: this.switchHeight,
                            width: this.switchWidth,
                            borderRadius: this.switchHeight,
                        },
                        this.switchStatus ? { backgroundColor: Colors.mainColor } : { backgroundColor: closeColor ?? Colors.T2 },
                    ]}
                >
                    {disable && (
                        <View
                            style={{
                                height: this.switchHeight,
                                width: this.switchWidth,
                                borderRadius: this.switchHeight,
                                backgroundColor: Colors.S2Mask40,
                                position: "absolute",
                                zIndex: -100,
                            }}
                        />
                    )}
                    <View
                        style={[
                            {
                                height: this.dotSize,
                                width: this.dotSize,
                                backgroundColor: Colors.white,
                                borderRadius: this.dotSize,
                                top: Sizes.dp2,
                                transform: [
                                    {
                                        translateX: this.animation,
                                    },
                                ],
                            },
                        ]}
                    />
                </View>
            </AbcView>
        );
    }
    componentWillUnmount(): void {
        this.animation.destory();
    }

    _changeSwitchStatus(): void {
        this.switchStatus = !this.switchStatus;
        this.setState({});
        this.props.onChange?.(this.switchStatus);
    }
}
