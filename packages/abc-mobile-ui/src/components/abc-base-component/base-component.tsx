import * as React from "react";
import { DisposableTracker, applyMixins } from "@app/utils";
import { getLanguageObserver, getErrorHandler } from "../../index";

type AnyType = any;

export class AbcBaseComponent<P = {}, S = {}, SS = any> extends React.Component<P, S, SS> {
    constructor(props: P) {
        super(props);
        getLanguageObserver()
            ?.subscribe(() => {
                this.setState({});
            })
            ?.addToDisposableBag(this);
    }

    componentDidMount(): void {
        return;
    }

    componentWillUnmount(): void {
        if (super.componentWillUnmount) super.componentWillUnmount();

        this.closeDisposables();
    }

    componentDidCatch(error: AnyType, errorInfo: AnyType): void {
        const getComponentErrorHandler = getErrorHandler();
        const _errorHandler = getComponentErrorHandler?.();
        if (_errorHandler) {
            _errorHandler(error, errorInfo);
        }
    }
}

export interface AbcBaseComponent extends DisposableTracker {}

applyMixins(AbcBaseComponent, [DisposableTracker]);
