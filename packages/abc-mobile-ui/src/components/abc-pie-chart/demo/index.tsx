import React from "react";
import { View, Text } from "@hippy/react";
import { Ab<PERSON><PERSON><PERSON><PERSON><PERSON>, AbcPyramid<PERSON>hart, PieChartDataItem } from "../index";
import { pxToDp } from "@app/utils";
import { TextStyles, DINAlternate, Colors, Sizes } from "@app/theme";
import { AbcText } from "../../abc-text";

const DINAlternateFontFamily = { fontFamily: DINAlternate };
export function AbcPieChartDemo() {
    const dataList: PieChartDataItem[] = [
        {
            id: "100001",
            label: "治疗",
            value: 400,
            color: Colors.G1,
        },
        {
            id: "100002",
            label: "挂号费",
            value: 200,
            color: Colors.Y1,
        },
        {
            id: "100003",
            label: "中成药",
            value: 300,
            color: Colors.B1,
        },
    ];

    return (
        <View>
            <View>
                <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>Abc<PERSON><PERSON><PERSON>hart 基础用法 </AbcText>
                <Abc<PERSON>ie<PERSON><PERSON>
                    style={{ height: pxToDp(126), width: pxToDp(126) }}
                    data={dataList}
                    showLegend={true}
                    centerViewBuilder={() => (
                        <View>
                            <Text style={[TextStyles.t18BB, DINAlternateFontFamily, { textAlign: "center" }]}>{200}</Text>
                            <Text style={TextStyles.t11NT6}>零售人次</Text>
                        </View>
                    )}
                />
            </View>

            <View style={{ marginTop: Sizes.dp20 }}>
                <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>AbcPyramidChart 基础用法</AbcText>
                <AbcPyramidChart style={{ flex: 1, height: pxToDp(168) }} data={dataList} showLegend={true} />
            </View>
        </View>
    );
}
