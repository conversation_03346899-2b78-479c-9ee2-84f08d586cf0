import React from "react";
import { colorParse, Style, View, Text, ScrollView } from "@hippy/react";
import { ABCStyles, Color, flattenStyles, Sizes, TextStyles, DINAlternate } from "@app/theme";
import _ from "lodash";
import { UniqueKey, ignore, pxToDp } from "@app/utils";
import { AbcBaseComponent } from "../abc-base-component";
import { AbcAssetImage } from "../abc-asset-image";

const DINAlternateFontFamily = { fontFamily: DINAlternate };

export interface PieChartDataItem {
    id?: string;
    label?: string;
    value: number;
    color: Color;
}

interface PieChartViewProps {
    style: Style | Style[];

    showLegend?: boolean;
    legendBuilder?: () => JSX.Element;

    /**
     * 圆环中心内容相关
     * total 总数
     * title 文案
     * centerViewBuilder 自定义中心内容
     */
    total?: number;
    title?: string;
    centerViewBuilder?: () => JSX.Element;

    //终端使用
    data: PieChartDataItem[];
    /**
     * 同心圆（内）的比例
     * @description 0 - 1
     * @description 0 : 实心圆
     * @description 1 : 无显示
     */
    pieChartPercent?: number;

    /**
     * @description 扇形区域是否显示数值
     * @type boolean
     * @default false
     */
    drawValuesEnabled?: boolean;

    /**
     * @description 扇形区域是否显示标题
     * @type boolean
     * @default false
     */
    drawEntryLabelsEnabled?: boolean;

    /**
     * @description 扇形区域是否可旋转
     * @type boolean
     * @default false
     */
    rotationEnabled?: boolean;

    /**
     * @description 扇区可点击
     * @type boolean
     * @default false
     */
    highlightPerTapEnabled?: boolean;

    sliceSpace?: number;

    /**
     * @description 扇区可点击
     * @type function
     * @param arg1
     */
    onSelectChanged?(arg1: PieChartDataItem): void;
}

export class AbcPieChart extends React.Component<PieChartViewProps, {}> {
    private instance: HTMLDivElement | null = null;
    private _key = UniqueKey();
    private _scrollView: ScrollView | null | undefined;

    get showCenterView(): boolean {
        const { total, title, centerViewBuilder } = this.props;
        return !_.isEmpty(total) || !_.isEmpty(title) || !!centerViewBuilder;
    }

    constructor(props: PieChartViewProps) {
        super(props);
    }

    UNSAFE_componentWillReceiveProps(nextProps: Readonly<PieChartViewProps>): void {
        if (_.isEqual(this.props.data, nextProps.data)) {
            this._key = UniqueKey();
        }
        //解决更新数据后，滚动无法回弹问题
        this._scrollView?.scrollTo(0, 0, false);
    }

    private _transformData(data: PieChartDataItem[]) {
        data.forEach((data) => {
            if (data.color != undefined) (data as any).color = colorParse(data.color);

            //防止小数影响显示 + 对于小于 0 的值当 0 处理
            data.value = Math.max((data.value ?? 0) * 1000, 0);
        });
    }

    // 饼图
    protected _renderPieChartView(): JSX.Element {
        const { data, style, centerViewBuilder, ...nativeProps } = this.props;
        let total = 0;
        data.forEach((item) => (total += item.value));
        if (total == 0) {
            return (
                <View
                    style={[
                        {
                            height: pxToDp(126),
                            width: pxToDp(126),
                        },
                    ]}
                >
                    <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
                        {centerViewBuilder ? (
                            centerViewBuilder()
                        ) : (
                            <View>
                                <Text
                                    style={[
                                        TextStyles.t11NT4,
                                        {
                                            textAlign: "center",
                                            lineHeight: Sizes.dp126,
                                        },
                                    ]}
                                >
                                    暂无数据
                                </Text>
                            </View>
                        )}
                    </View>
                    <AbcAssetImage
                        name={"outpatient_empty_pie_chart"}
                        style={[ABCStyles.absoluteFill, { height: pxToDp(126), width: pxToDp(126) }]}
                    />
                </View>
            );
        } else {
            const _data = _.cloneDeep(data);
            ignore(style);
            this._transformData(_data);
            return (
                <View
                    style={[
                        {
                            height: pxToDp(126),
                            width: pxToDp(126),
                        },
                    ]}
                >
                    <div
                        key={this._key}
                        // @ts-ignore
                        style={{ flex: 1 }}
                        // @ts-ignore
                        nativeName="PieChartView"
                        ref={(ref: HTMLDivElement) => {
                            this.instance = ref;
                        }}
                        data={_data}
                        {...nativeProps}
                    />
                </View>
            );
        }
    }

    protected _renderCenterView(): JSX.Element {
        const { centerViewBuilder, title, data } = this.props;
        let total = 0;
        data.forEach((item) => (total += item.value));
        if (!this.showCenterView || total == 0) return <View />;
        if (centerViewBuilder)
            return <View style={[ABCStyles.absoluteFill, ABCStyles.centerChild, { zIndex: 2 }]}>{centerViewBuilder()}</View>;

        return (
            <View style={[ABCStyles.absoluteFill, ABCStyles.centerChild]}>
                <Text style={[TextStyles.t18BB, DINAlternateFontFamily, { textAlign: "center" }]}>{`${total}`}</Text>
                <Text style={[TextStyles.t11NT4]}>{`${title}`}</Text>
            </View>
        );
    }

    protected _renderLegendBuilder(): JSX.Element {
        const { showLegend, data } = this.props;
        if (!showLegend) return <View />;
        let total = 0;
        data.forEach((item) => (total += item.value));
        return (
            <ScrollView
                ref={(ref) => (this._scrollView = ref)}
                style={{ marginLeft: Sizes.dp39, flex: 1 }}
                contentContainerStyle={{ paddingVertical: Sizes.dp16 }}
                showsVerticalScrollIndicator={false}
            >
                {data.map((item, index, self) => (
                    <_LegendItemView
                        key={index}
                        title={item.value}
                        color={item.color}
                        subTitle={item.label}
                        percent={Math.round((total ? item.value / total : 0) * 100)}
                        hideBottom={index == self.length - 1}
                    />
                ))}
            </ScrollView>
        );
    }

    public render(): JSX.Element {
        const { style } = this.props;
        const height = flattenStyles(style)?.height ?? pxToDp(146);
        return (
            <View style={[{ flexDirection: "row", height: height }]}>
                <View style={[flattenStyles(style)]}>
                    {this._renderCenterView()}
                    {this._renderPieChartView()}
                </View>
                <View style={{ width: pxToDp(160) }}>{this._renderLegendBuilder()}</View>
            </View>
        );
    }
}

interface _LegendItemViewProps {
    title?: string | number;
    subTitle?: string;
    percent?: number;
    color?: Color;
    hideBottom?: boolean;
}

class _LegendItemView extends AbcBaseComponent<_LegendItemViewProps> {
    render(): JSX.Element {
        const { color, title, subTitle, percent, hideBottom } = this.props;
        return (
            <View style={{ marginBottom: hideBottom ? 0 : Sizes.dp24 }}>
                <View style={[ABCStyles.rowAlignCenter]}>
                    <View
                        style={{
                            backgroundColor: color,
                            width: Sizes.dp10,
                            height: Sizes.dp10,
                            marginRight: Sizes.dp8,
                            borderRadius: Sizes.dp2,
                        }}
                    />
                    <View style={[ABCStyles.rowAlignCenter]}>
                        <Text style={[TextStyles.t18BB, DINAlternateFontFamily]}>{`${title} `}</Text>
                        <Text style={[TextStyles.t14NT1.copyWith({ lineHeight: Sizes.dp18 })]}>{`(${percent}%)`}</Text>
                    </View>
                </View>
                <View style={{ marginLeft: Sizes.dp18 }}>
                    <Text style={[TextStyles.t11NT4]}>{`${subTitle}`}</Text>
                </View>
            </View>
        );
    }
}

export class AbcPyramidChart extends AbcPieChart {
    protected _renderLegendBuilder(): JSX.Element {
        const { showLegend, data } = this.props;
        if (!showLegend) return <View />;
        let total = 0;
        data.forEach((item) => (total += item.value));
        return (
            // 三角文本
            <View style={{ marginLeft: Sizes.dp43, flex: 1, marginTop: Sizes.dp16 }}>
                {data.map((item, index, self) => (
                    <_LegendItemView
                        key={index}
                        title={item.value}
                        color={item.color}
                        subTitle={item.label}
                        percent={Math.round((total ? item.value / total : 0) * 100)}
                        hideBottom={index == self.length - 1}
                    />
                ))}
            </View>
        );
    }

    _renderCenterView(): JSX.Element {
        return <View />;
    }

    // 金字塔图
    _renderPieChartView(): JSX.Element {
        const { data } = this.props;
        let total = 0;
        data.forEach((item) => (total += item.value));
        if (total == 0) {
            return (
                <View style={{ width: pxToDp(168), height: pxToDp(168) }}>
                    <View style={[ABCStyles.absoluteFill]}>
                        <AbcAssetImage name={"outpatient_empty_pyramid_chart"} style={{ height: pxToDp(168), width: pxToDp(168) }} />
                    </View>
                    {data.map((item, index) => (
                        <View key={index} style={[ABCStyles.centerChild, { paddingTop: index != 2 ? 0 : -Sizes.dp12 }]}>
                            <Text style={[TextStyles.t14NT6.copyWith({ fontSize: index != 2 ? Sizes.dp14 : Sizes.dp12 })]}>
                                {`${item.value ?? 0}%`}
                            </Text>
                        </View>
                    ))}
                </View>
            );
        } else {
            const views: JSX.Element[] = [];
            data.forEach((item, index) => {
                if (index != 2) {
                    views.push(
                        <View key={index} style={[ABCStyles.centerChild]}>
                            <Text style={[TextStyles.t14NW]}>{`${Math.round(((item.value ?? 0) / total) * 100)}%`}</Text>
                        </View>
                    );
                } else {
                    views.push(
                        <View key={index} style={[{ flex: 1, paddingTop: Sizes.dp10 }]}>
                            <Text style={[TextStyles.t12NM]}>{`${Math.round(((item.value ?? 0) / total) * 100)}%`}</Text>
                        </View>
                    );
                }
            });
            return (
                <View style={{ width: pxToDp(168), height: pxToDp(168) }}>
                    <View style={[ABCStyles.absoluteFill]}>
                        <AbcAssetImage name={"stat-funnel"} style={{ width: pxToDp(168), height: pxToDp(168) }} ignoreTheme={false} />
                    </View>
                    <View style={[{ alignItems: "center", flex: 1 }]}>{views}</View>
                </View>
            );
        }
    }
}
