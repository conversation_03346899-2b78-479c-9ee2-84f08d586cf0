import React from "react";
import { isNil } from "lodash";
import { View, Text, StyleSheet, Style } from "@hippy/react";
import { Colors, FontSizes, Sizes, ABCStyles, flattenStyles, TextStyles } from "@app/theme";
import { AbcTextInput, KeyboardType } from "../abc-text-input/text-input";
import { AbcIconfont } from "../abc-iconfont";
import { AbcView } from "../abc-view";

const SearchInputStyle = StyleSheet.create({
    container: {
        flex: 1,
        ...Sizes.paddingLTRB(Sizes.dp12, Sizes.dp6),
        flexDirection: "row",
        alignItems: "center",
        backgroundColor: Colors.white,
    },
    contentInput: {
        flex: 1,
        height: Sizes.dp36,
        fontSize: FontSizes.size14,
        underlineColorAndroid: Colors.P4,
    },
});

interface SearchBarProps {
    editable?: boolean;
    autoFocus?: boolean;
    value?: string;
    disable?: Boolean;
    borderRadius?: number;
    border?: number;
    placeholder?: string;
    placeholderTextColor?: string;
    keyboardType?: KeyboardType;
    inputStyle?: Style;
    enableDefaultToolBar?: boolean;
    onChange?: (value: string) => void;
    onFocus?: () => void;
    onBlur?: () => void;
    maxLength?: number;
    showScanIcon?: boolean; // 是否展示扫码Icon
    onClickScan?(): void; // 点击扫码
    boxStyle?: Style; // 最外层容器样式
    containerStyle?: Style; // 包含左右图标的输入框样式
    scanStyle?: Style;
    showCancelBtn?: boolean; // 是否显示取消按钮
    onCancel?: () => void; //取消事件
    cancelBtnStyle?: Style; // 取消按钮的样式
    showDeleteIcon?: boolean; // 删除按钮
    onClear?: () => void; //  删除事件
    hiddenScanWhenHasValue?: boolean; // 有值时取消扫一扫图标
    showSearchIcon?: boolean; // 是否显示搜索图标
}

interface SearchBarState {
    value: any;
    clearVisible: boolean;
}

export class AbcSearchBar extends React.Component<SearchBarProps, SearchBarState> {
    _textInput?: AbcTextInput | null;

    static defaultProps = {
        placeholder: "请输入搜索内容",
        disable: false,
        border: 0,
        borderRadius: Sizes.dp6,
        editable: true,
        enableDefaultToolBar: true,
        showSearchIcon: true,
    };

    constructor(props: SearchBarProps) {
        super(props);
        this.state = {
            value: props.value,
            clearVisible: false,
        };
    }

    UNSAFE_componentWillReceiveProps(nextProps: Readonly<SearchBarProps>): void {
        if (this.state.value != nextProps.value) {
            this.setState({ value: nextProps.value });
        }
        if (isNil(nextProps.value)) {
            this.setState({ value: "" });
        }
    }

    public setText(text: string): void {
        this._textInput?.setValue(text, { shouldChange: true });
    }

    public setTextNotUpdate(text: string): void {
        if (text) {
            this.setState({ clearVisible: true });
        } else {
            this.setState({ clearVisible: false });
        }
        this.setState({ value: text });
        this._textInput?.setValue(text, { shouldChange: false });
    }

    public handleTextChange(value: string): void {
        if (value) {
            this.setState({ clearVisible: true });
        } else {
            this.setState({ clearVisible: false });
        }
        this.setState({ value: value });
        this.props.onChange?.(value);
    }

    private _clearInputValue(): void {
        const { onClear } = this.props;
        this.setState({ value: "" });
        if (this._textInput) {
            this.handleTextChange("");
            this._textInput.clear();
        }
        onClear?.();
    }

    _renderInput(): JSX.Element {
        const { value } = this.state;
        const {
            placeholder,
            autoFocus,
            keyboardType,
            inputStyle,
            editable,
            enableDefaultToolBar,
            maxLength,
            placeholderTextColor
        } = this.props || {};

        return (
            <AbcTextInput
                maxLength={maxLength}
                editable={editable}
                autoFocus={autoFocus ?? false}
                multiline={false}
                placeholder={placeholder}
                placeholderTextColor={!!placeholderTextColor ? placeholderTextColor : Colors.T6}
                defaultValue={value}
                keyboardType={keyboardType}
                enableDefaultToolBar={enableDefaultToolBar}
                style={{ ...SearchInputStyle.contentInput, ...inputStyle }}
                ref={(ref) => {
                    this._textInput = ref!;
                }}
                onFocus={() => {
                    this.setState({ clearVisible: !!this.state.value });
                    this.props.onFocus?.();
                }}
                onBlur={() => {
                    this.setState({ clearVisible: false });
                    this.props.onBlur?.();
                }}
                onChangeText={this.handleTextChange.bind(this)}
            />
        );
    }

    _renderSearchIcon(): JSX.Element {
        const { showSearchIcon } = this.props;
        if (!showSearchIcon) return <View />;
        return (
            <View style={{ marginRight: Sizes.dp8 }}>
                <AbcIconfont
                    name="s-search-line"
                    size={Sizes.dp16}
                    color={Colors.retail_T3}
                />
            </View>
        );
    }

    _renderDeletedIcon(): JSX.Element | boolean {
        const { showDeleteIcon, editable, showScanIcon, hiddenScanWhenHasValue } = this.props;
        if (!showDeleteIcon || !editable) return <View />;

        return this.state.clearVisible && (
            <AbcView style={{ marginRight: showScanIcon && !hiddenScanWhenHasValue ? Sizes.dp12 : Sizes.dp0 }} onClick={this._clearInputValue.bind(this)}>
                <AbcIconfont
                    name={"s-close-fill"}
                    size={Sizes.dp20}
                    color={Colors.retail_T3}
                />
            </AbcView>
        )
    }

    _renderScanIcon(): JSX.Element {
        const { showScanIcon, hiddenScanWhenHasValue = false, scanStyle, onCancel, onClickScan } = this.props;
        if (!!showScanIcon) {
            if (hiddenScanWhenHasValue && !!this.state.value) {
                return <View />;
            }
            return (
                <AbcView onClick={() => onClickScan?.()}>
                    <AbcIconfont
                        style={{ ...flattenStyles(scanStyle) }}
                        name={"scan-line"}
                        color={Colors.B7}
                        size={Sizes.dp20}
                    />
                </AbcView>
            );
        }
        return <View />;
    }

    render(): JSX.Element {
        const { borderRadius, border, editable, inputStyle, containerStyle, boxStyle, showCancelBtn, onCancel, cancelBtnStyle } = this.props;
        return (
            <View style={{ ...ABCStyles.rowAlignCenter, ...flattenStyles(boxStyle) }}>
                <View
                    style={{
                        flex: 1,
                        flexDirection: "row",
                        alignItems: "center",
                        height: Sizes.dp36,
                        borderRadius: borderRadius,
                        borderWidth: border,
                        ...SearchInputStyle.container,
                        ...flattenStyles(containerStyle),
                    }}
                >
                    {this._renderSearchIcon()}
                    <View style={{ flexGrow: 1 }}>
                        {this._renderInput()}
                        {!editable && (
                            <AbcView
                                style={{
                                    position: "absolute",
                                    left: 0,
                                    right: 0,
                                    top: 0,
                                    bottom: 0,
                                    backgroundColor: Colors.transparent,
                                }}
                                onClick={() => {
                                    this.handleTextChange(this.state.value);
                                }}
                            />
                        )}
                    </View>
                    {this._renderDeletedIcon()}
                    {this._renderScanIcon()}
                </View>
                {!!showCancelBtn && (
                    <AbcView
                        style={{ paddingLeft: Sizes.dp16, ...flattenStyles(cancelBtnStyle) }}
                        onClick={() => onCancel?.()}
                    >
                        <Text style={TextStyles.t16NT2}>取消</Text>
                    </AbcView>
                )}
            </View>
        );
    }
}