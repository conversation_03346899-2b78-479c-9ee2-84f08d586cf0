import React from "react";
import { View } from "@hippy/react";
import { AbcSearchBar } from "../index";
import { Colors, Sizes } from "@app/theme";
import { AbcText } from "../../abc-text";

export function AbcSearchBarDemo() {
    const inputVal = "";

    function onFocus() {
        console.log("onFocus")
    }

    function onBlur() {
        console.log("onBlur")
    }

    function onChange() {
        console.log("onChange")
    }

    function onClickScan() {
        console.log("onClickScan")
    }

    function onClear() {
        console.log("onClear")
    }

    function onCancel() {
        console.log("onCancel")
    }
    return (
        <View style={{ flex: 1, backgroundColor: "rgba(0, 0, 0, 0.1)" }}>
            <View style={{ marginBottom: Sizes.dp12 }}>
                <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp12 }}>基础用法</AbcText>
                <View style={{ ...Sizes.paddingLTRB(Sizes.dp12) }}>
                    <AbcSearchBar
                        showScanIcon={true}
                        showDeleteIcon={true}
                        value={inputVal}
                        borderRadius={Sizes.dp8}
                        placeholderTextColor={Colors.retail_T3}
                        inputStyle={{height: Sizes.dp24, fontSize: Sizes.dp16}}
                        boxStyle={{flex: 1}}
                        onFocus={onFocus}
                        onBlur={onBlur}
                        onClear={onClear}
                        onChange={onChange}
                        onClickScan={onClickScan}
                    />
                </View>
            </View>

            <View style={{ marginBottom: Sizes.dp12 }}>
                <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp12 }}>不可编辑</AbcText>
                <View style={{ ...Sizes.paddingLTRB(Sizes.dp12) }}>
                    <AbcSearchBar
                        showScanIcon={true}
                        showDeleteIcon={true}
                        editable={false}
                        value={inputVal}
                        borderRadius={Sizes.dp8}
                        placeholderTextColor={Colors.retail_T3}
                        inputStyle={{height: Sizes.dp24, fontSize: Sizes.dp16}}
                        boxStyle={{flex: 1}}
                        onFocus={onFocus}
                        onBlur={onBlur}
                        onClear={onClear}
                        onChange={onChange}
                        onClickScan={onClickScan}
                    />
                </View>
            </View>

            <View style={{ marginBottom: Sizes.dp12 }}>
                <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp12 }}>有值时隐藏扫一扫图标</AbcText>
                <View style={{ ...Sizes.paddingLTRB(Sizes.dp12) }}>
                    <AbcSearchBar
                        showScanIcon={true}
                        showDeleteIcon={true}
                        hiddenScanWhenHasValue={true}
                        value={inputVal}
                        borderRadius={Sizes.dp8}
                        placeholderTextColor={Colors.retail_T3}
                        inputStyle={{height: Sizes.dp24, fontSize: Sizes.dp16}}
                        boxStyle={{flex: 1}}
                        onFocus={onFocus}
                        onBlur={onBlur}
                        onClear={onClear}
                        onChange={onChange}
                        onClickScan={onClickScan}
                    />
                </View>
            </View>

            <View style={{ marginBottom: Sizes.dp12 }}>
                <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp12 }}>展示取消按钮</AbcText>
                <View style={{ ...Sizes.paddingLTRB(Sizes.dp12) }}>
                    <AbcSearchBar
                        showScanIcon={true}
                        showDeleteIcon={true}
                        showCancelBtn={true}
                        value={inputVal}
                        borderRadius={Sizes.dp8}
                        placeholderTextColor={Colors.retail_T3}
                        containerStyle={{ backgroundColor: Colors.retail_bg_grey2 }}
                        inputStyle={{height: Sizes.dp24, fontSize: Sizes.dp16}}
                        boxStyle={{flex: 1}}
                        onFocus={onFocus}
                        onBlur={onBlur}
                        onClear={onClear}
                        onChange={onChange}
                        onClickScan={onClickScan}
                        onCancel={onCancel}
                    />
                </View>
            </View>
        </View>
    );
}
