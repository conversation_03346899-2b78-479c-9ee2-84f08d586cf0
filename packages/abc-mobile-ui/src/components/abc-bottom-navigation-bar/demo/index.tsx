import React from "react";
import { View, Text } from "@hippy/react";
import { AbcBottomNavigationBar } from "../index";
import { AbcIconfont } from "../../abc-iconfont";
import { Colors } from "@app/theme";

export function AbcBottomNavigationBarDemo() {
    const items = [
        {
            title: "首页",
            icon: <AbcIconfont name="home" size={24} color={Colors.black} />,
            activeIcon: <AbcIconfont name="home" size={24} color={Colors.mainColor} />,
        },
        {
            title: "分类",
            icon: <AbcIconfont name="category" size={24} color={Colors.black} />,
            activeIcon: <AbcIconfont name="category" size={24} color={Colors.mainColor} />,
        },
        {
            title: "购物车",
            icon: <AbcIconfont name="cart" size={24} color={Colors.black} />,
            activeIcon: <AbcIconfont name="cart" size={24} color={Colors.mainColor} />,
        },
        {
            title: "我的",
            icon: <AbcIconfont name="user" size={24} color={Colors.black} />,
            activeIcon: <AbcIconfont name="user" size={24} color={Colors.mainColor} />,
        },
    ];
    return (
        <View>
            <Text>基础用法</Text>
            <AbcBottomNavigationBar items={items} />
        </View>
    );
}
