import React from "react";
import { View } from "@hippy/react";
import { AbcIconfont } from "../index";
import { Sizes, Colors } from "@app/theme";
import { AbcText } from "../../abc-text/index";

export function AbcIconfontDemo() {
    return (
        <View>
            <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>基础用法</AbcText>
            <View style={{ flex: 1, flexDirection: "row", justifyContent: "space-between" }}>
                <AbcIconfont name={"arrow_up"} size={Sizes.dp20} color={Colors.T6} />
                <AbcIconfont name={"arrow_down"} size={Sizes.dp20} color={Colors.T6} />
                <AbcIconfont name={"Arrow_Right"} size={Sizes.dp20} color={Colors.P1} />
                <AbcIconfont name={"calendar"} size={Sizes.dp20} color={Colors.P1} />
                <AbcIconfont name={"Chosen"} size={Sizes.dp20} color={Colors.mainColor} />
                <AbcIconfont name={"select"} size={Sizes.dp20} color={Colors.P1} />
                <AbcIconfont name={"Attention1"} size={Sizes.dp20} color={Colors.P1} />
                <AbcIconfont name={"money"} size={Sizes.dp20} color={Colors.P1} />
            </View>
        </View>
    );
}
