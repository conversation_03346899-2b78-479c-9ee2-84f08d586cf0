// /**
//  * create by dengjie
//  * desc:
//  * create date 2020/6/4
//  */
//
// import { Color, Colors } from "../../theme";
// import React from "react";
// import { AbcView } from "../views/abc-view";
// import { IconFontView } from "../index";
//
// interface SolidIconFontViewProps {
//     type?: string;
//     iconName?: string;
//     iconColor?: Color;
//     fillColor?: Color;
//     size?: any;
//     onClick?(): void;
// }
//
// //实体图标
// class SolidIconFontView extends React.Component<SolidIconFontViewProps> {
//     private iconName: any;
//     private iconColor: Color | undefined;
//     private fillColor: any;
//     private iconSize = 12;
//     private fillSize = 16;
//
//     constructor(props: SolidIconFontViewProps) {
//         super(props);
//         this.init();
//     }
//
//     init() {
//         const { type, iconColor, iconName, fillColor, size } = this.props;
//         size && ((this.fillSize = size), (this.iconSize = size - 2));
//         switch (type) {
//             case "delete": {
//                 this.iconName = "cross_small";
//                 this.iconColor = Colors.white;
//                 this.fillColor = Colors.red;
//                 break;
//             }
//             default: {
//                 this.iconName = iconName;
//                 this.iconColor = iconColor;
//                 this.fillColor = fillColor;
//             }
//         }
//     }
//
//     render() {
//         return (
//             // eslint-disable-next-line react/jsx-no-undef
//             <AbcView
//                 style={{
//                     backgroundColor: this.fillColor,
//                     width: this.fillSize,
//                     height: this.fillSize,
//                     borderRadius: this.fillSize,
//                     justifyContent: "center",
//                     alignItems: "center",
//                 }}
//             >
//                 {/* eslint-disable-next-line react/jsx-no-undef */}
//                 <IconFontView name={this.iconName} size={this.iconSize} color={this.iconColor} />
//             </AbcView>
//         );
//     }
// }
