/**
 * Created by he<PERSON><PERSON> on 2020/3/14.
 */

//import createIconSet from 'create-icon-set';

import React from "react";
import { ClickableProps, Style, View } from "@hippy/react";
import { iconfontMap } from "./iconfont-map";

import { ABCStyles, Color, Colors, Sizes } from "@app/theme";
import { getLog } from "../../index";
import { AbcText } from "../abc-text";
import { AbcSvg } from "../abc-svg";
import icons from "./icons";

const glyphs = new Map<string, string>();
iconfontMap.glyphs.forEach((item) => {
    glyphs.set(item.name, String.fromCharCode(item.unicode_decimal));
});

type IconFontName = keyof typeof icons | string;

export enum IconfontViewMode {
    Font = 1,
    Svg = 2,
}

interface IconFontViewPros extends ClickableProps {
    name: IconFontName;
    size: number | string;
    fontFamily: string | "iconfont";
    color: Color;
    style?: Style;
    mode?: IconfontViewMode;
}

export default class AbcIconfont extends React.Component<IconFontViewPros> {
    static defaultProps = {
        size: Sizes.dp24,
        fontFamily: "iconfont",
        color: Colors.T3,
    };

    constructor(props: IconFontViewPros) {
        super(props);
    }

    _renderBySVG(name: IconFontName, size: number | string, color: string, style: Style): JSX.Element {
        return (
            <View
                style={{
                    height: size,
                    width: size,
                    ...style,
                }}
            >
                <AbcSvg
                    content={`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">${icons[name].replace(
                        /currentColor/g,
                        color
                    )}</svg>`}
                    style={[ABCStyles.absoluteFillObject]}
                />
            </View>
        );
    }

    render(): JSX.Element {
        const { fontFamily, name, size, color, style, ...props } = this.props;
        /**
         * 指定渲染模式或者以color结尾的图标
         */
        if (this.props.mode === IconfontViewMode.Svg || ((name as string).startsWith("s-") && !this.props.mode)) {
            return this._renderBySVG(name, size, color, style);
        }
        return (
            <AbcText
                style={{
                    ...style,
                    fontFamily: fontFamily,
                    fontSize: Number(size),
                    color: color,
                }}
                {...props}
                accessibilityLabel={name as string}
            >
                {glyphs.get(name as string) || ""}
            </AbcText>
        );
    }

    static dropDown(): JSX.Element {
        getLog().d(`dropDown()`);
        return <AbcIconfont name="Dropdown_Triangle" size={Sizes.dp14} color={Colors.P1} />;
    }
}

interface RightArrowViewProps {
    color?: Color;
    size?: number;
}

export class AbcRightArrowIcon extends React.Component<RightArrowViewProps> {
    render(): JSX.Element {
        const { color, size } = this.props;
        return <AbcIconfont name={"Arrow_Right"} size={size ?? Sizes.dp16} color={color ?? Colors.P1} />;
    }
}

export class AbcDropdownArrowIcon extends React.Component {
    render(): JSX.Element {
        return <AbcIconfont name={"Dropdown_Triangle"} size={Sizes.dp14} color={Colors.P1} />;
    }
}

interface TreeDotViewProps extends RightArrowViewProps {}

export class AbcTreeDotIcon extends React.Component<TreeDotViewProps> {
    render(): JSX.Element {
        const { color, size } = this.props;
        return <AbcIconfont name={"three_dot"} size={size ?? Sizes.dp20} color={color ?? Colors.black} />;
    }
}

export class AbcPositiveSelectIcon extends React.Component<RightArrowViewProps> {
    render(): JSX.Element {
        const { size } = this.props;
        return <AbcIconfont name={"Positive_Selected"} size={size ?? Sizes.dp14} color={Colors.mainColor} />;
    }
}
