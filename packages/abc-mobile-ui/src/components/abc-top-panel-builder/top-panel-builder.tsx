import React from "react";
import { Animation, Dimensions, View } from "@hippy/react";
import { Colors } from "@app/theme";
import { AbcNavigator } from "../abc-navigator";
import { AbcView } from "../abc-view";
import { UiUtils, DeviceUtils, delayed } from "@app/utils";

/**
 * create by dengjie
 * desc:
 * create date 2020/6/18
 */

interface AbcTopPanelBuilderProps {
    offsetY: number;
}

class AbcTopPanelBuilder extends React.Component<AbcTopPanelBuilderProps> {
    private readonly _animation: Animation;

    constructor(props: AbcTopPanelBuilderProps) {
        super(props);
        this._animation = new Animation({
            startValue: -Dimensions.get("window").height, // 开始值
            toValue: 0, // 动画结束值
            duration: 200, // 动画持续时长
            delay: 0, // 至动画真正开始的延迟时间
            mode: "timing", // 动画模式
            timingFunction: "linear", // 动画缓动函数
        });
    }

    componentDidMount(): void {
        this._animation.start();
    }

    componentWillUnmount(): void {
        this._animation.destory();
    }

    render(): JSX.Element {
        return (
            <View
                style={{
                    //解决安卓机型下，筛选列表会露出后面内容的问题
                    height: Dimensions.get("window").height + (DeviceUtils.isAndroid() ? UiUtils.safeStatusHeight() : 0),
                }}
            >
                <AbcView
                    style={{ height: this.props.offsetY }}
                    onClick={() => {
                        this._animation.updateAnimation({
                            startValue: 0,
                            toValue: -Dimensions.get("window").height,
                            duration: 100,
                        });
                        this._animation.start();
                        delayed(100).subscribe(() => {
                            AbcNavigator.pop();
                        });
                    }}
                />
                <View style={{ flex: 1, backgroundColor: Colors.maskColor, overflow: "hidden" }}>
                    <View
                        style={[
                            { backgroundColor: Colors.white },
                            {
                                transform: [
                                    {
                                        translateY: this._animation,
                                    },
                                ],
                            },
                        ]}
                    >
                        {!this.props.offsetY && <View style={{ height: UiUtils.safeStatusHeight() }} />}
                        {this.props.children}
                    </View>
                </View>
            </View>
        );
    }
}

export { AbcTopPanelBuilder };
