import React from "react";
import { View } from "@hippy/react";
import { AbcSteps } from "../index";
import { AbcText } from "../../abc-text";
import { Colors, Sizes } from "@app/theme";

export function AbcStepsDemo() {
    const stepList = [
        {
            id: "10000001",
            title: "待发货",
            complete: true,
            date: "2025-06-24",
        },
        {
            id: "10000002",
            title: "已发货",
            complete: true,
            date: "2025-06-24",
        },
        {
            id: "10000003",
            title: "待收货",
            complete: false,
            date: "2025-06-24",
        },
    ];

    const stepConfig = {
        finishIcon: "Chosen",
        activeColor: Colors.mainColor,
        inactiveColor: Colors.T4,
        inactiveBorderColor: Colors.T4,
    };

    return (
        <View>
            <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>基础用法</AbcText>
            <AbcSteps
                data={stepList}
                direction={"row"}
                finishIcon={stepConfig.finishIcon}
                activeColor={stepConfig.activeColor}
                inactiveColor={stepConfig.inactiveColor}
                inactiveBorderColor={stepConfig.inactiveBorderColor}
            />

            <View style={{ marginTop: Sizes.dp16 }}>
                <AbcSteps
                    data={stepList}
                    direction={"column"}
                    finishIcon={stepConfig.finishIcon}
                    activeColor={stepConfig.activeColor}
                    inactiveColor={stepConfig.inactiveColor}
                    inactiveBorderColor={stepConfig.inactiveBorderColor}
                />
            </View>
        </View>
    );
}
