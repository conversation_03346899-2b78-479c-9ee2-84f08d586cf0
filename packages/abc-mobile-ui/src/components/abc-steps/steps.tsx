import { AbcBaseComponent } from "../abc-base-component";
import { Style, Text, View } from "@hippy/react";
import React from "react";
import { ABCStyles, Color, Colors, flattenStyles, Sizes, TextStyles } from "@app/theme";
import { AbcIconfont, AbcSizedBox } from "../index";

const fixedDistance = Sizes.dp6;

export interface StepStatusListItem {
    id: string | number;
    title: string;
    complete: boolean;
    date: string;
    renderContent?: () => JSX.Element; //自定义流程描述的内容
    renderIcon?: () => JSX.Element; //自定义流程图标展示
}
export interface StepStatusList {
    tabTitle?: string;
    stepList: StepStatusListItem[];
}
interface AbcStepsProps {
    data: StepStatusListItem[];
    direction?: "column" | "row"; //排列方向
    finishIcon?: string; //图标
    activeColor?: Color; //图标激活颜色

    isLastItem?: boolean; //是否是最后一个节点
    inactiveColor?: Color; //未完成图标的背景颜色
    inactiveBorderColor?: Color; //未完成图标的边框颜色
    lineStyle?: Style; //线条样式
    contentStyle?: Style | Style[];
    subContentStyle?: Style | Style[];
}
export class AbcSteps extends AbcBaseComponent<AbcStepsProps> {
    render(): JSX.Element {
        const { direction, data, finishIcon, activeColor, inactiveColor, inactiveBorderColor, lineStyle, contentStyle, subContentStyle } =
            this.props;
        const horizontalLayout = direction == "row";
        return (
            <View style={{ flexDirection: direction }}>
                {data?.map((item, index, self) =>
                    horizontalLayout ? (
                        <StepsRowItemView
                            data={item}
                            key={index++}
                            finishIcon={finishIcon}
                            activeColor={activeColor}
                            isLastItem={self.length == index}
                            inactiveColor={inactiveColor}
                            inactiveBorderColor={inactiveBorderColor}
                            lineStyle={lineStyle}
                            contentStyle={contentStyle}
                            subContentStyle={subContentStyle}
                        />
                    ) : (
                        <StepsColumnItemView
                            data={item}
                            key={index++}
                            finishIcon={finishIcon}
                            activeColor={activeColor}
                            isLastItem={self.length == index}
                            inactiveColor={inactiveColor}
                            inactiveBorderColor={inactiveBorderColor}
                            lineStyle={lineStyle}
                            contentStyle={contentStyle}
                            subContentStyle={subContentStyle}
                        />
                    )
                )}
            </View>
        );
    }
}

interface StepsItemProps {
    data: StepStatusListItem;
    finishIcon?: string;
    activeColor?: Color;
    isLastItem?: boolean;
    inactiveColor?: Color;
    inactiveBorderColor?: Color;
    itemDistance?: number; //每个节点间距
    lineStyle?: Style; //线条样式
    contentStyle?: Style | Style[];
    subContentStyle?: Style | Style[];
}
class StepsRowItemView extends AbcBaseComponent<StepsItemProps> {
    private _listViewW = 0;
    render(): JSX.Element {
        const { data, finishIcon, activeColor, isLastItem, inactiveColor, inactiveBorderColor } = this.props;
        return (
            <View
                style={{ paddingRight: Sizes.dp8 }}
                onLayout={(evt) => {
                    //@ts-ignore
                    this._listViewW = evt.layout.width ?? 0;
                    this.forceUpdate();
                }}
            >
                <View style={{}}>
                    <Text style={[TextStyles.t16NT1, { alignSelf: "center" }]}>{data.title}</Text>
                    {data.complete ? (
                        <Text style={TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp16 })}>{data.date}</Text>
                    ) : (
                        <AbcSizedBox height={Sizes.dp16} />
                    )}
                </View>

                <AbcSizedBox height={Sizes.dp8} />

                <View
                    style={{
                        alignSelf: "center",
                    }}
                >
                    <View
                        style={{
                            position: "absolute",
                            height: Sizes.dpHalf,
                            backgroundColor: inactiveColor,
                            width: isLastItem ? 0 : this._listViewW,
                            bottom: Sizes.dp7,
                        }}
                    />
                    <View style={[ABCStyles.rowAlignCenter, {}]}>
                        {data?.complete ? (
                            <AbcIconfont name={finishIcon!} size={Sizes.dp14} color={activeColor} />
                        ) : (
                            <View
                                style={{
                                    width: Sizes.dp14,
                                    height: Sizes.dp14,
                                    borderRadius: Sizes.dp8,
                                    backgroundColor: Colors.white,
                                    borderColor: inactiveBorderColor,
                                    borderWidth: Sizes.dpHalf,
                                }}
                            />
                        )}
                    </View>
                </View>
            </View>
        );
    }
}

class StepsColumnItemView extends AbcBaseComponent<StepsItemProps> {
    private _listViewH = 0;
    static defaultProps = {
        itemDistance: Sizes.dp24,
    };
    render() {
        const { data, isLastItem, inactiveColor, inactiveBorderColor, itemDistance, lineStyle, contentStyle, subContentStyle } = this.props;
        return (
            <View
                style={{ paddingBottom: itemDistance }}
                onLayout={(evt) => {
                    //@ts-ignore
                    this._listViewH = evt.layout.height ?? 0;
                    this.forceUpdate();
                }}
            >
                <View style={{ flexDirection: "row" }}>
                    <View>
                        <View
                            style={{
                                backgroundColor: Colors.dividerLineColor,
                                height: isLastItem ? 0 : this._listViewH,
                                position: "absolute",
                                left: Sizes.dp4,
                                width: Sizes.dp1,
                                top: fixedDistance,
                                ...flattenStyles(lineStyle),
                            }}
                        />
                        {!data?.renderIcon && (
                            <View>
                                {data?.complete ? (
                                    <View
                                        style={{
                                            width: Sizes.dp8,
                                            height: Sizes.dp8,
                                            borderRadius: Sizes.dp4,
                                            backgroundColor: Colors.theme2,
                                            top: fixedDistance,
                                        }}
                                    />
                                ) : (
                                    <View
                                        style={{
                                            width: Sizes.dp8,
                                            height: Sizes.dp8,
                                            borderRadius: Sizes.dp4,
                                            backgroundColor: inactiveColor,
                                            borderColor: inactiveBorderColor,
                                            borderWidth: Sizes.dpHalf,
                                            top: fixedDistance,
                                        }}
                                    />
                                )}
                            </View>
                        )}
                        {data?.renderIcon && data?.renderIcon()}
                    </View>

                    <View style={{ marginLeft: Sizes.dp16, flex: 1 }}>
                        {!data?.renderContent && (
                            <View style={{ flex: 1 }}>
                                <Text
                                    style={[
                                        TextStyles.t16NB.copyWith({ color: data.complete ? Colors.T6 : Colors.black }),
                                        { flex: 1, ...flattenStyles(contentStyle) },
                                    ]}
                                >
                                    {data?.title ?? ""}
                                </Text>
                                {!!data?.date && <AbcSizedBox height={Sizes.dp2} />}
                                {data?.complete && (
                                    <Text
                                        style={[
                                            TextStyles.t12NT6.copyWith({ lineHeight: Sizes.dp17 }),
                                            { ...flattenStyles(subContentStyle) },
                                        ]}
                                    >
                                        {data?.date ?? ""}
                                    </Text>
                                )}
                            </View>
                        )}
                        {data?.renderContent && data.renderContent()}
                    </View>
                </View>
            </View>
        );
    }
}
