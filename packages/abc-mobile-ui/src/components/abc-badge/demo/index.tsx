import React from "react";
import { View, Text } from "@hippy/react";
import { AbcBadge } from "../index";
import { AbcIconfont } from "../../abc-iconfont";
import { AbcFlex } from "../../abc-flex";
import { Colors, Sizes, TextStyles } from "@app/theme";

export function AbcBadgeDemo() {
    return (
        <View>
            <Text style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>基础用法</Text>
            <AbcFlex>
                <AbcBadge dot={true}>
                    <Text style={[TextStyles.t16NB]}>小红点</Text>
                </AbcBadge>
                <AbcBadge maxValue={99} value={33} position={{ right: 0 }}>
                    <AbcIconfont
                        name={"board_1"}
                        size={Sizes.dp20}
                        color={Colors.mainColor}
                        style={{ width: Sizes.dp20, height: Sizes.dp20, marginHorizontal: Sizes.dp12 }}
                    />
                </AbcBadge>
                <AbcBadge maxValue={99} value={199} position={{ right: 0 }}>
                    数字过大展示...
                </AbcBadge>
            </AbcFlex>
        </View>
    );
}
