/**
 * create by dengjie
 * desc:
 * create date 2020/7/21
 */
import React from "react";
import { View, Text, StyleSheet, Style } from "@hippy/react";
import { AbcBaseComponent } from "../abc-base-component/index";
import { Colors, flattenStyles, Sizes, TextStyles } from "@app/theme";
import _ from "lodash";
import { AbcAssetImage } from "../abc-asset-image/index";

interface BadgeProps {
    position?: { top?: number; left?: number; right?: number; bottom?: number };
    size?: number; // 主要针对dot 类型的badge大小
    dot?: boolean;
    value?: string | number;
    maxValue?: number;
    dotStyle?: Style;
    image?: string; //图片的形式
    imgPosition?: { top?: number; left?: number; right?: number; bottom?: number };
}

//@ts-ignore
const badgeStyle = StyleSheet.create({
    dotStyle: {
        position: "absolute",
        right: -Sizes.dp5,
        top: -Sizes.dp2,
        borderRadius: Sizes.dp4,
        backgroundColor: Colors.R2,
    },
});

export class AbcBadge extends AbcBaseComponent<BadgeProps> {
    constructor(props: BadgeProps) {
        super(props);
    }

    private _renderBadgeValue(): JSX.Element {
        const { value, dot, maxValue, position, size, dotStyle, image, imgPosition } = this.props;
        if (!!image) {
            return (
                <View style={[badgeStyle.dotStyle, { borderRadius: 0, backgroundColor: "transparent", ...imgPosition }]}>
                    <AbcAssetImage name={image ?? ""} style={{ width: Sizes.dp26, height: Sizes.dp16 }} />
                </View>
            );
        } else if (!_.isUndefined(value)) {
            let _text = value;
            if (_.isNumber(_text) && _.isNumber(maxValue)) {
                _text = _text > maxValue ? "..." : _text;
            }
            return (
                <View
                    style={[
                        badgeStyle.dotStyle,
                        Sizes.paddingLTRB(Sizes.dp4, 0),
                        {
                            borderRadius: Sizes.dp8,
                            top: -Sizes.dp8,
                            right: -Sizes.dp6,
                            minWidth: Sizes.dp8,
                            ...position,
                        },
                    ]}
                >
                    <Text style={[TextStyles.t12NW.copyWith({ lineHeight: Sizes.dp16 }), { textAlign: "center" }]}>{_text ?? ""}</Text>
                </View>
            );
        } else if (dot) {
            return (
                <View
                    style={[
                        badgeStyle.dotStyle,
                        {
                            width: size ?? Sizes.dp4,
                            height: size ?? Sizes.dp4,
                            ...position,
                            ...flattenStyles(dotStyle),
                        },
                    ]}
                />
            );
        } else {
            return <View />;
        }
    }

    render(): JSX.Element {
        return (
            <View
                style={{
                    position: "relative",
                    alignItems: "center",
                    alignSelf: "center",
                }}
            >
                {this.props.children}
                {this._renderBadgeValue()}
            </View>
        );
    }
}
