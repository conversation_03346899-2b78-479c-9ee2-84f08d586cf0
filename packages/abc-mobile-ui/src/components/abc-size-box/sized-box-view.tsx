import React from "react";
import { View } from "@hippy/react";

interface SizedBoxProps {
    width?: number;
    height?: number;
}

export default class AbcSizedBox extends React.Component<SizedBoxProps> {
    static defaultProps = {
        width: undefined,
        height: undefined,
    };

    constructor(props: SizedBoxProps) {
        super(props);
    }

    render(): JSX.Element {
        const { width, height, children } = this.props;

        return <View style={{ width: width, height: height }}>{children}</View>;
    }
}
