import React from "react";
import { View } from "@hippy/react";
import { AbcDropdownMenu } from "../index";
import { optionsItem } from "../types"
import {AbcText} from "../../abc-text";
import {ABCStyles, Colors, Sizes} from "@app/theme";

export class AbcDropdownMenuDemo extends React.Component {
    state = {
        iconRef: null,
        options: [
            { title: '全部商品', value: 0, select: false },
            { title: '新款商品', value: 1, select: false, disable: true },
            { title: '活动商品', value: 2, select: false },
            { title: '折扣商品', value: 3, select: false },
        ]
    };

    handleOnChange(item: optionsItem) {
        const newOptions = [...this.state.options];
        newOptions.forEach(it => it.select = it.value === item.value)

        this.setState({
            options: newOptions
        })
    }

    render(): JSX.Element {
        return (
            <View collapsable={false}>
                <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>基础用法</AbcText>

                <View collapsable={false}>
                    <AbcDropdownMenu
                        style={[
                            ABCStyles.rowAlignCenter,
                            {
                                width: Sizes.dp100,
                                borderWidth: Sizes.dp1,
                                borderColor: Colors.border_color_light
                            }
                        ]}
                        options={this.state.options}
                        defaultShowText={"请选择"}
                        onChange={this.handleOnChange.bind(this)}
                    />
                </View>
            </View>
        );
    }
}