import React from "react";
import {View, Text, Style} from "@hippy/react";
import {ABCStyles, Colors, flattenStyles, Sizes, TextStyles} from "@app/theme";
import { AbcView } from "../abc-view";
import { AbcIconfont } from "../abc-iconfont";
import type { optionsItem } from "./types"
import { AbcBaseComponent } from "../abc-base-component";
import { UiUtils } from "@app/utils";
import { AbcNavigator } from "../abc-navigator";
import { AbcDropdownItem } from "./dropdowm-item";

export interface DropdownMenuProps {
    options: Array<optionsItem>
    /**
     * 无选中项占位内容
     */
    defaultShowText?: string;

    style?: Style | Style[];
    onChange?: (item: optionsItem) => void;
    showBorderRadius?: boolean;
}

interface DropDownFilterStates {
    selected: boolean;
}

export class AbcDropdownMenu extends AbcBaseComponent<DropdownMenuProps, DropDownFilterStates> {
    constructor(props: DropdownMenuProps) {
        super(props);
        this.state = {
            selected: false,
        };
    }

    _iconRef: View | null = null;
    hasInit = false;

    protected createBaseViewStyle(): Style {
        let defaultStyle = {};

        defaultStyle = Object.assign(defaultStyle, {
            ...Sizes.paddingLTRB(Sizes.dp14, Sizes.dp12, Sizes.dp14, Sizes.dp16),
        });

        return defaultStyle;
    }

    private async _showDropdownMenu(): Promise<void> {
        try {
            this.setState({ selected: true });
            const layout = await UiUtils.measureInWindow(this._iconRef);

            const { onChange, showBorderRadius = true } = this.props
            await AbcNavigator.showRightSheet(
                <AbcDropdownItem
                    options={this.props.options}
                    width={UiUtils.getScreenWidth()}
                    height={layout.y + layout.height - UiUtils.safeStatusHeight()}
                    showBorderRadius={showBorderRadius}
                    onChange={onChange}
                />
            )
            this.setState({ selected: false });
        } catch (e) {
            console.log("_showDropdownMenu err:" + e)
        }
    }

    render(): JSX.Element {
        const selectItem = this.props.options.find(item => item.select)
        const { selected } = this.state || {};
        return (
            <View
                style={[ABCStyles.rowAlignCenter, this.createBaseViewStyle(), flattenStyles(this.props.style)]}
                ref={(ref) => (this._iconRef = ref)}
                onClick={() => {
                    this._showDropdownMenu();
                }}
                collapsable={false}
            >
                <Text
                    style={[
                        TextStyles.t14NT1.copyWith(selected || !!selectItem ? { color: Colors.mainColor } : { color: Colors.t1}),
                        { flexShrink: 1 },
                    ]}
                    numberOfLines={1}
                >
                    { selectItem ? selectItem.title : this.props.defaultShowText ?? "" }
                </Text>
                <AbcIconfont
                    name={selected ? "s-fillarrowup-fill" : "s-fillarrowdown-fill"}
                    size={Sizes.dp16}
                    color={selected || !!selectItem ? Colors.mainColor : Colors.T3}
                />
            </View>
        );
    }
}