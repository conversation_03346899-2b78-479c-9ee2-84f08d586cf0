import React from "react";
import {Animation, Dimensions, ScrollView, Style, Text, View} from "@hippy/react";
import {AbcView} from "../abc-view";
import {AbcSafeArea} from "../abc-safe-area";
import {optionsItem} from "./types";
import {Colors, Sizes, ABCStyles, TextStyles, FontWeights } from "@app/theme";
import {delayed} from "@app/utils";
import {AbcNavigator} from "../abc-navigator";
import { AbcIconfont } from "../abc-iconfont";

export interface optionsItemProps {
    options: Array<optionsItem>;
    width?: number;
    height?: number;
    showBorderRadius?: boolean;
    onChange?: (item: optionsItem) => void
}

export class AbcDropdownItem<P extends optionsItemProps> extends React.Component<P> {
    protected _animation: Animation;
    protected _bgAnimation: Animation;
    protected _animationDuration = 250;

    constructor(props: P) {
        super(props);

        this._animation = new Animation({
            startValue: -Dimensions.get("window").height + (props.height ?? 0), // 开始值
            toValue: 0, // 动画结束值
            duration: this._animationDuration, // 动画持续时长
            delay: 0, // 至动画真正开始的延迟时间
            mode: "timing", // 动画模式
            timingFunction: "linear", // 动画缓动函数
        });

        this._bgAnimation = new Animation({
            startValue: 0, // 开始值
            toValue: 1, // 动画结束值
            duration: this._animationDuration, // 动画持续时长
            delay: 0, // 至动画真正开始的延迟时间
            mode: "timing", // 动画模式
            timingFunction: "linear", // 动画缓动函数
        });
    }

    componentDidMount(): void {
        this._animation.start();
        this._bgAnimation.start();
    }

    componentWillUnmount(): void {
        this._animation.destory();
        this._bgAnimation.destory();
    }

    updateAnimation(): void {
        this._animation.updateAnimation({
            startValue: 0,
            toValue: -Dimensions.get("window").height + (this.props.height ?? 0),
            duration: this._animationDuration,
        });
        this._bgAnimation.updateAnimation({
            startValue: 1,
            toValue: 0,
            duration: this._animationDuration,
        });
        this._animation.start();
        this._bgAnimation.start();
    }

    navigatorPopWithAnimation(): void {
        this.updateAnimation();
        delayed(this._animationDuration).subscribe(() => {
            AbcNavigator.pop()
        });
    }

    async _handleCheckItem(item: optionsItem) {
        if (item.disable) return
        const { onChange } = this.props;
        onChange?.(item)
        this.navigatorPopWithAnimation();
    }

    protected createPanelView(): JSX.Element {
        const iteHeight = Sizes.dp56;
        const menuItems: JSX.Element[] = [];
        const { options = [], showBorderRadius  } = this.props;
        const borderRadiusStyle= showBorderRadius ? { borderBottomLeftRadius: Sizes.dp6, borderBottomRightRadius: Sizes.dp6, overflow: "hidden" } : {}

        options.forEach(item => {
            menuItems.push(
                <AbcView
                    style={[
                        ABCStyles.bottomLine,
                        {
                            height: iteHeight,
                            paddingHorizontal: Sizes.listHorizontalMargin,
                            justifyContent: "center",
                            flexDirection: "row",
                            alignItems: "center",
                            borderColor: Colors.border_color_light,
                        },
                    ]}
                    key={item.value}
                    onClick={this._handleCheckItem.bind(this, item)}
                >
                    <Text
                        style={[
                            {
                                flexGrow: 1,
                                alignItems: "center",
                                fontSize: Sizes.dp16,
                                fontWeight: FontWeights.normal,
                                color: item.select ? Colors.mainColor : item?.disable ? Colors.t4 : Colors.t1,
                            },
                        ]}
                    >
                        {item.title!}
                    </Text>
                    {item.select && (
                        <AbcIconfont name={"Positive_Selected"} size={Sizes.dp14} color={Colors.mainColor} />
                    )}
                </AbcView>
            );
        })

        const _screenHeight = Dimensions.get("screen").height;
        const _top = this.props.height ?? 0;
        return (
            <View
                style={{
                    ...borderRadiusStyle,
                    position: "absolute",
                    right: 0,
                    backgroundColor: Colors.white,
                    width: this.props.width ?? Sizes.dp140,
                    height: Math.min(
                        _screenHeight - _top - _screenHeight / 3,
                        (menuItems.length) * iteHeight
                    ),
                    transform: [
                        {
                            translateY: this._animation,
                        },
                    ],
                }}
            >
                <ScrollView showsVerticalScrollIndicator={false}>{menuItems}</ScrollView>
            </View>
        )
    }

    public render(): JSX.Element {
        const _screenHeight = Dimensions.get("screen").height;
        const _top = this.props.height ?? 0;
        return (
            <AbcSafeArea
                style={{
                    height: _screenHeight,
                }}>
                <AbcView
                    style={{flexGrow: 1}}
                    onClick={() => {
                        this.navigatorPopWithAnimation();
                    }}
                >
                    <View
                        style={[
                            {
                                position: "relative",
                                height: _screenHeight - _top,
                                top: _top,
                                overflow: "hidden",
                            },
                        ]}
                    >
                        <View
                            style={{
                                height: Dimensions.get("window").height - (this.props.height ?? 0),
                                position: "absolute",
                                top: 0,
                                left: 0,
                                right: 0,
                                backgroundColor: Colors.mask_normal,
                                zIndex: -1,
                                opacity: this._bgAnimation,
                            }}
                        />
                        {this.createPanelView()}
                    </View>
                </AbcView>
            </AbcSafeArea>
        )
    }
}