/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/5/9
 *
 * @description 基础键盘输入,在原hippy的textinput上添加了自定义键盘工具栏的特性
 *   e.g  <AbcTextInput style={{width: 100}} multiline={false}
 placeholder={"点击输入"} keyboardType={"numeric"}
 customPanelBuilder={(text) => (<_CustomPanel value={text.value}/>)}/>
 */
import { AbcBaseComponent } from "../abc-base-component";
import { Dimensions, Platform, Style, Text, View } from "@hippy/react";
import React from "react";
import { ABCStyles, Colors, Sizes, TextStyles } from "@app/theme";
import { AbcDivider } from "../abc-divider";
import { AbcIconfont } from "../abc-iconfont";
import { AbcStepper } from "../abc-stepper";
import _ from "lodash";
import { AbcSafeAreaBottom } from "../abc-safe-area";
import { AbcText } from "../abc-text/text";
import { AbcView } from "../abc-view/view";
import { AbcNumberKeyboardBuilder } from "../abc-number-keyboard-builder";
import { delayed, UiUtils, ignore, DeviceUtils, AbcUIManagerModule, AccessibilityLabelType } from "@app/utils";
import { AbcKeyboardAwarePanel, KeyboardTypeValue } from "../abc-keyboard-aware-panel/keyboard-aware-panel";

type PanelBuilder = (textInput: AbcTextInput) => JSX.Element | undefined;

export interface KeyboardBuilder {
    build(textInput: AbcTextInput): JSX.Element;

    keyboardHeight(): number;

    bottomSafeAreaColor?: string;
}

interface KeyboardWillShowEvent {
    keyboardHeight: number;
}

export type KeyboardType = "default" | "numeric" | "password" | "email" | "phone-pad" | "search";

export interface TextFormatter {
    (oldValue: string, newValue: string): string;
}

export interface AbcTextInputProps {
    /**
     * The value to show for the text input. TextInput is a controlled component,
     * which means the native value will be forced to match this value prop if provided.
     * For most uses, this works great, but in some cases this may cause flickering
     * - one common cause is preventing edits by keeping value the same.
     * In addition to setting the same value, either set editable={false},
     * or set/update maxLength to prevent unwanted edits without flicker.
     */
    value?: string;
    /**
     * Provides an initial value that will change when the user starts typing.
     * Useful for use-cases where you do not want to deal with listening to events
     * and updating the value prop to keep the controlled state in sync.
     */
    defaultValue?: string;
    /**
     * If `false`, text is not editable.
     *
     * Default: true
     */
    editable?: boolean;
    /**
     * Determines which keyboard to open, e.g.`numeric`.
     *
     * The following values work across platforms:
     * * `default`
     * * `number-pad`
     * * `decimal-pad`
     * * `numeric`
     * * `email-address`
     * * `phone-pad`
     * * `search`
     */
    keyboardType?: KeyboardType;
    /**
     * Determines how the return key should look.
     *
     * The following values work across platforms:
     * * `done`
     * * `go`
     * * `next`
     * * `search`
     * * `send`
     */
    returnKeyType?: "done" | "go" | "next" | "search" | "send";
    /**
     * Limits the maximum number of characters that can be entered.
     * Use this instead of implementing the logic in JS to avoid flicker.
     */
    maxLength?: number;
    /**
     * If `true`, the text input can be multiple lines. The default value is `false`.
     * It is important to note that this aligns the text to the top on iOS,
     * and centers it on Android. Use with textAlignVertical set to top for the same behavior
     * in both platforms.
     */
    multiline?: boolean;
    /**
     * Sets the number of lines for a TextInput.
     * Use it with multiline set to true to be able to fill the lines.
     */
    numberOfLines?: number;
    /**
     * If `true`, focuses the input on `componentDidMount`.
     *
     * Default: false
     */
    autoFocus?: boolean;
    /**
     * The color of the `TextInput` underline.
     */
    underlineColorAndroid?: string;
    /**
     * The string that will be rendered before text input has been entered.
     */
    placeholder?: string;
    /**
     * The text color of the placeholder string.
     */
    placeholderTextColor?: string;
    /**
     * The text colors array of the placeholder string.
     */
    placeholderTextColors?: string[];
    style: Style;

    textAlignVertical?: "auto" | "top" | "bottom" | "center";
    resizeMode?: boolean;

    /**
     * auto select text when focus
     */
    selectTextOnFocus?: boolean;

    /**
     * Function that is format value when the text input is changed.
     */
    formatter?: TextFormatter | TextFormatter[];

    //ios上有效，控制键盘与编辑框的距离
    keyboardDistanceFromTextField?: number;

    accessibilityLabel?: string;

    /**
     * Callback that is called when the text input is blurred.
     */
    onBlur?(): void;

    /**
     * Callback that is called when the text input is blurred.
     */
    onFocus?(): void;

    /**
     * Callback that is called when text input ends.
     */
    onEndEditing?(text: string): void;

    /**
     * Callback that is called when the text input's text changes.
     * Changed text is passed as a single string argument to the callback handler.
     *
     * @param {string} text - Text content.
     * @param textInput
     */
    onChangeText?(text: string, textInput?: AbcTextInput): void;

    /**
     * Callback that is called when the text input's content size changes.
     *
     * @param {Object} evt - Content size change event data.
     * @param {number} evt.nativeEvent.contentSize.width - Width of content.
     * @param {number} evt.nativeEvent.contentSize.height - Height of content.
     */
    onContentSizeChange?(evt: {
        contentSize: {
            width: number;
            height: number;
        };
    }): void;

    /**
     * Callback that is called when keyboard popup
     *
     * @param {Object} evt - Keyboard will show event data.
     * @param {number} evt.keyboardHeight - Keyboard height.
     */
    onKeyboardWillShow?(evt: KeyboardWillShowEvent): void;

    /**
     * Callback that is called when the text input selection is changed.
     *
     * @param {Object} evt -  Selection change event data.
     * @param {number} evt.nativeEvent.selection.start - Start index of selection
     * @param {number} evt.nativeEvent.selection.end - End index of selection.
     */
    onSelectionChange?(evt: {
        nativeEvent: {
            selection: {
                start: number;
                end: number;
            };
        };
    }): void;

    /**
     * 自定义键盘,在输入法激活后显示在键盘之上
     * @param text
     */
    customPanelBuilder?: PanelBuilder;
    customKeyboardBuilder?: KeyboardBuilder;

    //是否显示默认工具栏
    enableDefaultToolBar?: boolean; //true

    //在自定义键盘强制走系统键盘时，是否显示默认工具栏
    enableDefaultTooBarIfSystemKeyboard?: boolean; //false

    //在失焦时强制同步一次编辑框的值到native层
    syncTextOnBlur?: boolean; //false

    //全选时是否显示系统默认辅助气泡（ios）
    textShareMenuVisible?: boolean;

    onSyncChangeText?(text?: string): void;
}

interface TextInputResponse {
    text: string;
}

export class AbcTextInput extends AbcBaseComponent<AbcTextInputProps> {
    static focusInput?: AbcTextInput; // 当前处于焦点状态的输入框

    public _value?: string;

    public get value(): string | undefined {
        return this._value;
    }

    public set value(newValue: string | undefined) {
        this._value = newValue;
        // if (this._value !== newValue) {
        // this._onChangeText(newValue ?? "");
        this.props.onSyncChangeText?.(newValue);
        // }
    }

    public focused = false;

    public forceUseSystemKeyboard = false; //强制使用系统输入法键盘

    private _defaultValue?: string;

    private nativeTextInput?: HTMLDivElement | null;
    private inputAccessView?: AbcInputAccessoryView | null;
    private customKeyboardHandler: AbcCustomKeyboardView | null = null;

    constructor(props: AbcTextInputProps) {
        super(props);

        this._syncProps(this.props);
    }

    static defaultProps = {
        returnKeyType: "done",
        placeholderTextColor: Colors.T4,
        selectTextOnFocus: false,
        editable: true,
        syncTextOnBlur: false,
        textShareMenuVisible: true,
    };

    /**
     * @ignore
     */
    public componentWillUnmount(): void {
        this.blur();
    }

    /**
     *在当前光标处插入文本
     * @param value 要插入的文件
     */
    public insertText(value: string): void {
        if (!this.focused || _.isEmpty(value)) return;
        AbcUIManagerModule.callUIFunction(this.nativeTextInput, "insertText", [value]);
    }

    /**
     * 删除当前光标处字符
     */
    public deleteCurrentChar(): void {
        if (!this.focused) return;
        AbcUIManagerModule.callUIFunction(this.nativeTextInput, "deleteCurrentChar", []);
    }

    /**
     * 切换系统输入法和自定义键盘
     */
    async toggleSystemKeyboard(): Promise<void> {
        this.forceUseSystemKeyboard = !this.forceUseSystemKeyboard;
        if (this.focused) {
            this.blur();
            this.setState({});
            await delayed(100).toPromise();
            this.focus();
        }
    }

    /**
     * Get the content of `TextInput`.
     *
     * @returns {Promise<string>}
     */
    public getValue(): Promise<string> {
        return new Promise((resolve) => {
            AbcUIManagerModule.callUIFunction(this.nativeTextInput, "getValue", (res: TextInputResponse) => resolve(res.text));
        });
    }

    /**
     * Set the content of `TextInput`.
     *
     * @param {string} value - New content of TextInput
     * @returns {string}
     * @param options
     */
    public setValue(value: string, options?: { shouldChange?: boolean; syncDefaultValue?: boolean }): string {
        AbcUIManagerModule.callUIFunction(this.nativeTextInput, "setValue", [value]);
        if (options?.shouldChange) this._onChangeText(value);

        this.value = value;

        const syncDefaultValue = options?.syncDefaultValue ?? false;
        if (syncDefaultValue) {
            this._defaultValue = value;
        }
        return value;
    }

    /**
     * Make the `TextInput` focused.
     */
    public focus(): void {
        if (!this.nativeTextInput) return;
        AbcUIManagerModule.callUIFunction(this.nativeTextInput, "focusTextInput", []);
    }

    /**
     * Make the `TextInput` blured.
     */
    public blur(): void {
        if (!this.nativeTextInput) return;
        this.focused = false;
        AbcUIManagerModule.callUIFunction(this.nativeTextInput, "blurTextInput", []);
    }

    /**
     * Show input method selection dialog.
     */
    public showInputMethod(): void {
        if (!this.nativeTextInput) return;
        AbcUIManagerModule.callUIFunction(this.nativeTextInput, "showInputMethod", []);
    }

    /**
     *结束输入,会产生onEndEditing事件
     */
    public finishEditing(): void {
        this.blur();
        this.props.onEndEditing?.(this.value ?? "");
    }

    /**
     * Hide the input method selection dialog.
     */
    public hideInputMethod(): void {
        if (!this.nativeTextInput) return;
        AbcUIManagerModule.callUIFunction(this.nativeTextInput, "hideInputMethod", []);
    }

    /**
     * Clear the content of `TextInput`
     */
    public clear(): void {
        this.setValue("");
        // if (!this.nativeTextInput) return;
        // AbcUIManagerModule.callUIFunction(this.nativeTextInput, 'clear', []);
    }

    componentWillReceiveProps(nextProps: Readonly<AbcTextInputProps> /*, nextContext: any*/): void {
        if (nextProps.value !== this.props.value || nextProps.defaultValue != this.props.defaultValue) this._syncProps(nextProps);
    }

    private _syncProps(props: AbcTextInputProps) {
        this._defaultValue = this.focused ? this._defaultValue : props.defaultValue;
        if (!this.focused) {
            this.value = props.value ?? props.defaultValue ?? "";
        }
    }

    render(): JSX.Element {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const {
            style,
            resizeMode,
            textAlignVertical,
            defaultValue,
            customKeyboardBuilder,
            selectTextOnFocus,
            formatter,
            accessibilityLabel,
            ...nativeProps
        } = this.props;
        const { flex, fontWeight, fontSize, fontFamily, color, lineHeight, textAlign, ...containerStyle } = style;
        ignore(defaultValue, customKeyboardBuilder, formatter);

        let width: number | undefined;
        let height: number | undefined;

        if (typeof style.width === "number") {
            const paddingLeft = style.paddingLeft ?? style.paddingHorizontal ?? style.padding ?? 0;
            const paddingRight = style.paddingRight ?? style.paddingHorizontal ?? style.padding ?? 0;
            width = style.width - (paddingLeft + paddingRight);
        }

        if (typeof style.height === "number") {
            const paddingTop = style.paddingTop ?? style.paddingVertical ?? style.padding ?? 0;
            const paddingBottom = style.paddingBottom ?? style.paddingVertical ?? style.padding ?? 0;
            height = style.height - (paddingTop + paddingBottom);
        }

        const textStyle: Style = {
            flex: 1,
            fontWeight: fontWeight,
            fontSize: fontSize,
            fontFamily: fontFamily,
            color: color ?? Colors.black,
            lineHeight: lineHeight,
            textAlign: textAlign,
            width: width,
            height: height ?? 14,
        };

        //数字相关的键盘内容默认选中内容
        let _selectTextOnFocus = selectTextOnFocus;
        if (this.props.keyboardType == "numeric" || this.props.customKeyboardBuilder instanceof AbcNumberKeyboardBuilder) {
            _selectTextOnFocus = true;
        }

        ["underlineColorAndroid", "placeholderTextColor", "placeholderTextColors"].forEach((prop) => {
            (textStyle as any)[prop] = (this.props as any)[prop];
        });
        return (
            // @ts-ignore
            <div
                nativeName="AbcTextInput"
                // @ts-ignore
                style={[{ flex: flex }, containerStyle]}
                resizeMode={resizeMode ?? false}
            >
                <div
                    nativeName="AbcTextInputImpl"
                    ref={(ref) => {
                        this.nativeTextInput = ref;
                    }}
                    selectTextOnFocus={_selectTextOnFocus}
                    accessibilityLabel={`${AccessibilityLabelType.INPUT}-${accessibilityLabel ?? nativeProps.placeholder}`}
                    {...nativeProps}
                    defaultValue={this._defaultValue}
                    // @ts-ignore
                    textAlignVertical={textAlignVertical ?? "center"}
                    // @ts-ignore
                    style={textStyle}
                    // @ts-ignore
                    onKeyboardWillShow={(evt: KeyboardWillShowEvent) => this._onKeyboardWillShow(evt)}
                    onBlur={() => this._onBlur()}
                    onFocus={() => this._onFocus()}
                    onChangeText={(text: { text: string }) => this._onChangeText(text.text)}
                    onEndEditing={(evt: { text: string }) => this.props.onEndEditing?.(evt.text)}
                />
                {this._needInputAccessoryView() && (
                    <AbcInputAccessoryView
                        ref={(ref) => {
                            this.inputAccessView = ref;
                        }}
                        textInput={this}
                    />
                )}
                {this._needCustomKeyboardView() && (
                    <AbcCustomKeyboardView
                        textInput={this}
                        ref={(ref) => {
                            this.customKeyboardHandler = ref;
                        }}
                    />
                )}
            </div>
        );
    }

    private _needInputAccessoryView() {
        return this.props.editable;
    }

    private _needCustomKeyboardView() {
        const { customKeyboardBuilder } = this.props;
        return customKeyboardBuilder && !this.forceUseSystemKeyboard && (this.props.editable ?? true);
    }

    private _onBlur() {
        this.focused = false;
        if (AbcTextInput.focusInput == this) {
            AbcTextInput.focusInput = undefined;
        }

        this.inputAccessView?.setFocus(this.focused);
        this.customKeyboardHandler?.setFocus(this.focused);
        this.props.onBlur?.();

        const { syncTextOnBlur = false } = this.props;
        if (syncTextOnBlur) {
            this._syncProps(this.props);
            this.setValue(this.value ?? "");
            this.setState({});
        }
    }

    private _onFocus() {
        const focusChanged = !this.focused;
        /**
         * 解决自定义键盘切换到系统键盘后，焦点切换
         */
        this.focused = true;
        AbcTextInput.focusInput = this;
        this.inputAccessView?.setFocus(this.focused);
        this.customKeyboardHandler?.setFocus(this.focused);

        // 更新当前活跃的键盘类型
        if (this.props.keyboardType) {
            let keyboardTypeValue: KeyboardTypeValue = "default";

            // 将 AbcTextInput 的 keyboardType 映射到 AbcKeyboardAwarePanel 的 KeyboardTypeValue
            if (this.props.keyboardType === "numeric" || this.props.keyboardType === "phone-pad") {
                keyboardTypeValue = "number";
            } else if (this.props.keyboardType === "password") {
                keyboardTypeValue = "password";
            }

            // 更新 AbcKeyboardAwarePanel 的当前活跃键盘类型
            AbcKeyboardAwarePanel.setCurrentKeyboardType(keyboardTypeValue);
        }

        if (focusChanged) this.props.onFocus?.();
    }

    _formatText(oldValue: string, newValue: string): string {
        if (this.props.formatter) {
            if (_.isArray(this.props.formatter)) {
                for (const item of this.props.formatter) {
                    const formatValue = item(oldValue, newValue);
                    if (newValue != formatValue) return formatValue;
                }
            } else {
                return this.props.formatter(oldValue, newValue);
            }
        }
        return newValue;
    }

    private _onChangeText(text: string) {
        if (this.value == text) return;
        const _value = this._formatText(this.value!, text);
        if (_value !== text) {
            this.setValue(_value, { shouldChange: true });
            return;
        }

        this.value = _value;

        this.props.onChangeText?.(this.value, this);

        this.inputAccessView?.updateText(this.value);

        this.setState({});
    }

    private _onKeyboardWillShow(originEvt: KeyboardWillShowEvent) {
        const { onKeyboardWillShow } = this.props;
        const evt = originEvt;
        const { scale } = Dimensions.get("screen");
        if (Platform.OS === "android") {
            evt.keyboardHeight /= scale;
        }
        if (typeof onKeyboardWillShow === "function") {
            onKeyboardWillShow(evt);
        }
    }
}

interface AbcInputAccessoryViewProps {
    textInput: AbcTextInput; //绑定的输入法
}

interface AbcInputAccessoryViewState {
    value: string;
    focus: boolean;
}

const _isAndroid = Platform.OS !== "ios";

/**
 * 键盘工具栏,用于在键盘顶部显示自定义工具栏
 */
class AbcInputAccessoryView extends AbcBaseComponent<AbcInputAccessoryViewProps, AbcInputAccessoryViewState> {
    constructor(props: AbcInputAccessoryViewProps) {
        super(props);
        this.state = {
            value: this.props.textInput.value ?? "",
            focus: this.props.textInput.focused,
        };
    }

    /**
     * 输入变化,更新文本
     * @param value
     */
    updateText(value: string) {
        if (this.state.value === value) {
            return;
        }

        this.setState({ value: value });
    }

    /**
     *设置是否处于焦点状态
     */
    setFocus(focus: boolean) {
        if (this.state.focus == focus) {
            return;
        }

        this.setState({ focus: focus });
    }

    render() {
        const textInput = this.props.textInput;
        if (_isAndroid) if (!textInput.focused) return null;

        const { keyboardType, enableDefaultToolBar = true, enableDefaultTooBarIfSystemKeyboard = false } = textInput.props;

        let { customPanelBuilder } = textInput.props;
        if (!customPanelBuilder) {
            const defaultToolBar = enableDefaultToolBar || (enableDefaultTooBarIfSystemKeyboard && textInput.forceUseSystemKeyboard);
            if (!defaultToolBar) {
                return <View />;
            }

            if (keyboardType == "numeric") {
                // eslint-disable-next-line react/display-name
                customPanelBuilder = (textInput: AbcTextInput) => {
                    return <NumberFastInput textInput={textInput} />;
                };
            } else {
                if (_isAndroid) return <View />;

                // eslint-disable-next-line react/display-name
                customPanelBuilder = (/*ignored*/) => {
                    return <SimpleFinishPanel />;
                };
            }
        }

        const panel = customPanelBuilder(this.props.textInput);

        return (
            //注:此处大小设置为0,键盘工具栏脱离正常的排板(设置为0,0),显示在键盘上
            <div
                // @ts-ignore
                nativeName="AbcInputAccessoryView"
                style={{
                    backgroundColor: "transparent",
                    position: "absolute",
                    height: _isAndroid ? undefined : 0,
                }}
            >
                <View collapsable={false}>{panel}</View>
            </div>
        );
    }
}

interface AbcCustomKeyboardViewProps {
    textInput: AbcTextInput; //绑定的输入法
}

interface AbcCustomKeyboardViewState {
    focus: boolean;
}

class AbcCustomKeyboardView extends AbcBaseComponent<AbcCustomKeyboardViewProps, AbcCustomKeyboardViewState> {
    private _nativeCustomKeyboard: any;

    constructor(props: AbcCustomKeyboardViewProps) {
        super(props);

        this.state = { focus: false };
    }

    /**
     *设置是否处于焦点状态
     */
    setFocus(focus: boolean) {
        if (this.state.focus == focus) {
            return;
        }

        this.setState({ focus: focus });
    }

    render() {
        const { textInput } = this.props;
        const { customKeyboardBuilder } = textInput.props;
        return (
            <div
                // @ts-ignore
                nativeName="AbcCustomKeyboardView"
                keyboardHeight={customKeyboardBuilder!.keyboardHeight() + UiUtils.safeAreaBottomHeight()}
                ref={(ref) => (this._nativeCustomKeyboard = ref)}
                style={{
                    backgroundColor: "transparent",
                    position: "absolute",
                    height: _isAndroid ? undefined : 0,
                }}
            >
                <View collapsable={false}>
                    {textInput.focused && (
                        <View>
                            {customKeyboardBuilder?.build(textInput)}
                            <AbcSafeAreaBottom
                                showBottomSafeAreaWhenKeyboardShow={true}
                                bottomSafeAreaColor={customKeyboardBuilder?.bottomSafeAreaColor ?? Colors.keyboardBg}
                            />
                        </View>
                    )}
                </View>
            </div>
        );
    }
}

//////////////////////////////////////////////////////////////////////
///////////////////////以下定义几种常见的键盘面板///////////////////////////
//////////////////////////////////////////////////////////////////////
interface NumberFastInputProps {
    textInput: AbcTextInput;
}

///数字键盘面板
export class NumberFastInput extends AbcBaseComponent<NumberFastInputProps> {
    render(): JSX.Element {
        return (
            <View>
                <AbcDivider lineHeight={0.5} />
                <View
                    style={{
                        flexDirection: "row",
                        height: Sizes.listItemHeight,
                        justifyContent: "space-between",
                        backgroundColor: Colors.white,
                    }}
                >
                    <View
                        style={{
                            width: Sizes.dp64,
                            justifyContent: "center",
                            alignItems: "center",
                        }}
                        onClick={() => this._onClickFinish()}
                    >
                        <AbcIconfont name="arrow_down" size={Sizes.dp24} color={Colors.P1} />
                    </View>
                    <AbcStepper
                        iconStyle={{ borderTopWidth: 0, borderBottomWidth: 0 }}
                        value={_.isNaN(Number(this.props.textInput.value)) ? "" : this.props.textInput.value!}
                        onChange={this._onClickAdd1.bind(this)}
                    />
                    <View
                        style={[
                            {
                                width: Sizes.dp64,
                                justifyContent: "center",
                                alignItems: "center",
                            },
                        ]}
                        onClick={() => this._onClickFinish()}
                    >
                        <Text style={[TextStyles.t16MM]}>完成</Text>
                    </View>
                </View>
                <AbcDivider lineHeight={0.5} />
            </View>
        );
    }

    private _onClickFinish(): void {
        AbcTextInput.focusInput?.finishEditing();
    }

    private _onClickAdd1(value: number) {
        const { textInput } = this.props;
        let curValue = parseInt(textInput.value ?? "");
        if (isNaN(curValue)) {
            curValue = 0;
        }
        const newValue = (curValue + value).toString();
        const _value = textInput._formatText(textInput.value!, newValue);
        if (_value === newValue) {
            textInput!.setValue(newValue, { shouldChange: true });
        }
    }
}

///简单面板,只显示一个完成按钮
export class SimpleFinishPanel<T> extends AbcBaseComponent<T> {
    render(): JSX.Element {
        return (
            <View
                style={{
                    flexDirection: "row",
                    height: Sizes.listItemHeight,
                    justifyContent: "flex-end",
                    backgroundColor: Colors.white,
                    borderColor: Colors.dividerLineColor,
                    borderTopWidth: 0.5,
                    borderRadius: DeviceUtils.isIOS() ? 1 : undefined, //IOS iphonex等机型上0.5的边线显示问题,加个radius就好了(未分析原因)
                }}
            >
                <View
                    style={{
                        width: Sizes.dp64,
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                    onClick={() => this._onClickFinish()}
                >
                    <AbcIconfont name="arrow_down" size={Sizes.dp24} color={Colors.P1} />
                </View>
            </View>
        );
    }

    protected _onClickFinish(): void {
        AbcTextInput.focusInput?.blur();
    }
}

///切换输入法面板
interface ToggleSystemKeyboardPanelProps {
    textInput?: AbcTextInput | null;
}
export class ToggleSystemKeyboardPanel extends SimpleFinishPanel<ToggleSystemKeyboardPanelProps> {
    render(): JSX.Element {
        return (
            <View
                style={{
                    flexDirection: "row",
                    height: Sizes.listItemHeight,
                    justifyContent: "space-between",
                    backgroundColor: Colors.white,
                    borderColor: Colors.dividerLineColor,
                    borderTopWidth: 0.5,
                    borderRadius: DeviceUtils.isIOS() ? 1 : undefined, //IOS iphonex等机型上0.5的边线显示问题,加个radius就好了(未分析原因)
                }}
            >
                {!!this.props?.textInput && (
                    <View>
                        <AbcText
                            style={[
                                TextStyles.t16NM.copyWith({ lineHeight: Sizes.listItemHeight }),
                                {
                                    textAlign: "center",
                                    paddingLeft: Sizes.dp8,
                                },
                            ]}
                            onClick={() => {
                                this.props?.textInput?.toggleSystemKeyboard();
                            }}
                        >
                            {this.props?.textInput?.forceUseSystemKeyboard ? "简拼输入法" : "系统输入法"}
                        </AbcText>
                    </View>
                )}
                <View
                    style={{
                        width: Sizes.dp64,
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                    onClick={() => this._onClickFinish()}
                >
                    <AbcIconfont name="arrow_down" size={Sizes.dp24} color={Colors.P1} />
                </View>
            </View>
        );
    }
}

///简单面板带一个确定按钮
interface SimpleConformKeyboardPanelProps {
    textInput?: AbcTextInput | null;
    onClick?(): void;
}
export class SimpleConformKeyboardPanel extends SimpleFinishPanel<SimpleConformKeyboardPanelProps> {
    render(): JSX.Element {
        const { textInput, onClick } = this.props;
        return (
            <View
                style={[
                    Sizes.paddingLTRB(Sizes.listHorizontalMargin, Sizes.dp12, Sizes.listHorizontalMargin, Sizes.dp12),
                    {
                        borderTopWidth: 1,
                        flexDirection: "row",
                        backgroundColor: Colors.white,
                        borderColor: Colors.dividerLineColor,
                    },
                ]}
            >
                <View style={[ABCStyles.rowAlignCenter, { flex: 1, justifyContent: "space-between" }]}>
                    <AbcView
                        style={{
                            justifyContent: "center",
                            alignItems: "center",
                            padding: Sizes.dp8,
                            height: Sizes.dp28,
                        }}
                        onClick={() => {
                            textInput?.blur();
                            this.setState({});
                        }}
                    >
                        <AbcIconfont name="arrow_down" size={Sizes.dp24} color={Colors.P1} />
                    </AbcView>
                    <AbcView
                        onClick={() => {
                            onClick?.();
                        }}
                    >
                        <Text style={[TextStyles.t14MM, { lineHeight: Sizes.dp20 }]}>确定</Text>
                    </AbcView>
                </View>
            </View>
        );
    }
}

export class AbcToggleSystemKeyboardPanel extends ToggleSystemKeyboardPanel {
    render(): JSX.Element {
        return (
            <View
                style={[
                    ABCStyles.rowAlignCenterSpaceBetween,
                    ABCStyles.bottomLine,
                    {
                        height: Sizes.dp44,
                        backgroundColor: Colors.keyboardBg,
                        paddingHorizontal: Sizes.dp8,
                        borderColor: Colors.P1,
                    },
                ]}
            >
                <View
                    style={{
                        width: Sizes.dp64,
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                    onClick={() => this._onClickFinish()}
                >
                    <AbcIconfont name="arrow_down" size={Sizes.dp24} color={Colors.T4} />
                </View>
                {!!this.props?.textInput && (
                    <View style={ABCStyles.rowAlignCenter}>
                        <AbcText
                            style={[
                                TextStyles.t14NT1.copyWith({ lineHeight: Sizes.dp20 }),
                                {
                                    textAlign: "center",
                                },
                            ]}
                            onClick={() => {
                                this.props?.textInput?.toggleSystemKeyboard();
                            }}
                        >
                            {this.props?.textInput?.forceUseSystemKeyboard ? "简拼输入法" : "系统输入法"}
                        </AbcText>
                    </View>
                )}
            </View>
        );
    }
}
