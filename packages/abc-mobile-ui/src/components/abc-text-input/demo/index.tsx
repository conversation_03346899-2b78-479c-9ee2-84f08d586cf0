import React, { useState, ReactElement } from "react";
import { View, Text, StyleSheet } from "@hippy/react";
import { AbcTextInput } from "../index";
import { Colors, Sizes } from "@app/theme";

const styles = StyleSheet.create({
    container: {
        padding: Sizes.dp16,
        backgroundColor: Colors.white,
    },
    title: {
        fontSize: Sizes.dp16,
        fontWeight: "bold",
        marginBottom: Sizes.dp8,
        marginTop: Sizes.dp16,
    },
    input: {
        marginBottom: Sizes.dp16,
    },
    valueText: {
        fontSize: Sizes.dp14,
        color: Colors.T1,
        marginBottom: Sizes.dp8,
    },
});

export function AbcTextInputDemo(): ReactElement {
    const [text1, setText1] = useState("");
    const [text2, setText2] = useState("");
    const [text3, setText3] = useState("");

    return (
        <View style={styles.container}>
            <Text style={styles.title}>基础用法</Text>
            <AbcTextInput style={styles.input} placeholder="请输入内容" value={text1} onChangeText={setText1} />
            <Text style={styles.valueText}>当前值: {text1}</Text>

            <Text style={styles.title}>数字键盘</Text>
            <AbcTextInput style={styles.input} placeholder="请输入数字" keyboardType="numeric" value={text2} onChangeText={setText2} />

            <Text style={styles.title}>禁用状态</Text>
            <AbcTextInput style={styles.input} placeholder="禁用状态的输入框" editable={false} value={text3} onChangeText={setText3} />
        </View>
    );
}
