import React from "react";
import { View, Text } from "@hippy/react";
import { AbcTag } from "../index";
import { Colors, Sizes, TextStyles } from "@app/theme";
import { AbcText } from "../../abc-text";
import { pxToDp } from "@app/utils";
import { AbcFlex } from "../../abc-flex";

export function AbcTagDemo() {
    return (
        <View>
            <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>基础用法</AbcText>
            <AbcFlex gap={pxToDp(16)}>
                <AbcTag
                    text={"停售"}
                    textStyle={TextStyles.t10NT2.copyWith({ lineHeight: Sizes.dp30 })}
                    style={{
                        width: Sizes.dp60,
                        height: Sizes.dp30,
                        borderWidth: Sizes.dp1,
                        ...Sizes.paddingLTRB(Sizes.dp4),
                    }}
                />

                <AbcTag
                    text={"文本"}
                    style={{
                        width: Sizes.dp60,
                        height: Sizes.dp30,
                        ...Sizes.paddingLTRB(Sizes.dp4),
                        borderColor: Colors.mainColor,
                        borderRadius: Sizes.dp24,
                    }}
                    textStyle={[TextStyles.t12NM.copyWith({ lineHeight: Sizes.dp30, color: Colors.mainColor })]}
                />
            </AbcFlex>
        </View>
    );
}
