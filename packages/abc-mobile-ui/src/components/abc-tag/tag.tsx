/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/7/1
 */

import { Style, Text, View } from "@hippy/react";
import { AbcBaseComponent } from "../abc-base-component";
import { AbcView } from "../abc-view";
import { ABCStyles, Color, Colors, flattenStyles, hexa2hex, Sizes, TextStyles } from "@app/theme";
import React from "react";
import { AbcSvg } from "../abc-svg";
import { AbcAssetImage } from "../abc-asset-image";

interface AbcTagProps {
    checked?: boolean;
    style?: Style | Style[];
    text?: string;
    textStyle?: Style | Style[];
    onClick?(): void;
    extendAfterView?: JSX.Element | JSX.Element[];
}

export class AbcTag extends AbcBaseComponent<AbcTagProps> {
    constructor(props: AbcTagProps) {
        super(props);
    }

    render(): JSX.Element {
        const { checked, text, style, onClick, textStyle, extendAfterView } = this.props;
        return (
            <AbcView
                style={[
                    {
                        borderWidth: 0.5,
                        borderColor: Colors.P3,
                        borderRadius: Sizes.dp3,
                    },
                    Sizes.paddingLTRB(Sizes.dp8, 0),
                    Sizes.marginLTRB(Sizes.dp4, Sizes.dp3),
                    flattenStyles(style),
                    checked
                        ? {
                              borderColor: Colors.mainColor,
                          }
                        : {},
                ]}
                onClick={() => {
                    onClick?.();
                }}
            >
                <Text
                    style={[
                        checked ? TextStyles.t14NM : TextStyles.t14NT2,
                        {
                            textAlign: "center",
                            lineHeight: Sizes.dp28,
                        },
                        flattenStyles(textStyle),
                    ]}
                    numberOfLines={1}
                >
                    {text ?? ""}
                </Text>
                {extendAfterView && extendAfterView}
            </AbcView>
        );
    }
}

export class NewAbcTag extends AbcTag {
    render(): JSX.Element {
        const { checked, text, style, onClick, textStyle, extendAfterView } = this.props;
        return (
            <AbcView
                style={[
                    {
                        borderRadius: Sizes.dp4,
                        backgroundColor: Colors.bg1,
                        overflow: "hidden",
                    },
                    Sizes.paddingLTRB(Sizes.dp8, 0),
                    Sizes.marginLTRB(Sizes.dp4, Sizes.dp3),
                    flattenStyles(style),
                    checked
                        ? {
                              backgroundColor: Colors.theme2Mask8,
                          }
                        : {},
                ]}
                onClick={() => {
                    onClick?.();
                }}
            >
                <Text
                    style={[
                        checked ? TextStyles.t14NM : TextStyles.t14NT2.copyWith({ color: Colors.t2 }),
                        {
                            textAlign: "center",
                            lineHeight: Sizes.dp28,
                        },
                        flattenStyles(textStyle),
                    ]}
                    numberOfLines={1}
                >
                    {text ?? ""}
                </Text>
                {extendAfterView && extendAfterView}
            </AbcView>
        );
    }
}

interface CustomTagConfig {
    /**
     * 图标中的文字信息
     */
    text: string;

    /**
     * 标签尺寸
     */
    size?: number;

    /**
     * 文字颜色
     */
    color: Color;

    /**
     * 内层填充色
     */
    fillColor?: Color;

    /**
     * 外层填充色
     */
    strokeColor?: Color;

    /**
     * 图片地址
     * 使用此属性直接返回图片，非svg
     */
    iconUrl?: string;

    /**
     * 展示类型
     * 0:文字;1:图标
     */
    viewMode?: number;
}

interface AbcSvgTagProps {
    /**
     * 主题：rect/circle/polygon
     * 暂时启用 rect/circle/polygon
     */
    theme?: "rect" | "circle" | "polygon" | string;

    /**
     * 自定义tag的配置
     */
    customTagConfig?: CustomTagConfig;
}

export const AbcSvgTag: React.FC<AbcSvgTagProps> = (props) => {
    const {
        theme = "rect",
        customTagConfig = {
            text: "荐",
            size: 20,
            color: "#1489FF",
            fillColor: "#edf4fc",
            strokeColor: "#abd3fc",
        },
    } = props;

    const fillColor = customTagConfig.fillColor ?? hexa2hex(customTagConfig.color, 0.95);
    const strokeColor = customTagConfig.strokeColor ?? hexa2hex(customTagConfig.color, 0.7);

    const canUseCustomTagConfig = ["rect", "circle", "polygon"].includes(theme);

    if (customTagConfig.viewMode == 1 && !!customTagConfig.iconUrl) {
        return (
            <View>
                <AbcAssetImage
                    src={customTagConfig.iconUrl}
                    style={{ width: customTagConfig.size ?? 20, height: customTagConfig.size ?? 20 }}
                />
            </View>
        );
    }
    const firstText = () => {
        if (!canUseCustomTagConfig && !customTagConfig.text) return "";
        const text = customTagConfig.text ?? "";

        return text.slice(0, 1);
    };

    let themeSvgContent = "";
    let textSvgContent = "";
    if (theme == "circle") {
        themeSvgContent = `<circle
                            cx="10"
                            cy="10"
                            r="10"
                            fill="${strokeColor ?? ""}"
                           />
                           <circle
                            cx="10"
                            cy="10"
                            r="10"
                            transform="scale(0.9) translate(1,1)"
                            fill="${fillColor ?? ""}"
                           />`;
    } else if (theme == "rect") {
        themeSvgContent = `<rect
                            width="100%"
                            height="100%"
                            rx="2"
                            ry="2"
                            fill="${strokeColor ?? ""}"
                           />
                           <rect
                            width="100%"
                            height="100%"
                            rx="2"
                            ry="2"
                            transform="scale(0.9) translate(1,1)"
                            fill="${fillColor ?? ""}"
                           />`;
    } else if (theme == "polygon") {
        themeSvgContent = `<polygon
                            points="5,0  15,0  20,5  20,15  15,20  5,20  0,15  0,5"
                            fill="${strokeColor}"
                           />
                           <polygon
                            points="5,0  15,0  20,5  20,15  15,20  5,20  0,15  0,5"
                            transform="scale(0.9) translate(1,1)"
                            fill="${fillColor}"
                           />`;
    }
    if (!!firstText()) {
        textSvgContent = `<text
                           x="10"
                           y="14"
                           fill="${customTagConfig.color}"
                           font-size="${12}"
                           text-anchor="middle"
                           dominant-baseline="central"
                         >${firstText()}</text>`;
    }

    const svgContent = `<svg
                                version="1.1"
                                xmlns="http://www.w3.org/2000/svg" 
                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                width="${20}"
                                height="${20}"
                                viewBox="0 0 20 20">
                                    ${themeSvgContent}
                                    ${textSvgContent}
                               </svg>`;

    return (
        <View style={[ABCStyles.centerChild, { width: customTagConfig.size, height: customTagConfig.size }]}>
            <AbcSvg
                content={svgContent}
                style={[
                    {
                        width: customTagConfig.size,
                        height: customTagConfig.size,
                    },
                ]}
            />
        </View>
    );
};
