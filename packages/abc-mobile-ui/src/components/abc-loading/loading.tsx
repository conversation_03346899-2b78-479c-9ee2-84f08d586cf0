import React from "react";
import { Animation, Style, View } from "@hippy/react";
import { AbcAssetImage } from "../abc-asset-image";
import { ABCStyles, Sizes, TextStyles } from "@app/theme";
import { AbcBaseComponent } from "../abc-base-component";
import { AbcSizedBox } from "../abc-size-box";
import { AbcText } from "../abc-text";

interface LoadingViewProps {
    name?: string;
    ignoreTheme?: boolean;
    color: string;
    size: number;
    count: number;
    withAnimation?: boolean;

    style?: Style;
}

export class AbcLoading extends React.Component<LoadingViewProps> {
    static defaultProps = {
        color: "rgb(0, 0, 0)",
        count: 12,
        size: 40,
        withAnimation: true,
    };

    animation: Animation;

    constructor(props: LoadingViewProps) {
        super(props);

        this.animation = new Animation({
            startValue: 0, // 开始值
            toValue: 3.14 * 2, // 动画结束值
            duration: 1000, // 动画持续时长
            delay: 0, // 至动画真正开始的延迟时间
            mode: "timing", // 动画模式
            timingFunction: "ease-in-out", // 动画缓动函数
            repeatCount: "loop",
        });
    }

    UNSAFE_componentWillReceiveProps(nextProps: Readonly<LoadingViewProps>): void {
        if (nextProps.withAnimation == this.props.withAnimation) return;
        if (nextProps.withAnimation) {
            this.animation.start();
        } else {
            this.animation.pause();
        }
    }

    componentDidMount(): void {
        if (this.props.withAnimation) this.animation.start();
    }

    componentWillUnmount(): void {
        this.animation.destroy();
    }

    public render(): JSX.Element {
        const { size, style, name, ignoreTheme } = this.props;
        return (
            <AbcAssetImage
                name={name ?? "progress-middle"}
                ignoreTheme={ignoreTheme ?? false}
                style={[
                    { width: size, height: size },
                    style ?? {},
                    {
                        transform: [
                            {
                                rotateZ: this.animation,
                            },
                        ],
                    },
                ]}
            />
        );
    }
}

interface LoadingWithTextProps {
    text?: string;
    showLoading?: boolean;
    size?: number;
    withAnimation?: boolean;
}

export class AbcLoadingWithText extends AbcBaseComponent<LoadingWithTextProps> {
    constructor(props: LoadingWithTextProps) {
        super(props);
    }

    static defaultProps = {
        showLoading: true,
        size: Sizes.dp20,
        withAnimation: true,
    };

    public render(): JSX.Element {
        const { showLoading, withAnimation } = this.props;
        return (
            <View style={[ABCStyles.rowAlignCenter, { justifyContent: "center", paddingVertical: Sizes.dp16 }]}>
                {showLoading && <AbcLoading size={this.props.size} withAnimation={withAnimation} />}
                <AbcSizedBox width={Sizes.dp4} />
                <AbcText
                    style={[
                        TextStyles.t12NT4,
                        {
                            textAlign: "center",
                            ...Sizes.marginLTRB(0, Sizes.dp1),
                        },
                    ]}
                >
                    {this.props.text ?? "加载中..."}
                </AbcText>
            </View>
        );
    }
}
