/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/6/1
 *
 * @description
 */
import React from "react";
import { View } from "@hippy/react";
import { AbcBaseComponent } from "../abc-base-component";
import { keyboardListener, DeviceUtils } from "@app/utils";

interface KeyboardListenerProps {
    shouldHide?: (visible: boolean) => boolean;
}

export class AbcKeyboardListener extends AbcBaseComponent<KeyboardListenerProps> {
    private _keyboardVisible = false;

    constructor(props: KeyboardListenerProps) {
        super(props);
    }

    componentDidMount(): void {
        keyboardListener?.subscribe((visible) => {
            this._keyboardVisible = visible.visible;
            this.setState({});
        });
        // TODO addToDisposableBag
        // ?.addToDisposableBag(this);
    }

    render(): JSX.Element {
        const { shouldHide } = this.props;
        if (shouldHide) {
            this._keyboardVisible = shouldHide(this._keyboardVisible);
        }
        if (this._keyboardVisible) {
            return <View />;
        }
        return <View style={{ height: this._keyboardVisible ? 0 : undefined }}>{this.props.children}</View>;
    }
}

export class KeyboardHeightIOSView extends AbcBaseComponent {
    private _keyboardVisible = false;
    private _keyboardHeight = 0;
    componentDidMount(): void {
        if (DeviceUtils.isIOS())
            keyboardListener?.subscribe((visible) => {
                this._keyboardVisible = visible.visible;
                this._keyboardHeight = visible.keyboardHeight ?? 0;
                this.setState({});
            });
        // TODO addToDisposableBag
        // ?.addToDisposableBag(this);
    }

    render(): JSX.Element {
        if (!DeviceUtils.isIOS() || !this._keyboardVisible) return <View />;

        return <View />;
    }
}

export class KeyboardListenerViewFillBlack extends AbcBaseComponent {
    private _keyboardVisibleEvent: { visible: boolean; keyboardHeight?: number } = { visible: true, keyboardHeight: 0 };

    constructor(props: KeyboardListenerProps) {
        super(props);
    }

    componentDidMount(): void {
        keyboardListener?.subscribe((visible) => {
            this._keyboardVisibleEvent = visible;
            this.setState({});
        });
        // TODO addToDisposableBag
        // ?.addToDisposableBag(this);
    }

    render(): JSX.Element {
        if (this._keyboardVisibleEvent.visible) {
            return <View style={{ height: this._keyboardVisibleEvent.keyboardHeight }} />;
        }
        return <View style={{ height: 0 }} />;
    }
}
