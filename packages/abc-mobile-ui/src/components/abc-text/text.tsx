/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/5/30
 *
 * @description
 */

import { ClickableProps, LayoutableProps, Style, Text, View } from "@hippy/react";
import React from "react";
import { Const } from "@app/utils";
import { AbcIconfont } from "../index";
import { Sizes, Colors } from "@app/theme";

interface AbcTextProps extends LayoutableProps, ClickableProps {
    airTestKey?: string;
    /**
     * Used to truncate the text with an ellipsis after computing the text layout,
     * including line wrapping, such that the total number of lines does not exceed this number.
     * This prop is commonly used with `ellipsizeMode`.
     */
    numberOfLines?: number;
    /**
     * Determines what the opacity of the wrapped view.
     */
    opacity?: number;
    /**
     * When numberOfLines is set, this prop defines how text will be truncated.
     * numberOfLines must be set in conjunction with this prop.
     * This can be one of the following values:
     *
     * * head - The line is displayed so that the end fits in the container
     *          and the missing text at the beginning of the line is indicated by an ellipsis glyph.
     *          e.g., "...wxyz
     * * middle - The line is displayed so that the beginning and
     *            end fit in the container and the missing text in the middle is indicated
     *            by an ellipsis glyph.
     *            e.g., "ab...yz"
     * * tail - The line is displayed so that the beginning fits in the container
     *          and the missing text at the end of the line is indicated by an ellipsis glyph.
     *          e.g., "abcd..."
     * * clip - Lines are not drawn past the edge of the text container.
     *
     * The default is `tail`.
     */
    ellipsizeMode?: "head" | "middle" | "tail" | "clip";
    children: number | string | string[];
    text?: string;
    style?: Style | Style[];
    size?: number | string;
    fontWeight?: string;
    theme?: string;
}

// 字体大小与行高对照表 - 基于 sizes.ts 中的变量
const FONT_SIZE_LINE_HEIGHT_MAP: Record<number, number> = {
    [Sizes.font_size_tiny]: Sizes.line_height_tiny, // 11px -> 18dp
    [Sizes.font_size_mini]: Sizes.line_height_mini, // 12px -> 20dp
    [Sizes.font_size_small]: Sizes.line_height_small, // 13px -> 20dp
    [Sizes.font_size_normal]: Sizes.line_height_normal, // 14px -> 22dp
    [Sizes.font_size_large]: Sizes.line_height_large, // 16px -> 24dp
    [Sizes.font_size_largex]: Sizes.line_height_largex, // 18px -> 26dp
    [Sizes.font_size_xlarge]: Sizes.line_height_xlarge, // 20px -> 28dp
    [Sizes.font_size_xxlarge]: Sizes.line_height_xxlarge, // 22px -> 22dp
    [Sizes.font_size_xxxlarge]: Sizes.line_height_xxxlarge, // 24px -> 32dp
};

// 简化名称到完整变量名的映射
const SIZE_NAME_MAP: Record<string, number> = {
    tiny: Sizes.font_size_tiny,
    mini: Sizes.font_size_mini,
    small: Sizes.font_size_small,
    normal: Sizes.font_size_normal,
    large: Sizes.font_size_large,
    largex: Sizes.font_size_largex,
    xlarge: Sizes.font_size_xlarge,
    xxlarge: Sizes.font_size_xxlarge,
    xxxlarge: Sizes.font_size_xxxlarge,
};

// 计算行高的函数
const calculateLineHeight = (fontSize: number): number => {
    // 如果在对照表中有定义，直接返回
    if (FONT_SIZE_LINE_HEIGHT_MAP[fontSize]) {
        return FONT_SIZE_LINE_HEIGHT_MAP[fontSize];
    }

    // 如果没有定义，按规则计算：奇数+7，偶数+8
    return fontSize % 2 === 0 ? fontSize + 8 : fontSize + 7;
};

// 解析 size 参数，支持数字和字符串
const parseSize = (size: number | string | undefined): number | undefined => {
    if (typeof size === "number") {
        return size;
    }
    if (typeof size === "string") {
        // 先检查是否是预定义的名称
        if (SIZE_NAME_MAP[size]) {
            return SIZE_NAME_MAP[size];
        }
        // 尝试解析为数字
        const numericSize = parseInt(size, 10);
        if (!isNaN(numericSize)) {
            return numericSize;
        }
    }
    return undefined;
};

// 文本主题色映射
const TEXT_THEME_COLORS = [
    "black",
    "grey",
    "grey_light",
    "grey_pale",
    "white",
    "white_light",
    "white_pale",
    "success",
    "theme",
    "warning",
    "error",
];

// 解析主题色
const parseThemeColor = (theme: string | undefined, style: Style | Style[] | undefined): string | undefined => {
    if (!theme) return undefined;

    // 如果在文本主题色中，直接从 Colors 读取
    if (TEXT_THEME_COLORS.includes(theme)) {
        return (Colors as any)[theme];
    }

    // 如果不在文本主题色中，尝试从 Colors 中读取其他颜色
    if ((Colors as any)[theme]) {
        return (Colors as any)[theme];
    }

    // 如果都没有，从 style 中读取 color
    if (style) {
        const styleArray = Array.isArray(style) ? style : [style];
        for (const styleItem of styleArray) {
            if (styleItem && typeof styleItem === "object" && styleItem.color) {
                return styleItem.color as string;
            }
        }
    }

    return undefined;
};

export class AbcText extends React.Component<AbcTextProps> {
    private _lastClickTime?: Date;

    componentWillUnmount(): void {
        this._lastClickTime = undefined;
    }

    public render(): JSX.Element {
        let { onClick } = this.props;
        const { children, airTestKey, size, fontWeight, style, theme, ...otherProps } = this.props;
        const _airTestKey = airTestKey ?? this._createAirTestKey();

        // 处理样式
        let computedStyle = style;

        // 安全地解析字体大小
        let finalFontSize = parseSize(size);
        // 如果 size 属性没有提供有效值，尝试从 style 中获取
        if (!finalFontSize && style) {
            const styleArray = Array.isArray(style) ? style : [style];
            for (const styleItem of styleArray) {
                if (styleItem && typeof styleItem === "object" && styleItem.fontSize) {
                    finalFontSize = styleItem.fontSize as number;
                    break;
                }
            }
        }

        // 如果有字体大小、字体粗细或主题色需要设置
        const additionalStyle: Style = {};

        if (finalFontSize) {
            additionalStyle.fontSize = finalFontSize;
            additionalStyle.lineHeight = calculateLineHeight(Number(finalFontSize));
        }

        if (fontWeight) {
            additionalStyle.fontWeight = fontWeight;
        }

        // 处理主题色
        const themeColor = parseThemeColor(theme, style);
        if (themeColor) {
            additionalStyle.color = themeColor;
        }

        // 合并样式
        if (Object.keys(additionalStyle).length > 0) {
            if (Array.isArray(style)) {
                computedStyle = [...style, additionalStyle];
            } else if (style) {
                computedStyle = [style, additionalStyle];
            } else {
                computedStyle = additionalStyle;
            }
        } else {
            computedStyle = style;
        }

        if (onClick) {
            onClick = this._onClick.bind(this);
        }
        return (
            <Text accessibilityLabel={_airTestKey} {...otherProps} style={computedStyle} onClick={onClick}>
                {children}
            </Text>
        );
    }

    findStringInChildNodes = (children: React.ReactChild, searchString?: string) => {
        let str: string | undefined = undefined;
        React.Children.forEach(children, (child) => {
            if (!!str) return;
            if (child && typeof child === "object" && child.props) {
                str = this.findStringInChildNodes(child.props.children, searchString);
            } else if (typeof child === "string") {
                if (searchString && child.includes(searchString)) {
                    str = child;
                } else {
                    str = child;
                }
            }
        });
        return str;
    };

    private _createAirTestKey(): string | undefined {
        const children = this.props.children;
        //@ts-ignore
        const airTestKey = this.findStringInChildNodes(children);
        return airTestKey;
    }

    _onClick(): void {
        if (!this._lastClickTime || new Date().getTime() - this._lastClickTime.getTime() > Const.clickDebounceTime) {
            this.props.onClick?.();
        }
        this._lastClickTime = new Date();
    }
}

export class AbcMoreText extends AbcText {
    private showMore = false;

    constructor(props: AbcTextProps) {
        super(props);
    }

    private _onChangeShowMore(): void {
        this.showMore = !this.showMore;
        this.setState({});
    }

    render(): JSX.Element {
        let { onClick } = this.props;
        const { children, numberOfLines, style, ...others } = this.props;
        if (onClick) {
            onClick = this._onClick.bind(this);
        }
        return (
            <View style={[{ flexDirection: "row" }]}>
                {this.showMore ? (
                    <Text {...others} style={[{ flexShrink: 1, ...style }]} onClick={onClick} numberOfLines={undefined}>
                        {children}
                    </Text>
                ) : (
                    <Text {...others} style={[{ flexShrink: 1, ...style }]} onClick={onClick} numberOfLines={numberOfLines ?? 1}>
                        {children}
                    </Text>
                )}

                <View style={{ flexDirection: "row" }} onClick={this._onChangeShowMore.bind(this)}>
                    <View>{AbcIconfont.dropDown()}</View>
                    <Text
                        style={{
                            fontSize: 12,
                            fontWeight: "normal",
                            color: "#CED0DA",
                        }}
                    >
                        {this.showMore ? "更多" : "收起"}
                    </Text>
                </View>
            </View>
        );
    }
}
