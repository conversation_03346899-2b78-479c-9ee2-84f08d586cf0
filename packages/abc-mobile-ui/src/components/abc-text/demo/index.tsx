import React, { useState } from "react";
import { View } from "@hippy/react";
import { AbcText } from "../text";
import { FontWeights } from "@app/theme";
import { AbcScroll } from "../../abc-scroll";

export function AbcTextDemo() {
    const [selectedSize, setSelectedSize] = useState<string>("normal");
    const [isBold, setIsBold] = useState<boolean>(false);

    const fontSizes = [
        { name: "tiny", label: "tiny - 11px", value: "tiny" },
        { name: "mini", label: "mini - 12px", value: "mini" },
        { name: "small", label: "small - 13px", value: "small" },
        { name: "normal", label: "normal - 14px", value: "normal" },
        { name: "large", label: "large - 16px", value: "large" },
        { name: "largex", label: "largex - 18px", value: "largex" },
        { name: "xlarge", label: "xlarge - 20px", value: "xlarge" },
        { name: "xxlarge", label: "xxlarge - 22px", value: "xxlarge" },
        { name: "xxxlarge", label: "xxxlarge - 24px", value: "xxxlarge" },
    ];

    return (
        <AbcScroll style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
            <View style={{ padding: 16, backgroundColor: "#fff" }}>
                <View style={{ marginBottom: 16 }}>
                    <AbcText size="large" fontWeight={FontWeights.bold} style={{ marginBottom: 8 }}>
                        AbcText 组件演示
                    </AbcText>
                    <AbcText size="tiny" style={{ marginBottom: 4 }}>
                        文本组件，用于展示文本内容
                    </AbcText>
                </View>

                <View style={{ marginBottom: 16 }}>
                    <AbcText size="large" fontWeight={FontWeights.bold} style={{ marginBottom: 8 }}>
                        基础用法
                    </AbcText>
                    <AbcText size="tiny" style={{ marginBottom: 4 }}>
                        通过 size 指定尺寸，不同尺寸将字号和行高做了绑定
                    </AbcText>
                    <AbcText size="tiny" style={{ marginBottom: 4 }}>
                        通过 theme 指定主题
                    </AbcText>
                    <AbcText size="tiny" style={{ marginBottom: 4 }}>
                        通过 bold 进行加粗
                    </AbcText>
                </View>

                {/* 字体大小选择 */}
                <AbcText size="normal" style={{ marginBottom: 8 }}>
                    字体大小选择：
                </AbcText>
                <View style={{ flexDirection: "row", flexWrap: "wrap", marginBottom: 16 }}>
                    {fontSizes.map((size) => (
                        <View
                            key={size.name}
                            style={{
                                flexDirection: "row",
                                alignItems: "center",
                                marginRight: 16,
                                marginBottom: 8,
                            }}
                        >
                            <View
                                style={{
                                    width: 16,
                                    height: 16,
                                    borderRadius: 8,
                                    borderWidth: 2,
                                    borderColor: selectedSize === size.value ? "#1890ff" : "#d9d9d9",
                                    backgroundColor: selectedSize === size.value ? "#1890ff" : "transparent",
                                    marginRight: 8,
                                }}
                                onClick={() => setSelectedSize(size.value)}
                            />
                            <AbcText size="mini" style={{ color: "#666" }} onClick={() => setSelectedSize(size.value)}>
                                {size.label}
                            </AbcText>
                        </View>
                    ))}
                </View>

                {/* 加粗开关 */}
                <View style={{ flexDirection: "row", alignItems: "center", marginBottom: 16 }}>
                    <AbcText size="normal" style={{ marginRight: 8 }}>
                        加粗：
                    </AbcText>
                    <View
                        style={{
                            width: 44,
                            height: 24,
                            borderRadius: 12,
                            backgroundColor: isBold ? "#1890ff" : "#d9d9d9",
                            justifyContent: "center",
                            alignItems: isBold ? "flex-end" : "flex-start",
                            paddingHorizontal: 2,
                        }}
                        onClick={() => setIsBold(!isBold)}
                    >
                        <View
                            style={{
                                width: 20,
                                height: 20,
                                borderRadius: 10,
                                backgroundColor: "#fff",
                            }}
                        />
                    </View>
                </View>

                {/* 预览区域 */}
                <View
                    style={{
                        backgroundColor: "#f5f5f5",
                        padding: 16,
                        borderRadius: 8,
                        marginBottom: 24,
                    }}
                >
                    <AbcText size="normal" style={{ marginBottom: 8, color: "#666" }}>
                        当前效果预览：
                    </AbcText>
                    <AbcText size={selectedSize} fontWeight={isBold ? "bold" : "normal"} style={{ color: "#333" }}>
                        这是一段示例文本，用于展示 AbcText 组件的字体大小和样式效果。
                    </AbcText>
                </View>

                {/* 所有字体大小展示 */}
                <AbcText size="large" fontWeight={FontWeights.bold} style={{ marginBottom: 12 }}>
                    所有字体大小展示：
                </AbcText>
                <View style={{ backgroundColor: "#f9f9f9", padding: 16, borderRadius: 8 }}>
                    {fontSizes.map((size) => (
                        <View key={size.name} style={{ marginBottom: 12 }}>
                            <AbcText size="mini" style={{ color: "#999", marginBottom: 4 }}>
                                {size.label}
                            </AbcText>
                            <AbcText size={size.value} style={{ color: "#333" }}>
                                示例文本 - {size.label}
                            </AbcText>
                        </View>
                    ))}
                </View>

                {/* 字体粗细展示 */}
                <AbcText size="large" fontWeight={FontWeights.bold} style={{ marginTop: 24, marginBottom: 12 }}>
                    字体粗细展示：
                </AbcText>
                <View style={{ backgroundColor: "#f9f9f9", padding: 16, borderRadius: 8 }}>
                    <View style={{ marginBottom: 12 }}>
                        <AbcText size="mini" style={{ color: "#999", marginBottom: 4 }}>
                            normal (正常)
                        </AbcText>
                        <AbcText size="normal" fontWeight="normal" style={{ color: "#333" }}>
                            这是正常粗细的文本
                        </AbcText>
                    </View>
                    <View style={{ marginBottom: 12 }}>
                        <AbcText size="mini" style={{ color: "#999", marginBottom: 4 }}>
                            bold (加粗)
                        </AbcText>
                        <AbcText size="normal" fontWeight="bold" style={{ color: "#333" }}>
                            这是加粗的文本
                        </AbcText>
                    </View>
                </View>

                {/* 自定义数字大小展示 */}
                <AbcText size="large" fontWeight={FontWeights.bold} style={{ marginTop: 24, marginBottom: 12 }}>
                    自定义数字大小展示：
                </AbcText>
                <View style={{ backgroundColor: "#f9f9f9", padding: 16, borderRadius: 8 }}>
                    {[15, 17, 19, 21].map((size) => (
                        <View key={size} style={{ marginBottom: 12 }}>
                            <AbcText size="mini" style={{ color: "#999", marginBottom: 4 }}>
                                {`${size}px (自动计算行高: ${size % 2 === 0 ? size + 8 : size + 7}dp)`}
                            </AbcText>
                            <AbcText size={size} style={{ color: "#333" }}>
                                {`自定义 ${size}px 字体大小`}
                            </AbcText>
                        </View>
                    ))}
                </View>

                {/* 文本主题色展示 */}
                <AbcText size="large" fontWeight={FontWeights.bold} style={{ marginTop: 24, marginBottom: 12 }}>
                    文本主题色展示：
                </AbcText>
                <View style={{ backgroundColor: "#f9f9f9", padding: 16, borderRadius: 8 }}>
                    {[
                        { name: "black", label: "黑色", bg: "#fff" },
                        { name: "grey", label: "灰色", bg: "#fff" },
                        { name: "grey_light", label: "浅灰色", bg: "#fff" },
                        { name: "grey_pale", label: "极浅灰色", bg: "#fff" },
                        { name: "white", label: "白色", bg: "#333" },
                        { name: "white_light", label: "半透明白色", bg: "#333" },
                        { name: "white_pale", label: "浅透明白色", bg: "#333" },
                        { name: "success", label: "成功色", bg: "#fff" },
                        { name: "theme", label: "主题色", bg: "#fff" },
                        { name: "warning", label: "警告色", bg: "#fff" },
                        { name: "error", label: "错误色", bg: "#fff" },
                    ].map((themeItem) => (
                        <View key={themeItem.name} style={{ marginBottom: 12, backgroundColor: themeItem.bg, padding: 8, borderRadius: 4 }}>
                            <AbcText size="mini" style={{ color: "#999", marginBottom: 4 }}>
                                {`theme = ${themeItem.name} - ${themeItem.label}`}
                            </AbcText>
                            <AbcText size="normal" theme={themeItem.name}>
                                这是 {themeItem.label} 的文本示例
                            </AbcText>
                        </View>
                    ))}
                </View>

                {/* 属性详情 */}
                <AbcText size="large" fontWeight={FontWeights.bold} style={{ marginTop: 24, marginBottom: 12 }}>
                    属性详情：
                </AbcText>
                <View style={{ backgroundColor: "#f9f9f9", padding: 16, borderRadius: 8 }}>
                    {/* 表格头部 */}
                    <View
                        style={{
                            flexDirection: "row",
                            borderBottomWidth: 1,
                            borderBottomColor: "#e8e8e8",
                            paddingBottom: 8,
                            marginBottom: 8,
                        }}
                    >
                        <AbcText size="mini" fontWeight={FontWeights.bold} style={{ color: "#333", flex: 1, paddingRight: 12 }}>
                            属性名
                        </AbcText>
                        <AbcText size="mini" fontWeight={FontWeights.bold} style={{ color: "#333", flex: 2, paddingRight: 12 }}>
                            描述
                        </AbcText>
                        <AbcText size="mini" fontWeight={FontWeights.bold} style={{ color: "#333", flex: 2, paddingRight: 12 }}>
                            类型
                        </AbcText>
                        <AbcText size="mini" fontWeight={FontWeights.bold} style={{ color: "#333", flex: 1 }}>
                            默认值
                        </AbcText>
                    </View>

                    {/* 表格内容 */}
                    <View style={{ flexDirection: "row", paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: "#f0f0f0" }}>
                        <AbcText size="mini" style={{ color: "#333", flex: 1, paddingRight: 12 }}>
                            size
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#666", flex: 2, paddingRight: 12 }}>
                            字体大小，支持预定义名称或数字
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#999", flex: 2, paddingRight: 12 }}>
                            number | string
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#999", flex: 1 }}>
                            -
                        </AbcText>
                    </View>

                    <View style={{ flexDirection: "row", paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: "#f0f0f0" }}>
                        <AbcText size="mini" style={{ color: "#333", flex: 1, paddingRight: 12 }}>
                            fontWeight
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#666", flex: 2, paddingRight: 12 }}>
                            字体粗细
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#999", flex: 2, paddingRight: 12 }}>
                            number
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#999", flex: 1 }}>
                            -
                        </AbcText>
                    </View>

                    <View style={{ flexDirection: "row", paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: "#f0f0f0" }}>
                        <AbcText size="mini" style={{ color: "#333", flex: 1, paddingRight: 12 }}>
                            numberOfLines
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#666", flex: 2, paddingRight: 12 }}>
                            限制文本显示的行数
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#999", flex: 2, paddingRight: 12 }}>
                            number
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#999", flex: 1 }}>
                            -
                        </AbcText>
                    </View>

                    <View style={{ flexDirection: "row", paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: "#f0f0f0" }}>
                        <AbcText size="mini" style={{ color: "#333", flex: 1, paddingRight: 12 }}>
                            ellipsizeMode
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#666", flex: 2, paddingRight: 12 }}>
                            文本截断模式
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#999", flex: 2, paddingRight: 12 }}>
                            {`"head" | "middle" | "tail" | "clip"`}
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#999", flex: 1 }}>
                            tail
                        </AbcText>
                    </View>

                    <View style={{ flexDirection: "row", paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: "#f0f0f0" }}>
                        <AbcText size="mini" style={{ color: "#333", flex: 1, paddingRight: 12 }}>
                            opacity
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#666", flex: 2, paddingRight: 12 }}>
                            文本透明度
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#999", flex: 2, paddingRight: 12 }}>
                            number
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#999", flex: 1 }}>
                            -
                        </AbcText>
                    </View>

                    <View style={{ flexDirection: "row", paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: "#f0f0f0" }}>
                        <AbcText size="mini" style={{ color: "#333", flex: 1, paddingRight: 12 }}>
                            style
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#666", flex: 2, paddingRight: 12 }}>
                            自定义样式
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#999", flex: 2, paddingRight: 12 }}>
                            Style | Style[]
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#999", flex: 1 }}>
                            -
                        </AbcText>
                    </View>

                    <View style={{ flexDirection: "row", paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: "#f0f0f0" }}>
                        <AbcText size="mini" style={{ color: "#333", flex: 1, paddingRight: 12 }}>
                            children
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#666", flex: 2, paddingRight: 12 }}>
                            文本内容
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#999", flex: 2, paddingRight: 12 }}>
                            number | string | string[]
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#999", flex: 1 }}>
                            -
                        </AbcText>
                    </View>

                    <View style={{ flexDirection: "row", paddingVertical: 8 }}>
                        <AbcText size="mini" style={{ color: "#333", flex: 1, paddingRight: 12 }}>
                            onClick
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#666", flex: 2, paddingRight: 12 }}>
                            点击事件回调，带防抖功能
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#999", flex: 1 }}>
                            -
                        </AbcText>
                    </View>

                    <View style={{ flexDirection: "row", paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: "#f0f0f0" }}>
                        <AbcText size="mini" style={{ color: "#333", flex: 1, paddingRight: 12 }}>
                            theme
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#666", flex: 2, paddingRight: 12 }}>
                            文本主题色
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#999", flex: 2, paddingRight: 12 }}>
                            string
                        </AbcText>
                        <AbcText size="mini" style={{ color: "#999", flex: 1 }}>
                            -
                        </AbcText>
                    </View>
                </View>
            </View>
        </AbcScroll>
    );
}
