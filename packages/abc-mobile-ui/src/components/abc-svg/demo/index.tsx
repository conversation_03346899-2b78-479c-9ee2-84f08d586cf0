import React from "react";
import { View, Text } from "@hippy/react";
import { AbcSvg } from "../index";
import icons from "../../abc-iconfont/icons";
import { pxToDp } from "@app/utils";
import { AbcText } from "../../abc-text";
import { Colors, Sizes } from "@app/theme";
import { AbcFlex } from "../../abc-flex";

export function AbcSvgDemo() {
    return (
        <View>
            <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>基础用法</AbcText>
            <AbcFlex gap={pxToDp(20)}>
                <AbcSvg
                    content={`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                ${icons["s-user-fill"].replace(/currentColor/g, "red")}' +
                '</svg>`}
                    style={{ width: pxToDp(20), height: pxToDp(20) }}
                />

                <AbcSvg
                    content={`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                ${icons["s-setting-fill"].replace(/currentColor/g, "#7a8794")}' +
                '</svg>`}
                    style={{ width: pxToDp(20), height: pxToDp(20) }}
                />
            </AbcFlex>
        </View>
    );
}
