/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2022-10-09
 *
 * @description Svg显示
 *
 */
import React from "react";
import { Style } from "@hippy/react";

interface svgProps {
    content: string; //svg内容
    style?: Style | Style[];
}

export class AbcSvg extends React.Component<svgProps, {}> {
    private instance: HTMLDivElement | null = null;

    /**
     * @ignore
     */
    constructor(props: svgProps) {
        super(props);
    }

    /**
     * @ignore
     */
    public render(): JSX.Element {
        return (
            <div
                // @ts-ignore
                nativeName="SVGView"
                ref={(ref: HTMLDivElement) => {
                    this.instance = ref;
                }}
                {...this.props}
            />
        );
    }
}
