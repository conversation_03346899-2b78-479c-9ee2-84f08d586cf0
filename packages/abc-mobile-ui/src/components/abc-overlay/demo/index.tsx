import React from "react";
import { View, Text } from "@hippy/react";
import abcOverlay from "../overlay";
import { UniqueKey } from "@app/utils";
import { AbcButton } from "../../abc-button";

export function AbcOverlayDemo(): JSX.Element {
    const key = UniqueKey();

    function onOpen() {
        abcOverlay.show(<AbcButton onClick={onClose}>关闭 AbcOverlay</AbcButton>, key);
    }

    function onClose() {
        abcOverlay.hide(key);
    }

    return (
        <View>
            <Text>遮罩层示例</Text>
            <AbcButton onClick={onOpen}>显示 AbcOverlay</AbcButton>
        </View>
    );
}

export default AbcOverlayDemo;
