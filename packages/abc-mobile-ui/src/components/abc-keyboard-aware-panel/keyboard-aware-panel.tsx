import React from "react";
import { AbcBaseComponent } from "../abc-base-component";
import { View } from "@hippy/react";
import { AbcView } from "../abc-view";
import { keyboardListener, DeviceUtils, pxToDp, UiUtils } from "@app/utils";
import { AbcNavigator } from "../abc-navigator";
import { Sizes } from "@app/theme";
// 键盘类型定义
export type KeyboardTypeValue = "number" | "password" | "default";

// 目前用于键盘聚焦时，将弹窗内容向上顶，改变弹窗的高度（自定义键盘感知面板组件）
export class AbcKeyboardAwarePanel extends AbcBaseComponent<{
    children: React.ReactNode;
    topMaskHeight: number;
    keyboardAdjustFactor?: number; // 键盘高度调整因子，默认为1，表示完全按照键盘高度调整；小于1的值表示只减少部分空白区域
    minSpaceWhenKeyboardShow?: number; // 键盘显示时的最小空白区域高度，默认为0
    keyboardType?: KeyboardTypeValue; // 键盘类型，用于在Android上设置不同的键盘高度
    onClose?: () => void; // 关闭弹窗的回调
}> {
    // 静态属性，用于跟踪当前活跃的键盘类型
    static currentKeyboardType: KeyboardTypeValue = "default";

    // 静态方法，用于更新当前活跃的键盘类型
    static setCurrentKeyboardType(type: KeyboardTypeValue): void {
        AbcKeyboardAwarePanel.currentKeyboardType = type;
    }

    private _keyboardVisible = false;
    private _keyboardHeight = 0;

    componentDidMount(): void {
        keyboardListener.subscribe((e) => {
            this._keyboardVisible = e.visible;

            // 根据键盘类型设置不同的高度
            if (e.visible) {
                if (DeviceUtils.isAndroid()) {
                    // 使用当前活跃的键盘类型或props中传入的键盘类型
                    const effectiveKeyboardType = this.props.keyboardType || AbcKeyboardAwarePanel.currentKeyboardType;
                    this._keyboardHeight = this.getKeyboardHeightByType(effectiveKeyboardType);
                } else {
                    // iOS设备使用系统提供的键盘高度
                    if (AbcKeyboardAwarePanel.currentKeyboardType == "password") {
                        // 密码键盘高度调整为更小的值，确保上面的内容可见
                        this._keyboardHeight = Math.max(e.keyboardHeight ?? 0, pxToDp(366));
                    } else {
                        this._keyboardHeight = e.keyboardHeight ?? 0;
                    }
                }
            } else {
                this._keyboardHeight = 0;
            }

            this.setState({});
        });
        // TODO addToDisposableBag
        // ?.addToDisposableBag(this);
    }

    // 根据键盘类型获取键盘高度
    private getKeyboardHeightByType(keyboardType: KeyboardTypeValue): number {
        switch (keyboardType) {
            case "number":
                // 数字键盘通常较小
                return pxToDp(206);
            case "password":
                // 密码键盘
                return pxToDp(350);
            default:
                // 默认键盘
                return pxToDp(280);
        }
    }

    render(): JSX.Element {
        const { children, topMaskHeight, keyboardAdjustFactor = 1, minSpaceWhenKeyboardShow = 0, onClose } = this.props;

        // 计算顶部遮罩的高度 - 当键盘显示时减少高度
        // 使用 keyboardAdjustFactor 来控制减少的高度
        // 同时确保空白区域不小于 minSpaceWhenKeyboardShow
        let adjustedTopMaskHeight = topMaskHeight;

        if (this._keyboardVisible) {
            const reducedHeight = this._keyboardHeight * keyboardAdjustFactor;
            adjustedTopMaskHeight = Math.max(topMaskHeight - reducedHeight, minSpaceWhenKeyboardShow);
        }

        return (
            <View style={{ flex: 1, position: "relative" }}>
                <AbcView
                    style={{ height: adjustedTopMaskHeight + UiUtils.safeStatusHeight() }}
                    onClick={() => (onClose ? onClose() : AbcNavigator.pop())}
                />
                <View
                    style={{
                        flex: 1,
                        borderTopLeftRadius: Sizes.dp6,
                        borderTopRightRadius: Sizes.dp6,
                        overflow: "hidden",
                    }}
                >
                    {children}
                </View>
            </View>
        );
    }
}
