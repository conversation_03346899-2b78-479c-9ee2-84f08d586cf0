import React from "react";
import { Dimensions, Style, StyleSheet, View } from "@hippy/react";
import { Color, Colors } from "@app/theme";
import { AbcKeyboardListener } from "../abc-keyboard-listener";
import { Utils, UiUtils } from "@app/utils";
import { AbcTextInput } from "../abc-text-input";
import { AccessibilityLabelType } from "@app/utils";

interface AbcSafeAreaProps {
    airTestKey?: string;
    showStatusBar?: boolean;
    statusBarColor?: string;
    style?: Style;
    bottomSafeAreaColor?: Color;
    showBottomSafeArea?: boolean;
    showBottomSafeAreaWhenKeyboardShow?: boolean; //在键盘显示时，是否民显示底部安全区域
    touchEmptyBlurTextInput?: boolean; //点击空白区域关闭键盘-default = false
}

interface AbcSafeAreaState {
    isVertical: boolean;
}

// @ts-ignore
const styles = StyleSheet.create({
    container: {
        flex: 1,
        position: "relative",
    },
});

export default class AbcSafeArea extends React.Component<AbcSafeAreaProps, AbcSafeAreaState> {
    static defaultProps = {
        statusBarColor: Colors.white,
        bottomSafeAreaColor: Colors.transparent,
        showStatusBar: true,
        showBottomSafeArea: true,
    };

    constructor(props: AbcSafeAreaProps) {
        super(props);
        const { width } = Dimensions.get("window");
        const { height } = Dimensions.get("window");
        this.state = {
            isVertical: width < height,
        };
    }

    private _renderStatusBar(statusBarColor: string): JSX.Element {
        return <View style={[{ backgroundColor: statusBarColor || "#fff" }, { height: UiUtils.safeStatusHeight() }]} />;
    }

    public render(): JSX.Element {
        const {
            bottomSafeAreaColor,
            children,
            statusBarColor,
            style,
            showBottomSafeArea,
            airTestKey,
            showStatusBar,
            touchEmptyBlurTextInput,
        } = this.props;
        // @ts-ignore
        return (
            <View
                accessibilityLabel={`${AccessibilityLabelType.PAGE}-${airTestKey}`}
                style={[styles.container, style]}
                onLayout={(e) => {
                    // @ts-ignore
                    const { width, height } = e.layout;
                    this.setState({ isVertical: width < height });
                }}
                onClick={() => {
                    if (touchEmptyBlurTextInput) {
                        AbcTextInput.focusInput?.blur();
                    }
                }}
            >
                {showStatusBar && this._renderStatusBar(statusBarColor!)}
                <View style={{ flex: 1 }}>{children}</View>
                {showBottomSafeArea && (
                    <AbcKeyboardListener>
                        {Utils.isiPhoneX() && <View style={{ backgroundColor: bottomSafeAreaColor, height: 32 }} />}
                    </AbcKeyboardListener>
                )}
            </View>
        );
    }
}

export class AbcSafeAreaBottom extends AbcSafeArea {
    render(): JSX.Element {
        const { bottomSafeAreaColor, showBottomSafeAreaWhenKeyboardShow = false } = this.props;
        if (showBottomSafeAreaWhenKeyboardShow && Utils.isiPhoneX()) {
            return <View style={{ backgroundColor: bottomSafeAreaColor, height: 32 }} />;
        }
        return (
            <AbcKeyboardListener>
                {Utils.isiPhoneX() && <View style={{ backgroundColor: bottomSafeAreaColor, height: 32 }} />}
            </AbcKeyboardListener>
        );
    }
}

export class AbcSafeAreaTop extends AbcSafeArea {
    public render(): JSX.Element {
        const { bottomSafeAreaColor, style } = this.props;
        return (
            <View
                style={[{ ...style }]}
                onLayout={(e) => {
                    // @ts-ignore
                    const { width, height } = e.layout;
                    this.setState({ isVertical: width < height });
                }}
            >
                <AbcKeyboardListener>
                    {Utils.isiPhoneX() && (
                        <View
                            style={{
                                backgroundColor: bottomSafeAreaColor,
                                height: UiUtils.safeAreaBottomHeight(),
                            }}
                        />
                    )}
                </AbcKeyboardListener>
            </View>
        );
    }
}
