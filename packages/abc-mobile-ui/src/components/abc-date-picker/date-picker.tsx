import React, { useState, useMemo, useCallback } from "react";
import { StyleSheet, Text, View } from "@hippy/react";
import { AbcNavigator } from "../abc-navigator";
import { ABCStyles, Colors, Sizes, TextStyles } from "@app/theme";
import { AbcWheel } from "../abc-wheel";
import _ from "lodash";
import { getMaxDaysOfMonth } from "@app/utils";

interface DatePickerProps {
    initialDate?: Date;
    minDate?: Date;
    maxDate?: Date;
    height?: number;
    showDay?: boolean;

    onChange?(date: Date): void;
}

interface DatePickerInterface {
    show: (initialDate?: Date, options?: { minDate?: Date; maxDate?: Date; showDay?: boolean }) => Promise<Date>;
}

const styles = StyleSheet.create({
    titleBarContainer: {
        borderBottomWidth: 1,
        height: Sizes.listItemHeight,
        justifyContent: "space-between",
        borderColor: Colors.dividerLineColor,
        ...ABCStyles.rowAlignCenter,
        ...Sizes.paddingLTRB(Sizes.listHorizontalMargin, 0),
    },
});

export const AbcDatePicker: React.FC<DatePickerProps> & DatePickerInterface = (props) => {
    const { initialDate = new Date(), minDate, maxDate, height, showDay = true } = props;
    // 年列表
    const yearsList = useMemo(() => {
        const minY = minDate?.getFullYear() ?? 1990;
        const maxY = maxDate?.getFullYear() ?? 2099;
        return _.range(minY, maxY + 1);
    }, [minDate, maxDate]);

    // 状态
    const [year, setYear] = useState(initialDate.getFullYear());
    const [month, setMonth] = useState(initialDate.getMonth() + 1);
    const [day, setDay] = useState(initialDate.getDate());

    // 月份列表
    const monthsList = useMemo(() => {
        let minMonth = 1,
            maxMonth = 12;
        if (minDate && year === minDate.getFullYear()) minMonth = minDate.getMonth() + 1;
        if (maxDate && year === maxDate.getFullYear()) maxMonth = maxDate.getMonth() + 1;
        return _.range(minMonth, maxMonth + 1);
    }, [year, minDate, maxDate]);

    // 天数列表
    const daysList = useMemo(() => {
        let minDay = 1,
            maxDay = getMaxDaysOfMonth(year, month);
        if (minDate && year === minDate.getFullYear() && month === minDate.getMonth() + 1) minDay = minDate.getDate();
        if (maxDate && year === maxDate.getFullYear() && month === maxDate.getMonth() + 1) maxDay = maxDate.getDate();
        return _.range(minDay, maxDay + 1);
    }, [year, month, minDate, maxDate]);

    // 处理选择
    const handleYearChange = useCallback(
        (index: number) => {
            const newYear = yearsList[index];
            let minMonth = 1,
                maxMonth = 12;
            if (minDate && newYear === minDate.getFullYear()) minMonth = minDate.getMonth() + 1;
            if (maxDate && newYear === maxDate.getFullYear()) maxMonth = maxDate.getMonth() + 1;
            const newMonthsList = _.range(minMonth, maxMonth + 1);
            let newMonth = month;
            if (!newMonthsList.includes(month)) newMonth = newMonthsList[0];

            let minDay = 1,
                maxDay = getMaxDaysOfMonth(newYear, newMonth);
            if (minDate && newYear === minDate.getFullYear() && newMonth === minDate.getMonth() + 1) minDay = minDate.getDate();
            if (maxDate && newYear === maxDate.getFullYear() && newMonth === maxDate.getMonth() + 1) maxDay = maxDate.getDate();
            const newDaysList = _.range(minDay, maxDay + 1);
            let newDay = day;
            if (!newDaysList.includes(day)) newDay = newDaysList[0];

            setYear(newYear);
            setMonth(newMonth);
            setDay(newDay);
        },
        [yearsList, month, day, minDate, maxDate]
    );

    const handleMonthChange = useCallback(
        (index: number) => {
            const newMonth = monthsList[index];
            let minDay = 1,
                maxDay = getMaxDaysOfMonth(year, newMonth);
            if (minDate && year === minDate.getFullYear() && newMonth === minDate.getMonth() + 1) minDay = minDate.getDate();
            if (maxDate && year === maxDate.getFullYear() && newMonth === maxDate.getMonth() + 1) maxDay = maxDate.getDate();
            const newDaysList = _.range(minDay, maxDay + 1);
            let newDay = day;
            if (!newDaysList.includes(day)) newDay = newDaysList[0];

            setMonth(newMonth);
            setDay(newDay);
        },
        [monthsList, year, day, minDate, maxDate]
    );

    const handleDayChange = useCallback(
        (index: number) => {
            setDay(daysList[index]);
        },
        [daysList]
    );

    // 标题栏
    const creatTitleBar = useCallback(
        (callback?: () => void, cancel?: () => void) => (
            <View style={styles.titleBarContainer}>
                <Text
                    style={[TextStyles.t14MT2.copyWith({ lineHeight: Sizes.dp20, color: Colors.t3 })]}
                    onClick={() => (cancel ? cancel() : AbcNavigator.pop())}
                >
                    取消
                </Text>
                <Text style={[TextStyles.t14MM.copyWith({ lineHeight: Sizes.dp20 })]} onClick={callback}>
                    确定
                </Text>
            </View>
        ),
        []
    );

    // 工具栏
    const renderToolBar = () =>
        creatTitleBar(() => {
            AbcNavigator.pop(new Date([year, month, day].join("/")));
        });

    return (
        <View style={[ABCStyles.panelTopStyle]}>
            <View style={{ backgroundColor: Colors.white, height: height ?? 358 }}>
                {renderToolBar()}
                <View style={{ flexGrow: 1, flexDirection: "row", backgroundColor: "#D2D4DB" }}>
                    <AbcWheel
                        style={{ flex: 1, backgroundColor: Colors.white }}
                        initialIndex={_.findIndex(yearsList, (item: number) => item === year)}
                        items={yearsList.map((item) => `${item.toString()}年`)}
                        onSelectChanged={handleYearChange}
                    />
                    <AbcWheel
                        style={{ flex: 1, backgroundColor: Colors.white }}
                        initialIndex={_.findIndex(monthsList, (item: number) => item === month)}
                        items={monthsList.map((item) => `${item.toString()}月`)}
                        onSelectChanged={handleMonthChange}
                    />
                    {/* 如需显示天数，取消注释 */}
                    {showDay && (
                        <AbcWheel
                            style={{ flex: 1, backgroundColor: Colors.white }}
                            initialIndex={_.findIndex(daysList, (item: number) => item === day)}
                            items={daysList.map((item) => `${item.toString()}日`)}
                            onSelectChanged={handleDayChange}
                        />
                    )}
                </View>
            </View>
        </View>
    );
};

// 静态方法
AbcDatePicker.show = async (initialDate?: Date, options?: { minDate?: Date; maxDate?: Date; showDay?: boolean }): Promise<Date> => {
    const { minDate, maxDate, showDay } = options ?? {};
    return AbcNavigator.showBottomSheet(
        <AbcDatePicker initialDate={initialDate ?? new Date()} minDate={minDate} maxDate={maxDate} showDay={showDay} />
    );
};
