/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/11/29
 */
import React from "react";
import { View } from "@hippy/react";
import { AbcBaseComponent } from "../abc-base-component";
import abcOverlay from "../abc-overlay/overlay";
import { UiUtils, pxToDp, UniqueKey } from "@app/utils";
import { ABCStyles, Sizes, Colors } from "@app/theme";
import _ from "lodash";

interface AbcOverlayDialogProps {
    top?: number;
}
export interface AbcOverlayDialogOptions {
    key?: string;

    top?: number;
    left?: number;
    width?: number;
    height?: number;

    showTopMask?: boolean;

    onClickMask?(): void;
}
export class AbcOverlayDialog extends AbcBaseComponent {
    private static _overlayKeys: string[] = [];
    private static key: string;
    private static _overlayViews: Map<string, AbcOverlayDialogOptions | undefined> = new Map<string, AbcOverlayDialogOptions | undefined>();
    constructor(props: AbcOverlayDialogProps) {
        super(props);
    }

    static get hasOpenedOverlayDialog(): boolean {
        return !!this._overlayKeys.length;
    }

    static show<T>(element: JSX.Element, options?: AbcOverlayDialogOptions): void {
        const key = options?.key ?? UniqueKey();
        this.key = key;
        this._overlayKeys.push(key);
        this._overlayViews.set(key, options);
        const _element = this._createOverlayPanelView(element, { ...options, key });
        abcOverlay.show(_element, key);
    }

    static hide(): void {
        // @ts-ignore
        _.remove(this._overlayKeys, (item: any) => item == this.key);
        abcOverlay.hide(this.key);
    }

    static activeMaskClick(): void {
        const options = this._overlayViews.get(this.key);
        this._overlayViews.delete(this.key);
        options?.onClickMask?.();
    }

    private static _createOverlayPanelView(element: JSX.Element, options?: AbcOverlayDialogOptions): JSX.Element {
        const { top = pxToDp(200), left = 0, width = UiUtils.getScreenWidth(), showTopMask } = options ?? {};
        const height = options?.height ?? UiUtils.getScreenHeight() - top;
        return (
            <View
                style={[ABCStyles.absoluteFill, showTopMask ? { backgroundColor: Colors.maskColor } : {}]}
                onClick={() => options?.onClickMask?.()}
            >
                <View
                    style={{
                        position: "absolute",
                        top,
                        left,
                        width,
                        height,
                        backgroundColor: Colors.white,
                        borderTopLeftRadius: Sizes.dp10,
                        borderTopRightRadius: Sizes.dp10,
                        borderBottomLeftRadius: 1,
                        borderBottomRightRadius: 1,
                    }}
                    onClick={() => 1}
                >
                    {element}
                </View>
            </View>
        );
    }
}
