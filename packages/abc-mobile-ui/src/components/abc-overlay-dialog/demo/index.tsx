import React from "react";
import { View, Text } from "@hippy/react";
import { AbcOverlayDialog } from "../index";
import { AbcButton } from "../../abc-button";
import { UiUtils, pxToDp, UniqueKey } from "@app/utils";

export function AbcOverlayDialogDemo() {
    const key = UniqueKey();

    function onOpen() {
        AbcOverlayDialog.show(<AbcButton onClick={onClose}>关闭 AbcOverlay</AbcButton>, {
            key,
            width: pxToDp(100),
            height: pxToDp(40),
            top: UiUtils.safeStatusHeight() + pxToDp(200),
            left: pxToDp(120),
            showTopMask: true,
            onClickMask: () => {
                AbcOverlayDialog.hide();
            },
        });
    }

    function onClose() {
        AbcOverlayDialog.hide();
    }

    return (
        <View>
            <Text>基础用法</Text>
            <AbcButton onClick={onOpen}>显示 AbcOverlay</AbcButton>
        </View>
    );
}
