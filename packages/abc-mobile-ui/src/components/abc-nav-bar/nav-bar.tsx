import React from "react";
import {View} from "@hippy/react";
import {ABCStyles, Colors, Sizes, TextStyles} from "@app/theme";
import {pxToDp} from "@app/utils";
import {AbcIconfont} from "../abc-iconfont";
import {AbcText} from "../abc-text";

export interface NavBarProps {
    bgColor?: string;
    showLeftIcon?: boolean;
    leftIcon?: string;
    leftIconColor?: string;
    showLeftText?: boolean;
    leftText?: string;
    leftTextColor?: string;
    onClickLeft?: () => void;
    middleView?: React.ReactNode,
    rightView?: React.ReactNode,
}

export class AbcNavBar extends React.Component<NavBarProps> {
    render(): JSX.Element {
        const {
            bgColor = Colors.mainColor,
            showLeftIcon = true,
            leftIcon = "s-backto-line",
            leftIconColor = Colors.white,
            showLeftText = true,
            leftText = "返回",
            leftTextColor = Colors.white,
            onClickLeft,
            middleView,
            rightView,
        } = this.props;

        return (
            <View style={{flex: 1, flexDirection: "row", height: Sizes.dp44, backgroundColor: bgColor}}>
                <View
                    style={[ABCStyles.rowAlignCenter, {width: pxToDp(104), paddingHorizontal: Sizes.dp16}]}
                    onClick={() => onClickLeft?.()}
                >
                    {showLeftIcon && <AbcIconfont name={leftIcon} size={Sizes.dp24} color={leftIconColor}
                                                  style={{marginRight: Sizes.dp4}}/>
                    }
                    {showLeftText && (
                        <AbcText style={TextStyles.t16NT1.copyWith({color: leftTextColor})}>{leftText}</AbcText>
                    )}
                </View>
                <View style={{flex: 1, width: pxToDp(167)}}>
                    {middleView}
                </View>
                <View style={{width: pxToDp(104), paddingHorizontal: Sizes.dp16}}>
                    {rightView}
                </View>
            </View>
        );
    }
}