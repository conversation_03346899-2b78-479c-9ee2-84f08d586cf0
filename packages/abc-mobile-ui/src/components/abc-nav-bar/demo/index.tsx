import React from "react";
import { View } from "@hippy/react";
import { AbcNavBar } from "../index";
import {AbcText} from "../../abc-text";
import {ABCStyles, Colors, Sizes, TextStyles} from "@app/theme";
import {AbcIconfont} from "../../abc-iconfont";

const MiddleView: React.FC = () => {
    return (
        <View style={[ABCStyles.centerChild]}>
            <AbcText style={[TextStyles.t16MT1.copyWith( {color: Colors.white })]}>标题</AbcText>
            <AbcText style={[TextStyles.t12NT1.copyWith( {color: Colors.t6 })]}>智能审方·通过</AbcText>
        </View>
    )
}

const RightView: React.FC = () => {
    return (
        <View style={[ABCStyles.centerChild]}>
            <AbcIconfont name={"s-more-h-line"} size={Sizes.dp24} color={Colors.white}/>
        </View>
    )
}

export function AbcNavBarDemo() {
    function onClickLeft() {}

    return (
        <View>
            <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20, paddingLeft: Sizes.dp20 }}>基础用法</AbcText>
            <AbcNavBar
                onClickLeft={onClickLeft}
                middleView={<MiddleView />}
                rightView={<RightView />}
            />

            <View style={{ marginTop: Sizes.dp16 }}>
                <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20, paddingLeft: Sizes.dp20 }}>自定义背景色 bgColor</AbcText>
                <AbcNavBar
                    bgColor={"#ccc"}
                    onClickLeft={onClickLeft}
                    middleView={<MiddleView />}
                    rightView={<RightView />}
                />
            </View>

            <View style={{ marginTop: Sizes.dp16 }}>
                <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20, paddingLeft: Sizes.dp20 }}>隐藏 leftIcon</AbcText>
                <AbcNavBar
                    showLeftIcon={false}
                    onClickLeft={onClickLeft}
                    middleView={<MiddleView />}
                    rightView={<RightView />}
                />
            </View>

            <View style={{ marginTop: Sizes.dp16 }}>
                <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20, paddingLeft: Sizes.dp20 }}>隐藏 leftText</AbcText>
                <AbcNavBar
                    showLeftText={false}
                    onClickLeft={onClickLeft}
                    middleView={<MiddleView />}
                    rightView={<RightView />}
                />
            </View>

            <View style={{ marginTop: Sizes.dp16 }}>
                <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20, paddingLeft: Sizes.dp20 }}>自定义 leftText</AbcText>
                <AbcNavBar
                    leftText={"上一页"}
                    onClickLeft={onClickLeft}
                    middleView={<MiddleView />}
                    rightView={<RightView />}
                />
            </View>
        </View>
    );
}
