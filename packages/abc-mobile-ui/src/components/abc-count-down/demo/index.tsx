import React from "react";
import { View } from "@hippy/react";
import { AbcCountDown } from "../index";
import {Colors, Sizes, TextStyles} from "@app/theme";
import {AbcText} from "../../abc-text";


export function AbcCountDownDemo() {

    const duringTime = 3000
    function onFinish() {}

    return (
        <View>
            <View>
                <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>基础用法</AbcText>
                <AbcCountDown
                    duringTime={duringTime}
                    textStyle={TextStyles.t14NT1}
                    onFinish={onFinish}
                />
            </View>

            <View style={{marginTop: Sizes.dp16}}>
                <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>展示毫秒</AbcText>
                <AbcCountDown
                    duringTime={duringTime}
                    millisecond={true}
                    textStyle={TextStyles.t14NT1}
                    onFinish={onFinish}
                />
            </View>

            <View style={{marginTop: Sizes.dp16}}>
                <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>展示时刻标识</AbcText>
                <AbcCountDown
                    duringTime={duringTime}
                    lockFlag={"chinese"}
                    textStyle={TextStyles.t14NT1}
                    onFinish={onFinish}
                />
            </View>
        </View>
    );
}
