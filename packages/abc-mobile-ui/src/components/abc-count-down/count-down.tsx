import React from "react";
import _ from "lodash";
import { LayoutEvent, Style, Text, View } from "@hippy/react";
import { AbcBaseComponent } from "../abc-base-component";
import { ABCStyles, Colors } from "@app/theme";
import { numberWithFillZero, DifferenceDate, getDateDifference } from "@app/utils";

interface CountDownProps {
  /**
   * 倒计时的时长
   **/
  duringTime?: number; //时长
  /**
   * 倒计时的结束时间戳
   **/
  endTime?: Date;
  /**
   * 是否展示毫秒渲染
   **/
  millisecond?: boolean;
  textStyle?: Style;

  /**
   * 时刻标识（中文：【时分秒】，英文:【:】）
   */
  lockFlag?: string;
  /**
   * 结束时的回调
   **/
  onFinish?(): void;
  /**
   * 变化过程中的回调
   **/
  onChange?(arg1: Date): void;
}

interface CountDownState {
  date: DifferenceDate;
}
export class AbcCountDown extends AbcBaseComponent<CountDownProps, CountDownState> {
  private interval?: NodeJS.Timeout;
  private _charWidth = 0; //字符宽度，用于解决数字1宽度将小，引起闪的问题

  constructor(props: CountDownProps) {
    super(props);
    this.state = {
      date: new DifferenceDate(0),
    };
  }

  componentWillMount(): void {
    const { endTime, duringTime } = this.props;
    let _endTime = new Date();
    if (_.isNumber(duringTime)) {
      _endTime = new Date(new Date().getTime() + duringTime);
    } else if (_.isDate(endTime)) {
      _endTime = endTime;
    }
    const date = this.getDateData(_endTime);
    if (date) {
      this.setState({ date });
    }
  }

  componentDidMount(): void {
    const _intervalTime = this.props.millisecond ? 16.7 : 1000;
    this.interval = setInterval(() => {
      const _endTime = new Date(new Date().getTime() + this.state.date.differenceTime - _intervalTime);
      const date = this.getDateData(_endTime);
      if (date.differenceTime >= 0) {
        this.setState({ date });
      } else {
        this.setState({ date: new DifferenceDate(0) });
        this.props.onFinish?.();
        this.stop();
      }
    }, _intervalTime);
  }

  componentWillUnmount(): void {
    this.stop();
  }

  getDateData(endTime: Date): DifferenceDate {
    return getDateDifference(new Date(), endTime);
  }

  stop(): void {
    this.interval && clearInterval(this.interval);
  }

  rest(): void {
    this.setState({
      date: new DifferenceDate(0),
    });
  }

  render(): JSX.Element {
    const { textStyle = {}, lockFlag = "english" } = this.props;
    const minWidth = this._charWidth * 2;
    return (
        <View style={{ flexDirection: "row" }}>
          {/*以上注释*/}
          <View style={[ABCStyles.rowAlignCenter]}>
            {this.state.date.inHours > 0 && (
                <Text style={textStyle}>{numberWithFillZero(this.state.date.inHours)}</Text>
            )}
            {this.state.date.inHours > 0 && <Text style={textStyle}>{`${lockFlag == "english" ? ":" : " 时"}`}</Text>}

            <Text style={textStyle}>
              {numberWithFillZero(this.state.date.inMinutes - this.state.date.inHours * 60)}
            </Text>
            <Text style={textStyle}>{`${lockFlag == "english" ? ":" : " 分 "}`}</Text>
            <Text style={[textStyle ?? {}, { minWidth: minWidth }]}>
              {numberWithFillZero(this.state.date.inSeconds % 60)}
            </Text>
            {lockFlag != "english" && <Text style={textStyle}>{" 秒"}</Text>}
            {this.props.millisecond && (
                <View style={[ABCStyles.rowAlignCenter]}>
                  {lockFlag == "english" && <Text style={textStyle}>:</Text>}
                  <Text style={[textStyle ?? {}, { minWidth: minWidth }]}>
                    {numberWithFillZero(this.state.date.inMillisecond).substr(-1, 2)}
                  </Text>
                </View>
            )}
          </View>
          {/*隐藏元素用于计算数字宽度，解决1和其它数字变化时宽度变化问题*/}
          <Text
              style={[textStyle, { position: "absolute", color: Colors.transparent }]}
              onLayout={this._onCharWidthLayout.bind(this)}
          >
            0
          </Text>
        </View>
    );
  }

  private _onCharWidthLayout(evt: LayoutEvent): void {
    // @ts-ignore
    this._charWidth = evt.layout.width;
  }
}
