/**
 * Created by he<PERSON><PERSON> on 2020/3/12.
 */
import React from "react";
import { Style, View } from "@hippy/react";

import { Color, Colors, Sizes } from "@app/theme";
import { UiUtils } from "@app/utils";
import _ from "lodash";

interface DividerLineProps {
    lineMargin?: number;
    lineHeight?: number;
    style?: Style | Style[];
    color?: Color;
}

export default class AbcDivider extends React.Component<DividerLineProps> {
    static defaultProps = {
        lineMargin: 0,
        lineHeight: 0.5,
        color: Colors.dividerLineColor,
    };

    constructor(props: DividerLineProps) {
        super(props);
    }

    render(): JSX.Element {
        const { color, lineHeight, lineMargin, style } = this.props;
        return (
            <View
                style={{
                    backgroundColor: color,
                    height: lineHeight,
                    marginTop: lineMargin,
                    marginBottom: lineMargin,
                    ...style,
                }}
            />
        );
    }
}

interface GroupDividerProps {
    height?: number;
    borderLine?: boolean;
    backgroundColor?: Color;
}

export class GroupDivider extends React.Component<GroupDividerProps> {
    static defaultProps = {
        height: Sizes.dp8,
        borderLine: false,
    };

    render(): JSX.Element {
        const { height, borderLine, backgroundColor } = this.props;
        return (
            <View
                style={[
                    { height: height, backgroundColor: backgroundColor },
                    borderLine
                        ? {
                              borderTopWidth: 0.5,
                              borderBottomWidth: 0.5,
                              borderColor: Colors.dividerLineColor,
                          }
                        : {},
                ]}
            />
        );
    }
}

interface DashedDividerLineProps extends DividerLineProps {
    direction?: "row" | "column";
    height?: number; //column模式必传
    dashedWidth?: number;
}

export class DashedDividerLine extends React.Component<DashedDividerLineProps> {
    static defaultProps = {
        direction: "row",
        lineMargin: 0,
        lineHeight: 0.5,
        color: Colors.dividerLineColor,
        dashedWidth: Sizes.dp2,
    };

    private _dashedList: number[] = [];

    get isColumn(): boolean {
        return this.props.direction == "column";
    }

    constructor(props: DividerLineProps) {
        super(props);
    }

    UNSAFE_componentWillMount(): void {
        const { dashedWidth, height } = this.props;
        this._dashedList = _.range(
            0,
            (this.isColumn ? height ?? UiUtils.getScreenWidth() : UiUtils.getScreenWidth()) / (2 * (dashedWidth ?? Sizes.dp2))
        );
    }

    render(): JSX.Element {
        const { lineMargin, style, direction } = this.props;
        return (
            <View
                style={{
                    flexDirection: direction,
                    flex: this.isColumn ? undefined : 1,
                    overflow: "hidden",
                    marginTop: lineMargin,
                    marginBottom: lineMargin,
                    ...style,
                }}
            >
                {this._dashedList.map((index) => this._renderDashedItem(index))}
            </View>
        );
    }

    private _renderDashedItem(index: number): JSX.Element {
        const { color, lineHeight, dashedWidth = Sizes.dp2 } = this.props;
        return (
            <View
                key={index}
                style={[
                    {
                        backgroundColor: color,
                    },
                    this.isColumn
                        ? { height: dashedWidth, width: lineHeight, marginBottom: dashedWidth / 2 }
                        : { width: dashedWidth, height: lineHeight, marginRight: dashedWidth },
                ]}
            />
        );
    }
}

/**
 * 渐变分割线
 */
export class GradualChangeDividerLine extends React.Component<DividerLineProps> {
    static defaultProps = {
        lineMargin: 0,
        lineHeight: 0.5,
        color: Colors.dividerLineColor,
    };

    constructor(props: DividerLineProps) {
        super(props);
    }

    // private _iosDividerLineViewView(): JSX.Element {
    //     const { style } = this.props;
    //     return (
    //         <View
    //             style={[
    //                 createShadowStyle({
    //                     shadowColor: Colors.S1,
    //                     shadowOpacity: 0.04,
    //                     shadowRadius: Sizes.dp6,
    //                     //@ts-ignore
    //                     shadowOffset: { height: Sizes.dp4 },
    //                 }),
    //                 { paddingVertical: Sizes.dp4, marginBottom: Sizes.dp4, zIndex: 2, ...style },
    //             ]}
    //         />
    //     );
    // }

    private _androidDividerLineView(): JSX.Element {
        const { style, lineMargin } = this.props;
        return (
            <View
                style={{
                    backgroundColor: Colors.bg1,
                    height: Sizes.dp10,
                    marginTop: lineMargin,
                    marginBottom: lineMargin,
                    // shadowColor: Colors.bg1,
                    // shadowOpacity: 0.04,
                    // @ts-ignore
                    // shadowOffset: { width: 0, height: Sizes.dp5 },
                    ...style,
                }}
            />
        );
    }

    render(): JSX.Element {
        return <View>{this._androidDividerLineView()}</View>;
    }
}
