/**
 * 成都字节星球科技公司
 *
 * Created by <PERSON><PERSON><PERSON> on 2020-03-20
 *
 * @description
 *
 */

import React from "react";
import { View, Text, Style } from "@hippy/react";
import { Colors, Sizes, TextStyles } from "@app/theme";
import { AbcAssetImage } from "../abc-asset-image/index";
import { pxToDp } from "@app/utils";

interface ABCEmptyViewProps {
    tips?: string;
    style?: Style;
    type?: "search" | "other";
    imageName?: string;
}

export class AbcContentEmpty extends React.Component<ABCEmptyViewProps> {
    constructor(props: ABCEmptyViewProps) {
        super(props);
    }

    render(): JSX.Element {
        const { style } = this.props;
        return (
            <View
                style={
                    style ?? {
                        paddingTop: 80,
                        backgroundColor: Colors.white,
                        flex: 1,
                        alignItems: "center",
                    }
                }
            >
                <AbcAssetImage name={"search_empty"} style={{ width: Sizes.dp80, height: Sizes.dp80, marginBottom: Sizes.dp12 }} />
                <Text style={TextStyles.t14NT3}>{this.props.tips}</Text>
            </View>
        );
    }
}

export class AbcEmptyItemView extends React.Component<ABCEmptyViewProps> {
    constructor(props: ABCEmptyViewProps) {
        super(props);
    }

    static defaultProps = {
        tips: "没有找到相关结果",
        imageName: "other_empty_v2",
    };

    render(): JSX.Element {
        const { tips, imageName, style: { textAlign, ...style } = {}, type } = this.props;
        let _imageName = imageName;
        if (type) {
            switch (type) {
                case "search":
                    _imageName = "search_empty_v2";
                    break;
            }
        }

        return (
            <View
                style={{
                    flex: 1,
                    paddingTop: pxToDp(193),
                    backgroundColor: Colors.white,
                    alignItems: "center",
                    ...style,
                }}
            >
                <AbcAssetImage name={_imageName} style={{ width: Sizes.dp72, height: Sizes.dp72, marginBottom: Sizes.dp12 }} />
                <Text style={[TextStyles.t14NT3.copyWith({ color: Colors.t3 }), { lineHeight: Sizes.dp20, textAlign }]}>{tips}</Text>
            </View>
        );
    }
}
