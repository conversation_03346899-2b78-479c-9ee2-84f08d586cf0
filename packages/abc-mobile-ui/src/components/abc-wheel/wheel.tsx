/* eslint-disable no-underscore-dangle */

import React from "react";
import {Style, StyleSheet, Text, View} from "@hippy/react";
import {ABCStyles, Colors, Sizes, TextStyles} from "@app/theme";
import { AbcNavigator } from "../abc-navigator";

interface WheelProps {
    items: string[];
    initialIndex: number;
    onSelectChanged?: (index: number, value: string) => void;
    style?: Style | Style[];
}

export class AbcWheel extends React.Component<WheelProps, {}> {
    static defaultProps = {
        items: [],
        initialIndex: 0,
    };

    private instance: HTMLDivElement | null = null;

    /**
     * @ignore
     */
    constructor(props: WheelProps) {
        super(props);
    }

    /**
     * @ignore
     */
    public render(): JSX.Element {
        const { items, initialIndex, ...nativeProps } = this.props;

        return (
            <div
                nativeName="Wheel"
                ref={(ref: HTMLDivElement) => {
                    this.instance = ref;
                }}
                items={items}
                initialIndex={initialIndex}
                {...nativeProps}
                // @ts-ignore
                onSelectChanged={this._onSelectChanged.bind(this)}
            />
        );
    }

    private _onSelectChanged(evt: { index: number }) {
        const { onSelectChanged, items } = this.props;
        if (onSelectChanged) {
            onSelectChanged(evt.index, items[evt.index]);
        }
    }
}


const styles = StyleSheet.create({
    titleBarContainer: {
        borderBottomWidth: 1,
        height: Sizes.listItemHeight,
        justifyContent: "space-between",
        borderColor: Colors.dividerLineColor,
        ...ABCStyles.rowAlignCenter,
        ...Sizes.paddingLTRB(Sizes.listHorizontalMargin, 0),
    },
});
export class wheelHelper {
    static creatTitleBar(callback?: () => void, cancel?: () => void): JSX.Element {
        return (
            <View style={styles.titleBarContainer}>
                <Text
                    style={[TextStyles.t14MT2.copyWith({ lineHeight: Sizes.dp20, color: Colors.t3 })]}
                    onClick={() => {
                        cancel ? cancel() : AbcNavigator.pop();
                    }}
                >
                    取消
                </Text>
                <Text style={[TextStyles.t14MM.copyWith({ lineHeight: Sizes.dp20 })]} onClick={callback}>
                    确定
                </Text>
            </View>
        );
    }
}