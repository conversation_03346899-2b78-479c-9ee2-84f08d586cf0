import React, {useEffect, useRef} from "react";
import {View} from "@hippy/react";
import {pxToDp} from "@app/utils";
import {Colors, Sizes, ABCStyles, FontWeights} from "@app/theme";
import {AbcSafeAreaBottom} from "../abc-safe-area";
import {AbcNavigator} from "../abc-navigator";
import {TransitionType, BottomSheetOptions} from "../abc-navigator/navigator";
import {AbcView} from "../abc-view";
import {UiUtils} from "@app/utils"
import {AbcIconfont} from "../abc-iconfont";
import {AbcText} from "../abc-text";

export interface PopUpProps {
    visible: boolean;
    minHeight: number;
    onClose: () => void; // 点击遮罩层时触发
    maxHeight?: number;
    height?: number;
    round?: boolean;
    contentPadding?: number;
    title?: string; // 标题
    hiddenHeaderPaddingBottom?: boolean;
    titleJustifyContent?: string; // 标题和描述信息对齐方式， 默认左对齐
    description?: string // 描述内容
    showCloseIcon?: boolean;
    onCloseIcon?: () => void; // 点击关闭图标时触发
    options?: BottomSheetOptions;
    customView?: React.ReactNode;
}

export function AbcPopUp(props: PopUpProps) {
    const bottomSheetRef = useRef<any>(null);

    const {
        minHeight,
        maxHeight,
        height,
        contentPadding = Sizes.dp16,
        round = true,
        visible,
        title,
        hiddenHeaderPaddingBottom,
        titleJustifyContent = "flex-start",
        description,
        showCloseIcon,
        customView,
        options,
        onClose,
        onCloseIcon,
    } = props;

    let heightStyle = {}
    if (minHeight) {
        heightStyle = {
            minHeight: pxToDp(minHeight)
        }
    }

    if (maxHeight) {
        heightStyle = {
            ...heightStyle,
            maxHeight: pxToDp(maxHeight)
        }
    }

    if (height) {
        heightStyle = {
            ...heightStyle,
            height: pxToDp(height)
        }
    }

    function showHeader() {
        if (!title && !showCloseIcon) {
            return <View/>
        }

        return (
            <View
                style={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp16, Sizes.dp16, hiddenHeaderPaddingBottom ? Sizes.dp0 : Sizes.dp16), {flexDirection: "row"}]}>
                <View style={{flex: 1, alignItems: titleJustifyContent}}>
                    <View style={[ABCStyles.rowAlignCenter, {height: Sizes.dp24}]}>
                        <AbcText
                            style={{color: Colors.t1, fontWeight: FontWeights.medium, fontSize: Sizes.dp16}}>{title || ""}</AbcText>
                    </View>
                    {
                        description && (
                            <View style={[ABCStyles.rowAlignCenter, {height: Sizes.dp22}]}>
                                <AbcText
                                    style={{color: Colors.t2, fontWeight: FontWeights.normal, fontSize: Sizes.dp14}}>{description}</AbcText>
                            </View>
                        )
                    }
                </View>
                {showCloseIcon && (
                    <View onClick={onCloseIcon}>
                        <AbcIconfont size={Sizes.dp24} name={"s-closesheet-color"}></AbcIconfont>
                    </View>
                )}
            </View>
        )
    }

    useEffect(() => {
        if (visible) {
            const contentView = (
                <View style={{flex: 1, position: "relative"}}>
                    <AbcView
                        style={{height: UiUtils.getScreenHeight()}}
                        onClick={onClose}
                    />
                    <View style={{
                        ...heightStyle,
                        backgroundColor: Colors.white,
                        ...(round && {
                            borderTopLeftRadius: Sizes.border_radius_medium,
                            borderTopRightRadius: Sizes.border_radius_medium,
                        }),
                    }}>
                        {showHeader()}
                        <View style={{flex: 1, padding: contentPadding }}>
                            {customView}
                        </View>
                        <AbcSafeAreaBottom bottomSafeAreaColor={Colors.white}/>
                    </View>
                </View>
            );
            // 弹出弹窗
            const handler = AbcNavigator.navigateToPage(contentView, {
                transitionType: TransitionType.inFromBottom,
                backgroundColor: options?.backgroundColor ?? 0x33000000,
                justifyContent: options?.justifyContent ?? "flex-end",
                enableBackgroundAnimation: options?.enableBackgroundAnimation ?? true,
                dismissWhenTouchOutside: options?.dismissWhenTouchOutside ?? true, //点击到区域外自动隐藏
            })
            bottomSheetRef.current = handler
        } else {
            // 关闭弹窗
            if (bottomSheetRef.current) {
                AbcNavigator.pop()
                bottomSheetRef.current = null;
            }
        }
    }, [visible]);

    return null;
}