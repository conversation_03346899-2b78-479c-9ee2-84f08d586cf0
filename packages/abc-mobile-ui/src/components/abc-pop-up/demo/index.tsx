import React, { useState } from "react";
import { ScrollView, Text, View } from "@hippy/react";
import { AbcPopUp } from "../index";
import { AbcText } from "../../abc-text";
import { ABCStyles, Colors, Sizes, TextStyles } from "@app/theme";
import { AbcButton } from "../../abc-button";
import { AbcToast } from "../../abc-toast";

interface ActionsProps {
    actions?: Array<{
        name?: string;
        value?: string;
    }>;
    onSelected?: (item: any) => void;
}
const ActionsView: React.FC<ActionsProps> = ({ actions, onSelected }) => {
    if (!actions || !actions.length) return <View />;
    const [currentIndex, setCurrentIndex] = useState(-1);
    return (
        <View>
            {!!actions?.length && (
                <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
                    {actions?.map((item, index) => {
                        const isShowBottomLine = index < actions.length - 1;
                        return (
                            <View
                                key={item.name || item.value || index}
                                style={[
                                    isShowBottomLine ? ABCStyles.bottomLine : {},
                                    ABCStyles.rowAlignCenter,
                                    {
                                        justifyContent: "center",
                                        paddingVertical: Sizes.dp16,
                                        borderColor: Colors.border_color_light,
                                        backgroundColor: currentIndex === index ? Colors.cp_pressed : "",
                                        zIndex: 9999,
                                    },
                                ]}
                                onClick={() => {
                                    onSelected?.(item)
                                }}
                                onTouchDown={() => {
                                    setCurrentIndex(index);
                                }}
                                onTouchEnd={() => {
                                    setCurrentIndex(-1);
                                }}
                            >
                                <AbcText style={[TextStyles.t16NB, { lineHeight: Sizes.dp24 }]}>{item.name || ""}</AbcText>
                            </View>
                        );
                    })}
                </ScrollView>
            )}
        </View>
    );
};

export function AbcPopUpDemo() {
    const [visible, setVisible] = useState(false);
    const [visibleRound, setVisibleRound] = useState(false);
    const [visibleTitle, setVisibleTitle] = useState(false);
    const [visibleContentPadding_0, setVisibleContentPadding_0] = useState(false);

    function onCloseIcon() {
        AbcToast.show("点击关闭按钮");
    }

    const [actions, setActions] = useState([
        {
            name: "选项一",
            value: "1",
        },
        {
            name: "选项二",
            value: "2",
        },
        {
            name: "选项三",
            value: "3",
        },
    ]);

    function onSelected(item: any) {
        setVisibleContentPadding_0(false)
    }

    return (
        <View>
            <AbcText style={{color: Colors.T1, marginBottom: Sizes.dp20}}>基础用法</AbcText>
            <View>
                <AbcButton onClick={() => setVisible(true)}>AbcPopUp</AbcButton>
                <AbcPopUp
                    minHeight={300}
                    visible={visible}
                    title={"标题"}
                    hiddenHeaderPaddingBottom={true}
                    showCloseIcon={true}
                    onCloseIcon={onCloseIcon}
                    customView={(
                        <View>
                            <View style={{ height: Sizes.dp100 }}>自定义内容</View>
                            <View style={{ height: Sizes.dp100 }}>自定义内容</View>
                            <View style={{ height: Sizes.dp100 }}>自定义内容</View>
                            <View style={{ height: Sizes.dp100 }}>自定义内容</View>
                            <View style={{ height: Sizes.dp100 }}>自定义内容</View>
                        </View>
                    )}
                    onClose={() => {
                        setVisible(false);
                    }}
                />
            </View>

            <View style={{ marginTop: Sizes.dp16 }}>
                <AbcButton onClick={() => setVisibleRound(true)}>不展示圆角</AbcButton>
                <AbcPopUp
                    minHeight={300}
                    visible={visibleRound}
                    round={false}
                    customView={<View>不展示圆角</View>}
                    onClose={() => {
                        setVisibleRound(false);
                    }}
                />
            </View>

            <View style={{ marginTop: Sizes.dp16 }}>
                <AbcButton onClick={() => setVisibleTitle(true)}>展示标题/描述信息/删除按钮</AbcButton>
                <AbcPopUp
                    minHeight={300}
                    visible={visibleTitle}
                    title={"自定义标题"}
                    description={"描述信息"}
                    showCloseIcon={true}
                    onCloseIcon={onCloseIcon}
                    customView={<View>自定义内容</View>}
                    onClose={() => {
                        setVisibleTitle(false);
                    }}
                />
                <View style={{ marginTop: Sizes.dp16 }}>
                    <AbcButton onClick={() => setVisibleContentPadding_0(true)}>内容区域无padding</AbcButton>
                    <AbcPopUp
                        minHeight={300}
                        visible={visibleContentPadding_0}
                        title={"标题"}
                        description={"描述信息"}
                        showCloseIcon={true}
                        onCloseIcon={onCloseIcon}
                        contentPadding={0}
                        customView={<ActionsView actions={actions} onSelected={onSelected}></ActionsView>}
                        onClose={() => {
                            setVisibleContentPadding_0(false);
                        }}
                    />
                </View>
            </View>
        </View>
    );
}
