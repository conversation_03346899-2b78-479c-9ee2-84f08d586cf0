import React from "react";
import { View, Text } from "@hippy/react";
import { AbcList } from "../index";
import {Colors, Sizes} from "@app/theme";

function renderRow(data: {name: string}) {
    return (
        <View style={{
                flex: 1,
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                height: Sizes.dp40,
                borderBottomWidth: Sizes.dp1,
                borderColor: Colors.border_color_light
            }}
        >
            <Text>{ data.name }</Text>
        </View>
    )
}

interface listInterface {
    name: string,
    id: string
}

export function AbcListDemo() {
    let list: listInterface[] = [];
    for(let i= 1; i <= 100; i++) {
        list.push({
            name: `列表第${i}项`,
            id: String(i)
        })
    }


    return (
        <View>
            <Text style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>基础用法</Text>
            <AbcList
                scrollEventThrottle={300}
                numberOfRows={list.length}
                dataSource={list}
                getRowKey={(index) => list[index]?.id }
                renderRow={(data) => {
                    return renderRow(data);
                }}
            />
        </View>
    );
}
