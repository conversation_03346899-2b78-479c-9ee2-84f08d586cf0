import React, { useState } from "react";
import { View, Text } from "@hippy/react";
import { AbcRadioButton, AbcRadioGroup2 } from "../index";
import { ABCStyles, Sizes, TextStyles } from "@app/theme";
import { AbcFlex } from "../../abc-flex";

export function AbcRadioButtonDemo() {
    const [value, setValue] = useState(0);
    function handleChange(val: number | string) {
        setValue(val as number);
    }
    return (
        <View>
            <Text>基础用法</Text>
            <View style={{ backgroundColor: "#fff", padding: Sizes.dp16 }}>
                <AbcRadioGroup2 value={value} gap={Sizes.dp8} direction={"row"} onChange={handleChange}>
                    <AbcRadioButton title={"选项1222"} label={0} width={Sizes.dp100} />
                    <AbcRadioButton title={"选项2"} label={1} />
                    <AbcRadioButton title={"选项3"} label={2} />
                    <AbcRadioButton title={"选项4"} label={3} disable={true} />
                    <AbcRadioButton title={"选项5"} label={4} />
                    <AbcRadioButton title={"选项6"} label={5} />
                </AbcRadioGroup2>
            </View>
        </View>
    );
}
