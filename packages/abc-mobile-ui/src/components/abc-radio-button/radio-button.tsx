import React, { useContext } from "react";
import { View, Text } from "@hippy/react";
import { ABCStyles, Colors, Sizes, TextStyles } from "@app/theme";
import { AbcRadioGroupContext } from "./radio-group";

interface AbcRadioButtonProps {
    label: string | number;
    disable?: boolean;
    title?: string;
    width?: number;
    height?: number;
    children?: JSX.Element;
}

export const AbcRadioButton: React.FC<AbcRadioButtonProps & { style?: any }> = ({
    label,
    disable,
    title,
    width,
    height,
    style,
    children,
}) => {
    const { value, onClick, cancelable } = useContext(AbcRadioGroupContext);
    const getRadioButtonColors = (): { bgColor: string; bdColor: string; textColor: string; lineHeight: number } => {
        if (disable) {
            return {
                bgColor: Colors.retail_cp_grey,
                bdColor: Colors.retail_border_light,
                textColor: Colors.retail_T3,
                lineHeight: Sizes.dp22,
            };
        }
        if (value === label) {
            return {
                bgColor: Colors.retail_theme_light,
                bdColor: Colors.B7,
                textColor: Colors.B7,
                lineHeight: Sizes.dp22,
            };
        }
        return {
            bgColor: Colors.white,
            bdColor: Colors.retail_border_light,
            textColor: Colors.T2,
            lineHeight: Sizes.dp22,
        };
    };

    return (
        <View
            style={[
                Sizes.paddingLTRB(Sizes.dp8, Sizes.dp6),
                ABCStyles.rowAlignCenter,
                {
                    backgroundColor: getRadioButtonColors().bgColor,
                    borderRadius: Sizes.dp8,
                    borderColor: getRadioButtonColors().bdColor,
                    borderWidth: Sizes.dp1,
                    justifyContent: "center",
                    width,
                    height,
                },
                style,
            ]}
            onClick={() => !disable && onClick?.(cancelable && value === label ? "" : label)}
            collapsable={false}
        >
            {children ? (
                children
            ) : (
                <Text
                    style={TextStyles.t14NT2.copyWith({
                        color: getRadioButtonColors().textColor,
                        lineHeight: getRadioButtonColors().lineHeight,
                    })}
                >
                    {title ?? ""}
                </Text>
            )}
        </View>
    );
};
