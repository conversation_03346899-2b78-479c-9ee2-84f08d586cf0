import React, { createContext, ReactNode } from "react";
import { AbcFlex } from "../abc-flex";

interface AbcRadioGroupContextType {
    value?: string | number;
    cancelable?: boolean; // 是否支持反选
    onClick?: (value: string | number) => void;
}

export const AbcRadioGroupContext = createContext<AbcRadioGroupContextType>({
    value: undefined,
    onClick: undefined,
    cancelable: false,
});

interface AbcRadioGroupProps {
    children: ReactNode;
    value?: string | number;
    gap?: number;
    direction?: "row" | "column";
    onChange?: (value: string | number) => void;
    cancelable?: boolean;
}

export const AbcRadioGroup2: React.FC<AbcRadioGroupProps> = ({
    children,
    value: propValue,
    gap,
    direction = "row",
    onChange,
    cancelable,
}) => {
    // 使用props传入的value作为初始值

    const onClick = (value: string | number) => {
        onChange?.(value);
    };

    const isRow = direction === "row";

    return (
        <AbcRadioGroupContext.Provider
            value={{
                value: propValue,
                onClick,
                cancelable,
            }}
        >
            <AbcFlex vertical={!isRow} gap={gap} wrap={isRow ? "wrap" : undefined}>
                {children}
            </AbcFlex>
        </AbcRadioGroupContext.Provider>
    );
};
