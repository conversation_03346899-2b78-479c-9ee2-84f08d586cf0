# 组件名称 ComponentName

> **使用说明**: 这是 ABC Mobile UI 组件库的标准文档模板。请根据实际组件替换相应内容。

## 概述

简要描述组件的用途和功能特点。

**模板说明**:
- 替换 `ComponentName` 为实际组件名称（如 `AbcButton`）
- 描述组件的主要功能和使用场景
- 说明组件的特色功能

## 基础用法

**模板说明**: 提供组件的基本使用方式，包括最简单的用法和常用配置

### 默认用法

```tsx
import { ComponentName } from '@abc/mobile-ui';

// 最简单的使用方式
<ComponentName>基础内容</ComponentName>
```

### 常用配置

```tsx
// 带常用属性的使用方式
<ComponentName
  size="large"
  theme="primary"
  onClick={() => console.log('clicked')}
>
  内容文本
</ComponentName>
```

## 属性详情 Props

**模板说明**: 根据组件的实际 interface 或 props 定义填写此表格

| 属性名 | 描述 | 类型 | 默认值 |
|--------|------|------|--------|
| children | 文本内容，支持字符串、数字或字符串数组 | `number \| string \| string[]` | - |
| size | 字体大小，支持预定义名称或自定义数字 | `'tiny' \| 'mini' \| 'small' \| 'normal' \| 'large' \| 'largex' \| 'xlarge' \| 'xxlarge' \| 'xxxlarge' \| number \| string` | - |
| fontWeight | 字体粗细，支持 CSS 标准值 | `string` | `'normal'` |
| theme | 主题色，支持预定义主题或自定义颜色 | `'black' \| 'grey' \| 'grey_light' \| 'grey_pale' \| 'white' \| 'white_light' \| 'white_pale' \| 'success' \| 'theme' \| 'warning' \| 'error' \| string` | - |
| numberOfLines | 限制文本显示的行数，超出部分将被截断 | `number` | - |
| ellipsizeMode | 文本截断模式，需配合 numberOfLines 使用 | `'head' \| 'middle' \| 'tail' \| 'clip'` | `'tail'` |
| opacity | 文本透明度，取值范围 0-1 | `number` | `1` |
| style | 自定义样式，支持单个样式对象或样式数组 | `Style \| Style[]` | - |
| airTestKey | 自动化测试标识，用于测试定位 | `string` | 自动生成 |
| onClick | 点击事件回调（内置防抖功能） | `() => void` | - |
| onLongClick | 长按事件回调 | `() => void` | - |
| onLayout | 布局变化回调事件 | `(evt: LayoutEvent) => void` | - |
| accessibilityLabel | 无障碍访问标签 | `string` | - |

**填写指南**:
- **表格头部**: 固定为 `属性名`、`描述`、`类型`、`默认值` 四列
- **描述列**: 详细说明属性的作用和使用场景，包括取值范围、注意事项等
- **类型列**: 使用 TypeScript 类型定义，联合类型用 `\|` 分隔，用反引号包裹
- **默认值列**: 没有默认值用 `-` 表示，有默认值的要明确标出
- **属性分组**: 可根据功能将属性分组，但保持表格的简洁性


