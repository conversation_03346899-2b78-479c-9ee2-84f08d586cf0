import React from "react";
import { View } from "@hippy/react";
import { AbcBannerTips } from "../index";
import {AbcText} from "../../abc-text";
import {Colors, Sizes} from "@app/theme";

export function AbcBannerTipsDemo() {
    function onMaskClick() {
        console.log('onMaskClick----')
    }

    function onClick() {
        console.log("onClick----")
    }

    return (
        <View>
           <View>
               <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>基础用法</AbcText>
               <AbcBannerTips tips={"收费单收费异常，请及时处理"} onMaskClick={onMaskClick}/>
           </View>

            <View style={{ marginTop: Sizes.dp16}}>
                <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>展示去处理</AbcText>
                <AbcBannerTips tips={"收费单收费异常，请及时处理"} showHandleBtn={true} onClick={onClick} />
            </View>

            <View style={{ marginTop: Sizes.dp16}}>
                <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>展示倒计时</AbcText>
                <AbcBannerTips tips={"收费单收费异常，请及时处理"} duringTime={30} />
            </View>

            <View style={{ marginTop: Sizes.dp16}}>
                <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>自定义icon</AbcText>
                <AbcBannerTips iconName={"fail"} tips={"获取数据异常"}  />
                <View style={{ marginTop: Sizes.dp16}} >
                    <AbcBannerTips iconName={"push"} tips={"已发送"}  />
                </View>
            </View>
        </View>
    );
}
