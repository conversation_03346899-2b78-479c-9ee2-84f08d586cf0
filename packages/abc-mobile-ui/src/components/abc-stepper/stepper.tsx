/**
 * create by dengjie
 * desc: 步进器
 * create date 2020/4/26
 */
import React from "react";
import { Style, Text, View } from "@hippy/react";
import { AbcIconfont } from "../abc-iconfont";
import { getLog } from "../../index";
import { Colors, Sizes, TextStyles } from "@app/theme";
import { DeviceUtils } from "@app/utils";

interface StepperProp {
    value: string | number;
    min?: string | number;
    max?: string | number;
    step?: number;
    iconStyle?: Style;
    onChange?(value: number): void;
}

export class AbcStepper extends React.Component<StepperProp, any> {
    constructor(props: StepperProp) {
        super(props);
        this.state = {};
    }

    static defaultProps = {
        step: 1,
    };

    private _renderHandleIcon(options: { type: string }): JSX.Element | undefined {
        let _iconView: JSX.Element | undefined = undefined;
        const { iconStyle } = this.props;
        switch (options.type) {
            case "reduce": {
                _iconView = (
                    <View
                        style={[
                            {
                                width: Sizes.dp48,
                                justifyContent: "center",
                                alignItems: "center",
                                backgroundColor: Colors.white,
                                borderColor: Colors.dividerLineColor,
                                ...Sizes.flattenBorderStyles(0.5),
                                borderRadius: DeviceUtils.isIOS() ? 1 : undefined,
                                ...iconStyle,
                            },
                        ]}
                        onClick={this._onAdd.bind(this, 0 - this.props.step!)}
                    >
                        <AbcIconfont name={"Minus"} color={Colors.black} size={Sizes.dp16} />
                    </View>
                );
                break;
            }
            case "add": {
                _iconView = (
                    <View
                        style={[
                            {
                                width: Sizes.dp48,
                                justifyContent: "center",
                                alignItems: "center",
                                backgroundColor: Colors.white,
                                borderColor: Colors.dividerLineColor,
                                ...Sizes.flattenBorderStyles(0.5),
                                borderRadius: DeviceUtils.isIOS() ? 1 : undefined,
                                ...iconStyle,
                            },
                        ]}
                        onClick={this._onAdd.bind(this, this.props.step!)}
                    >
                        <AbcIconfont name={"Plus"} color={Colors.black} size={Sizes.dp16} />
                    </View>
                );
                break;
            }
        }
        return _iconView;
    }

    render(): JSX.Element {
        return (
            <View
                style={{
                    flex: 1,
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "stretch",
                }}
            >
                {this._renderHandleIcon({ type: "reduce" })}
                <View
                    style={{
                        flex: 1,
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                >
                    <Text style={[TextStyles.t18MB, { alignItems: "center" }]} numberOfLines={1} ellipsizeMode={"middle"}>
                        {this.props.value}
                    </Text>
                </View>
                {this._renderHandleIcon({ type: "add" })}
            </View>
        );
    }

    private _onAdd(value: number): void {
        getLog()?.d(value.toString());
        this.props.onChange?.(value);
    }
}
