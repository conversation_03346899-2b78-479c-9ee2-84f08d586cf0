import React, { useState } from "react";
import { View } from "@hippy/react";
import { AbcStepper } from "../index";
import { AbcText } from "../../abc-text";
import { Colors, Sizes } from "@app/theme";

export function AbcStepperDemo() {
    const [stepperVal, setStepperVal] = useState(0);

    function handleChange(val: number) {
        setStepperVal(stepperVal + val);
    }
    return (
        <View>
            <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>基础用法</AbcText>
            <AbcStepper iconStyle={{ borderTopWidth: 0, borderBottomWidth: 0 }} value={stepperVal} onChange={handleChange} />
        </View>
    );
}
