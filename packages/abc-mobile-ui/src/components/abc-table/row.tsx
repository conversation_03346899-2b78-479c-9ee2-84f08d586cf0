import React from "react";
import { AbcBaseComponent } from "../abc-base-component";
import { Style, View } from "@hippy/react";
import { Colors, flattenStyles } from "@app/theme";

interface AbcRowProps {
    style?: Style | Style[];
}
export class AbcRow extends AbcBaseComponent<AbcRowProps> {
    render(): JSX.Element {
        const { style } = this.props;
        return (
            <View style={[{ flexDirection: "row", borderColor: Colors.dividerLineColor }, flattenStyles(style)]}>
                {this.props.children}
            </View>
        );
    }
}
