import React from "react";
import { AbcBaseComponent } from "../abc-base-component";
import { ScrollView, Text, View } from "@hippy/react";
import { ABCStyles, Colors, Sizes, TextStyles } from "@app/theme";
import { AbcRow } from "./row";
import { AbcCol } from "./col";
import _ from "lodash";
import { JsonProperty, Unique<PERSON>ey, UiUtils } from "@app/utils";

class ColStyle {
    width?: number | string;
    minWidth?: number;
    height?: number;
    lineHeight?: number;
    textIndent?: number;
    fontSize?: number;
    fontWeight?: number;
    backgroundColor?: string;
}
class ColObject {
    @JsonProperty({ type: ColStyle })
    style?: ColStyle;
}
class Checker {
    name?: string;
    type?: string;
    @JsonProperty({ type: ColStyle })
    style?: ColStyle;
    date?: string;
    id?: string;
}
class Constraints {
    max?: number;
    message?: string;
    min?: number;
    precision?: number;
    required?: number;
    scale?: number;
    size?: number;
    unit?: string;
    validateType?: number;
    componentConfig?: { type?: string };
}
class Items {
    id?: string;
    name?: string;
    type?: string;
    @JsonProperty({ type: ColStyle })
    colStyle?: ColStyle;
    path?: string;
    displayName?: string;
    componentType?: number;
    options?: string[];
    value?: string;
    unit?: string;
    valueQuantity?: number;
    @JsonProperty({ type: Array, clazz: Constraints })
    constraints?: Constraints[];
    inspectType?: number;
}
class InspectChild {
    @JsonProperty({ type: Items })
    items?: Items[];
    flag?: boolean;
}
class Children {
    inspectType?: number;
    @JsonProperty({ type: InspectChild })
    children?: InspectChild[];
    @JsonProperty({ type: ColObject })
    colObject?: ColObject;
    name?: string;
    type?: string;
}
export class TableHeaderConfig {
    name?: string;
    type?: string;
    @JsonProperty({ type: ColObject })
    colObject?: ColObject;
    @JsonProperty({ type: Checker })
    checker?: Checker;
    @JsonProperty({ type: Children })
    children?: Children[];
}
interface AbcTableProps {
    title?: string;
    width?: number;
    children?: AbcTableProps[];
    rightEye?: string;
    leftEye?: string;
    key?: string;
    writable?: boolean; //可修改
    configurable?: boolean; //可删除
    tableHeaderConfig: TableHeaderConfig[];
}
class HeaderParams {
    id?: string;
    name?: string;
    width?: number;
}

const ExaminationTableFormComponentsTypeEnum = Object.freeze({
    RADIO_TYPE: 0,
    CHECKBOX_TYPE: 1,
    AUTO_COMPLETE_TYPE: 2,
    INPUT_TYPE: 3,
    UPLOAD_TYPE: 4,
    TEXT_AREA_TYPE: 5,
    SELECT_TYPE: 6,
});

const ExaminationTableFormComponents = Object.freeze({
    [ExaminationTableFormComponentsTypeEnum.INPUT_TYPE]: "TableFormInput",
    [ExaminationTableFormComponentsTypeEnum.AUTO_COMPLETE_TYPE]: "TableFormAutoComplete",
    [ExaminationTableFormComponentsTypeEnum.SELECT_TYPE]: "TableFormSelect",
    [ExaminationTableFormComponentsTypeEnum.RADIO_TYPE]: "TableFormRadio",
    [ExaminationTableFormComponentsTypeEnum.CHECKBOX_TYPE]: "TableFormRadio",
    [ExaminationTableFormComponentsTypeEnum.TEXT_AREA_TYPE]: "TableFormTextArea",
    [ExaminationTableFormComponentsTypeEnum.UPLOAD_TYPE]: "TableFormUploader",
});
export class AbcTable extends AbcBaseComponent<AbcTableProps> {
    private _scrollXWidth?: number;
    private createFormItem(item?: Items, path?: string, isLast?: boolean, lastChild?: boolean) {
        // ?当前指标项知道自己所在项目的路径
        item!.path = path;
        const componentType = (item?.componentType ?? 2) as keyof typeof ExaminationTableFormComponents;
        const isRadio = ExaminationTableFormComponents[componentType] === "TableFormRadio";
        let valueUnitStr = "";

        if (item?.valueQuantity != 1) {
            const valueCol = (item?.value?.indexOf("@") ?? -1) > -1 ? (item?.value != "@" ? item?.value?.split("@") : []) : [];
            valueUnitStr =
                !_.isEmpty(valueCol) && !_.isEmpty(item?.constraints)
                    ? (!!valueCol![0] ? valueCol![0] + item?.constraints?.[0]?.unit : "-") +
                      "@" +
                      (!!valueCol![1] ? valueCol![1] + item?.constraints?.[1]?.unit : "-")
                    : (item?.constraints?.length ?? 0) > 1
                    ? "@"
                    : (!!item?.value ? item?.value : "") +
                          (!!item?.unit
                              ? item?.unit
                              : !_.isEmpty(item?.constraints) && !!item?.constraints?.[0].unit
                              ? item?.constraints?.[0].unit
                              : "") || "-";
        } else {
            valueUnitStr = (!!item?.value ? item?.value : "") + (!!item?.unit ? item?.unit : "") || "";
        }
        return (
            <AbcCol
                style={{
                    flex: 1,
                    flexWrap: "wrap",
                    justifyContent: "center",
                    paddingHorizontal: Sizes.dp9,
                    borderBottomWidth: isLast ? 1 : 0,
                    borderRightWidth: lastChild ? 0 : 1,
                }}
                key={UniqueKey()}
            >
                {!!isRadio ? (
                    <View style={[{ flex: 1, flexWrap: "wrap", paddingVertical: Sizes.dp16 }]}>
                        <Text style={[TextStyles.t12NT1.copyWith({ color: Colors.t2 })]}>{item?.value ?? "-"}</Text>
                    </View>
                ) : (
                    <View style={[{ flex: 1, flexWrap: "wrap", paddingVertical: Sizes.dp18 }]}>
                        <Text style={[TextStyles.t12NT1.copyWith({ color: Colors.t2 }), { lineHeight: Sizes.dp17 }]}>
                            {!!item?.displayName ? item?.displayName + ":" : item?.name ? item.name + ":" : ""}
                            {!!valueUnitStr ? valueUnitStr : ""}
                        </Text>
                    </View>
                )}
            </AbcCol>
        );
    }
    private createNode(nodes?: TableHeaderConfig[], path = "") {
        return nodes?.map((node, index) => {
            if (!node) return <View key={UniqueKey()} />;
            const strWidth = typeof node.colObject?.style?.width == "string" ? Number(node.colObject?.style?.width.replace("px", "")) : 128;

            return (
                <AbcRow key={UniqueKey()}>
                    {node.type === "title" && node.name ? (
                        <AbcCol
                            style={{
                                width: strWidth ?? Sizes.dp128,
                                alignItems: "center",
                                justifyContent: "center",
                                borderBottomWidth: 1,
                                // paddingHorizontal: Sizes.dp6,
                            }}
                        >
                            <Text style={[TextStyles.t12NT1.copyWith({ fontWeight: "500" })]}>{node?.name ?? ""}</Text>
                        </AbcCol>
                    ) : null}
                    <AbcCol style={{ flex: 1, borderRightWidth: 0 }}>
                        {node?.children?.map((item, index2) => {
                            if (item.inspectType) {
                                // const span = 24 / (item?.children?.length ?? 1);
                                /*?单眼 item.children就有两项，双眼就一项，使用布局组件分下，然后就渲染每项数据中的指标*/
                                return (
                                    <AbcRow key={UniqueKey()} style={{ borderBottomWidth: 1 }}>
                                        {item?.children?.map((child, index3) => {
                                            return (
                                                <AbcCol
                                                    style={[
                                                        {
                                                            borderRightWidth: 0,
                                                            flex: 1,
                                                        },
                                                    ]}
                                                    key={UniqueKey()}
                                                >
                                                    {/*<AbcRow*/}
                                                    {/*    style={{*/}
                                                    {/*        borderBottomWidth: 0,*/}
                                                    {/*        flexWrap: "wrap",*/}
                                                    {/*    }}*/}
                                                    {/*>*/}
                                                    {(child?.items ?? [])?.map((e, i) => {
                                                        let lane = "";
                                                        // ?有 path说明递归了，其中【item】是我们自己包装的一层，所以第一层 index不拼接到 path
                                                        if (path) {
                                                            // eslint-disable-next-line prefer-template
                                                            lane += path + "-" + index2 + "-" + index3 + "-" + i;
                                                        } else {
                                                            // eslint-disable-next-line prefer-template
                                                            lane += index + "-" + index2 + "-" + index3 + "-" + i;
                                                        }
                                                        return this.createFormItem(
                                                            e,
                                                            lane,
                                                            i != (child.items?.length ?? 0) - 1,
                                                            index3 == (item?.children?.length ?? 0) - 1
                                                        );
                                                    })}
                                                    {/*</AbcRow>*/}
                                                </AbcCol>
                                            );
                                        })}
                                    </AbcRow>
                                );
                            }
                            let lane = "";
                            // ?有 path说明递归了，其中【item】是我们自己包装的一层，所以第一层 index不拼接到 path
                            if (path) {
                                // eslint-disable-next-line prefer-template
                                lane += path + "-" + index2;
                                // lane = `${path}-${index2}`
                            } else {
                                // eslint-disable-next-line prefer-template
                                lane += index + "-" + index2;
                                // lane = `${index}-${index2}`
                            }
                            return this.createNode([item as TableHeaderConfig], lane);
                        })}
                    </AbcCol>

                    {/*{node.checker ? (*/}
                    {/*    <AbcCol style={[ABCStyles.rowAlignCenter]}>*/}
                    {/*        <Text style={[TextStyles.t12NT1.copyWith({ color: Colors.t2 }), { lineHeight: Sizes.dp17 }]}>*/}
                    {/*            {node.checker.date ?? ""}*/}
                    {/*        </Text>*/}
                    {/*    </AbcCol>*/}
                    {/*) : null}*/}
                </AbcRow>
            );
        });
    }
    private _renderHeaderView(list?: HeaderParams[]): JSX.Element {
        return (
            <View
                style={[
                    ABCStyles.rowAlignCenter,
                    {
                        borderBottomWidth: 1,
                        backgroundColor: Colors.bg1,
                        borderBottomColor: Colors.dividerLineColor,
                    },
                ]}
            >
                {list?.map((item, index) => {
                    return (
                        <View
                            key={item?.id}
                            style={[
                                !!item.width ? { width: item.width } : { flex: 1 },
                                {
                                    borderRightWidth: index! < (list?.length ?? 0) - 1 ? 1 : 0,
                                    paddingVertical: Sizes.dp11,
                                    borderRightColor: Colors.dividerLineColor,
                                    justifyContent: "center",
                                    alignItems: "center",
                                    height: Sizes.dp40,
                                },
                            ]}
                        >
                            <Text style={[TextStyles.t12NT1.copyWith({ fontWeight: "500" })]}>{item?.name ?? ""}</Text>
                        </View>
                    );
                })}
            </View>
        );
    }

    render(): JSX.Element {
        const { tableHeaderConfig } = this.props;
        const strWidth =
            typeof tableHeaderConfig?.[0]?.colObject?.style?.width == "string"
                ? Number(tableHeaderConfig?.[0]?.colObject?.style?.width.replace("px", ""))
                : 128;
        const headerList = [
            {
                id: "1-1",
                name: "",
                width: strWidth,
            },
            {
                id: "1-2",
                name: "右眼（OD）",
            },
            {
                id: "1-3",
                name: "左眼（OS）",
            },
            // {
            //     id: "1-4",
            //     name: "检查人",
            //     width: 106,
            // },
        ];

        return (
            <ScrollView
                style={{ flex: 1, backgroundColor: Colors.white, paddingHorizontal: Sizes.dp8 }}
                horizontal={false}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{
                    flexDirection: "column",
                    width: this._scrollXWidth,
                }}
                onContentSizeChanged={(evt) => {
                    const minContentWidth = UiUtils.getScreenWidth() - (Sizes.dp33 ?? Sizes.dp1);
                    if (evt.width < minContentWidth) {
                        this._scrollXWidth = minContentWidth;
                        this.setState({});
                    }
                }}
            >
                {!_.isEmpty(tableHeaderConfig) && !!tableHeaderConfig?.find((t) => t?.children) && this._renderHeaderView(headerList)}
                {this.createNode(tableHeaderConfig)}
            </ScrollView>
        );
    }
}
