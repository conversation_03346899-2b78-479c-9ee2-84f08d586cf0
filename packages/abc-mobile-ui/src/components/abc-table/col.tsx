import React from "react";
import { AbcBaseComponent } from "../abc-base-component";
import { Style, View } from "@hippy/react";
import { Colors, flattenStyles } from "@app/theme";

interface AbcColProps {
    style?: Style | Style[];
}
export class AbcCol extends AbcBaseComponent<AbcColProps> {
    render(): JSX.Element {
        const { style } = this.props;
        return (
            <View style={[{ borderRightWidth: 1, borderColor: Colors.dividerLineColor }, flattenStyles(style)]}>{this.props.children}</View>
        );
    }
}
