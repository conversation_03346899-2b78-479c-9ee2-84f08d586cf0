/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-04-29
 *
 * @description
 */
import { AbcBaseComponent } from "../abc-base-component";
import React from "react";
import { Text, View } from "@hippy/react";
import { AbcShowDialog } from "../abc-dialog-builder";
import _ from "lodash";
import { AbcNavigator } from "../abc-navigator";
import { ABCStyles, Colors, Sizes, TextStyles } from "@app/theme";
import { AbcIconfont } from "../abc-iconfont";
import { delayed, AccessibilityLabelType } from "@app/utils";

interface AbcToastProps {
    tips: string;
    duration?: number | "LONG" | "SHORT";
    success?: boolean;
    warning?: boolean;
}

/**
 *
 */
export class AbcToast extends AbcBaseComponent<AbcToastProps> {
    private isPop = false;
    static show(
        tips: string,
        options?: {
            success?: boolean;
            warning?: boolean;
            autoBlurText?: boolean;
            duration?: number | "LONG" | "SHORT";
        }
    ): Promise<boolean | undefined> {
        return AbcShowDialog(<AbcToast tips={tips} success={options?.success} warning={options?.warning} duration={options?.duration} />, {
            routeName: "Toast",
            alignItems: "center",
            autoBlurText: options?.autoBlurText,
        });
    }

    constructor(props: AbcToastProps) {
        super(props);

        const { duration } = this.props;
        let durationNum = 1000; //short
        if (duration == "LONG") {
            durationNum = 3000; //
        } else if (_.isNumber(duration)) {
            durationNum = duration;
        }

        delayed(durationNum)?.subscribe(() => {
            this.isPop = true;
            AbcNavigator.pop(true);
        }).addToDisposableBag(this);
    }
    componentWillUnmount(): void {
        super.componentWillUnmount();
        if (!this.isPop) {
            AbcNavigator.pop(true);
        }
    }

    render(): JSX.Element {
        const { tips, success, warning } = this.props;
        let accessibilityLabel = `${AccessibilityLabelType.TOAST}-`;
        if (warning) {
            accessibilityLabel += "warning-";
        }
        return (
            <View
                accessibilityLabel={`${accessibilityLabel}${tips}`}
                style={[
                    ABCStyles.rowAlignCenter,
                    {
                        backgroundColor: Colors.toast_bg,
                        padding: Sizes.dp16,
                        borderRadius: Sizes.dp4,
                        marginHorizontal: Sizes.dp20,
                    },
                ]}
            >
                {warning ? (
                    // <AssetImageView name={"inventory_alarm"} style={{ width: Sizes.dp18, height: Sizes.dp18, marginRight: Sizes.dp6 }} />
                    <AbcIconfont size={Sizes.dp18} name={"Attention"} color={Colors.Y2} />
                ) : undefined}
                {success ? <AbcIconfont name={"Chosen"} size={Sizes.dp14} color={Colors.mainColor} /> : undefined}
                <Text style={[TextStyles.t16NW, { marginLeft: Sizes.dp6 }]}>{tips}</Text>
            </View>
        );
    }
}
