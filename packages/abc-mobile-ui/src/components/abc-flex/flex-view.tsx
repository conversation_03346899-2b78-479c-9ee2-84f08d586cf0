import React from "react";
import { Style, View } from "@hippy/react";

/**
 * Flex布局容器组件属性接口
 */
interface FlexViewProps {
    /**
     * 子元素间距
     */
    gap?: number;
    /**
     * flex 主轴的方向是否垂直，默认为水平方向
     */
    vertical?: boolean;
    /**
     * flex-wrap 属性，控制是否换行，可选值: 'nowrap' | 'wrap' | 'wrap-reverse'，默认 'nowrap'
     */
    wrap?: "nowrap" | "wrap" | "wrap-reverse";
    /**
     * justify-content 属性，控制主轴对齐方式，默认 'flex-start'
     */
    justify?: "flex-start" | "flex-end" | "center" | "space-between" | "space-around" | "space-evenly";
    /**
     * align-items 属性，控制交叉轴对齐方式，默认 'flex-start'
     */
    align?: "flex-start" | "flex-end" | "center" | "stretch" | "baseline";
    /**
     * align-content 属性，多行/列内容对齐方式，默认 'flex-start'
     */
    alignContent?: "flex-start" | "flex-end" | "center" | "stretch" | "space-between" | "space-around";
    /** 自定义样式 */
    style?: Style | Style[];
    /** 子元素 */
    children?: React.ReactNode;
    onClick?: () => void;
}

/**
 * 移动端Flex布局容器组件
 * 提供简单易用的Flex布局能力，专为移动端优化
 */
export class AbcFlex extends React.Component<FlexViewProps> {
    render(): JSX.Element {
        const {
            vertical = false,
            wrap = "nowrap",
            justify = "flex-start",
            align = "flex-start",
            alignContent = "flex-start",
            gap = 0,
            children,
            style,
            onClick,
        } = this.props;

        // 构建基础样式
        const baseStyle: Style = {
            flexDirection: vertical ? "column" : "row",
            flexWrap: wrap,
            justifyContent: justify,
            alignItems: align,
            alignContent: alignContent,
            // 容器用负 margin 抵消子元素 gap，模拟 web flex gap 行为
            marginRight: -gap,
            marginBottom: -gap,
        };

        const childArray = React.Children.toArray(children).filter(Boolean);
        // 所有子元素都加 marginRight/marginBottom，让 gap 行为更接近 web
        const spacedChildren = childArray.map((child) => {
            const childStyle: Style = {
                marginRight: gap,
                marginBottom: gap,
            };
            if (React.isValidElement(child)) {
                return React.cloneElement(child, {
                    style: [child.props?.style, childStyle] as any,
                } as any);
            }
            return child;
        });

        return (
            <View style={{ overflow: "hidden" }} collapsable={false}>
                <View style={[baseStyle, style]} onClick={onClick} collapsable={false}>
                    {spacedChildren}
                </View>
            </View>
        );
    }
}
