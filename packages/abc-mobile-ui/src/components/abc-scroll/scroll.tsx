import React from "react";
import { AbcBaseComponent } from "../abc-base-component";
import { ScrollView, Style } from "@hippy/react";
import { LayoutEvent, ScrollEvent, RefType2 } from "@app/utils";
import { AbcTextInput } from "../abc-text-input";

/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/11/10
 *
 * @description
 */

interface AbcScrollProps {
    /**
     * When true, the scroll view's children are arranged horizontally in a row
     * instead of vertically in a column.
     * The default value is `false`.
     */
    horizontal?: boolean;
    /**
     * When `true`, the scroll view stops on multiples of the scroll view's size when scrolling.
     * This can be used for horizontal pagination.
     * Default: false
     */
    pagingEnabled?: boolean;
    /**
     * When `false`, the view cannot be scrolled via touch interaction.
     * Default: true
     *
     * > Note that the view can always be scrolled by calling scrollTo.
     */
    scrollEnabled?: boolean;
    /**
     * When `true`, shows a horizontal scroll indicator.
     * Default: true
     */
    showsHorizontalScrollIndicator?: boolean;
    /**
     * When `true`, shows a vertical scroll indicator.
     * Default: true
     */
    showsVerticalScrollIndicator?: boolean;
    /**
     * These styles will be applied to the scroll view content container which wraps all
     * of the child views.
     */
    contentContainerStyle?: Style;
    /**
     * This controls how often the scroll event will be fired while scrolling
     * (as a time interval in ms). A lower number yields better accuracy for code
     * that is tracking the scroll position, but can lead to scroll performance
     * problems due to the volume of information being send over the bridge.
     * You will not notice a difference between values set between 1-16 as the JS run loop
     * is synced to the screen refresh rate. If you do not need precise scroll position tracking,
     * set this value higher to limit the information being sent across the bridge.
     *
     * The default value is zero, which results in the scroll event being sent only once
     * each time the view is scrolled.
     */
    scrollEventThrottle?: number;
    /**
     * The amount by which the scroll view indicators are inset from the edges of the scroll view.
     * This should normally be set to the same value as the `contentInset`.
     *
     * Default: {top: 0, right: 0, bottom: 0, left: 0}.
     */
    scrollIndicatorInsets?: {
        top: number;
        right: number;
        bottom: number;
        left: number;
    };
    /**
     * Called when the momentum scroll starts (scroll which occurs as the ScrollView starts gliding).
     */
    onMomentumScrollBegin?(): void;
    /**
     * Called when the momentum scroll ends (scroll which occurs as the ScrollView glides to a stop).
     */
    onMomentumScrollEnd?(): void;
    /**
     * Fires at most once per frame during scrolling.
     * The frequency of the events can be controlled using the `scrollEventThrottle` prop.
     *
     * @param {Object} evt - Scroll event data.
     * @param {number} evt.contentOffset.x - Offset X of scrolling.
     * @param {number} evt.contentOffset.y - Offset Y of scrolling.
     */
    onScroll?(evt: {
        contentOffset: {
            x: number;
            y: number;
        };
    }): void;
    /**
     * Called when the user begins to drag the scroll view.
     */
    onScrollBeginDrag?(): void;
    /**
     * Called when the user stops dragging the scroll view and it either stops or begins to glide.
     */
    onScrollEndDrag?(evt: ScrollEvent): void;
    style?: Style;
    onLayout?: (evt: LayoutEvent) => void;
    /**
     * called when content size changed
     * @param evt
     */
    onContentSizeChanged?: (evt: { width: number; height: number }) => void;

    /**
     * 滚动时是否允许输入框失焦
     */
    scrollBeginDragInputBlur?: boolean;
}

export class AbcScroll extends AbcBaseComponent<AbcScrollProps> {
    private _scrollView?: ScrollView | null;

    render(): JSX.Element {
        const { ...otherProps } = this.props;
        return (
            <ScrollView ref={(ref) => (this._scrollView = ref)} {...otherProps} onScrollBeginDrag={this._onScrollBeginDrag.bind(this)}>
                {this.props.children}
            </ScrollView>
        );
    }

    public async scrollChildToVisible(child: RefType2, firstChild: RefType2, lastChild: RefType2, animated?: boolean): Promise<void> {
        return this._scrollView?.scrollChildToVisible(child, firstChild, lastChild, animated);
    }
    /**
     * Scrolls to a given x, y offset, either immediately, with a smooth animation.
     *
     * @param {number} x - Scroll to horizon position X.
     * @param {number} y - Scroll To veritical position Y.
     * @param {boolean} animated - With smooth animation.By default is true.
     */
    public scrollTo(
        x:
            | number
            | {
                  x: number;
                  y: number;
                  animated: boolean;
              },
        y: number,
        animated?: boolean
    ): void {
        this._scrollView?.scrollTo(x, y, animated);
    }

    private _onScrollBeginDrag(): void {
        const { scrollBeginDragInputBlur = true } = this.props;
        if (scrollBeginDragInputBlur && AbcTextInput.focusInput) {
            AbcTextInput.focusInput.blur();
        }

        this.props.onScrollBeginDrag?.();
    }
}
