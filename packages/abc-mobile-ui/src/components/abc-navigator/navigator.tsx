/* eslint-disable no-underscore-dangle */

import React from "react";
import { Style, View } from "@hippy/react";
import { AbcTextInput } from "../abc-text-input";
import { AbcView } from "../abc-view";
import { AbcTopPanelBuilder } from "../abc-top-panel-builder";
import { AbcViewProps, Color } from "@app/theme";
import _ from "lodash";
import { Subject, Subscription } from "rxjs";
import { AbcUIManagerModule, ignore, UniqueKey, delayed, keyboardListener, getLog, AbcError, Completer, TaskQueue } from "@app/utils";
import { AbcOverlayDialog } from "../abc-overlay-dialog";

const kTAG = "AbcNavigator.";
type AnyType = any;

export interface BottomSheetOptions {
    backgroundColor?: number;
    justifyContent?: JustifyContent;
    dismissWhenTouchOutside?: boolean;
    enableBackgroundAnimation?: boolean;
}

interface AbcNavigatorProps {
    initialRoute: Route;
    style?: Style | Style[];
    router?: (url: string) => JSX.Element;
}

type JustifyContent =
    | "start"
    | "center"
    | "end"
    | "flex-start"
    | "flex-end"
    | "left"
    | "right"
    | "normal"
    | "space-between"
    | "space-around"
    | "space-evenly"
    | "stretch";

type AlignItems = "stretch" | "center" | "flex-start" | "flex-end" | "baseline";

export interface DialogOptions {
    routeName?: string;
    alignItems?: AlignItems; //nil as center
    autoBlurText?: boolean;
    dialogMaskBg?: string; // 弹窗遮罩层的背景颜色
    borderRadius?: number;
    isHasCloseBtn?: boolean; //是否显示右上角的关闭按钮
    marginVertical?: number; // 弹窗上下外间距
    contentPadding?: Style; //内容内边距
    titleToContentSpace?: number; //标题和内容间距
    titleLineHeight?: number; //标题行间距
    dismissWhenTouchOutside?: boolean; //点击到区域外自动隐藏
}

interface Route {
    routeName: string;
    component: JSX.Element;
    initProps?: any;
    transitionType?: TransitionType;
    backgroundColor?: number | Color;
    justifyContent?: JustifyContent;
    alignItems?: AlignItems;
    dismissWhenTouchOutside?: boolean; //点击到区域外自动隐藏,
    enableBackgroundAnimation?: boolean; //切页时背景色是否渐变
    enableGestureRecognizer?: boolean; //是否允许扣边跟手动画返回
    animated?: false;
    replace?: boolean; //是否替换当前栈顶页,
    clearStack?: boolean; //是否清除历史栈
    _innerKey?: string;
    autoBlurText?: boolean; //default true
}

interface Top {
    data: RouteState;
    next: Top;
}

interface RouteState extends Route {
    completer: Completer<any>;
    key: string; //用于每个页面分配一个唯一key,解决历史栈中多个同类型页面，key相同的问题
}

class Stack {
    top: Top | null = null;

    size = 0;

    public push(route: RouteState) {
        (this.top as Top) = {
            data: route,
            next: this.top as Top,
        };
        this.size += 1;
    }

    /**
     * Returns latest push router.
     */
    public peek() {
        return this.top === null ? null : this.top.data;
    }

    /**
     * Return back to previous page.
     */
    public pop() {
        if (this.top === null) {
            return null;
        }

        const out = this.top;

        this.top = this.top.next;

        if (this.size > 0) {
            this.size -= 1;
        }

        return out.data;
    }

    // /**
    //  * Clear history stack
    //  */
    // clear() {
    //     this.top = null;
    //     this.size = 0;
    // }

    /**
     * Returns all of routes
     */
    public displayAll(): RouteState[] {
        const arr: RouteState[] = [];
        if (this.top === null) {
            return arr;
        }

        let current = this.top;

        for (let i = 0, len = this.size; i < len; i += 1) {
            arr[i] = current.data;
            current = current.next;
        }

        return arr;
    }
}

interface ABCNavigatorItemProps extends AbcViewProps {
    //是否开启背景色渐变，对于动画框之类，mask渐变
    enableBackgroundAnimation?: boolean;
}

function ABCNavigatorItem(props: ABCNavigatorItemProps) {
    return (
        <div
            // @ts-ignore
            // eslint-disable-next-line react/no-unknown-property
            nativeName="ABCNavigatorItem"
            {...props}
            /* eslint-disable-next-line @typescript-eslint/no-empty-function */
            onClick={() => {}}
            style={{
                ...props?.style,
                position: "absolute",
                left: 0,
                top: 0,
                right: 0,
                bottom: 0,
                // @ts-ignore
                collapsable: false,
            }}
        />
    );
}

export enum TransitionType {
    none,
    inFromRight, //default
    inFromBottom,
}

let gNavigator: AbcNavigator | null;

// Navigator前进后退事件
export class ABCNavigatorEventBase {
    public from: string;
    public to: string;

    constructor(from: string, to: string) {
        this.from = from;
        this.to = to;
    }
}

export class ABCNavigatorEventBack extends ABCNavigatorEventBase {
    constructor(from: string, to: string) {
        super(from, to);
    }
}

export class ABCNavigatorEventForward extends ABCNavigatorEventBase {
    constructor(from: string, to: string) {
        super(from, to);
    }
}

/**
 * Container that allows to flip left and right between child views.
 * Each child view of the ViewPage will be treated as a separate page
 * and will be stretched to fill the ViewPage.
 * @noInheritDoc
 */
class AbcNavigator extends React.Component<AbcNavigatorProps> {
    private _overlayList: JSX.Element[] = [];
    public set overlayList(list: JSX.Element[]) {
        this._overlayList = list;
        this.setState({});
    }

    static getInstance(): AbcNavigator | null {
        return gNavigator;
    }

    public static sNavigatorObserver = new Subject<ABCNavigatorEventBase>(); //用于通知前进后退相关事件
    private instance: HTMLDivElement | null = null;

    private queue = new TaskQueue();

    private _popLock?: Completer<void>; //用于表示当前处于pop退栈过程中，在push时需要等待
    private _pushLock?: Completer<void>; //用于表示当前处理push入栈过程中，在pop时需要等待

    static set navigator(value: AbcNavigator | null) {
        gNavigator = value;
    }

    private stack = new Stack();
    private _waitKeyboardHidden?: Completer<void>;

    /**
     * @ignore
     */
    constructor(props: AbcNavigatorProps) {
        super(props);
        this._firstPush(props.initialRoute);
        keyboardListener.subscribe((visible) => {
            if (this._waitKeyboardHidden && !visible.visible) {
                this._waitKeyboardHidden?.resolve();
                this._waitKeyboardHidden = undefined;
            }
        });
    }

    static navigateToPage<T>(
        page: JSX.Element | string,
        options?: {
            transitionType?: TransitionType;
            backgroundColor?: number | Color;
            justifyContent?: JustifyContent;
            alignItems?: AlignItems;
            dismissWhenTouchOutside?: boolean; //点击到区域外时，隐藏
            replace?: boolean; //是否替换当前栈顶页
            clearStack?: boolean; //是否清除之前的历史记录
            routeName?: string;
            enableBackgroundAnimation?: boolean; //false,切页时，背景色是否渐变
            enableGestureRecognizer?: boolean; //true,屏幕边缘扣边返回
            autoBlurText?: boolean; //在页面切换时使输入法失焦,default true
        }
    ): Promise<T> {
        getLog?.()?.d(kTAG + ":navigateToPage page  = " + page);
        let routeName = options?.routeName;
        if (_.isString(page)) {
            const newPage = gNavigator!.urlToPage(page);
            if (!newPage) {
                return Promise.reject("未找到对应的页面");
            }
            if (!routeName) routeName = page;
            page = newPage;
        }

        routeName = routeName ?? page.type?.name ?? "";
        return gNavigator!._push({
            routeName: routeName!,
            component: page,
            transitionType: options?.transitionType,
            backgroundColor: options?.backgroundColor,
            justifyContent: options?.justifyContent,
            alignItems: options?.alignItems,
            dismissWhenTouchOutside: options?.dismissWhenTouchOutside,
            replace: options?.replace ?? false,
            clearStack: options?.clearStack ?? false,
            enableBackgroundAnimation: options?.enableBackgroundAnimation,
            enableGestureRecognizer: options?.enableGestureRecognizer ?? true,
            autoBlurText: options?.autoBlurText ?? true,
        });
    }

    static currentRoute(): RouteState {
        return gNavigator!.stack.peek()!;
    }

    static getAllRoutes(): RouteState[] {
        return gNavigator!.stack.displayAll()!;
    }

    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
    static pop(options?: any, animation = true, fromEdgeGesture = false): void {
        if (AbcOverlayDialog.hasOpenedOverlayDialog) {
            if (gNavigator!.stack.peek()?.autoBlurText ?? true) gNavigator?.waitKeyboardHiddenIfNeed();
            AbcOverlayDialog.hide();
            return;
        }
        AbcNavigator.popWithFinishCallback({ options: options, animation: animation, fromEdgeGesture: fromEdgeGesture });
    }

    /**
     *
     * @param condition condition: (routeName: string) => boolean,需要停止时返回true
     * @param fallbackUrl 如果没有找到对应的页面，则跳转到对应的页面
     * @return Promise<boolean> 如果找到符合条件的项，true, 否则false
     */
    static popUntil(condition: string | ((routeName: string) => boolean), fallbackUrl?: string): Promise<boolean> {
        return gNavigator!._popUntil(condition).then((rsp) => {
            if (!rsp && fallbackUrl) {
                AbcNavigator.navigateToPage(fallbackUrl);
            }
            return rsp;
        });
    }

    static popWithFinishCallback({
        options,
        animation = true,
        fromEdgeGesture = false,
    }: {
        options?: any;
        animation?: boolean;
        fromEdgeGesture?: boolean;
    }): Promise<void> {
        return gNavigator!._pop(options, animation, fromEdgeGesture);
    }

    /**
     * 显示一个对话框
     * @param dialog 显示的对话框
     * @param options 其它控制参数
     */
    static showDialog<T>(dialog: JSX.Element, options?: DialogOptions): Promise<T> {
        return AbcNavigator.navigateToPage(dialog, {
            transitionType: TransitionType.none,
            backgroundColor: !!options?.dialogMaskBg ? options.dialogMaskBg : 0x33000000,
            enableBackgroundAnimation: true,
            justifyContent: "center",
            alignItems: options?.alignItems ? options?.alignItems : "stretch",
            autoBlurText: options?.autoBlurText,
            routeName: options?.routeName,
            dismissWhenTouchOutside: options?.dismissWhenTouchOutside, //点击到区域外自动隐藏
        });
    }

    /**
     * 显示一个bottom sheet对话框
     * @param sheet 要显示的对话框
     * @param options
     */
    static showBottomSheet<T>(sheet: JSX.Element, options?: BottomSheetOptions): Promise<T> {
        return AbcNavigator.navigateToPage(sheet, {
            transitionType: TransitionType.inFromBottom,
            backgroundColor: options?.backgroundColor ?? 0x33000000,
            justifyContent: options?.justifyContent ?? "flex-end",
            enableBackgroundAnimation: options?.enableBackgroundAnimation ?? true,
            dismissWhenTouchOutside: options?.dismissWhenTouchOutside ?? true, //点击到区域外自动隐藏
        });
    }

    /**
     * 显示一个right sheet对话框
     * @param sheet 要显示的对话框
     */
    static showRightSheet<T>(sheet: JSX.Element): Promise<T> {
        return AbcNavigator.navigateToPage(sheet, {
            transitionType: TransitionType.none,
            dismissWhenTouchOutside: true, //点击到区域外自动隐藏
        });
    }

    /**
     * 显示一个Top sheet对话框
     * @param view 要显示的对话框
     * @param options
     */

    static showTopSheet<T>(view: JSX.Element, options?: { offsetY?: number }): Promise<T> {
        return AbcNavigator.navigateToPage(<AbcTopPanelBuilder offsetY={options?.offsetY ?? 0}>{view}</AbcTopPanelBuilder>, {
            transitionType: TransitionType.none,
            justifyContent: "flex-start",
            dismissWhenTouchOutside: true, //点击到区域外自动隐藏
        });
    }

    /**
     * 是否可以后退，如果当前页面为第一个页面，则不能pop
     */
    static canPop(): boolean {
        return gNavigator?._canPop() ?? false;
    }

    private _canPop() {
        return this.stack.size > 1;
    }

    private urlToPage(url: string): JSX.Element | undefined {
        return this.props.router?.(url);
    }

    //构造时首次入栈，不能考虑正在前进后退和刷新问题
    private _firstPush(route: Route) {
        route._innerKey = route._innerKey ?? UniqueKey();
        this.stack.push({
            ...route,
            completer: new Completer<AnyType>(),
            key: UniqueKey(),
        });
    }

    private async _push<T>(route: Route, update = true): Promise<T> {
        getLog().d(kTAG + ":_push name = " + route.routeName + ", update = " + update);
        route._innerKey = route._innerKey ?? UniqueKey();

        //页面切换时，取消焦点输入法，隐藏键盘
        (route.autoBlurText ?? true) && AbcTextInput.focusInput?.blur();
        await this._waitPreAction();

        this._pushLock = new Completer();
        let completer = new Completer<T>();
        const currentRoute = this.stack.peek();
        if (route.replace) {
            completer = currentRoute!.completer;
        }

        this.stack.push({
            ...route,
            completer: completer,
            key: UniqueKey(),
        });

        if (update) {
            this.setState({});
        }

        const guessAnimDuration = 400;
        await delayed(guessAnimDuration).toPromise(); //等待前进后退动画执行完成
        //处理清栈和替换顶栈
        if (route.clearStack) {
            const currentRoute = this.stack.peek(); //临时保存刚入栈的页面
            this.stack.pop(); //回退当前页面，剩下的项为需要清除掉的项

            //对于历史页面的wait进行取消
            const routes = this.stack.displayAll();
            for (const preRoute of routes) {
                preRoute.completer.resolve(undefined);
            }
            this.stack = new Stack();

            this.stack.push(currentRoute!);
            this.setState({});
        } else if (route.replace) {
            const currentRoute = this.stack.peek();
            this.stack.pop(); //退
            this.stack.pop();
            this.stack.push(currentRoute!);
            this.setState({});
        }

        this._pushLock.resolve();
        this._pushLock = undefined;

        getLog().d(kTAG + ":_push finish," + route.routeName);

        AbcNavigator.sNavigatorObserver.next(
            new ABCNavigatorEventForward(currentRoute?.routeName ?? "", this.stack.peek()?.routeName ?? "")
        );
        return completer.promise;
    }

    private async _doPop(options: any | undefined, animate = true) {
        getLog().d(kTAG + ":_doPop, this.stack.size=" + this.stack.size.toString());
        //如果当前正在入栈，等待其执行完成
        await this._waitPreAction();

        if (this.stack.size == 1) {
            return;
        }

        this._popLock = new Completer();
        const currentRoute = this.stack.peek();
        getLog().d(
            kTAG +
                "_doPop start, this.stack = " +
                this.stack +
                ", options = " +
                options +
                ", currentPage = " +
                currentRoute!.component.type.name +
                ", animate = " +
                animate +
                "this.stack.size=" +
                this.stack.size
        );
        const completer = new Completer<boolean>();
        AbcUIManagerModule.callUIFunction(this.instance, "pop", [animate], function (/*finish: any*/) {
            completer.resolve(true);
        });

        await completer.promise;
        const routeState: RouteState = this.stack.pop()!;

        getLog().d(
            kTAG +
                ":_doPop finish routeState = " +
                routeState!.component.type.name +
                "stack.size=" +
                this.stack.size +
                ", current = " +
                this.stack.peek()?.routeName
        );

        this.setState({});
        routeState.completer.resolve(options);

        this._popLock.resolve();
        AbcNavigator.sNavigatorObserver.next(new ABCNavigatorEventBack(currentRoute?.routeName ?? "", this.stack.peek()?.routeName ?? ""));
        this._popLock = undefined;
    }

    private async _pop(options: any | undefined, animation = true, fromEdgeGesture = false): Promise<void> {
        getLog().d(kTAG + ":_pop, this.stack.size" + this.stack.size.toString());
        if (!fromEdgeGesture && this.stack.peek()?.autoBlurText) await this.waitKeyboardHiddenIfNeed();

        const completer = new Completer<void>();
        this.queue.schedule(() => {
            return this._doPop(options, animation).finally(() => completer.resolve());
        });

        return completer.promise;
    }

    private async _popUntil(condition: string | ((routeName: string) => boolean)): Promise<boolean> {
        getLog().d(kTAG + ":_popUntil, this.stack.size" + this.stack.size.toString());
        await this.waitKeyboardHiddenIfNeed();

        const completer = new Completer<boolean>();
        this.queue.schedule(async () => {
            getLog().d(kTAG + ":do _popUntil, this.stack.size" + this.stack.size.toString());
            if (this.stack.size == 1) {
                completer.resolve(false);
                return;
            }

            const currentRoute = this.stack.peek();
            let findTarget = false;
            let realMatch = false;
            while (true) {
                const route = this.stack.peek();
                realMatch = (condition instanceof Function && !condition(route!.routeName)) || condition == route!.routeName;
                if (realMatch || this.stack.size === 1) {
                    //this.stack.size === 1 最后一项也作为目标项
                    findTarget = true;
                    break;
                }

                this.stack.pop();

                route?.completer.resolve(undefined);
            }

            if (findTarget) {
                this.stack.push(currentRoute!);
                this.setState({});
                await delayed(100).toPromise(); //给个100ms, A->B->C->D, 由D->A时， 让native层将中间BC页面去掉，不然后退时会闪C的问题
                const rsp = await this._doPop(undefined).catch((error) => new AbcError(error));
                if (rsp instanceof AbcError) {
                    completer.reject(rsp.detailError);
                } else {
                    completer.resolve(realMatch);
                }
            } else {
                this.setState({});
            }
        });

        return completer.promise;
    }

    private async _waitPreAction() {
        getLog().d(kTAG + "_waitPreAction this._popLock = " + this._popLock + "， this._pushLock = " + this._pushLock);
        if (this._popLock) await this._popLock.promise;
        if (this._pushLock) await this._pushLock.promise; //等待前一个push完成
    }

    /**
     * @ignore
     */
    public render(): JSX.Element {
        const { initialRoute, ...nativeProps } = this.props;
        ignore(initialRoute);
        const mappedChildren = this.stack
            .displayAll()
            .reverse()
            .map((route) => {
                const viewPageItemProps: any = {
                    transitionType: route.transitionType,
                };

                const style: Style = {
                    backgroundColor: route.backgroundColor,
                    justifyContent: route.justifyContent,
                    alignItems: route.alignItems,
                };

                if (route.transitionType == TransitionType.inFromBottom) {
                    style.flex = 1;
                }

                const dismissWhenTouchOutside = route?.dismissWhenTouchOutside ?? false;

                let content = route.component;
                if (dismissWhenTouchOutside) {
                    content = (
                        <AbcView style={[style, { flex: 1, backgroundColor: undefined }]} onClick={() => AbcNavigator.pop()}>
                            <View
                                onClick={() => {
                                    ignore();
                                }}
                            >
                                {content}
                            </View>
                        </AbcView>
                    );
                }

                viewPageItemProps.key = route._innerKey;
                return (
                    <ABCNavigatorItem
                        {...viewPageItemProps}
                        style={style}
                        key={route.key}
                        enableBackgroundAnimation={route.enableBackgroundAnimation}
                        enableGestureRecognizer={route.enableGestureRecognizer}
                    >
                        {content}
                    </ABCNavigatorItem>
                );
            });

        return (
            <View style={{ flexGrow: 1 }}>
                <div
                    // @ts-ignore
                    // eslint-disable-next-line react/no-unknown-property
                    nativeName="ABCNavigator"
                    ref={(ref: any) => {
                        this.instance = ref;
                    }}
                    {...nativeProps}
                    /* eslint-disable-next-line @typescript-eslint/no-empty-function */
                    onClick={() => {}}
                    // @ts-ignore
                    // eslint-disable-next-line react/no-unknown-property
                    onPopFromEdgeGesture={_.debounce(this.onPopFromEdgeGesture.bind(this), 200, { leading: true, trailing: false })}
                    // eslint-disable-next-line react/no-unknown-property
                    onKeyBack={this._onKeyBack.bind(this)}
                >
                    {mappedChildren}
                </div>
                {this._overlayList}
            </View>
        );
    }

    private _onKeyBack(): void {
        getLog().d(kTAG + "_onKeyBack");
        if (this._pushLock || this._popLock) return;

        this._pop(undefined, true).then();
    }

    onPopFromEdgeGesture(): void {
        getLog().d(kTAG + "onPopFromEdgeGesture");
        this.queue.schedule(() => {
            this._doPop(undefined, false);
        });
    }

    private async waitKeyboardHiddenIfNeed() {
        if (keyboardListener.value?.visible ?? false) {
            this._waitKeyboardHidden = this._waitKeyboardHidden ?? new Completer();
            let subscribable: Subscription | undefined = undefined;
            subscribable = delayed(500).subscribe(() => {
                this._waitKeyboardHidden?.resolve();
                this._waitKeyboardHidden = undefined;
                subscribable?.unsubscribe();
            });
            AbcTextInput.focusInput?.blur();
            await this._waitKeyboardHidden.promise.catch(() => undefined);
        }
    }
}

export { AbcNavigator };
