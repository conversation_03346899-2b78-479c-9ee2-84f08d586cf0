import { Style } from "@hippy/react";

type AlignItems = "stretch" | "center" | "flex-start" | "flex-end" | "baseline";

export interface DialogOptions {
    routeName?: string;
    alignItems?: AlignItems; //nil as center
    autoBlurText?: boolean;
    dialogMaskBg?: string; // 弹窗遮罩层的背景颜色
    borderRadius?: number;
    isHasCloseBtn?: boolean; //是否显示右上角的关闭按钮
    marginVertical?: number; // 弹窗上下外间距
    contentPadding?: Style; //内容内边距
    titleToContentSpace?: number; //标题和内容间距
    titleLineHeight?: number; //标题行间距
    dismissWhenTouchOutside?: boolean; //点击到区域外自动隐藏
}