/**
 * ABC 移动端组件库 ESLint 配置
 * 团队风格：TypeScript + React，注重可维护性、可读性、团队协作和一致性
 * 支持 Prettier 自动格式化
 */
module.exports = {
    root: true,
    env: {
        browser: true,
        node: true,
        es6: true,
    },
    parser: "@typescript-eslint/parser",
    parserOptions: {
        ecmaVersion: 6,
        sourceType: "module",
        ecmaFeatures: {
            jsx: true,
        },
    },
    plugins: ["react", "@typescript-eslint"],
    extends: [
        "plugin:react/recommended",
        "plugin:@typescript-eslint/recommended",
        "plugin:prettier/recommended",
    ],
    rules: {
        "@typescript-eslint/no-explicit-any": ["off"],
        "@typescript-eslint/no-var-requires": ["off"],
        "@typescript-eslint/no-empty-interface": ["off"],
        "@typescript-eslint/no-non-null-assertion": ["off"],
        "@typescript-eslint/ban-types": ["off"],
        "@typescript-eslint/no-this-alias":["off"],
        "@typescript-eslint/ban-ts-comment": ["off"],
        "react/display-name": ["off"],
        "react/prop-types": ["off"],
        "react/no-string-refs": ["off"]
    },

    "settings": {
        "react": {
            "createClass": "createReactClass",
            "pragma": "React",
            "version": "16.12.0",
            "flowVersion": "0.53"
        },
        "propWrapperFunctions": [
            "forbidExtraProps",
            {"property": "freeze", "object": "Object"},
            {"property": "myFavoriteWrapper"}
        ],
        "linkComponents": [
            "Hyperlink",
            {"name": "Link", "linkAttribute": "to"}
        ]
    }
};
