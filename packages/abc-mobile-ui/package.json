{"name": "@app/abc-mobile-ui", "version": "1.0.0", "main": "dist/index.js", "module": "dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js"}}, "types": "dist/index.d.ts", "peerDependencies": {"react": "^17.0.2", "react-dom": "^17.0.2"}, "files": ["dist"], "scripts": {"dev": "pnpm run build:types:watch & pnpm run build:js:watch", "build:types": "tsc --emitDeclarationOnly --declaration", "build:js": "webpack --config webpack.config.js", "build": "pnpm run build:js", "build:types:watch": "tsc --emitDeclarationOnly --declaration --watch", "build:js:watch": "webpack --watch --config webpack.config.js", "watch": "pnpm run build:types:watch & pnpm run build:js:watch", "clean": "<PERSON><PERSON><PERSON> dist"}, "devDependencies": {"@types/minimatch": "^6.0.0", "@typescript-eslint/eslint-plugin": "^3.9.1", "@typescript-eslint/parser": "^3.9.1", "clean-webpack-plugin": "^4.0.0", "eslint": "^7.7.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.20.6", "prettier": "^2.0.5", "ts-loader": "^9.5.2", "typescript": "^4.9.5"}, "dependencies": {"@app/utils": "workspace:*", "@app/theme": "workspace:*"}}