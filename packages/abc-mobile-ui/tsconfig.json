{"extends": "../../tsconfig.json", "compilerOptions": {"target": "es2016", "jsx": "react", "module": "commonjs", "moduleResolution": "node", "declaration": true, "emitDeclarationOnly": false, "outDir": "./dist", "declarationDir": "./dist", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "baseUrl": "../../", "paths": {"@app/theme": ["packages/theme/dist"], "@app/utils": ["packages/utils/dist"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}