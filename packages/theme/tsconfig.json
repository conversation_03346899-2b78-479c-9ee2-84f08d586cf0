{
  "compilerOptions": {
    "target": "es2016",
    "jsx": "react",
    "module": "commonjs",
    "moduleResolution": "node",
    "declaration": true,
    "emitDeclarationOnly": false,
    "outDir": "./dist",
    "declarationDir": "./dist",
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "skipLibCheck": true,
    "paths": {
      "@app/utils": ["../utils/dist"],
    },
    "types": ["node"]
  },
  "include": ["src"],
  "exclude": ["node_modules", "dist"]
}