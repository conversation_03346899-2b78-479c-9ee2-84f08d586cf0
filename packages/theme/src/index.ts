/**
 * Created by he<PERSON><PERSON> on 2020/3/11.
 */
export { default as Colors } from "./colors";
export * from "./colors";
export { default as FontSizes } from "./font-sizes";
export { default as FontWeights } from "./font-weights";
export { default as TextStyles, TextStyle, DINAlternate, refreshTextStyle, DINMittelschriftAlternate, KarlaRegulars } from "./text-styles";
export { default as Sizes } from "./sizes";
export * from "./styles";

import { ThemeType, ThemeNames, ThemeManager } from "./themes";
export { ThemeType, ThemeNames, ThemeManager };

let _sharedPreferences: any = null;
let _appInfo: any = null;

interface optionsInterface {
    sharedPreferences?: any;
    appInfo?: any;
}
export function themeInit(options: optionsInterface): void {
    if (options?.sharedPreferences) {
        _sharedPreferences = options.sharedPreferences;
    }

    if (options?.appInfo) {
        _appInfo = options.appInfo;
    }
}
export function getSharedPreferences(): any {
    return _sharedPreferences;
}

let currentTheme = ThemeType.normal;

export function getAppInfo(): any {
    return _appInfo;
}

export function setAbcTheme(theme: number) {
    const oldTheme = currentTheme;
    currentTheme = theme;

    if (currentTheme !== oldTheme) {
        ThemeManager.setTheme(theme);
    }
}
