/**
 * 成都字节星球科技公司
 *
 * Created by <PERSON><PERSON><PERSON> on 2020-03-22
 *
 * @description
 *
 */

// @ts-ignore
import { LayoutEvent, Style, TouchEvent } from "@hippy/react";
import colors from "./colors";

import { Colors, Sizes } from "./index";
import { DeviceUtils, Version } from "@app/utils";
import { ThemeManager } from "./themes";
import { getAppInfo } from "./index";
import { Color } from "./default-colors";

export interface StyleObj {
    [key: string]: Style;
}

export class ABCStyleSheet {
    static create(styleObj: StyleObj): StyleObj {
        return styleObj;
    }
}

function _buildABCStyles(): ABCStylesCommon {
    // @ts-ignore
    return ABCStyleSheet.create({
        absoluteFillObject: {
            position: "absolute",
            left: 0,
            top: 0,
            right: 0,
            bottom: 0,
        },

        centerChild: {
            flex: 1,
            alignItems: "center",
            justifyContent: "center",
        },

        bottomLine: {
            borderColor: Colors.dividerLineColor,
            borderBottomWidth: Sizes.dpHalf, // 解决部分底部线条显示问题
            borderRadius: DeviceUtils.isIOS() ? 1 : undefined,
        },
        rightLine: {
            borderColor: Colors.dividerLineColor,
            borderRightWidth: Sizes.dpHalf, // 解决部分底部线条显示问题
            borderRadius: DeviceUtils.isIOS() ? 1 : undefined,
        },
        topLine: {
            borderColor: colors.dividerLineColor,
            borderTopWidth: 0.5,
            borderRadius: DeviceUtils.isIOS() ? 1 : undefined, //IOS iphonex等机型上0.5的边线显示问题,加个radius就好了(未分析原因)
        },

        listHorizontalMargin: {
            marginHorizontal: Sizes.listHorizontalMargin,
        },

        listHorizontalPadding: {
            paddingHorizontal: Sizes.listHorizontalMargin,
        },

        borderOne: {
            borderWidth: 1,
            borderColor: Colors.P1,
        },

        borderOneRed: {
            borderWidth: 1,
            borderColor: Colors.errorBorder,
        },
        rowAlignCenter: {
            flexDirection: "row",
            alignItems: "center",
        },

        rowAlignCenterSpaceBetween: {
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
        },
        listItem: {
            flexDirection: "row",
            alignItems: "center",
            height: Sizes.listItemHeight,
            backgroundColor: Colors.contentBgColor,
        },

        listItemWithPadding: {
            flexDirection: "row",
            alignItems: "center",
            height: Sizes.listItemHeight,
            backgroundColor: Colors.contentBgColor,
            paddingHorizontal: Sizes.listHorizontalMargin,
        },
        listItemWithPaddingAndBottomLine: {
            flexDirection: "row",
            alignItems: "center",
            height: Sizes.listItemHeight,
            paddingHorizontal: Sizes.listHorizontalMargin,
            borderColor: colors.dividerLineColor,
            borderBottomWidth: Sizes.dpHalf,
            borderRadius: DeviceUtils.isIOS() ? 1 : undefined,
        },

        outline1DP3Radius: {
            borderWidth: 1,
            borderColor: Colors.P6,
            borderRadius: 3,
        },

        halfDPBorder: {
            borderWidth: Sizes.dpHalf,
            borderColor: Colors.P6,
            borderRadius: Sizes.dp3,
        },

        halfDPActiveBorder: {
            borderWidth: Sizes.dpHalf,
            borderColor: Colors.mainColor,
            borderRadius: Sizes.dp3,
        },
        errorBorder: {
            borderLeftWidth: 1,
            borderTopWidth: 1,
            borderRightWidth: 1,
            borderBottomWidth: 1,
            borderColor: Colors.errorBorder,
        },
        absoluteFill: {
            position: "absolute",
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
        },
        rowAlignCenterWithPadding: {
            flexDirection: "row",
            alignItems: "center",
            paddingHorizontal: Sizes.listHorizontalMargin,
        },
        flex1: {
            flex: 1,
        },
        panelTopStyle: {
            backgroundColor: Colors.white,
            borderTopLeftRadius: Sizes.dp6,
            borderTopRightRadius: Sizes.dp6,
            overflow: "hidden",
            borderBottomLeftRadius: 1,
            borderBottomRightRadius: 1,
        },
        dialogBaseTopRadius: {
            borderTopLeftRadius: Math.floor(Sizes.dp6),
            borderTopRightRadius: Math.floor(Sizes.dp6),
            overflow: "hidden",
        },
    });
}

interface ABCStylesCommon {
    absoluteFillObject: Style;
    centerChild: Style;

    bottomLine: Style;
    rightLine: Style;
    topLine: Style;

    listHorizontalMargin: Style;

    listHorizontalPadding: Style;

    borderOne: Style;

    borderOneRed: Style;
    rowAlignCenter: Style;
    rowAlignCenterSpaceBetween: Style;

    listItem: Style;
    listItemWithPadding: Style;
    listItemWithPaddingAndBottomLine: Style;

    outline1DP3Radius: Style;

    halfDPBorder: Style;

    halfDPActiveBorder: Style;
    errorBorder: Style;
    absoluteFill: Style;

    //行居中带padding 16
    rowAlignCenterWithPadding: Style;

    //panelStyle
    panelTopStyle: Style;

    //弹窗面板顶部圆角
    dialogBaseTopRadius: Style;

    flex1: Style;
}

export const ABCStyles: ABCStylesCommon = _buildABCStyles();

//主题变化时，重新刷新配置
ThemeManager.themeObserver.subscribe(() => {
    Object.assign(ABCStyles, _buildABCStyles());
});

export function flattenStyles(styles?: Style | (Style | undefined)[]): Style {
    if (styles instanceof Array) {
        const finalStyles: Style = {};
        styles.forEach((style?: Style) => {
            if (style) Object.assign(finalStyles, style);
        });

        return finalStyles;
    }

    return styles ?? {};
}

export function hexa2hex(hex: Color, alpha: number): string {
    if (!hex) return hex;

    // 从 hex 字符串中分离出 R、G 和 B 值
    const r = parseInt(hex.substring(1, 3), 16);
    const g = parseInt(hex.substring(3, 5), 16);
    const b = parseInt(hex.substring(5, 7), 16);

    // 对 R、G 和 B 值进行调整，以保持颜色相似性
    const rNew = Math.round(r * (1 - alpha) + 255 * alpha);
    const gNew = Math.round(g * (1 - alpha) + 255 * alpha);
    const bNew = Math.round(b * (1 - alpha) + 255 * alpha);

    // 将新的 R、G 和 B 值组合为一个新的 hex 字符串
    const hexNew = `#${rNew.toString(16)}${gNew.toString(16)}${bNew.toString(16)}`;

    // 返回新的 hex 颜色值
    return hexNew;
}

export function createShadowStyle(style: Style, hideInAndroid = false): Style {
    const lessThan240 = new Version(getAppInfo()?.appVersion).compareTo(new Version("2.4.0")) < 0;
    if (DeviceUtils.isIOS() || !lessThan240) {
        return flattenStyles(style);
    }
    if (hideInAndroid) return {};
    const { shadowColor } = style;
    return { ...ABCStyles.halfDPBorder, color: shadowColor };
}

interface AbcTouchableProps {
    /**
     * The touchdown event occurs when the user touches an component.
     *
     * @param {Object} evt - Touch event data
     * @param {number} evt.page_x - Touch coordinate X
     * @param {number} evt.page_y = Touch coordinate Y
     */
    onTouchDown?(evt: TouchEvent): boolean | undefined | void;

    /**
     * The touchmove event occurs when the user moves the finger across the screen.

     *
     * @param {Object} evt - Touch event data
     * @param {number} evt.page_x - Touch coordinate X
     * @param {number} evt.page_y = Touch coordinate Y
     */
    onTouchMove?(evt: TouchEvent): boolean | undefined | void;

    /**
     * The touchend event occurs when the user removes the finger from an component.
     *
     * @param {Object} evt - Touch event data
     * @param {number} evt.page_x - Touch coordinate X
     * @param {number} evt.page_y = Touch coordinate Y
     */
    onTouchEnd?(evt: TouchEvent): boolean | undefined | void;

    /**
     * The touchcancel event occurs when the touch event gets interrupted.
     *
     * @param {Object} evt - Touch event data
     * @param {number} evt.page_x - Touch coordinate X
     * @param {number} evt.page_y = Touch coordinate Y
     */
    onTouchCancel?(evt: TouchEvent): boolean | undefined | void;
}

interface AbcClickableProps {
    /**
     * Called when the touch is released.
     */
    onClick?(): void;

    /**
     * Called when the touch with longer than about 1s is released.
     */
    onLongClick?(): void;
}

interface AbcLayoutableProps {
    /**
     * Invoked on mount and layout changes with:
     *
     * `{nativeEvent: { layout: {x, y, width, height}}}`
     *
     * This event is fired immediately once the layout has been calculated,
     * but the new layout may not yet be reflected on the screen
     * at the time the event is received, especially if a layout animation is in progress.
     *
     * @param {Object} evt - Layout event data
     * @param {number} evt.nativeEvent.x - The position X of component
     * @param {number} evt.nativeEvent.y - The position Y of component
     * @param {number} evt.nativeEvent.width - The width of component
     * @param {number} evt.nativeEvent.hegiht - The height of component
     */
    onLayout?(evt: LayoutEvent): void;
}

interface FocusEvent {
    /**
     * Focus status
     */
    focus: boolean;
}

export interface AbcViewProps extends AbcLayoutableProps, AbcClickableProps, AbcTouchableProps {
    /**
     * Overrides the text that's read by the screen reader when the user interacts with the element.
     * By default, the label is constructed by traversing all the children and accumulating
     * all the Text nodes separated by space.
     */
    accessibilityLabel?: string;
    /**
     * When `true`, indicates that the view is an accessibility element.
     * By default, all the touchable elements are accessible.
     */
    accessible?: boolean;
    /**
     * Views that are only used to layout their children or otherwise don't draw anything may be
     * automatically removed from the native hierarchy as an optimization.
     * Set this property to `false` to disable this optimization
     * and ensure that this `View` exists in the native view hierarchy.
     */
    collapsable?: false;
    /**
     * Specifies what should happen if content overflows an container's box.
     *
     * Default: iOS is 'visible', android is 'hidden'.
     */
    overflow?: "visible" | "hidden";
    focusable?: boolean;
    requestFocus?: boolean;
    nextFocusDownId?: string;
    nextFocusUpId?: string;
    nextFocusLeftId?: string;
    nextFocusRightId?: string;
    style?: Style | Style[];

    /**
     * The focus event occurs when the component is focused.
     *
     * @param {Object} evt - Focus event data
     * @param {boolean} evt.focus - Focus status
     */
    onFocus?(evt: FocusEvent): void;
}
