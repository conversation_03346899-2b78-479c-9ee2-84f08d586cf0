/**
 * Created by he<PERSON><PERSON> on 2020/3/11.
 */
import { pxToDp } from "@app/utils";
import _ from "lodash";

class FontSizes {
    size8!: number;
    size9!: number;
    size10!: number;
    size11!: number;
    size12!: number;
    size13!: number;
    size14!: number;
    size15!: number;
    size16!: number;
    size17!: number;
    size18!: number;
    size19!: number;
    size20!: number;
    size21!: number;
    size22!: number;
    size23!: number;
    size24!: number;
    size25!: number;
    size26!: number;
    size27!: number;

    constructor() {
        _.range(8, 28).forEach((index) => {
            // @ts-ignore
            this[`size${index}`] = pxToDp(index);
        });
    }
}

const fontSize = new FontSizes();

export default fontSize;
