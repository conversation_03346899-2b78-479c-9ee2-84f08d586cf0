{"name": "@app/theme", "version": "1.0.0", "main": "dist/index.js", "module": "dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js"}}, "types": "dist/index.d.ts", "peerDependencies": {"react": "^17.0.2", "react-dom": "^17.0.2"}, "files": ["dist"], "dependencies": {"lodash": "^4.17.15", "@app/utils": "workspace:*"}, "scripts": {"dev": "pnpm run build:types:watch & pnpm run build:js:watch", "build:types": "tsc --emitDeclarationOnly --declaration", "build:js": "webpack --config webpack.config.js", "build": "pnpm run build:types && pnpm run build:js", "build:types:watch": "tsc --emitDeclarationOnly --declaration --watch", "build:js:watch": "webpack --watch --config webpack.config.js", "watch": "pnpm run build:types:watch & pnpm run build:js:watch", "clean": "<PERSON><PERSON><PERSON> dist"}, "devDependencies": {"@types/node": "^22.14.1", "clean-webpack-plugin": "^4.0.0", "typescript": "^4.9.5", "ts-loader": "^9.5.2"}}