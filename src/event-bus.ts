import App from "./app";
import { AbcInputAccessoryView, AbcCustomKeyboardView, AbcTextInput } from "./sub-project/base-ui/views/abc-text-input";
import { AbcMedicineSearchInput } from "./sub-project/base-ui/views/medicine-search-input";

export type EventBusListener = (...args: any[]) => void;

class EventBus {
    private events: Record<string, Set<EventBusListener>> = {};

    on(event: string, listener: EventBusListener): void {
        if (!this.events[event]) this.events[event] = new Set();
        this.events[event].add(listener);
    }

    off(event: string, listener: EventBusListener): void {
        if (!this.events[event]) return;
        this.events[event].delete(listener);
        if (this.events[event].size === 0) {
            delete this.events[event];
        }
    }

    emit(event: string, ...args: any[]): void {
        if (!this.events[event]) return;
        // 拷贝一份，防止遍历时被修改
        Array.from(this.events[event]).forEach((listener) => listener(...args));
    }

    once(event: string, listener: EventBusListener): void {
        const fn: EventBusListener = (...args: any[]) => {
            listener(...args);
            this.off(event, fn);
        };
        this.on(event, fn);
    }

    public inputAccessView?: AbcInputAccessoryView | null;
    public customKeyboardHandler?: AbcCustomKeyboardView | null;

    public abcMedicineSearchInput?: AbcMedicineSearchInput | null;
}

export async function _initEventBus(app: App): Promise<void> {
    eventBus.on("visibleAccessoryView", (textInput: AbcTextInput) => {
        App.AppTextInputAccessoryGlobal = textInput;
        app.setState({});
        eventBus.inputAccessView?.setState({});
    });
    eventBus.on("visibleCustomKeyboard", (textInput: AbcTextInput) => {
        App.AppTextInputCustomKeyboardGlobal = textInput;
        app.setState({});
        eventBus.customKeyboardHandler?.setState({});
    });
    eventBus.on("update", (textAccessoryInput: AbcTextInput, textCustomKeyboardInput: AbcTextInput) => {
        App.AppTextInputAccessoryGlobal = textAccessoryInput;
        App.AppTextInputCustomKeyboardGlobal = textCustomKeyboardInput;
        eventBus.inputAccessView?.setState({});
        eventBus.customKeyboardHandler?.setState({});
    });
}

const eventBus = new EventBus();
export default eventBus;
