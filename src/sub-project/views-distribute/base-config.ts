import { HisType } from "../user-center/data/bean";

export default class BaseConfig {
    private histype: HisType;
    viewFeature = {
        // 新版Home
        homeV2: false,
        // 显示设置
        showSetting: true,
    };

    viewComponents: Record<string, any> = {};
    viewDistributeConfig = {};

    constructor(hisType: HisType) {
        this.histype = hisType;
    }

    static getInstance(hisType: HisType): BaseConfig {
        return new BaseConfig(hisType);
    }
}
