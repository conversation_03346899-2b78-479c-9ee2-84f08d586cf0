import { HisType } from "../user-center/data/bean";
import BaseConfig from "./base-config";
import PharmacyViewConfig from "./biz-pharmacy/config";

let _hisType: HisType;
let _globalDistributeConfig: BaseConfig;

export function initDistributeViewConfig(hisType: HisType = HisType.normal): void {
    if (hisType === _hisType || hisType === undefined) {
        return;
    }
    console.log("初始化hisType", hisType);
    _hisType = hisType;
    if (hisType === HisType.drugstoreButler) {
        _globalDistributeConfig = PharmacyViewConfig.getInstance(hisType);
    } else {
        _globalDistributeConfig = BaseConfig.getInstance(hisType);
    }
}

export function getDistributeConfig(): BaseConfig {
    return _globalDistributeConfig;
}
