import BaseConfig from "../base-config";
import { HisType } from "../../user-center/data/bean";

export default class PharmacyViewConfig extends BaseConfig {
    constructor(hisType: HisType) {
        super(hisType);
        this.viewFeature.homeV2 = true;
        this.viewFeature.showSetting = false;
    }

    static getInstance(hisType: HisType): BaseConfig {
        return new PharmacyViewConfig(hisType);
    }
}
