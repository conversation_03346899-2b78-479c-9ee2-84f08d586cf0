/**
 * create by dengjie
 * desc:
 * create date 2020/12/24
 */

import React from "react";
import { Text, View } from "@hippy/react";
import { BasePage } from "./base-ui";
import { ABCStyles, Colors, TextStyles } from "./theme";

interface UnPermissionPageProps {}

export class UnPermissionPage extends BasePage<UnPermissionPageProps> {
    constructor(props: UnPermissionPageProps) {
        super(props);
    }

    getAppBarTitle(): string {
        return "";
    }

    renderContent(): JSX.Element | undefined {
        return (
            <View style={[ABCStyles.centerChild, { flex: 1, backgroundColor: Colors.white }]}>
                <Text style={[TextStyles.t14NT3, { textAlign: "center" }]}>暂无该功能权限</Text>
            </View>
        );
    }
}
