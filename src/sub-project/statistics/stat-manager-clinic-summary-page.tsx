/**
 * create by <PERSON><PERSON>
 * desc: 统计模块 （营收、运营、业绩）
 * create date 2021/7/2
 */
import React from "react";
import { View } from "@hippy/react";
import { SizedBox, Tab, Tabs } from "../base-ui";
import { StatOperationView } from "./clinic-stat/stat-operation-view";
import { StatPerformanceRevenueView } from "./clinic-stat/stat-performance-revenue-view";
import { StatOperationalSituationView } from "./clinic-stat/stat-operational-situation-view";
import { Colors, Sizes, TextStyles } from "../theme";
import IconFontView from "../base-ui/iconfont/iconfont-view";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { StatisticsUtils } from "./utils/statistics-utils";
import { StatisticsShareButton } from "../share/statistics-share-button";
import { PostShareReportData } from "./data/statistics-bean";
import { ReportAgent } from "../share/report-share/data/report-agent";
import { Toast } from "../base-ui/dialog/toast";
import { WxApi, WxShareScene } from "../base-business/wxapi/wx-api";
import { userCenter } from "../user-center";
import { environment } from "../base-business/config/environment";
import { AssetImageView } from "../base-ui/views/asset-image-view";
import { CaptureView } from "../base-ui/views/capture-view";
import FileUtils from "../common-base-module/file/file-utils";
import { runtimeConstants } from "../data/constants/runtime-constants-manager";
import UiUtils, { pxToDp } from "../base-ui/utils/ui-utils";
import { BaseComponent } from "../base-ui/base-component";
import { StatOperationHospitalView } from "./clinic-stat/views/stat-operation-hospital-view";
import { StatOperationSituationHospitalView } from "./clinic-stat/views/stat-operation-situation-hospital-view";
import { StatPerformanceRevenueHospitalView } from "./clinic-stat/views/stat-performance-revenue-hospital-view";
import { DeviceUtils } from "../base-ui/utils/device-utils";

interface StatManagerClinicSummaryPageProps {
    date?: Date;
}

export class StatManagerClinicSummaryPage extends BaseComponent<StatManagerClinicSummaryPageProps> {
    protected _statOperationViewRef?: StatOperationView | null;
    protected _statOperationalSituationViewRef?: StatOperationalSituationView | null;
    protected _statPerformanceRevenueViewRef?: StatPerformanceRevenueView | null;
    protected _shareIconRef?: CaptureView | null;

    constructor(props: StatManagerClinicSummaryPageProps) {
        super(props);
    }

    getAppBar(): JSX.Element | undefined {
        return undefined;
    }

    ///保存到临时相册
    async _createWxShareIcon(): Promise<string | null> {
        const fileName = "app_icon_thumb.png";
        const path = await FileUtils.getTmpDir(fileName);
        const result = await this._shareIconRef?.captureToFile(path);
        if (result) {
            return path;
        } else {
            return null;
        }
    }

    async handleWxShare(): Promise<void> {
        const _shareData = new PostShareReportData();
        _shareData.statOperation = this._statOperationViewRef?.getShareData();
        _shareData.statOperationalSituation = this._statOperationalSituationViewRef?.getShareData();
        _shareData.statPerformanceRevenue = this._statPerformanceRevenueViewRef?.getShareData();
        const shareKey = await ReportAgent.postShareReportDateKsy({ data: _shareData }).catchIgnore();
        if (!!shareKey) {
            const SHARE_STAT_PATH = `${environment.serverGlobalHostname}/m/daily-report/detail`;
            const shareIcon = await this._createWxShareIcon();
            const clinicId = userCenter.clinic?.clinicId ?? "";
            WxApi.share({
                scene: WxShareScene.session,
                url: `${SHARE_STAT_PATH}?key=${shareKey}&clinicId=${clinicId}&region=${environment.serverHostName}`,
                title: `${runtimeConstants.PRODUCT_NAME_FUll} —— 懂医懂药懂经营`,
                desc: "下载App，门店经营运营数据实时查看",
                iconUrl: shareIcon ?? "",
            }).then();
        } else {
            Toast.show("获取分享内容失败", { warning: true });
        }
    }

    render(): JSX.Element {
        const { date } = this.props;
        const showOperation = StatisticsUtils.showOperation();
        const showAchievement = StatisticsUtils.containBillAchievement();
        const showOperationalSituation = StatisticsUtils.showOperationalSituation();

        const tabList: JSX.Element[] = [];
        const isDrugstoreButler = !!userCenter.clinic?.isDrugstoreButler;
        if (!!date || showOperation) {
            tabList.push(
                <Tab key={"RevenueDetails"} title={"营收概况"}>
                    <StatOperationView
                        ref={(ref) => {
                            this._statOperationViewRef = ref;
                        }}
                        date={date}
                    />
                </Tab>
            );
        }
        if (!isDrugstoreButler && (!!date || showOperationalSituation)) {
            tabList.push(
                <Tab key={"OperationalSituation"} title={"运营概况"}>
                    <StatOperationalSituationView
                        ref={(ref) => {
                            this._statOperationalSituationViewRef = ref;
                        }}
                        date={date}
                    />
                </Tab>
            );
        }
        if (!!date || showAchievement) {
            tabList.push(
                <Tab key={"PerformanceRevenue"} title={`${isDrugstoreButler ? "销售" : "开单"}业绩`}>
                    <StatPerformanceRevenueView
                        ref={(ref) => {
                            this._statPerformanceRevenueViewRef = ref;
                        }}
                        date={date}
                    />
                </Tab>
            );
        }
        return (
            <View style={{ flex: 1, backgroundColor: Colors.window_bg }}>
                <AssetImageView
                    name={"statistics_bg"}
                    ignoreTheme={false}
                    resizeMode={"cover"}
                    style={{ position: "absolute", top: 0, left: 0, right: 0, height: pxToDp(375) }}
                />
                <SizedBox height={UiUtils.safeStatusHeight()} />
                <Tabs
                    lazy={false}
                    style={{ backgroundColor: Colors.transparent }}
                    tabsStyle={{
                        justifyContent: "center",
                        paddingHorizontal: Sizes.listHorizontalMargin,
                        height: Sizes.dp44,
                    }}
                    tabStyle={{ marginRight: Sizes.dp28 }}
                    titleBorderStyle={{ width: Sizes.dp20, left: Sizes.dp22 }}
                    currentStyle={{ ...TextStyles.t16MT1 }}
                    leftSuffix={() => {
                        return (
                            <IconFontView
                                name={"back"}
                                size="20"
                                color={Colors.black}
                                style={{
                                    ...Sizes.paddingLTRB(
                                        DeviceUtils.isOhos() ? 0 : Sizes.dp16,
                                        DeviceUtils.isOhos() ? Sizes.dp7 : Sizes.dp14,
                                        DeviceUtils.isOhos() ? 0 : Sizes.dp16,
                                        Sizes.dp10
                                    ),
                                    marginLeft: DeviceUtils.isOhos() ? Sizes.dp16 : 0,
                                }}
                                onClick={() => {
                                    ABCNavigator.pop();
                                }}
                            />
                        );
                    }}
                    rightSuffix={() => {
                        return !date || !!userCenter.clinic?.isManagerModule ? (
                            <StatisticsShareButton onShareWx={this.handleWxShare.bind(this)} />
                        ) : (
                            <IconFontView
                                name={"back"}
                                size="20"
                                color={Colors.transparent}
                                style={Sizes.paddingLTRB(Sizes.dp16, Sizes.dp14, Sizes.dp16, Sizes.dp10)}
                            />
                        );
                    }}
                >
                    {tabList}
                </Tabs>
            </View>
        );
    }
}

export class StatManagerClinicHospitalSummaryPage extends StatManagerClinicSummaryPage {
    render(): JSX.Element {
        const { date } = this.props;
        const showOperation = StatisticsUtils.showOperation();
        const showAchievement = StatisticsUtils.containBillAchievement();
        const showOperationalSituation = StatisticsUtils.showOperationalSituation();

        const tabList: JSX.Element[] = [];
        if (!!date || showOperation) {
            tabList.push(
                <Tab key={"RevenueDetails"} title={"营收概况"}>
                    <StatOperationHospitalView
                        ref={(ref) => {
                            this._statOperationViewRef = ref;
                        }}
                        date={date}
                    />
                </Tab>
            );
        }
        if (!!date || showOperationalSituation) {
            tabList.push(
                <Tab key={"OperationalSituation"} title={"运营概况"}>
                    <StatOperationSituationHospitalView
                        ref={(ref) => {
                            this._statOperationalSituationViewRef = ref;
                        }}
                        date={date}
                    />
                </Tab>
            );
        }
        if (!!date || showAchievement) {
            tabList.push(
                <Tab key={"PerformanceRevenue"} title={`开单业绩`}>
                    <StatPerformanceRevenueHospitalView
                        ref={(ref) => {
                            this._statPerformanceRevenueViewRef = ref;
                        }}
                        date={date}
                    />
                </Tab>
            );
        }
        return (
            <View style={{ flex: 1, backgroundColor: Colors.window_bg }}>
                <AssetImageView
                    name={"statistics_bg"}
                    ignoreTheme={false}
                    resizeMode={"cover"}
                    style={{ position: "absolute", top: 0, left: 0, right: 0, height: pxToDp(375) }}
                />
                <SizedBox height={UiUtils.safeStatusHeight()} />
                <Tabs
                    customKey={"hospital_summary_tabs"}
                    lazy={false}
                    style={{ backgroundColor: Colors.transparent }}
                    tabsStyle={{
                        justifyContent: "center",
                        paddingHorizontal: Sizes.listHorizontalMargin,
                        height: Sizes.dp44,
                    }}
                    tabStyle={{ marginRight: Sizes.dp28 }}
                    titleBorderStyle={{ width: Sizes.dp20, left: Sizes.dp22 }}
                    currentStyle={{ ...TextStyles.t16MT1 }}
                    leftSuffix={() => {
                        return (
                            <IconFontView
                                name={"back"}
                                size="20"
                                color={Colors.black}
                                style={{ ...Sizes.paddingLTRB(Sizes.dp16, Sizes.dp14, Sizes.dp16, Sizes.dp10) }}
                                onClick={() => {
                                    ABCNavigator.pop();
                                }}
                            />
                        );
                    }}
                    rightSuffix={() => {
                        return !date || !!userCenter.clinic?.isManagerModule ? (
                            <StatisticsShareButton onShareWx={this.handleWxShare.bind(this)} />
                        ) : (
                            <IconFontView
                                name={"back"}
                                size="20"
                                color={Colors.transparent}
                                style={{ ...Sizes.paddingLTRB(Sizes.dp16, Sizes.dp14, Sizes.dp16, Sizes.dp10) }}
                            />
                        );
                    }}
                >
                    {tabList}
                </Tabs>
            </View>
        );
    }
}
