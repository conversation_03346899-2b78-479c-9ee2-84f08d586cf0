/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2023/1/11
 * @Copyright 成都字节流科技有限公司© 2023
 * @Example
 * authorityManager.availableAuthority(AuthorityAvailableSettingConstants.ACTION_APP_CAMERA).then(res => res.status)
 */
import { callNativeWithPromise } from "@hippy/react";
import { AuthorityAvailableSettingConstants, AuthoritySettingConstants } from "./authority-constants";

const kModuleName = "AuthorityManage";

interface AuthorityResponse {
    status: boolean;
}

class AuthorityManager {
    /**
     * 跳转到当前系统设置页面
     * @param authorityKey
     */
    public async authoritySysSettingPage(authorityKey: AuthoritySettingConstants): Promise<AuthorityResponse> {
        return callNativeWithPromise(kModuleName, "navToSysSetting", {
            authorityKey: authorityKey,
        });
    }

    /**
     * 校验当前app是否包含某项权限
     * @param authorityKey
     */
    public async availableAuthority(authorityKey: AuthorityAvailableSettingConstants): Promise<AuthorityResponse> {
        return callNativeWithPromise(kModuleName, "availableAuthority", {
            authorityKey: authorityKey,
        });
    }
}

const authorityManager = new AuthorityManager();
export { authorityManager };
