/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2023/1/11
 * @Copyright 成都字节流科技有限公司© 2023
 */

/**
 * app系统权限跳转设置页
 */
enum AuthoritySettingConstants {
    ACTION_APP_NOTIFICATION_SETTINGS = "ACTION_APP_NOTIFICATION_SETTINGS", // app消息通知页面
    ACTION_APPLICATION_DETAILS_SETTINGS = "ACTION_APPLICATION_DETAILS_SETTINGS", //跳转app的应用信息界面

    /** @deprecated 暂未实现 */
    ACTION_ACCESSIBILITY_SETTINGS = "ACTION_ACCESSIBILITY_SETTINGS", // 系统辅助功能页面
    /** @deprecated 暂未实现 */
    ACTION_ADD_ACCOUNT = "ACTION_ADD_ACCOUNT", //添加账户页面
    /** @deprecated 暂未实现 */
    ACTION_AIRPLANE_MODE_SETTINGS = "ACTION_AIRPLANE_MODE_SETTINGS", // 飞行模式页面
    /** @deprecated 暂未实现 */
    ACTION_MANAGE_APPLICATIONS_SETTINGS = "ACTION_MANAGE_APPLICATIONS_SETTINGS", //跳转默认应用管理（默认应用）列表页面
    /** @deprecated 暂未实现 */
    ACTION_MANAGE_ALL_APPLICATIONS_SETTINGS = "ACTION_MANAGE_ALL_APPLICATIONS_SETTINGS", //跳转默认应用管理（全部应用）列表页面
    /** @deprecated 暂未实现 */
    ACTION_BLUETOOTH_SETTINGS = "ACTION_BLUETOOTH_SETTINGS", //跳转蓝牙管理页面
    /** @deprecated 暂未实现 */
    ACTION_DATA_ROAMING_SETTINGS = "ACTION_DATA_ROAMING_SETTINGS", //网络管理页面
    /** @deprecated 暂未实现 */
    ACTION_INPUT_METHOD_SETTINGS = "ACTION_INPUT_METHOD_SETTINGS", //语言和时间管理页面
    /** @deprecated 暂未实现 */
    ACTION_DEVICE_INFO_SETTINGS = "ACTION_DEVICE_INFO_SETTINGS", //关于手机页面
    /** @deprecated 暂未实现 */
    ACTION_DISPLAY_SETTINGS = "ACTION_DISPLAY_SETTINGS", //显示和亮度页面
    /** @deprecated 暂未实现 */
    ACTION_LOCATION_SOURCE_SETTINGS = "ACTION_LOCATION_SOURCE_SETTINGS", //系统定位服务页面
    /** @deprecated 暂未实现 */
    ACTION_SETTINGS = "ACTION_SETTINGS", //系统设置主界面
    /** @deprecated 暂未实现 */
    ACTION_SOUND_SETTINGS = "ACTION_SOUND_SETTINGS", //声音设置页面
    /** @deprecated 暂未实现 */
    ACTION_WIFI_SETTINGS = "ACTION_WIFI_SETTINGS", //wlan界面
}

/**
 * app系统权限校验
 */
enum AuthorityAvailableSettingConstants {
    ACTION_APP_NOTIFICATION = "ACTION_APP_NOTIFICATION", //消息提示权限
    ACTION_APP_CAMERA = "ACTION_APP_CAMERA", //相机授权权限
}

export { AuthorityAvailableSettingConstants, AuthoritySettingConstants };
