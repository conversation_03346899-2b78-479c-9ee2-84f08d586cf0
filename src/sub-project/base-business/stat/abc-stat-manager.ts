/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description ABC 组件耗时统计
 * <AUTHOR>
 * @CreateDate 2023/10/26
 * @Copyright 成都字节流科技有限公司© 2023
 */
import FileUtils from "../../common-base-module/file/file-utils";
import { fromJsonToDate, JsonMapper, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { environment } from "../config/environment";
import { LogUtils } from "../../common-base-module/log";
import { UUIDGen } from "../../common-base-module/utils";

class AbcStatManagerLogListItem {
    @JsonProperty({ fromJson: fromJsonToDate })
    date?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    createTime?: Date;
    appUA?: string;
    isUpload?: boolean;
}
class AbcStatManagerLogDetail {
    id?: string;
    title?: string;
    label?: string;
    value?: any;
    createTime?: string;
    moduleName?: string;
}
export class AbcStatManager {
    dataList: AbcStatManagerLogListItem[] = [];
    detail: AbcStatManagerLogDetail[] = [];
    // 索引文件名
    private readonly indexFileName = "index";
    private readonly nowDateStr = new Date().format("yyyy-MM-dd");

    constructor() {
        this.initPerformanceFile();
    }

    /**
     * 初始化相关本地文件
     * 包含索引文件和详细文件
     * performance-|-2023-10-26.dat
     *             |-2023-10-27.dat
     *             |-index.dat
     */
    public async initPerformanceFile() {
        //创建+更新索引文件
        //创建日志文件
        const dir = await this._getPerformanceLogIndexFile();
        let dataListStr = "";
        if (await FileUtils.fileExists(dir)) {
            dataListStr = await FileUtils.readAsString(dir);
        }
        if (!!dataListStr.length) {
            this.dataList = (JSON.parse(dataListStr) as []).map((item) => JsonMapper.deserialize(AbcStatManagerLogListItem, item));
        }
        const newDate = new Date();
        const detailDir = await this._getPerformanceLogFile(newDate);
        if (!this.dataList.find((item) => item.date?.format("yyyy-MM-dd") == this.nowDateStr)) {
            this.dataList.unshift({ date: newDate, createTime: newDate, appUA: environment.appUA() });
            //写入索引文件和详情文件
            await Promise.all([
                FileUtils.writeAsString(detailDir, JSON.stringify(this.detail)),
                FileUtils.writeAsString(dir, JSON.stringify(this.dataList)),
            ]);
        } else {
            //初始化Detail
            const detailStr = await FileUtils.readAsString(detailDir);
            if (!!detailStr.length) {
                this.detail = (JSON.parse(detailStr) as []).map((item) => JsonMapper.deserialize(AbcStatManagerLogDetail, item));
            }
        }
        //上传文件数据
        this.dataList.map((item) => {
            if (!item.isUpload && item.date?.format("yyyy-MM-dd") != this.nowDateStr) {
                this.uploadPerformanceFile(item);
            }
        });
    }

    /**
     * 上传
     */
    public async uploadPerformanceFile(detail: AbcStatManagerLogListItem) {
        //组装需要上传的数据
        if (!detail.date) return;
        const dir = await this._getPerformanceLogIndexFile();
        const detailDir = await this._getPerformanceLogFile(detail.date);
        const detailStr = await FileUtils.readAsString(detailDir);
        if (!!detailStr.length) {
            LogUtils.d("AbcStatManager uploadPerformanceFile Detail ===> \n" + detailStr);
            await new Promise((resolve) => {
                setTimeout(() => {
                    resolve();
                }, 3000);
            }).then(() => {
                LogUtils.d("AbcStatManager uploadPerformanceFile Success ===> \n");
                detail.isUpload = true;
                // 上传成功后删除本地文件
                this._deletePerformanceLogFile(detailDir);
                FileUtils.writeAsString(dir, JSON.stringify(this.dataList));
            });
        }
    }

    /**
     * 保存一条统计数据
     */
    public async savePerformanceDetail(_detail: AbcStatManagerLogDetail) {
        const detail = { ..._detail };
        detail.id = UUIDGen.generate();
        this.detail.push(detail);
        const detailDir = await this._getPerformanceLogFile();
        LogUtils.d("AbcStatManager savePerformanceDetail detailDir" + detailDir);
        FileUtils.writeAsString(detailDir, JSON.stringify(this.detail));
    }

    /**
     * 获取当前目录根目录
     * @private
     */
    private async _getPerformanceLogRootDirFile(): Promise<string> {
        const docPath = await FileUtils.getDocPath();
        const performanceDir = `${docPath}/performance`;
        const dir = await FileUtils.fileExists(performanceDir);
        if (dir) {
            return performanceDir;
        }
        await FileUtils.createDir(performanceDir);
        return performanceDir;
    }

    /**
     * 获取当前模块索引文件详情文件路径
     * @private
     */
    private async _getPerformanceLogIndexFile(): Promise<string> {
        const fileDirName = await this._getPerformanceLogRootDirFile();
        return `${fileDirName}/${this.indexFileName}.dat`;
    }

    /**
     * 获取固定日期的报告详情文件路径
     * @param date
     * @private
     */
    private async _getPerformanceLogFile(date: Date = new Date()): Promise<string> {
        const fileDirName = await this._getPerformanceLogRootDirFile();
        return `${fileDirName}/${date.format("yyyy-MM-dd")}.dat`;
    }

    /**
     * 删除已上传文件
     */
    private async _deletePerformanceLogFile(dir: string): Promise<void> {
        await FileUtils.deleteFile(dir).catchIgnore();
    }
}

export const AbcStatManagerInstance = new AbcStatManager();
