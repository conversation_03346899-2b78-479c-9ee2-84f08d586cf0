/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/8/12
 *
 * @description
 */
import { callNativeWithPromise } from "@hippy/react";
import { AppInfo } from "../config/app-info";
import { AbcPlatformMix } from "../native-modules/abc-platform-mix";
import { LogUtils } from "../../common-base-module/log";

const kModuleName = "StatManager";

class StatManager {
    async init() {
        const channelId = await AbcPlatformMix.getAssetAsString("channel.txt").catch((/*ignore*/) => {
            return "unknown";
        });
        callNativeWithPromise(kModuleName, "init", {
            key: AppInfo.umengAppId,
            channel: channelId.trim(),
            reportCrash: false,
            logEnable: true,
            encrypt: true,
        });
    }

    public event(event: string, params?: { label?: string; map?: { [key: string]: any } }): void {
        LogUtils.d("StatManager.statEvent event:" + event);
        callNativeWithPromise(kModuleName, "event", {
            name: event,
            label: params?.label,
            map: params?.map,
        });
    }

    public beginPageView(name: string) {
        LogUtils.d("StatManager.statBeginPage name:" + name);
        callNativeWithPromise(kModuleName, "beginPageView", {
            name: name,
        });
    }

    public endPageView(name: string) {
        LogUtils.d("StatManager.statEndPage name:" + name);
        callNativeWithPromise(kModuleName, "endPageView", {
            name: name,
        });
    }
}

const statManager = new StatManager();

function statEvent(event: string, params?: { label?: string; map?: { [key: string]: any } }): void {
    statManager.event(event, params);
}

function statBeginPage(name: string): void {
    statManager.beginPageView(name);
}

function statEndPage(name: string): void {
    statManager.endPageView(name);
}

export { statManager, statEvent, statBeginPage, statEndPage };
