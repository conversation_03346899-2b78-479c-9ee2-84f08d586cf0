import { callNativeWithPromise } from "@hippy/react";
import { LogUtils } from "../../common-base-module/log";
import _ from "lodash";

/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/7/9An
 *
 * @description 用于调用微信相关native层api接口
 */

const kModuleName = "AliPay";

export enum AliPayResultStatus {
    success = 9000,
    waitingProcess = 8000,
    //8000	正在处理中，支付结果未知（有可能已经支付成功），请查询商户订单列表中订单的支付状态
    // 4000	订单支付失败
    // 5000	重复请求
    // 6001	用户中途取消
    // 6002	网络连接出错
    // 6004	支付结果未知（有可能已经支付成功），请查询商户订单列表中订单的支付状态
    // 其它	其它支付错误
}

interface AliPayResult {
    resultStatus: AliPayResultStatus;
    memo?: string;
    result: string;
}

export class AliPay {
    /**
     * 调起
     * @param params
     *
     * 返回结果，参考文档https://opendocs.alipay.com/open/204/105301
     * rsp: e.g:
     * {"resultStatus":"6001","result":"","memo":"支付未完成。"}
     * {"resultStatus":"9000","result":"{\"alipay_trade_app_pay_response\":{\"code\":\"10000\",\"msg\":\"Success\",\"app_id\":\"2021001173649494\",\"auth_app_id\":\"2021001173649494\",\"charset\":\"UTF-8\",\"timestamp\":\"2020-08-25 17:32:14\",\"out_trade_no\":\"705745933787480064\",\"total_amount\":\"0.01\",\"trade_no\":\"2020082522001430960561035309\",\"seller_id\":\"2088831641589764\"},\"sign\":\"C1FAREFCmEH/YIU/8iVeZf96BGYdtTqJ+fekRMvcgeBrBXNUSTP1lfr9a/JJcpHIB/XXG7Lum2YPKkMts44O/4YnX2QfP5YMuJyQ9aNHC47bJGNMFNylTlEL9jvK0O/10vtItWLTXXkehBF2aiREJMMrQzQl9T9n7C122qk+Zj+fjhURo2/FnV80MRYt2S2+Hoy7EuoHi7mv5YzCiFN6oq8GOTfiJ71eDyn6JcHtWtFR925YDYxFxGTa/wcosU5HuQSxiNGINk0xKVTyw9/gR1P+j1IySkbCjMTTE3/aanZLcg8iWkWBDIm87Hr9ErQia8DW/R3IGMr9k7t21F4bbg==\",\"sign_type\":\"RSA2\"}","memo":"","extendInfo":"{\"doNotExit\":true,\"isDisplayResult\":true}"}
     */
    static async payment(params: { orderInfo: string }): Promise<AliPayResult> {
        return callNativeWithPromise<any>(kModuleName, "payment", params)
            .catch((/*ignore: any*/) => {
                return false;
            })
            .then((rsp: AliPayResult) => {
                try {
                    LogUtils.d("AliPay.payment params = " + JSON.stringify(params) + ", result = " + JSON.stringify(rsp));
                    if (_.isString(rsp.resultStatus)) {
                        rsp.resultStatus = Number(rsp.resultStatus);
                    }
                } catch (e) {}
                return rsp;
            });
    }
}
