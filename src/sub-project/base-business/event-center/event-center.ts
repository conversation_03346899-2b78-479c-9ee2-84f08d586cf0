/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/7/24
 *
 * @description
 */
import { Subject, Subscription } from "rxjs";

export class EventBase {}

export class HomePageFirstShowEvent extends EventBase {}
export class NavigatorReadyEvent extends EventBase {}
export class AppShowEvent extends EventBase {}

class EventCenter {
    private eventBus = new Subject<EventBase>();

    post(event: EventBase) {
        this.eventBus.next(event);
    }

    subscribe(callback: (event: EventBase) => void): Subscription {
        return this.eventBus.subscribe((event) => {
            try {
                callback(event);
            } catch (e) {}
        });
    }
}

const eventCenter = new EventCenter();
export { eventCenter };
