import { LogUtils } from "../../common-base-module/log";
import { DisposableTracker } from "../../common-base-module/cleanup/disposable";
import { environment } from "../config/environment";
import { userCenter } from "../../user-center";
import { abcNetDelegate } from "../../net/abc-net-delegate";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import { SocketIo } from "../../common-base-module/socket-io/socket-io";
import { AnyType } from "../../common-base-module/common-types";

export type IListener = (...args: any[]) => void;

export class AbcFeSocket extends DisposableTracker {
    private socket?: SocketIo;
    private groupIdMap: Record<string, Array<IListener>> = {};
    private eventListenerMap: Record<string, { listeners: Array<IListener>; delegateListener: IListener }> = {};
    private reconnectAttempts = 0;
    private readonly maxReconnectAttempts = 5;
    private readonly reconnectDelay = 3000;
    private isConnecting = false;

    constructor() {
        super();
        this.connect();
    }

    private connect() {
        if (this.isConnecting || this.socket) {
            LogUtils.d("AbcFeSocket: Already connected or connecting");
            return;
        }

        if (!userCenter.isLogin()) {
            LogUtils.d("AbcFeSocket: User not logged in");
            return;
        }

        try {
            this.isConnecting = true;
            const socketUrl = `${environment.serverHostScheme}://${environment.serverHostName}`;
            const appToken = userCenter.appToken();
            const grayFlag = abcNetDelegate.grayFlag;

            LogUtils.d(`AbcFeSocket.connect socketUrl = ${socketUrl}, grayFlag = ${grayFlag}, appToken = ${appToken}`);

            const extraHeaders: any = {
                grayflag: grayFlag ?? "",
                APP_UA: environment.appUA() || "",
                Origin: environment.serverHostScheme + "://" + environment.serverHostName,
                "Content-Type": "application/json",
                Authorization: `Bearer ${appToken}`,
            };

            const _params = {
                namespace: `/app${DeviceUtils.isAndroid() ? "?sceneType=2" : ""}`,
                cookies: `_abcyun_token_=${appToken}; domain=${environment.serverHostName}; path=/`,
                extraHeaders: extraHeaders,
                connectParams: DeviceUtils.isIOS() ? { sceneType: 2 } : undefined,
                log: true,
                forceWebsockets: true,
            };

            this.close();

            this.socket = new SocketIo(socketUrl, _params);

            // 设置一个较长的超时时间
            const connectionTimeout = setTimeout(() => {
                if (this.socket) {
                    LogUtils.e("AbcFeSocket: Connection timeout");
                    this.close();
                }
            }, 15000);

            this.socket.on("connect", () => {
                clearTimeout(connectionTimeout);
                LogUtils.d("AbcFeSocket: Connected successfully");
                this.isConnecting = false;
                this.reconnectAttempts = 0;
                this.resubscribeGroups();
            });

            this.socket.on("disconnect", () => {
                LogUtils.d("AbcFeSocket: Disconnected");
                this.handleReconnect();
            });

            this.socket.connect();
        } catch (error) {
            LogUtils.e("AbcFeSocket connection error:", error);
            this.isConnecting = false;
            this.handleReconnect();
        }
    }

    private handleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            LogUtils.e("AbcFeSocket: Max reconnection attempts reached");
            return;
        }

        if (!userCenter.isLogin()) {
            LogUtils.d("AbcFeSocket: User not logged in, skip reconnect");
            return;
        }

        this.reconnectAttempts++;
        LogUtils.d(`AbcFeSocket: Reconnecting attempt ${this.reconnectAttempts}`);
        setTimeout(() => {
            if (userCenter.isLogin()) {
                this.connect();
            }
        }, this.reconnectDelay);
    }

    private resubscribeGroups() {
        const groupIds = Object.keys(this.groupIdMap);
        if (groupIds.length > 0) {
            LogUtils.d("AbcFeSocket: Resubscribing groups:", groupIds);
            this.emit("subscribe", { eventGroupIds: groupIds });
        }
    }

    public emit(event: string, data: AnyType): void {
        if (!this.socket) {
            LogUtils.w("AbcFeSocket: Socket not connected");
            return;
        }

        try {
            LogUtils.d(`AbcFeSocket emitting: ${event}`, data);
            this.socket.emit(event, data);
        } catch (error) {
            LogUtils.e("Error sending message:", error);
        }
    }

    public on(event: string, listener: IListener, groupId = ""): void {
        LogUtils.d(`AbcFeSocket.on: ${event}, groupId: ${groupId}`);

        if (groupId) {
            if (!this.groupIdMap[groupId]) {
                this.groupIdMap[groupId] = [];
                this.emit("subscribe", { eventGroupId: groupId });
            }
            this.groupIdMap[groupId].push(listener);
        }

        const key = event + "@@" + groupId;
        let eventListenerInfo = this.eventListenerMap[key];
        if (!eventListenerInfo) {
            eventListenerInfo = this.eventListenerMap[key] = {
                delegateListener: (data: any) => {
                    const socketData = typeof data === "string" ? JSON.parse(data) : data;
                    const eventGroupId = socketData?.eventGroupId || "";
                    if (eventGroupId === groupId) {
                        eventListenerInfo.listeners.forEach((l) => l(socketData));
                    }
                },
                listeners: [],
            };
        }
        eventListenerInfo.listeners.push(listener);
        this.socket?.on(event, eventListenerInfo.delegateListener);
    }

    public off(event: string, listener?: IListener, groupId = ""): void {
        LogUtils.d(`AbcFeSocket.off: ${event}, groupId: ${groupId}`);

        if (groupId && this.groupIdMap[groupId]) {
            const index = listener ? this.groupIdMap[groupId].indexOf(listener) : -1;
            if (index > -1) {
                this.groupIdMap[groupId].splice(index, 1);
            } else if (!listener) {
                delete this.groupIdMap[groupId];
            }

            if (!this.groupIdMap[groupId]?.length) {
                delete this.groupIdMap[groupId];
                this.emit("unsubscribe", { eventGroupId: groupId });
            }
        }

        const key = event + "@@" + groupId;
        const eventListenerInfo = this.eventListenerMap[key];
        if (eventListenerInfo) {
            if (listener) {
                const index = eventListenerInfo.listeners.indexOf(listener);
                if (index > -1) {
                    eventListenerInfo.listeners.splice(index, 1);
                }
            } else {
                eventListenerInfo.listeners = [];
            }

            if (eventListenerInfo.listeners.length === 0) {
                this.socket?.off(event, eventListenerInfo.delegateListener);
                delete this.eventListenerMap[key];
            }
        }
    }

    public close(): void {
        if (this.socket) {
            this.socket.disconnect();
            this.socket = undefined;
        }
        this.groupIdMap = {};
        this.eventListenerMap = {};
        this.isConnecting = false;
        this.reconnectAttempts = 0;
    }
}

// 创建单例实例
const abcFeSocket = new AbcFeSocket();
export { abcFeSocket };
