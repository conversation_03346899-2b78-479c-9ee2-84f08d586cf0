import PathUtils from "../file/path-utils";
import { SharedPreferences } from "./shared-preferences";

/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/7/10
 *
 * @description
 */

class ChainSharedPreferences extends SharedPreferences {
    constructor() {
        super();
    }

    protected async getSharedPreferencesFile(): Promise<string> {
        const path = await PathUtils.getLoginUserChainDataDir();
        return path + "/preferences.data";
    }
}

class ClinicSharedPreferences extends SharedPreferences {
    constructor() {
        super();
    }

    protected async getSharedPreferencesFile(): Promise<string> {
        const path = await PathUtils.getLoginUserDataDir();
        return path + "/preferences.data";
    }
}

class EmployeeSharedPreferences extends SharedPreferences {
    constructor() {
        super();
    }

    protected async getSharedPreferencesFile(): Promise<string> {
        const path = await PathUtils.getLoginEmployeeDataDir();
        return path + "/preferences.data";
    }
}

const clinicSharedPreferences = new ClinicSharedPreferences();
const chainSharedPreferences = new ChainSharedPreferences();
const employeeSharedPreferences = new EmployeeSharedPreferences();

export { clinicSharedPreferences, chainSharedPreferences, employeeSharedPreferences };
