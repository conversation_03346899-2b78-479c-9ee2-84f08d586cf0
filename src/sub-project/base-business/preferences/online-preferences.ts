/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/8/4
 *
 * @description
 */
import { Completer } from "../../common-base-module/async/completer";
import { ABCApiNetwork } from "../../net";

interface Preference {
    name: string;
    value: string;
}

class OnlinePreferences {
    static instance = new OnlinePreferences();

    _preferences: Preference[] = [];

    // 在线更新标记
    _onlineUpdate?: Completer<boolean>;

    init() {
        this.updatePreferences();
    }

    updatePreferences(): Promise<boolean> {
        if (this._onlineUpdate != null) return this._onlineUpdate.promise;

        this._onlineUpdate = new Completer();
        ABCApiNetwork.get<Preference[]>("appproperty/propertyList", {
            queryParameters: { appId: "cn.abcyun.clinic.app" },
        })
            .then((list) => {
                this._preferences = list;
                this._onlineUpdate?.resolve(true);
            })
            .catch((error) => {
                this._onlineUpdate?.reject(error);
                this._onlineUpdate = undefined;
            });

        return this._onlineUpdate.promise;
    }

    async getProperty(name: string, canUseCache = true): Promise<string | undefined> {
        if (!canUseCache || !this._onlineUpdate) {
            await this.updatePreferences();
        }

        return this._preferences.find((item) => item.name == name)?.value;
    }

    async getBool(name: string, canUseCache = true): Promise<boolean> {
        const value = await this.getProperty(name, canUseCache);
        return value == "1";
    }
}

const onlinePreferences = new OnlinePreferences();
export { onlinePreferences };
