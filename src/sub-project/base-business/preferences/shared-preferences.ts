/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/7/10
 *
 * @description 应用级 shared-preferences
 */

import { delayed } from "../../common-base-module/rxjs-ext/rxjs-ext";
import { Subscription } from "rxjs";
import { LogUtils } from "../../common-base-module/log";
import { errorToStr } from "../../common-base-module/utils";
import FileUtils from "../../common-base-module/file/file-utils";
import { AnyType } from "../../common-base-module/common-types";

interface PreferenceMap {
    [key: string]: any;
}

export class SharedPreferences {
    private _map: PreferenceMap = {};

    private _saveTimer?: Subscription;

    public setString(key: string, value: string): void {
        this._map[key] = value;
        this._save();
    }

    getString(key: string): string | undefined {
        return this._map[key];
    }

    public setObject(key: string, value: AnyType): void {
        this._map[key] = value;
        this._save();
    }

    public getObject(key: string): any {
        return this._map[key];
    }

    public clear(): void {
        this._map = {};
        this._save();
    }

    public async init(): Promise<void> {
        this._map = {};
        const file = await this.getSharedPreferencesFile().catch((/*ignore*/) => {
            return "";
        });

        try {
            if (await FileUtils.fileExists(file)) {
                const content = await FileUtils.readAsString(file);
                this._map = JSON.parse(content);
            }
        } catch (e) {
            LogUtils.d("SharedPreferences.init failed for:" + errorToStr(e));
        }

        if (!this._map) this._map = {};
    }

    public setInt(key: string, value: number): void {
        this._map[key] = value;
        this._save();
    }

    public getInt(key: string): number | undefined {
        return this._map[key];
    }

    public getBool(key: string): boolean | undefined {
        return this._map[key];
    }

    public setBool(key: string, value: boolean): void {
        this.setObject(key, value);
    }

    public async commit(): Promise<void> {
        await this._doSave();
    }

    protected getSharedPreferencesFile(): Promise<string> {
        return FileUtils.getSharedPreferencesFile();
    }

    private _save() {
        this._saveTimer?.unsubscribe();
        this._saveTimer = delayed(3000).subscribe(() => {
            this._doSave();
        });
    }

    private async _doSave() {
        this._saveTimer?.unsubscribe();
        this._saveTimer = undefined;
        try {
            const file = await this.getSharedPreferencesFile();
            await FileUtils.writeAsString(file, JSON.stringify(this._map));
        } catch (e) {
            LogUtils.d("SharedPreferences.save failed for:" + errorToStr(e));
        }
    }
}

const sharedPreferences = new SharedPreferences();

export { sharedPreferences };
