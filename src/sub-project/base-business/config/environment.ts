/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/7/10
 *
 * @description
 */

import { AppInfo } from "./app-info";
import { sharedPreferences } from "../preferences/shared-preferences";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import { AbcPlatformMix } from "../native-modules/abc-platform-mix";

export enum ServerEnvType {
    dev,
    test,
    normal,
}

const PREF_GUID = "app_guid";
const PREF_API_ENV = "api_env";
const REGIN_API_OBJ = "regin_api_obj";

class Environment {
    static serverHostNames = new Map<ServerEnvType, string>([
        [ServerEnvType.dev, "dev.abczs.cn"],
        [ServerEnvType.test, "test.abczs.cn"],
        [ServerEnvType.normal, "abcyun.cn"],
    ]);

    static serverGlobalHostNames = new Map<ServerEnvType, string>([
        [ServerEnvType.dev, "global-dev.abczs.cn"],
        [ServerEnvType.test, "global-test.abczs.cn"],
        [ServerEnvType.normal, "global.abcyun.cn"],
    ]);

    static serverHostSchemes = new Map<ServerEnvType, string>([
        [ServerEnvType.dev, "https"],
        [ServerEnvType.test, "https"],
        [ServerEnvType.normal, "https"],
    ]);

    static webSocketSchemes = new Map<ServerEnvType, string>([
        [ServerEnvType.dev, "wss"],
        [ServerEnvType.test, "wss"],
        [ServerEnvType.normal, "wss"],
    ]);

    static webViewPageOaNames = new Map<ServerEnvType, string>([
        [ServerEnvType.dev, "dev-share.abczs.cn"],
        [ServerEnvType.test, "test-share.abczs.cn"],
        [ServerEnvType.normal, "share.abcyun.cn"],
    ]);

    static mallHostNames = new Map<ServerEnvType, string>([
        [ServerEnvType.dev, "mall.dev.abczs.cn"],
        [ServerEnvType.test, "mall.test.abczs.cn"],
        [ServerEnvType.normal, "mall.abcyun.cn"],
    ]);

    _serverEnvType: ServerEnvType;

    _serverScheme!: string;
    _serverWSScheme!: string;
    _serverHostname!: string;
    _serverNormalHostname!: string;
    _serverGlobalHostname!: string;
    _webViewPageOaName!: string;
    _mallHostname!: string;
    private _appUA!: string;

    private _guid!: string;

    private _hasInit = false;

    constructor() {
        this._serverEnvType = ServerEnvType.normal;
        this._setupApiEnv();
    }

    get serverGlobalHostname() {
        return this._serverGlobalHostname;
    }

    get isGlobalEnv() {
        return this.serverHostName === Environment.serverGlobalHostNames.get(this._serverEnvType)!;
    }

    async init() {
        if (this._hasInit) return;
        let guid = await sharedPreferences.getString(PREF_GUID);
        if (!guid) {
            guid = Date.now().toString();

            await sharedPreferences.setString(PREF_GUID, guid);
        }

        this._guid = guid;
        this._serverEnvType = sharedPreferences.getInt(PREF_API_ENV) ?? ServerEnvType.normal;

        this._setupApiEnv();
        this.initReginApiObj();

        this._hasInit = true;
    }

    /**
     * 分区缓存相关配置
     */
    async initReginApiObj() {
        let reginApiObj = sharedPreferences.getObject(REGIN_API_OBJ);
        if (!!reginApiObj) {
            reginApiObj = JSON.parse(reginApiObj) as { hostName: string };
            this._serverHostname = reginApiObj.hostName;
        }
    }

    /**
     *
     * @param hostName
     */
    async setReginApiObj(hostName: string) {
        this._serverHostname = hostName;
        await AbcPlatformMix.setRegionHost(hostName);
        await sharedPreferences.setObject(REGIN_API_OBJ, JSON.stringify({ hostName }));
    }

    /**
     * 原始请求host
     */
    get isNormalEnv() {
        return this.serverHostName === Environment.serverHostNames.get(this._serverEnvType)! || !sharedPreferences.getObject(REGIN_API_OBJ);
    }

    async clearReginApiObj() {
        this._setupApiEnv();
        this._serverHostname = this.serverGlobalHostname;
        await AbcPlatformMix.setRegionHost(this.serverGlobalHostname);
        await sharedPreferences.setObject(REGIN_API_OBJ, JSON.stringify({ hostName: this.serverGlobalHostname }));
    }

    get serverHostName() {
        return this._serverHostname;
    }

    async clearReginApiObjForce() {
        this._setupApiEnv();
        await AbcPlatformMix.setRegionHost("");
        await sharedPreferences.setObject(REGIN_API_OBJ, "");
    }

    get serverHostScheme() {
        return this._serverScheme;
    }

    get serverEnvType(): ServerEnvType {
        return this._serverEnvType;
    }

    get guid() {
        return this._guid;
    }

    get webViewPageOaName() {
        return this._webViewPageOaName;
    }

    get mallHostname() {
        return this._mallHostname;
    }

    appUA() {
        if (!this._appUA)
            this._appUA = `VN=${AppInfo.appVersion}&OS=${DeviceUtils.isAndroid() ? "Android" : DeviceUtils.isIOS() ? "iOS" : "OHOS"}&OSV=${
                AppInfo.osVersion
            }&guid=${this.guid}&${AppInfo.pluginInfo?.pluginDisplayInfo ?? ""}`;

        return this._appUA;
    }

    switchToEnv(envType: ServerEnvType) {
        this._serverEnvType = envType;
        this._setupApiEnv();

        sharedPreferences.setInt(PREF_API_ENV, this._serverEnvType);
    }

    private _setupApiEnv() {
        this._serverScheme = Environment.serverHostSchemes.get(this._serverEnvType)!;
        this._serverHostname = Environment.serverHostNames.get(this._serverEnvType)!;
        this._serverNormalHostname = Environment.serverHostNames.get(this._serverEnvType)!;
        this._serverGlobalHostname = Environment.serverGlobalHostNames.get(this._serverEnvType)!;
        this._serverWSScheme = Environment.webSocketSchemes.get(this._serverEnvType)!;
        this._webViewPageOaName = Environment.webViewPageOaNames.get(this._serverEnvType)!;
        this._mallHostname = Environment.mallHostNames.get(this._serverEnvType)!;
    }
}

const environment = new Environment();

export { environment };
