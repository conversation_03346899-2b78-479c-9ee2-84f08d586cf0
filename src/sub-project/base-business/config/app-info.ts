/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020/7/10
 *
 * @description
 *
 */
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import FileUtils from "../../common-base-module/file/file-utils";

const kPluginConfFile = "conf.json";
const kAssertSchema = "assets://";

class PluginInfo {
    name?: string;
    version?: string;
    timestamp?: string; // 生成时间
    repoTag?: string; //打包tag

    get pluginDisplayInfo(): string {
        return `name=${this.name}&version=${this.version}&timestamp=${this.timestamp}&repoTag=${this.repoTag}`;
    }
}

export class AppInfo {
    static wxAppId = "wx4d345812e44f5060";
    static umengAppId = "5dc36a074ca35767f200082f";

    static appVersion = "";
    static osVersion = "";
    static pwd: string; //当前js bundle 的根目录
    static launchGrayFlag?: number; //启动时灰度标记
    static androidFullScreen?: boolean; //Android是否打开了全屏模式
    static pluginInfo?: PluginInfo;

    public static init(appVersion: string, osVersion: string, pwd: string, launchGrayFlag?: number, androidFullScreen?: boolean): void {
        this.appVersion = appVersion;
        this.osVersion = osVersion;
        this.pwd = pwd;
        this.launchGrayFlag = launchGrayFlag;
        this.androidFullScreen = androidFullScreen;
        this.initPlugInfo();
    }

    public static async initPlugInfo(): Promise<void> {
        const { AbcPlatformMix } = require("../native-modules/abc-platform-mix");
        const rootDir = this.pwd;
        if (rootDir.startsWith(kAssertSchema)) {
            const content = await AbcPlatformMix.getAssetAsString(rootDir.substr(kAssertSchema.length) + "/" + kPluginConfFile);
            this.pluginInfo = JsonMapper.deserialize(PluginInfo, JSON.parse(content));
        } else {
            const confContent = await FileUtils.readAsString(rootDir + "/" + kPluginConfFile).catch(() => null);
            if (!confContent) {
                return;
            }
            this.pluginInfo = JsonMapper.deserialize(PluginInfo, JSON.parse(confContent));
        }
    }
}
