/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/12/10
 *
 * @description
 */
import { JsonProperty } from "../../../common-base-module/json-mapper/json-mapper";
import { Patient } from "../../data/beans";

export enum RegionConst {
    CHENG_DU = "sichuan_chengdu", // 成都
    BEI_JING = "beijing", // 北京
    LU_ZHOU = "luzhou", // 泸州
    CHONG_QING = "chongqing", // 重庆
    CHONG_QING_GB = "chongqing_gb", // 重庆国标
    SHEN_ZHEN = "shenzhen", // 深圳
    SHEN_ZHEN_GB = "shenzhen_gb", // 深圳国标
    WU_HAN = "wuhan", // 武汉
    NAN_CHANG = "nanchang", // 南昌
    HANG_ZHOU = "hangzhou", // 杭州
    QU_ZHOU = "quzhou", // 衢州
    GUANG_ZHOU = "guangzhou", // 广州
    GUANG_ZHOU_GB = "guangzhou_gb", // 广州国标
    MAO_MING = "maoming", // 茂名
    ZHAN_JIANG = "zhanjiang", // 湛江
    FO_SHAN = "foshan", // 佛山
    DONG_GUAN = "dongguan", // 东莞
    QING_DAO = "qingdao", // 青岛
    XI_AN = "xian", // 西安
    SHEN_YANG = "shenyang", //沈阳
    NAN_JING = "nanjing", // 南京
    HE_ZHOU = "hezhou", // 贺州
    NING_BO = "ningbo", // 宁波
    MIAN_YANG = "mianyang", // 绵阳
    HAN_ZHONG = "hanzhong", // 汉中
    NATIONAL_DEFAULT = "national-default", // 未开通社保地区的默认值
    DA_LIAN = "dalian", // 大连
    NAN_NING = "nanning", // 南宁
    SHANDONG_JINAN = "shandong_jinan", // 山东济南
}

export interface DiseasesCode {
    code: string;
    name: string;
    type: number;
    hint: string;
    diseaseType?: number;
}

export class ShebaoConfigBasicInfo {
    cityHospitalCode?: string;
    cityNationalCode?: string;
    hospitalCode?: string;
    hospitalName?: string;
    provHospitalCode?: string;
    provNationalCode?: string;
    switchSpecialDisease?: number;
    hospitalType?: string;
    isOpenSameDayMedicalSettleLimit?: number; // 是否开启同日结算提醒：0 不开启 1 开启
    isOpenElecSetlCertUpload?: number; // 电子结算凭证是否开通 0 未开启 1 已开启
    //  开启电子结算凭证
    get openElecSetlCertUpload(): boolean {
        return this.isOpenElecSetlCertUpload === 1;
    }
}

export class ClinicShebaoConfig {
    clinicId?: number;
    chainId?: number;
    region?: "hangzhou" | string; //区域
    status?: number; //status === 10  视为开通社保，不走记账
    apiVersion?: number; //对接服务商
    @JsonProperty({ type: ShebaoConfigBasicInfo })
    basicInfo?: ShebaoConfigBasicInfo; //医疗机构信息

    /* 成都银海需要以下字段，其他不需要 */
    agencyNo?: number; // 市医保经办机构码
    provinceAgencyNo?: number; // 省医保经办机构码
    matchingCodeType?: number; // 智能对码模式

    warningSwitch?: number; // 医保异动提醒 0 不告警 1 告警
    warningShebaoNotMatched?: number; // 药品项目未对码 0 不告警 1 告警
    warningDictExpired?: number; // 医保目录失效 0 不告警 1 告警
    warningPriceExceed?: number; // 定价高于限价 0 不告警 1 告警
    warningDaysInAdvance?: number; // 提前多少天告警，默认 15 天
    limitPriceType?: number; // 刷卡限价策略 0 宽松 1 严格
    warningDictExpiredProvince?: number; // 失效提醒 省目录
    warningShebaoNotMatchedProvince?: number; // 对码提醒 省目录
    dictDiseaseType?: string; //门诊诊断推荐   default--老版本,v2--21版本
    _isEnableShebaoSettle?: number; // 终端自定义字段（判断当前诊所是否开启显示医保结算）

    // 是否国标
    get isNational(): boolean {
        return this.isChongqingGb || this.isShenzhenGb || this.isGuangzhouGb;
    }

    get supportSocialMark(): boolean {
        return this.isChengdu || this.isChongqing || this.isShenzhen || this.isWuhan || this.isHangzhou || this.isQingdao || this.isNanjing;
    }

    // 是否显示医保刷卡标志
    get isDisplaySocialMark(): boolean {
        return this.supportSocialMark && this.isOpenSocial;
    }

    // 显示两定报表tab，支持地区：成都、重庆
    get isDisplayLiangDingReport(): boolean {
        return this.isChengdu || this.isChongqing;
    }

    // 显示医保设置tab，支持地区：除武汉、南昌以外的地区
    get isDisplayMatchCode(): boolean {
        return !(this.isWuhan || this.isNanchang);
    }

    // 显示医保限价tab，支持地区：成都、重庆、深圳、武汉
    get isDisplayLimitReport(): boolean {
        return this.isChengdu || this.isChongqing || this.isShenzhen || this.isWuhan;
    }

    // 是否需要必填病种编码
    get isRequiredDiagnosis(): boolean {
        return this.isOpenSocial && (this.isHangzhou || this.isQingdao || this.isNanjing);
    }

    // 是否需要诊断必须与医保目录对应
    get isSaveDiseaseCode(): boolean {
        return this.isOpenSocial && !this.isChengdu;
    }

    // 是否可以医保刷卡
    get isOpenSocial(): boolean {
        return this.status === 10;
    }

    get shebaoPayMethodEnable(): boolean {
        return this.status != 10;
    }

    // 是否杭州
    get isRegionHanZhou(): boolean {
        return this.region === RegionConst.HANG_ZHOU;
    }

    // 是否成都
    get isChengdu(): boolean {
        return this.region === RegionConst.CHENG_DU;
    }

    // 是否重庆
    get isChongqing(): boolean {
        return this.region === RegionConst.CHONG_QING;
    }

    // 是否重庆国标
    get isChongqingGb(): boolean {
        return this.region === RegionConst.CHONG_QING_GB;
    }

    // 是否深圳
    get isShenzhen(): boolean {
        return this.region === RegionConst.SHEN_ZHEN;
    }

    // 是否深圳国标
    get isShenzhenGb(): boolean {
        return this.region === RegionConst.SHEN_ZHEN_GB;
    }

    // 是否广州
    get isGuangzhou(): boolean {
        return this.region === RegionConst.GUANG_ZHOU;
    }

    // 是否广州国标
    get isGuangzhouGb(): boolean {
        return this.region === RegionConst.GUANG_ZHOU_GB;
    }

    // 是否武汉
    get isWuhan(): boolean {
        return this.region === RegionConst.WU_HAN;
    }

    // 是否南昌
    get isNanchang(): boolean {
        return this.region === RegionConst.NAN_CHANG;
    }

    // 是否杭州
    get isHangzhou(): boolean {
        return this.region === RegionConst.HANG_ZHOU;
    }

    // 是否青岛
    get isQingdao(): boolean {
        return this.region === RegionConst.QING_DAO;
    }

    // 是否南京
    get isNanjing(): boolean {
        return this.region === RegionConst.NAN_JING;
    }

    // 是否北京
    get isBeijing(): boolean {
        return this.region === RegionConst.BEI_JING;
    }
    //     是否是杭州地区
    get isRegionHangzhou(): boolean {
        return !!this.region?.includes(RegionConst.HANG_ZHOU);
    }
    //     是否是南京地区
    get isRegionNanjing(): boolean {
        return !!this.region?.includes(RegionConst.NAN_JING);
    }
    //    是否是南宁地区
    get isRegionNanning(): boolean {
        return !!this.region?.includes(RegionConst.NAN_NING);
    }
    //     是否是山东济南
    get isShandongJinan(): boolean {
        return this.region == RegionConst.SHANDONG_JINAN;
    }
}
// 社保支付方式
export const ShebaoPayMode = Object.freeze({
    // 优先统筹支付
    OVERALL: 0,
    // 优先个账支付
    SELF: 1,
    // 不使用医保支付
    NO_USE: 2,
});

// item 上 payType 字段，透传给社保，与shebaoPayMode有区别，shebaoPayMode是goods属性，payType是当前item支付属性
export const ShebaoPayTypeEnum = Object.freeze({
    // 优先统筹支付
    OVERALL: 0,
    // 优先个账支付
    SELF: 10,
    // 不使用医保支付
    NO_USE: 30,
});

export const ShebaoPayTypeByModeEnum = Object.freeze({
    [ShebaoPayMode.OVERALL]: ShebaoPayTypeEnum.OVERALL,
    [ShebaoPayMode.SELF]: ShebaoPayTypeEnum.SELF,
    [ShebaoPayMode.NO_USE]: ShebaoPayTypeEnum.NO_USE,
});

export const ShebaoPayModeByTypeEnum = Object.freeze({
    [ShebaoPayTypeEnum.OVERALL]: ShebaoPayMode.OVERALL,
    [ShebaoPayTypeEnum.SELF]: ShebaoPayMode.SELF,
    [ShebaoPayTypeEnum.NO_USE]: ShebaoPayMode.NO_USE,
});

class PrescriptionFormDetail {
    isDeal?: number; // 是否已经处理 0未处理 1已处理
    keyId?: string;
    prescriptionFormId?: string; // 处方FormId
    prescriptionFormItemId?: string; // 处方FormItemID
    prescriptionFormName?: string; // 处方名称
}
export class VerifyDetailItem {
    dealWay?: number; // 规则处理方式 0不处理 1普通处理 2双签处理 3两种方式都可以处理（或者）
    detail?: string; // 不满足时提示实际情况
    goodsShebaoPayMode?: number; // goods上的属性 医保支付方式
    hint?: string; // 审核未通过提示
    level?: string; // 级别 PASS, WARN, DANGER
    medName?: string; // 药品名称
    name?: string; // 规则名称
    @JsonProperty({ type: Array, clazz: PrescriptionFormDetail })
    prescriptionFormDetails?: PrescriptionFormDetail[];
    restrict?: string; // 限制信息说明
    shebaoCode?: string;
    shebaoPayMode?: number; // 用户设置医保支付方式
    verifySignatureStatus?: number; // 是否签名 中药处方超限用药用
    isDeal?: number; // 是否已经处理 0未处理 1已处理
    isSignature?: number; // 是否签名 透传给前端 0否 1是
}
export class SheBaoVerifyItem {
    cnName?: string; // 校验规则中文说明
    dealLevel?: number; //未通过时处理方式 0提醒 1不允许医保结算 2不允许完成接诊
    dealStatus?: number;
    dealType?: number; // 规则处理方式 0不处理 1处理
    hover?: string; // hover提示
    level?: string; // 级别 PASS, WARN, DANGER (所有的[ DANGER, OFF, PASS, WARN ])
    name?: string; // 校验规则类别-规则字段名
    ruleType?: number; // 控费规则类别 0 用药合规 1 医疗行为合规
    @JsonProperty({ type: Array, clazz: VerifyDetailItem })
    verifyDetails?: VerifyDetailItem[]; // 校验详情 根据级别分组

    // 终端自定义字段(原因)
    verifyResult?: string;
}
class RestrictRuleConfigHtos {
    dealLevel?: number; // 未通过时处理方式 0提醒 1不允许医保结算 2不允许完成接诊
    restrictExtend?: {
        count?: number; // 味数、项数
        day?: number; // 天数
        doseCount?: number; // 剂数
        medfeeSumamt?: number; // 金额
        settleCount?: number; // 单数
    };
    ruleDesc?: string; // 规则描述-填充后
    ruleDescOriginal?: string; // 规则描述-未填充自定义参数
    ruleFieldName?: string; // 规则字段名
    ruleName?: string; // 规则中文名
    ruleSourceDesc?: string; // 规则来源 医保目录 地方考核经验
    ruleSwitch?: number; // 开关 0关闭 1开启/控费开关
    ruleType?: number; // 控费规则类别 0 用药合规 1 医疗行为合规
    supportRegions?: string; // 适用地区 、分割
    dealType?: number; // 规则处理方式 0不处理 1处理
}
export class SheBaoRestrictVertConfig {
    restrictSwitch?: number; //控费开关 0关闭 1始终审核 2仅医保就诊审核
    alonePaySwitch?: number; //单独使用不予支付开关 0关闭 1开启
    excessSwitch?: number; //超药典最高限量开关 0关闭 1开启
    medicalGradeSwitch?: number; //超限定医疗机构等级开关 0关闭 1开启
    payScopeSwitch?: number; //超限定支付范围开关 0关闭 1开启
    treatmentMethodSwitch?: number; //超限定就医方式开关 0关闭 1开启
    ybPaySwitch?: number; //医保不予支付开关 0关闭 1开启
}
export class SheBaoRestrictConfig {
    openDays?: number;
    @JsonProperty({ type: Array, clazz: RestrictRuleConfigHtos })
    behaviorRestrictRuleConfigHtos?: RestrictRuleConfigHtos[]; // 医疗行为合规规则
    @JsonProperty({ type: Array, clazz: RestrictRuleConfigHtos })
    medicateRestrictRuleConfigHtos?: RestrictRuleConfigHtos[]; // 用药合规规则
    @JsonProperty({ type: Array, clazz: RestrictRuleConfigHtos })
    restrictRuleConfigHtos?: RestrictRuleConfigHtos[]; // 控费总开关
    get restrictConfig(): SheBaoRestrictVertConfig {
        const config: SheBaoRestrictVertConfig = {};
        this.restrictRuleConfigHtos?.forEach((x) => {
            const { ruleFieldName, ruleSwitch } = x;
            switch (ruleFieldName) {
                case "allRestrictRule":
                    if (ruleSwitch === 1) {
                        config.restrictSwitch = 1;
                    }
                    break;
                case "onlyYbRestrictRule":
                    if (ruleSwitch === 1) {
                        config.restrictSwitch = 2;
                    }
                    break;
                case "notRestrictRule":
                    if (ruleSwitch === 1) {
                        config.restrictSwitch = 0;
                    }
                    break;
                case "alonePayRule":
                    config.alonePaySwitch = ruleSwitch;
                    break;
                case "excessRule":
                    config.excessSwitch = ruleSwitch;
                    break;
                case "medicalGradeRule":
                    config.medicalGradeSwitch = ruleSwitch;
                    break;
                case "payScopeRule":
                    config.payScopeSwitch = ruleSwitch;
                    break;
                case "treatmentMethodRule":
                    config.treatmentMethodSwitch = ruleSwitch;
                    break;
                case "ybPayRule":
                    config.ybPaySwitch = ruleSwitch;
                    break;
                default:
                    break;
            }
        });
        return config;
    }
    @JsonProperty({ type: Array, clazz: RestrictRuleConfigHtos })
    treatmentRestrictRuleConfigHtos?: RestrictRuleConfigHtos[]; // 理疗项目合规规则
}
export class SheBaoRestrictRuleVerify {
    @JsonProperty({ type: Array, clazz: SheBaoVerifyItem })
    behaviorVerifyLists?: SheBaoVerifyItem[]; // 医疗行为合规证列表
    @JsonProperty({ type: Array, clazz: SheBaoVerifyItem })
    medicateVerifyLists?: SheBaoVerifyItem[]; // 用药合规验证列表
    //     风险项
    get riskItems(): SheBaoVerifyItem[] {
        const result: SheBaoVerifyItem[] = [];
        this.behaviorVerifyLists?.concat(this.medicateVerifyLists ?? []).forEach((item) => {
            if (item.verifyDetails) {
                item.verifyDetails.forEach((detail) => {
                    if (detail.level === "DANGER") {
                        result.push(item);
                    }
                });
            }
        });
        return result;
    }
    //     提醒项
    get remindItems(): SheBaoVerifyItem[] {
        const result: SheBaoVerifyItem[] = [];
        this.behaviorVerifyLists?.concat(this.medicateVerifyLists ?? []).forEach((item) => {
            if (item.verifyDetails) {
                item.verifyDetails.forEach((detail) => {
                    if (detail.level === "WARN") {
                        result.push(item);
                    }
                });
            }
        });
        return result;
    }
}
export class SheBaoBasePrescriptionFormItem {
    shebaoCode?: string; // 社保对码Code
    shebaoPayMode?: number; // 用户设置医保支付方式
    goodsShebaoPayMode?: number; // goods上的属性 医保支付方式
    prescriptionFormName?: string; // 处方名称
    keyId?: string;
    name?: string; // 药品名称
    subType?: number; // 药品子类型
    type?: number; // 药品类型
}
export class SheBaoChinesePrescriptionFormItem extends SheBaoBasePrescriptionFormItem {
    freq?: string;
    unit?: string; // 单位
    unitCount?: number; // 中药-单位数量
    usage?: string; // 用法
    verifySignatures?: number[]; // 签名 1是医保合规的签字
}
export class SheBaoBaseForm {
    keyId?: string;
    prescriptionFormName?: string; //处方名称
    prescriptionFormItems?: SheBaoBasePrescriptionFormItem[];
}
export class SheBaoChineseForm extends SheBaoBaseForm {
    dailyDosage?: string; // 每日剂量-》1日1剂
    doseCount?: string; // 剂数
    freq?: string; // 频次-》一日三次
    usage?: string; //用法-》制膏、冲服、煎服、制丸等
    usageDays?: string; //服用天数-》约服10天 约服1月
    usageLevel?: string; // 每日次数-》每次6g
    verifySignatureStatus?: number; // 是否签名 中药处方超限用药用
    @JsonProperty({ type: Array, clazz: SheBaoChinesePrescriptionFormItem })
    prescriptionFormItems?: SheBaoChinesePrescriptionFormItem[];
}
export class SheBaoRestrictRuleVerifyReq {
    departmentId?: string; // 科室ID
    insutypeList?: string[]; //险种
    isAdmission?: number; // 是否住院 0否 1是
    isSelfPay?: number; // 是否自费 0否 1是
    patient?: Patient;
    prescriptionChineseForms?: SheBaoChineseForm[];
    prescriptionInfusionForms?: SheBaoBaseForm[];
    prescriptionWesternForms?: SheBaoBaseForm[];
    prescriptionExternalForms?: any[];
    productForms?: any[];
    verifySource?: number; // 0 门诊 1 医保小端 2 住院 3 零售药店
}

export class SheBaoPrescriptionDoctorItem {
    caty?: string; // 科别
    departmentName?: string; // 科室名称
    doctorCode?: string; // 医生编码
    doctorName?: string; // 医生名称
    hospitalCode?: string; // 机构信息
    hospitalName?: string; //机构名称
    id?: string;
}
