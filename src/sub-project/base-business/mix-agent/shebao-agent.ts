/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/12/10
 *
 * @description 社保相关的api接口
 */

import { ABCApiNetwork } from "../../net";
import {
    ClinicShebaoConfig,
    DiseasesCode,
    RegionConst,
    SheBaoPrescriptionDoctorItem,
    SheBaoRestrictConfig,
    SheBaoRestrictRuleVerify,
    SheBaoRestrictRuleVerifyReq,
} from "./data/shebao-bean";
import { clinicScopeMemoryCache } from "../../common-base-module/cleanup/memory-cache";
import { JsonMapper } from "@app/utils";

class GetDiseasesQueryListRsp {
    diagnosisInfos?: DiseasesCode[];
}

export class ShebaoAgent {
    /**
     * 获取诊所社保配置
     */
    static getClinicShebaoConfig(cache = true): Promise<ClinicShebaoConfig> {
        const key = "SHE_BAO_CONFIG";
        if (clinicScopeMemoryCache.has(key) && cache) {
            return Promise.resolve(clinicScopeMemoryCache.get(key));
        }

        return ABCApiNetwork.get("shebao/configs", {
            clazz: ClinicShebaoConfig,
        }).then((rsp) => {
            // 医保异动提醒 - 杭州默认是开启全部的，其他地区默认是关闭的

            const isHangzhou = rsp.region === RegionConst.HANG_ZHOU;
            if (rsp.warningSwitch === null && isHangzhou) {
                rsp.warningSwitch = 1;
                rsp.warningShebaoNotMatched = 1;
                rsp.warningShebaoNotMatchedProvince = 1;
                rsp.warningDictExpired = 1;
                rsp.warningDictExpiredProvince = 1;
                rsp.warningPriceExceed = 1;
                rsp.warningDaysInAdvance = 15;
            }
            clinicScopeMemoryCache.set(key, rsp);
            return rsp;
        });
    }

    /**
     * 获取医保目录code
     * @param body
     */
    static getDiseasesQueryList(body: DiseasesCode[]): Promise<GetDiseasesQueryListRsp> {
        return ABCApiNetwork.post("shebao/diagnosis/query", {
            body: { diagnosisInfos: body },
            clazz: GetDiseasesQueryListRsp,
        });
    }

    /**
     * 获取医保目录code
     * @param keyword
     */
    static getDiseasesSearchList(keyword?: string): Promise<GetDiseasesQueryListRsp> {
        return ABCApiNetwork.get("shebao/diagnosis/search", {
            queryParameters: { keyword },
            clazz: GetDiseasesQueryListRsp,
        });
    }

    /**
     * 通过chargeSheetId查收费信息
     * @param chargeSheetId
     */
    static async getSocialSettleInfo(chargeSheetId: string): Promise<{
        acctPay?: number;
        fundPaymentFee?: number;
        isSupport?: number;
        medTypeLabel?: string;
        medfeeSumamt?: number;
        specialNeedsHint?: string;
    }> {
        return await ABCApiNetwork.get(`shebao/national/charge/payment/${chargeSheetId}`);
    }

    /**
     * 获取控费配置
     */
    static async getSheBaoRestrictConfig(): Promise<SheBaoRestrictConfig> {
        return await ABCApiNetwork.get(`shebao-restrict/config`, {
            clazz: SheBaoRestrictConfig,
        });
    }

    /**
     * 获取控费规则
     * @param options
     */
    static async querySheBaoRestrictNationalVerify(options: SheBaoRestrictRuleVerifyReq): Promise<SheBaoRestrictRuleVerify> {
        return await ABCApiNetwork.post(`shebao-restrict/national/charge-rule/verify`, {
            body: options,
            clazz: SheBaoRestrictRuleVerify,
            clearUndefined: true,
        });
    }

    /**
     * 更新医保控费规则
     * @param businessId
     * @param options
     */
    static async updateVerifyResult(businessId: string, options: SheBaoRestrictRuleVerifyReq): Promise<SheBaoRestrictRuleVerify> {
        return await ABCApiNetwork.post(`shebao-restrict/national/charge-rule/verify-result/${businessId}`, {
            body: options,
            clazz: SheBaoRestrictRuleVerify,
            clearUndefined: true,
        });
    }

    /**
     * 获取医师处方信息
     * @param options
     */
    static async queryNationalPrescriptionDoctorList(options: {
        limit?: number;
        offset?: number;
    }): Promise<SheBaoPrescriptionDoctorItem[]> {
        const { limit = 30, offset = 0 } = options;
        const rsp: { rows: SheBaoPrescriptionDoctorItem[] } = await ABCApiNetwork.get(
            "shebao/national/management/prescription/doctor-info",
            {
                queryParameters: {
                    limit,
                    offset,
                },
            }
        );
        return rsp?.rows?.map((item) => JsonMapper.deserialize(SheBaoPrescriptionDoctorItem, item)) ?? [];
    }
}
