import logUtils from "../../common-base-module/log/log-utils";
import {
    SseClient,
    SseClientManager,
    SseConnectionConfig,
    SseEventListeners,
    SseConnectionState,
    SseEvent,
    SseConnectionInfo,
} from "./sse-client";

/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2025-06-30 16:23:55
 *
 * @description SSE 辅助工具类
 * 提供简化的 SSE 连接 API，参考 file-downloader.ts 的静态方法模式
 */

/**
 * 简化的 SSE 连接配置
 */
export interface SimpleSseConfig {
    /** 请求 URL */
    url: string;
    /** HTTP 方法，默认 GET */
    method?: string;
    /** 自定义请求头 */
    headers?: HeadersInit;
    /** 请求体（用于 POST 等方法） */
    body?: string;
    /** 是否自动重连，默认 true */
    autoReconnect?: boolean;
    /** 重连间隔（毫秒），默认 3000 */
    reconnectInterval?: number;
    /** 最大重连次数，默认 5 */
    maxReconnectAttempts?: number;
}

/**
 * SSE 连接结果
 */
export interface SseConnectionResult {
    /** SSE 客户端实例 */
    client: SseClient;
    /** 连接 ID */
    connectionId: string;
    /** 断开连接方法 */
    disconnect: () => Promise<void>;
    /** 销毁连接方法 */
    destroy: () => Promise<void>;
}

/**
 * SSE 辅助工具类
 * 参考 FileDownloader 的设计模式，提供简洁易用的静态方法
 */
export class SseHelper {
    private static manager = SseClientManager.getInstance();

    /**
     * 创建 SSE 连接
     * 参考 FileDownloader.downloadFile 的方法签名设计
     *
     * @param config SSE 连接配置
     * @param listeners 事件监听器
     * @returns Promise<SseConnectionResult>
     */
    public static async connect(config: SimpleSseConfig, listeners: SseEventListeners = {}): Promise<SseConnectionResult> {
        logUtils.d(`SseHelper connecting to: ${config.url}`);

        // 创建连接配置
        const connectionConfig: SseConnectionConfig = {
            url: config.url,
            method: config.method || "GET",
            headers: config.headers,
            body: config.body,
            autoReconnect: config.autoReconnect !== false,
            reconnectInterval: config.reconnectInterval || 3000,
            maxReconnectAttempts: config.maxReconnectAttempts || 5,
            connectTimeout: 30000,
        };

        // 创建客户端
        const client = this.manager.createConnection(connectionConfig, listeners);

        try {
            // 建立连接
            await client.connect();

            const result: SseConnectionResult = {
                client,
                connectionId: client.getConnectionId(),
                disconnect: () => client.disconnect(),
                destroy: () => this.manager.removeConnection(client.getConnectionId()),
            };

            logUtils.d(`SseHelper connected successfully: ${client.getConnectionId()}`);
            return result;
        } catch (error) {
            // 连接失败时清理资源
            await this.manager.removeConnection(client.getConnectionId());
            logUtils.e(`SseHelper connection failed: ${error}`);
            throw error;
        }
    }

    /**
     * 快速创建 SSE 连接（仅监听消息）
     * 简化版本，只需要提供 URL 和消息处理函数
     *
     * @param url SSE 服务器 URL
     * @param onMessage 消息处理函数
     * @param options 可选配置
     * @returns Promise<SseConnectionResult>
     */
    public static async quickConnect(
        url: string,
        onMessage: (event: SseEvent & { connectionId: string }) => void,
        options: Partial<SimpleSseConfig> = {}
    ): Promise<SseConnectionResult> {
        const config: SimpleSseConfig = {
            url,
            ...options,
        };

        const listeners: SseEventListeners = {
            onmessage: onMessage,
            onopen: (event) => {
                logUtils.d(`SseHelper quick connection opened: ${event.connectionId}`);
            },
            onerror: (event) => {
                logUtils.e(`SseHelper quick connection error: ${event.error}`);
            },
            onclose: (event) => {
                logUtils.d(`SseHelper quick connection closed: ${event.connectionId}`);
            },
        };

        return this.connect(config, listeners);
    }

    /**
     * 断开指定连接
     *
     * @param connectionId 连接 ID
     * @returns Promise<boolean> 是否成功断开
     */
    public static async disconnect(connectionId: string): Promise<boolean> {
        try {
            const client = this.manager.getConnection(connectionId);
            if (!client) {
                logUtils.w(`SseHelper connection not found: ${connectionId}`);
                return false;
            }

            await client.disconnect();
            logUtils.d(`SseHelper disconnected: ${connectionId}`);
            return true;
        } catch (error) {
            logUtils.e(`SseHelper disconnect failed: ${error}`);
            return false;
        }
    }

    /**
     * 销毁指定连接
     *
     * @param connectionId 连接 ID
     */
    public static async destroy(connectionId: string): Promise<void> {
        try {
            await this.manager.removeConnection(connectionId);
        } catch (error) {
            logUtils.e(`SseHelper destroy failed: ${error}`);
        }
    }

    /**
     * 获取连接状态
     *
     * @param connectionId 连接 ID
     * @returns SseConnectionState | undefined
     */
    public static getConnectionState(connectionId: string): SseConnectionState | undefined {
        const client = this.manager.getConnection(connectionId);
        return client?.getConnectionState();
    }

    /**
     * 检查连接是否存在且已连接
     *
     * @param connectionId 连接 ID
     * @returns boolean
     */
    public static isConnected(connectionId: string): boolean {
        const client = this.manager.getConnection(connectionId);
        return client?.isConnected() || false;
    }

    /**
     * 获取所有连接信息
     *
     * @returns Promise<SseConnectionInfo[]>
     */
    public static async getAllConnections(): Promise<SseConnectionInfo[]> {
        return this.manager.getAllConnections();
    }

    /**
     * 断开所有连接
     *
     * @returns Promise<void>
     */
    public static async disconnectAll(): Promise<void> {
        await this.manager.disconnectAll();
        logUtils.d("SseHelper disconnected all connections");
    }

    /**
     * 获取当前连接数量
     *
     * @returns number
     */
    public static getConnectionCount(): number {
        return this.manager.getConnectionCount();
    }
}

/**
 * 导出便捷的类型和常量
 */
export { SseConnectionState, SseEvent, SseEventListeners } from "./sse-client";

/**
 * 默认导出 SseHelper 类
 */
export default SseHelper;
