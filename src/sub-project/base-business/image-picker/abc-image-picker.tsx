/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020-03-27
 *
 * @description
 *
 */
import React from "react";
import _, { isNil } from "lodash";
import { LogUtils } from "../../common-base-module/log";
import { Toast } from "../../base-ui/dialog/toast";
import { LoadingDialog } from "../../base-ui/dialog/loading-dialog";
import { errorSummary, errorToStr } from "../../common-base-module/utils";
import { OssImageUtils, UploadImageInfo } from "../file-uploader/oss-image-utils";
import { callNativeWithPromise, Text, View } from "@hippy/react";
import { ABCError } from "../../common-base-module/common-error";
import { Colors, Sizes, TextStyles } from "../../theme";
import { SizedBox } from "../../base-ui";
import { showOptionsBottomSheet } from "../../base-ui/abc-app-library/dialog/sheet-menu";
import { AbcView } from "../../base-ui/views/abc-view";
import FileUtils from "../../common-base-module/file/file-utils";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import { AssetImageView } from "../../base-ui/views/asset-image-view";
import { PermissionEvent } from "../../common-base-module/permission/permission-event";
import { PermissionPurposeType } from "../../common-base-module/permission/data";
import { OssUpdateModules, OssUpdateModulesV2 } from "../data/beans";
import { showBottomPanel } from "../../base-ui/abc-app-library/panel";
import { MoreOperationsDialog } from "../../charge/retail-pharmacy/views/more-operations-dialog";
import UiUtils, { pxToDp } from "../../base-ui/utils/ui-utils";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { getDistributeConfig } from "../../views-distribute/utils";

// import {Permission} from "../common-base-module/permission/permission";

export enum ImagePickerSource {
    camera,
    gallery,
}

const kModuleName = "ImagePicker";

const kMaxWidth = 800;
const kMaxHeight = 1000;

//ios gallery saver sdk 在无权限时抛出的异常是一串英文，需要转一下
const errorMessageMap = new Map([
    ["The user is not allowed to use the camera.", "无相机访问权限"],
    ["The user did not allow camera access.", "无相机访问权限"],
    ["The user is not allowed to use the photo.", "无相册访问权限"],
    ["The user did not allow photo access.", "无相册访问权限"],
]);

/**
 * 上传递图片，支持拍照上传，相册选择两种，并完成上传过程
 */
// 原代码
export class ImagePicker {
    // static async pickImageAndUpload(source?: ImagePickerSource): Promise<UploadImageInfo | undefined> {
    //     let select = 0;
    //     if (_.isNil(source)) {
    //         const options = ["拍照上传", "相册选择"];
    //         const selectOptions = await showOptionsBottomSheet({
    //             title: "上传图片",
    //             options: options,
    //         });
    //         if (_.isEmpty(selectOptions)) return Promise.resolve(undefined);
    //         LogUtils.d("ImagePicker.pickImageAndUpload = " + selectOptions);
    //         select = selectOptions![0];
    //     } else {
    //         select = source;
    //     }
    //
    //     const path = await ImagePicker.pickImage({ source: select }).catch((error) => new ABCError(error)); //0拍照上传，1从相册选择
    //     if (path instanceof ABCError) {
    //         const message = errorSummary(path);
    //
    //         await Toast.show(errorMessageMap.get(message) ?? message, { warning: true });
    //         return;
    //     }
    //     LogUtils.d("ImagePicker.info = " + path);
    //     if (!path) return;
    //
    //     if (path.endsWith("gif")) {
    //         await Toast.show("不支持gif动态图", { warning: true });
    //         return;
    //     }
    //
    //     const dialog = new LoadingDialog("正在上传图片");
    //     dialog.show();
    //
    //     try {
    //         const info = await OssImageUtils.uploadImage(path);
    //
    //         await dialog.hide();
    //         return info;
    //     } catch (err) {
    //         await dialog.hide();
    //         let errorStr: string | undefined;
    //         const networkErrorFlag = "ABCNetworkError";
    //         if (_.isString(err)) {
    //             errorStr = err as string;
    //             if (errorStr.indexOf("ABCNetworkError") >= 0 || errorStr.indexOf("网络错误")) {
    //                 errorStr = "网络错误";
    //             }
    //         }
    //         if (_.isString(err.message) && (err.message as string).indexOf(networkErrorFlag)) {
    //             errorStr = "网络错误";
    //         }
    //
    //         if (!errorStr) {
    //             errorStr = errorSummary(err);
    //         }
    //
    //         LogUtils.d("ImagePicker.pickImageAndUpload upload error  = " + errorToStr(err));
    //         await Toast.show(`上传失败：${errorStr}`, { warning: true });
    //     } finally {
    //         // if (source === ImagePickerSource.camera || DeviceUtils.isIOS()) {
    //         //     await FileUtils.deleteFile(path);
    //         // }
    //     }
    // }

    static async pickImage(params: {
        source: ImagePickerSource;
        maxWidth?: number; //default kMaxWidth
        maxHeight?: number; //default kMinWidth
        imageQuality?: number;
    }): Promise<string> {
        const { source, maxWidth = kMaxWidth, maxHeight = kMaxHeight, imageQuality } = params;
        return callNativeWithPromise(kModuleName, "pickImage", {
            source: source,
            maxWidth: maxWidth,
            maxHeight: maxHeight,
            imageQuality: imageQuality,
        });
    }
}

// UI重构 图片(照片|拍摄)
export class AbcImagePicker {
    static async pickImageAndUpload(
        source: ImagePickerSource | null = null,
        ossPath: string,
        module?: OssUpdateModules
    ): Promise<UploadImageInfo | undefined> {
        let select = 0;
        const optionWeight: JSX.Element[] = [];
        if (_.isNil(source)) {
            if (getDistributeConfig().viewFeature.homeV2) {
                const operateList = [
                    {
                        name: "拍摄照片",
                        show: true,
                        needClose: false,
                        onClick: () => {
                            ABCNavigator.pop(0);
                        },
                    },
                    {
                        name: "从手机相册选择",
                        show: true,
                        needClose: false,
                        onClick: () => {
                            ABCNavigator.pop(1);
                        },
                    },
                ];
                const result: number = await showBottomPanel(<MoreOperationsDialog title={"上传处方"} operateList={operateList} />, {
                    topMaskHeight: UiUtils.getSafeContentHeight() - pxToDp(216),
                    maskBg: Colors.blackMask60,
                });
                if (isNil(result)) return;
                select = result;
            } else {
                const options = [
                    { name: "拍摄", img: "camera" },
                    { name: "照片", img: "photo" },
                ];
                options.map((item, index) => {
                    optionWeight.push(this._renderAbcImagePickerGroupItem(index, item.img, item.name));
                });

                const selectOptions = await showOptionsBottomSheet({
                    title: "图片",
                    optionsWidgets: optionWeight,
                    itemHeight: Sizes.dp115,
                });

                if (_.isEmpty(selectOptions)) return Promise.resolve(undefined);
                select = selectOptions![0];
            }
        } else {
            select = source;
        }

        const path = await AbcImagePicker.pickImage({ source: select }).catch((error) => new ABCError(error)); // 0拍照上传，1从相册选择
        PermissionEvent.PermissionCloseObservable.next();
        if (path instanceof ABCError) {
            const message = errorSummary(path);

            await Toast.show(errorMessageMap.get(message) ?? message, { warning: true });
            return;
        }
        if (!path) return;

        if (path.endsWith("gif")) {
            await Toast.show("不支持gif动态图", { warning: true });
            return;
        }

        const dialog = new LoadingDialog("正在上传图片");
        dialog.show();

        let info;
        try {
            if (module === OssUpdateModules.MEDICAL_RECORD) {
                info = await OssImageUtils.uploadImageV2(OssUpdateModulesV2.MEDICAL_RECORD, path);
            } else if (module === OssUpdateModules.UPLOAD_STOCK_IN_ORDER_ATTACHMENTS) {
                info = await OssImageUtils.uploadImageV2(OssUpdateModulesV2.UPLOAD_STOCK_IN_ORDER_ATTACHMENTS, path);
            } else if (module === OssUpdateModules.UPLOAD_RECEIVE_ORDER_ATTACHMENTS) {
                info = await OssImageUtils.uploadImageV2(OssUpdateModulesV2.UPLOAD_RECEIVE_ORDER_ATTACHMENTS, path);
            } else if (module === OssUpdateModules.EXECUTE_RECORD) {
                info = await OssImageUtils.uploadImageV2(OssUpdateModulesV2.EXECUTE_RECORD, path);
            } else if (module === OssUpdateModules.REGISTRATION_MEDICAL_IMAGE) {
                info = await OssImageUtils.uploadImageV2(OssUpdateModulesV2.REGISTRATION_MEDICAL_IMAGE, path);
            } else if (module === OssUpdateModules.REGISTER_PRESCRIPTION) {
                info = await OssImageUtils.uploadImageV2(OssUpdateModulesV2.REGISTER_PRESCRIPTION, path);
            } else {
                info = await OssImageUtils.uploadImage(path, ossPath);
            }

            await dialog.hide();
            return info;
        } catch (err) {
            await dialog.hide();
            let errorStr: string | undefined;
            const networkErrorFlag = "ABCNetworkError";
            if (_.isString(err)) {
                errorStr = err as string;
                if (errorStr.indexOf("ABCNetworkError") >= 0 || errorStr.indexOf("网络错误")) {
                    errorStr = "网络错误";
                }
            }
            if (_.isString(err.message) && (err.message as string).indexOf(networkErrorFlag)) {
                errorStr = "网络错误";
            }

            if (!errorStr) {
                errorStr = errorSummary(err);
            }

            LogUtils.d("ImagePicker.pickImageAndUpload upload error  = " + errorToStr(err));
            await Toast.show(`上传失败：${errorStr}`, { warning: true });
        } finally {
            if (source === ImagePickerSource.camera || DeviceUtils.isIOS()) {
                await FileUtils.deleteFile(path);
            }
        }
    }

    /**
     * 图像选择器组项(图片、标题)
     * @param index
     * @param img
     * @param name
     * @private
     */
    private static _renderAbcImagePickerGroupItem(index: number, img: string, name: string) {
        return (
            <AbcView
                key={index}
                style={{
                    flex: 1,
                    alignSelf: "center",
                    justifyContent: "center",
                    marginTop: Sizes.dp16,
                }}
            >
                <View
                    style={{
                        borderRadius: Sizes.dp6,
                        backgroundColor: Colors.bg1,
                        height: Sizes.dp56,
                        width: Sizes.dp56,
                        justifyContent: "center",
                        alignItems: "center",
                        alignSelf: "center",
                    }}
                >
                    <AssetImageView name={img ?? ""} style={{ width: Sizes.dp56, height: Sizes.dp56 }} />
                </View>
                <SizedBox height={Sizes.dp12} />
                <Text style={[TextStyles.t16NT1, { alignSelf: "center" }]}>{name ?? ""}</Text>
            </AbcView>
        );
    }

    static async pickImage(params: {
        source: ImagePickerSource;
        maxWidth?: number; //default kMaxWidth
        maxHeight?: number; //default kMinWidth
        imageQuality?: number;
        showPermissionTip?: boolean;
    }): Promise<string> {
        const { source, maxWidth = kMaxWidth, maxHeight = kMaxHeight, imageQuality, showPermissionTip = true } = params;
        showPermissionTip && PermissionEvent.PermissionObservable.next(
            source == ImagePickerSource.camera ? PermissionPurposeType.cameraPermissions : PermissionPurposeType.mediaPermissions
        );
        return callNativeWithPromise(kModuleName, "pickImage", {
            source: source,
            maxWidth: maxWidth,
            maxHeight: maxHeight,
            imageQuality: imageQuality,
        });
    }
}
