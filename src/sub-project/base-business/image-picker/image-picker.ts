/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020-03-27
 *
 * @description
 *
 */
import _ from "lodash";
import { showOptionsBottomSheet } from "../../base-ui/dialog/bottom_sheet";
import { LogUtils } from "../../common-base-module/log";
import { Toast } from "../../base-ui/dialog/toast";
import { LoadingDialog } from "../../base-ui/dialog/loading-dialog";
import { errorSummary, errorToStr } from "../../common-base-module/utils";
import { OssImageUtils, UploadImageInfo } from "../file-uploader/oss-image-utils";
import { callNativeWithPromise } from "@hippy/react";
import { ABCError } from "../../common-base-module/common-error";
import { PermissionEvent } from "../../common-base-module/permission/permission-event";
import { PermissionPurposeType } from "../../common-base-module/permission/data";
import { OssUpdateModules, OssUpdateModulesV2 } from "../data/beans";

// import {Permission} from "../common-base-module/permission/permission";

export enum ImagePickerSource {
    camera,
    gallery,
}

const kModuleName = "ImagePicker";

const kMaxWidth = 800;
const kMaxHeight = 1000;

//ios gallery saver sdk 在无权限时抛出的异常是一串英文，需要转一下
const errorMessageMap = new Map([
    ["The user is not allowed to use the camera.", "无相机访问权限"],
    ["The user did not allow camera access.", "无相机访问权限"],
    ["The user is not allowed to use the photo.", "无相册访问权限"],
    ["The user did not allow photo access.", "无相册访问权限"],
]);

/**
 * 上传递图片，支持拍照上传，相册选择两种，并完成上传过程
 */
export class ImagePicker {
    static async pickImageAndUpload(
        source: ImagePickerSource | null = null,
        ossPath: string,
        module?: OssUpdateModules
    ): Promise<UploadImageInfo | undefined> {
        let select = 0;
        if (_.isNil(source)) {
            const options = ["拍照上传", "相册选择"];
            const selectOptions = await showOptionsBottomSheet({
                title: "上传图片",
                options: options,
            });
            if (_.isEmpty(selectOptions)) return Promise.resolve(undefined);
            LogUtils.d("ImagePicker.pickImageAndUpload = " + selectOptions);
            select = selectOptions![0];
        } else {
            select = source;
        }

        const path = await ImagePicker.pickImage({ source: select }).catch((error) => new ABCError(error)); //0拍照上传，1从相册选择
        PermissionEvent.PermissionCloseObservable.next();
        if (path instanceof ABCError) {
            const message = errorSummary(path);

            await Toast.show(errorMessageMap.get(message) ?? message, { warning: true });
            return;
        }
        LogUtils.d("ImagePicker.info = " + path);
        if (!path) return;

        if (path.endsWith("gif")) {
            await Toast.show("不支持gif动态图", { warning: true });
            return;
        }

        const dialog = new LoadingDialog("正在上传图片");
        dialog.show();

        try {
            let info;
            if (module === OssUpdateModules.IM) {
                info = await OssImageUtils.uploadImageV2(OssUpdateModulesV2.IM, path);
            } else if (module === OssUpdateModules.CRM) {
                info = await OssImageUtils.uploadImageV2(OssUpdateModulesV2.CRM, path);
            } else if (module === OssUpdateModules.MEDICAL_RECORD) {
                info = await OssImageUtils.uploadImageV2(OssUpdateModulesV2.MEDICAL_RECORD, path);
            } else {
                info = await OssImageUtils.uploadImage(path, ossPath);
            }

            await dialog.hide();
            return info;
        } catch (err) {
            await dialog.hide();
            let errorStr: string | undefined;
            const networkErrorFlag = "ABCNetworkError";
            if (_.isString(err)) {
                errorStr = err as string;
                if (errorStr.indexOf("ABCNetworkError") >= 0 || errorStr.indexOf("网络错误")) {
                    errorStr = "网络错误";
                }
            }
            if (_.isString(err.message) && (err.message as string).indexOf(networkErrorFlag)) {
                errorStr = "网络错误";
            }

            if (!errorStr) {
                errorStr = errorSummary(err);
            }

            LogUtils.d("ImagePicker.pickImageAndUpload upload error  = " + errorToStr(err));
            await Toast.show(`上传失败：${errorStr}`, { warning: true });
        } finally {
            // if (source === ImagePickerSource.camera || DeviceUtils.isIOS()) {
            //     await FileUtils.deleteFile(path);
            // }
        }
    }

    static async pickImage(params: {
        source: ImagePickerSource;
        maxWidth?: number; //default kMaxWidth
        maxHeight?: number; //default kMinWidth
        imageQuality?: number;
    }): Promise<string> {
        const { source, maxWidth = kMaxWidth, maxHeight = kMaxHeight, imageQuality } = params;
        PermissionEvent.PermissionObservable.next(
            source == ImagePickerSource.camera ? PermissionPurposeType.cameraPermissions : PermissionPurposeType.mediaPermissions
        );
        return callNativeWithPromise(kModuleName, "pickImage", {
            source: source,
            maxWidth: maxWidth,
            maxHeight: maxHeight,
            imageQuality: imageQuality,
        });
    }
}
