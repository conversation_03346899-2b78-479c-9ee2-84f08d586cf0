/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/8/12
 *
 * @description
 */
import { callNativeWithPromise } from "@hippy/react";
import { Version } from "../../base-ui/utils/version-utils";
import { AppInfo } from "../config/app-info";

const kModuleName = "UrlLauncher";

class UrlLauncher {
    supportTel(): boolean {
        return new Version(AppInfo.appVersion).compareTo(new Version("2.1.0")) >= 0;
    }

    tel(phoneNumber: string): Promise<void> {
        return this.launch(`tel:${phoneNumber}`);
    }

    canLaunch(url: string): Promise<boolean> {
        return callNativeWithPromise(kModuleName, "canLaunch", { url: url });
    }

    launch(url: string, useSafariVC?: boolean, universalLinksOnly?: boolean): Promise<void> {
        return callNativeWithPromise(kModuleName, "launch", {
            url: url,
            useSafariVC: useSafariVC,
            universalLinksOnly: universalLinksOnly,
        });
    }

    closeWebView(): Promise<void> {
        return callNativeWithPromise(kModuleName, "closeWebView", {});
    }

    supportPieChart(): boolean {
        return new Version(AppInfo.appVersion).compareTo(new Version("2.2.0")) >= 0;
    }
}

const urlLauncher = new UrlLauncher();
export { urlLauncher };
