import { callNativeWithPromise } from "@hippy/react";
import { AppInfo } from "../config/app-info";

/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/7/9An
 *
 * @description 用于调用微信相关native层api接口
 */

const kModuleName = "WxApi";

//微信分享场景目标
export enum WxShareScene {
    session /**< 聊天界面    */,
    timeline /**< 朋友圈     */,
    favorite /**< 收藏       */,
    specifiedSession /**< 指定联系人  */,
}

export enum WxShareTransactionType {
    img = "img", //图片分享
    webpage = "webpage", // 链接分享
    miniProgram = "miniProgram", //小程序分享
}

interface ThumbSize {
    width: number;
    height?: number;
}

export interface WXApiShareParams {
    scene: WxShareScene;
    url: string;
    title: string;
    desc: string;
    iconUrl: string;
    transaction?: WxShareTransactionType;

    /**
     * 图片缩略图尺寸
     */
    thumbSize?: ThumbSize;

    //小程序特有
    webpageUrl?: string;
    userName?: string;
    path?: string;
    withShareTicket?: string;
}

export enum WxPayResultCode {
    success = 0, //成功	展示成功页面
    // -1	错误	可能的原因：签名错误、未注册APPID、项目设置APPID不正确、注册的APPID与设置的不匹配、其他异常等。
    // -2	用户取消	无需处理。发生场景：用户不支付了，点击取消，返回APP。
}

export enum MiniProgramType {
    WXMiniProgramTypeRelease = 0, //**< 正式版  */
    WXMiniProgramTypeTest = 1, //**< 开发版  */
    WXMiniProgramTypePreview = 2, //**< 体验版  */
}

export class WxApi {
    private static registered_ = false;

    // registerApp
    /**
     * 注册wx appid
     */
    static async registerApp(): Promise<boolean> {
        if (this.registered_) return true;
        this.registered_ = true;

        return callNativeWithPromise<any>(kModuleName, "registerApp", {
            appId: AppInfo.wxAppId,
            enableMTA: true,
        }).catch((/*ignore: any*/) => {
            return false;
        });
    }

    static async sendAuth(
        scope: string,
        state: string
    ): Promise<
        | {
              description?: string;
              errStr?: string;
              errCode?: number;
              type?: number;
              country?: string;
              lang?: string;
              code?: string;
              state?: string;
          }
        | undefined
    > {
        await this.registerApp().catchIgnore();
        return callNativeWithPromise(kModuleName, "sendAuth", {
            scope: scope,
            state: state,
        });
    }

    /**
     * 检测微信是否安装
     */
    static async isWeChatInstalled(): Promise<boolean> {
        this.registerApp().catchIgnore().then();
        return callNativeWithPromise<boolean>(kModuleName, "isWeChatInstalled", {}).catch((/*ignore: any*/) => {
            return false;
        });
    }

    /**
     * 分享到微信好友或朋友圈
     */
    static async share(params: WXApiShareParams): Promise<boolean> {
        await this.registerApp().catchIgnore();
        return callNativeWithPromise<boolean>(kModuleName, "share", {
            thumbSize: { width: 128 },
            transaction: WxShareTransactionType.webpage,
            ...params,
        }).catch((/*ignore: any*/) => {
            return false;
        });
    }

    /**
     * 调起微信支付
     * https://pay.weixin.qq.com/wiki/doc/api/app/app.php?chapter=8_5
     * @param params
     */
    static async payment(params: {
        appId?: string;
        partnerId?: string;
        prepayId?: string;
        packageValue?: string;
        nonceStr?: string;
        timestamp?: string;
        sign?: string;
    }): Promise<{
        prepayId: string;
        returnKey: string;
        extData: string;
        errStr: string;
        openId: string;
        errCode: WxPayResultCode;
    }> {
        await this.registerApp().catchIgnore();
        return callNativeWithPromise<any>(kModuleName, "payment", params).catch((/*ignore: any*/) => {
            return false;
        });
    }

    /**
     * 调起微信跳转小程序
     * https://developers.weixin.qq.com/doc/oplatform/Mobile_App/Launching_a_Mini_Program/Android_Development_example.html
     * @param params
     */
    static async launchMiniProgram(params: {
        userName?: string; //小程序原始id
        path?: string; ///拉起小程序页面的可带参路径，不填默认拉起小程序首页，对于小游戏，可以只传入 query 部分，来实现传参效果，如：传入 "?foo=bar"。
        miniProgramType?: MiniProgramType; // 拉起小程序的类型
    }): Promise<void> {
        await this.registerApp().catchIgnore();
        return callNativeWithPromise<any>(kModuleName, "launchMiniProgram", params).catch((/*ignore: any*/) => {
            return false;
        });
    }
}
