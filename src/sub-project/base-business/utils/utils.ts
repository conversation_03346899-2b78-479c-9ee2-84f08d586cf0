/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/4/21
 */

import { GoodsAgent } from "../../data/goods/goods-agent";
import { FormItemBatchInfos, GoodsInfo, OnlineTreatmentUnits } from "../data/beans";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";

class GoodsUtils {
    private static customUnitList: OnlineTreatmentUnits | undefined;

    public static initGoodsCustomUnitIfNeed(): Promise<boolean> {
        if (GoodsUtils.customUnitList) return Promise.resolve(true);
        return GoodsAgent.getGoodsCustomUnit(false)
            .then((rsp) => {
                GoodsUtils.customUnitList = rsp;
                return true;
            })
            .catch(() => false);
    }

    public static unitIsCustom(unit?: string): boolean {
        return !this.customUnitList?.sysUnitList?.find((item) => item.name == unit);
    }

    public static clearGoodsStockInfo(goodsInfo: GoodsInfo): GoodsInfo {
        return JsonMapper.deserialize(GoodsInfo, {
            cMSpec: goodsInfo.cMSpec,
            grade: goodsInfo.grade,
            materialSpec: goodsInfo.materialSpec,
            medicineCadn: goodsInfo.medicineCadn,
            medicineDosageForm: goodsInfo.medicineDosageForm,
            noStocks: true,
            remark: goodsInfo.remark,
            subType: goodsInfo.subType,
            type: goodsInfo.type,
            name: goodsInfo.name,
            keyId: goodsInfo.keyId,
            pieceUnit: goodsInfo.unit,
        });
    }

    /**
     * 根据药品使用单位进行议价赋值
     * @param goodsInfo
     * @param options
     */
    public static changeGoodsUnitPriceWithUnit(
        goodsInfo: GoodsInfo,
        options: {
            unit?: string;
            unitPrice?: number;
            batchInfos?: FormItemBatchInfos[];
        }
    ): GoodsInfo {
        const isPackageUnit = goodsInfo.packageUnit == options.unit;
        if (isPackageUnit) {
            goodsInfo.packagePrice = options.unitPrice ?? goodsInfo.packagePrice;
        } else {
            goodsInfo.piecePrice = options.unitPrice ?? goodsInfo.piecePrice;
        }
        goodsInfo._batchInfos = options?.batchInfos;
        return goodsInfo;
    }
}

export { GoodsUtils };
