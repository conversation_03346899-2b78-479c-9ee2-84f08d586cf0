import CryptoJS from "crypto-js";

/**
 * 加密工具类
 */
/**
 * EncryptUtils provides a set of utility methods for performing various
 * encryption and hashing operations. It includes methods for MD5 hashing,
 * Base64 encoding and decoding, and AES encryption and decryption.
 */
export class EncryptUtils {
    static readonly passwordKey = "499cbd92351d1e4097d793dd191bf60a";
    // 退费审核账号密码加密对应的key
    static readonly refundAuditAccountPasswordKey = "v0zpnowyokb9k248u2mrbi0rkh22dni5";

    /**
     * MD5加密
     * @param content 需要加密的内容
     * @returns 加密后的字符串
     */
    static md5(content: string): string {
        return CryptoJS.MD5(content).toString();
    }

    /**
     * Base64编码
     * @param content 需要编码的内容
     * @returns 编码后的字符串
     */
    static base64Encode(content: string): string {
        return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(content));
    }

    /**
     * Base64解码
     * @param content 需要解码的内容
     * @returns 解码后的字符串
     */
    static base64Decode(content: string): string {
        return CryptoJS.enc.Base64.parse(content).toString(CryptoJS.enc.Utf8);
    }

    /**
     * AES加密
     * @param content 需要加密的内容
     * @param key 密钥
     * @returns 加密后的字符串
     */
    static aesEncrypt(content: string, key: string): string {
        const keyParsed = CryptoJS.enc.Utf8.parse(CryptoJS.MD5(key).toString());
        const encrypted = CryptoJS.AES.encrypt(content, keyParsed, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
        });
        return encrypted.toString();
    }

    /**
     * AES解密
     * @param content 需要解密的内容
     * @param key 密钥
     * @returns 解密后的字符串
     */
    static aesDecrypt(content: string, key: string): string {
        const keyParsed = CryptoJS.enc.Utf8.parse(CryptoJS.MD5(key).toString());
        const decrypted = CryptoJS.AES.decrypt(content, keyParsed, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
        });
        return decrypted.toString(CryptoJS.enc.Utf8);
    }
}
