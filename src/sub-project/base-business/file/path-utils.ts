/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-04-16
 *
 * @description
 */
import { userCenter } from "../../user-center";
import { LogUtils } from "../../common-base-module/log";
import FileUtils from "../../common-base-module/file/file-utils";
import { chainScopeMemoryCache, clinicScopeMemoryCache } from "../../common-base-module/cleanup/memory-cache";

export default class PathUtils {
    /**
     * 获取当前登录用户的数据存储根路径（以诊所为维度）
     */
    static async getLoginUserDataDir(): Promise<string> {
        const docPath = await FileUtils.getDocPath();
        const user = userCenter.employee;
        const clinic = userCenter.clinic;

        return `${docPath}/${user!.id}/${clinic!.clinicId}`;
    }

    /**
     * 获取库存管理模块数据存储根路径
     * @param createIfNotExists
     */
    static async getInventoryDir(createIfNotExists = true): Promise<string> {
        const dataPath = await PathUtils.getLoginUserDataDir();

        const inventoryRootDir = `${dataPath}/inventory`;

        if (!createIfNotExists) return inventoryRootDir;

        if (!(await FileUtils.fileExists(inventoryRootDir))) {
            await FileUtils.createDir(inventoryRootDir);
        }

        return inventoryRootDir;
    }

    /**
     * 获取库存管理模块数据存储根路径V2
     * 解决之前版本路径存储bug
     * @param createIfNotExists
     */
    static async getInventoryV2Dir(createIfNotExists = true): Promise<string> {
        const dataPath = await PathUtils.getLoginUserDataDir();

        const inventoryRootDir = `${dataPath}/inventory_v2`;

        if (!createIfNotExists) return inventoryRootDir;

        if (!(await FileUtils.fileExists(inventoryRootDir))) {
            await FileUtils.createDir(inventoryRootDir);
        }

        return inventoryRootDir;
    }

    /**
     * 获取当前登录用户连锁维度数据存储根路径
     */
    static async getLoginUserChainDataDir(): Promise<string> {
        const docPath = await FileUtils.getDocPath();
        const user = userCenter.employee;
        const clinic = userCenter.clinic;

        return `${docPath}/${user!.id}/${clinic!.chainId}`;
    }

    /**
     * 获取当前登录用户数据存储根路径(以用户为维度)
     */
    static async getLoginEmployeeDataDir(): Promise<string> {
        const docPath = await FileUtils.getDocPath();
        const user = userCenter.employee;
        return `${docPath}/${user!.id}`;
    }
    static getOutpatientDir(createIfNotExists = true, clinicScope = true): Promise<string> {
        return PathUtils.getModuleRootDir(createIfNotExists, "outpatient", clinicScope);
    }

    static getPatientDir(createIfNotExists = true, clinicScope = true): Promise<string> {
        return PathUtils.getModuleRootDir(createIfNotExists, "patient", clinicScope);
    }
    static async getModuleRootDir(createIfNotExists = true, module: string, clinicScope = true): Promise<string> {
        LogUtils.d("getModuleRootDir");
        const scopeKey = module + "ScopeKey_";
        if (clinicScope) {
            const path = clinicScopeMemoryCache.get(scopeKey);
            if (path != undefined) return path as string;
        } else {
            const path = chainScopeMemoryCache.get(scopeKey);
            if (path != undefined) return path as string;
        }
        LogUtils.d("getModuleRootDir" + module + ", 未命中缓存");
        const dataPath = clinicScope ? await PathUtils.getLoginUserDataDir() : await PathUtils.getLoginUserChainDataDir();

        const rootDir = `${dataPath}/patient`;

        if (!createIfNotExists) return rootDir;

        const result = await FileUtils.fileExists(rootDir);
        if (result) {
            return rootDir;
        }

        await FileUtils.createDir(rootDir);

        if (clinicScope) {
            clinicScopeMemoryCache.set(scopeKey, rootDir);
        } else {
            chainScopeMemoryCache.set(scopeKey, rootDir);
        }

        return rootDir;
    }

    /// 获取收费数据存放目录
    static async getChargeInvoiceDir(createIfNotExists = true): Promise<string> {
        const dataPath = await this.getLoginUserDataDir();

        const chargeRootDir = `${dataPath}/charge_invoice`;

        if (!createIfNotExists) return chargeRootDir;

        const dic = await FileUtils.fileExists(chargeRootDir);
        if (dic) {
            return chargeRootDir;
        }

        await FileUtils.createDir(chargeRootDir);

        return chargeRootDir;
    }

    /// 获取执行数据存放目录
    static async getExecuteInvoiceDir(createIfNotExists = true): Promise<string> {
        const dataPath = await this.getLoginUserDataDir();

        const executeRootDir = `${dataPath}/execute_invoice`;

        if (!createIfNotExists) return executeRootDir;

        const dic = await FileUtils.fileExists(executeRootDir);
        if (dic) {
            return executeRootDir;
        }

        await FileUtils.createDir(executeRootDir);

        return executeRootDir;
    }

    //     获取零售数据存放目录
    static async getRetailInvoiceDir(createIfNotExists = true): Promise<string> {
        const dataPath = await this.getLoginUserDataDir();

        const retailRootDir = `${dataPath}/retail_invoice`;

        if (!createIfNotExists) return retailRootDir;

        const dic = await FileUtils.fileExists(retailRootDir);
        if (dic) {
            return retailRootDir;
        }

        await FileUtils.createDir(retailRootDir);

        return retailRootDir;
    }
}
