import React from "react";
import { View, Text } from "@hippy/react";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { AbcToolBar, AbcToolBarButtonStyle1, BottomSheetHelper } from "../../base-ui/abc-app-library";
import { AbcScrollView } from "../../base-ui/views/abc-scroll-view";
import { SizedBox } from "../../base-ui";
import { userCenter } from "../../user-center";
import { BatchesInsufficientInventoryItem } from "./batches-beans";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";

const containerStyle = {
    subTitleStyle: TextStyles.t14NB.copyWith({ lineHeight: Sizes.dp20 }),
    contentStyle: TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp20 }),
};

interface DrugBatchesInsufficientInventoryDialogProps {
    list?: BatchesInsufficientInventoryItem[];
}
export const DrugBatchesInsufficientInventoryDialog: React.FC<DrugBatchesInsufficientInventoryDialogProps> = ({ list }) => {
    // 按 tips 分组
    const groupedByTips = React.useMemo(() => {
        if (!list?.length) return [];

        const groups = new Map<string, BatchesInsufficientInventoryItem[]>();

        list.forEach((item) => {
            const tips = item.tips || "";
            if (!groups.has(tips)) {
                groups.set(tips, []);
            }
            groups.get(tips)!.push(item);
        });

        return Array.from(groups.entries()).map(([tips, items]) => ({
            tips,
            items,
        }));
    }, [list]);

    return (
        <View style={{ flex: 1, backgroundColor: Colors.white }}>
            {BottomSheetHelper.createTitleBar("药品批次库存不足")}
            <AbcScrollView>
                {groupedByTips.map((group, groupIndex) => (
                    <View key={groupIndex}>
                        {/* Tips 标题 - 每个分组只显示一次 */}
                        <View
                            style={[
                                Sizes.paddingLTRB(Sizes.dp16, Sizes.dp8),
                                ABCStyles.bottomLine,
                                { backgroundColor: Colors.cardYellowBackgroundColor, borderColor: Colors.dividerLineColor },
                            ]}
                        >
                            <Text style={TextStyles.t14NT3.copyWith({ color: Colors.abc_color_Y6, lineHeight: Sizes.dp20 })}>
                                {group.tips}
                            </Text>
                        </View>

                        {/* 该分组下的所有药品 */}
                        {group.items.map((item, itemIndex) => {
                            const totalStock = item.batchList?.reduce((prev, next) => prev + (next.stockCheckCount ?? 0), 0) ?? 0;
                            const isLastInGroup = itemIndex === group.items.length - 1;

                            return (
                                <View
                                    key={`${groupIndex}-${itemIndex}`}
                                    style={{
                                        padding: Sizes.dp16,
                                        ...(!isLastInGroup ? ABCStyles.bottomLine : {}),
                                        borderColor: Colors.dividerLineColor,
                                    }}
                                >
                                    <View style={ABCStyles.rowAlignCenter}>
                                        <Text style={TextStyles.t16NB.copyWith({ lineHeight: Sizes.dp24 })}>{item?.name ?? ""}</Text>
                                        <SizedBox width={Sizes.dp8} />
                                        <Text style={containerStyle.contentStyle}>{item?.specification ?? ""}</Text>
                                    </View>
                                    <View style={{ paddingVertical: Sizes.dp4 }}>
                                        <Text style={containerStyle.contentStyle}>{item?.manufacture ?? ""}</Text>
                                    </View>
                                    <View style={ABCStyles.rowAlignCenter}>
                                        <Text style={containerStyle.contentStyle}>{`批次 ${item?.batchId ?? "-"}`}</Text>
                                        <SizedBox width={Sizes.dp16} />
                                        <Text style={containerStyle.contentStyle}>
                                            {`效期 ${!!item.expireDate ? new Date(item.expireDate)?.format("yyyy-MM-dd") : "-"}`}
                                        </Text>
                                    </View>
                                    <View style={{ paddingTop: Sizes.dp8 }}>
                                        <View style={ABCStyles.rowAlignCenterSpaceBetween}>
                                            <Text style={containerStyle.subTitleStyle}>从以下批次盘入</Text>
                                            <Text style={containerStyle.subTitleStyle}>{`${Math.abs(totalStock)}${item?.unit ?? ""}`}</Text>
                                        </View>
                                        {item.batchList?.map((batch, batchIndex) => {
                                            return (
                                                <View
                                                    key={`${item.batchId}-${batchIndex}`}
                                                    style={[ABCStyles.rowAlignCenter, { paddingTop: Sizes.dp4 }]}
                                                >
                                                    <Text style={containerStyle.contentStyle}>{`批次 ${batch?.batchId ?? "-"}`}</Text>
                                                    <SizedBox width={Sizes.dp16} />
                                                    <View style={[ABCStyles.rowAlignCenterSpaceBetween, { flex: 1 }]}>
                                                        <Text style={containerStyle.contentStyle}>
                                                            {`效期 ${
                                                                !!batch?.expiryDate
                                                                    ? new Date(batch?.expiryDate)?.format("yyyy-MM-dd")
                                                                    : "--"
                                                            }`}
                                                        </Text>
                                                        <Text style={containerStyle.contentStyle}>
                                                            {`${Math.abs(batch?.stockCheckCount ?? 0)}${item?.unit ?? ""}`}
                                                        </Text>
                                                    </View>
                                                </View>
                                            );
                                        })}
                                    </View>
                                </View>
                            );
                        })}
                    </View>
                ))}
            </AbcScrollView>
            <AbcToolBar>
                <AbcToolBarButtonStyle1
                    text={"自动盘点并发药"}
                    onClick={userCenter.clinic?.hasStockCheckPermission ? () => ABCNavigator.pop(true) : undefined}
                />
            </AbcToolBar>
        </View>
    );
};
