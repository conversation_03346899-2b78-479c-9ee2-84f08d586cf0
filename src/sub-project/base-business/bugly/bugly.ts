/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/8/12
 *
 * @description
 */
import { callNativeWithPromise, Clipboard } from "@hippy/react";
import logUtils from "../../common-base-module/log/log-utils";
import { errorToBuglyReportStr } from "../../common-base-module/utils";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import { ABCApiError, ABCNetworkError } from "../../net";
import { AnyType } from "../../common-base-module/common-types";
import { ABCNetworkTimeoutError } from "../../net/api";
import { Version } from "../../base-ui/utils/version-utils";
import { AppInfo } from "../config/app-info";

const kModuleName = "Bugly";

class Bugly {
    private _postCatchedExpceitonEnabled = false; //是否忽略异常上报
    private _exceptionTag = "";
    private _versionTag = "";

    constructor() {
        // @ts-ignore
        const global = __GLOBAL__ as any;

        function errorToDetailStr(e: AnyType): string {
            const { message } = e;
            let errorDetail = message ?? "";
            errorDetail += errorToBuglyReportStr(e);
            if (e instanceof ABCApiError || e instanceof ABCNetworkTimeoutError || e instanceof ABCNetworkError) {
                errorDetail += `method：${e.method}, url: ${e.url}`;
            }

            return errorDetail;
        }

        if (global && global.globalErrorHandle) {
            logUtils.d(`Bugly register uncaughtException`);
            global.globalErrorHandle.uncaughtException = (e: any) => {
                const { stack } = e;
                this.postCatchedException("uncaughtException", errorToDetailStr(e), stack);
                logUtils.e(`uncaughtException: ${errorToDetailStr(e)}`);
            };

            global.globalErrorHandle.unhandlePromiseRejection = (e: any) => {
                const { stack } = e;
                this.postCatchedException("unhandlePromiseRejection", errorToDetailStr(e), stack);
                logUtils.e(`unhandlePromiseRejection: ${errorToDetailStr(e)}`);
            };
        }
    }

    public setPostCatchedExceptionEnabled(enable: boolean) {
        this._postCatchedExpceitonEnabled = enable;
    }

    public init(): void {
        const greaterThan220 = new Version(AppInfo.appVersion).compareTo(new Version("2.2.0.0200")) > 0;
        if (greaterThan220) callNativeWithPromise(kModuleName, "init", {});
    }

    setUserId(userId: string) {
        callNativeWithPromise(kModuleName, "setUserId", { userId: userId });
    }

    setUserTag(userTag: string) {
        callNativeWithPromise(kModuleName, "setUserTag", { userTag: userTag });
    }

    public setExceptionTag(tag: string) {
        this._exceptionTag = tag;
    }

    public setVersionTag(vTag: string): void {
        this._versionTag = vTag;
    }

    putUserData(key: string, value: string) {
        callNativeWithPromise(kModuleName, "putUserData", {
            key: key,
            value: value,
        });
    }

    checkUpgrade(params: { isManual: boolean; isSilence: boolean; useCache: boolean }) {
        callNativeWithPromise(kModuleName, "checkUpgrade", params);
    }

    /**
     * 上传异常信息
     * @param crashName 错误类型
     * @param crashDetail 错误详情
     * @param crashStack 错误发生时的堆栈
     * @param extraInfo 其它额外需要上传的信息
     * @param showConfirmUIWhenNotRealReport{boolean} //当前环境不允许上报时，则显示ui确认对话框,default true
     */
    public postCatchedException(
        crashName: string,
        crashDetail?: string,
        crashStack?: string,
        extraInfo?: { [key: string]: string },
        showConfirmUIWhenNotRealReport?: boolean
    ) {
        const { environment, ServerEnvType } = require("../config/environment");
        if (!this._postCatchedExpceitonEnabled || environment.serverEnvType != ServerEnvType.normal) {
            if (showConfirmUIWhenNotRealReport ?? true) {
                const { showConfirmDialog } = require("../../base-ui/dialog/dialog-builder");
                const { LogUtils } = require("../../common-base-module/log");
                const content = crashName + "\n" + crashStack;
                LogUtils.e("postCatchedException: " + content);
                showConfirmDialog("发生异常", content.slice(0, Math.min(0, 100))).then(() => {
                    Clipboard.setString(crashName + "\n" + crashStack);
                });

                crashName = `[TestEnv]${crashName}`;
            }

            if (!this._postCatchedExpceitonEnabled) return;
        }

        const crashMessageSuffix = DeviceUtils.isIOS() ? "_" + this._versionTag : "";
        callNativeWithPromise(kModuleName, "postCatchedException", {
            exception_name: crashName,
            crash_message: DeviceUtils.isIOS() ? `${crashName}${crashMessageSuffix}` : crashDetail, //Android native层有bug,将crash_detail字段忽略了，所以此处在此通过crash_message来传递
            crash_detail: crashDetail,
            crash_stack: this._exceptionTag + "\n" + crashDetail + "\n" + crashStack,
            extra_info: extraInfo ?? { extraTag: this._exceptionTag },
        });
    }
}

const bugly = new Bugly();
export { bugly };
