/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/8/7
 *
 * @description
 */
import { environment, ServerEnvType } from "../config/environment";
import { UUIDGen } from "../../common-base-module/utils";
import { ossManager } from "./oss/oss-manager";
import { ImageUtils } from "../../base-ui/utils/image-utils";
import { FileUploader } from "./file-uploader";
import FileUtils from "../../common-base-module/file/file-utils";
import { File } from "../../common-base-module/file/file";
import { OssProxyToken } from "./oss/oss-token-agent";
import { OssSignAgent } from "./oss/oss-sign-agent";

interface ImageSize {
    width: number;
    height: number;
}

export interface UploadImageInfo {
    filePath?: string;
    url?: string;
    size?: ImageSize;
}

interface _Config {
    bucket: string;
    root: string;
    hostPrefix: string;
}

const __sConfig: Map<ServerEnvType, _Config> = new Map([
    [
        ServerEnvType.dev,
        {
            bucket: "cd-cis-static-assets-dev",
            root: "clinic-usage",
            hostPrefix: "https://dev-img.abcyun.cn",
        },
    ],
    [
        ServerEnvType.test,
        {
            bucket: "cd-cis-static-assets-test",
            root: "clinic-usage",
            hostPrefix: "https://test-img.abcyun.cn",
        },
    ],
    [
        ServerEnvType.normal,
        {
            bucket: "cd-cis-static-assets",
            root: "clinic-usage",
            hostPrefix: "https://img.abcyun.cn",
        },
    ],
]);

export class OssImageUtils {
    /**
     *
     * @param file 文件名
     * @param path oss存储文件路径 {clinicId}/{moduleName}
     * @example path: XXXXXXXX/crm
     */
    static async uploadImage(file: string, path: string): Promise<UploadImageInfo> {
        await FileUploader.initOssToken();
        const config = __sConfig.get(environment._serverEnvType)!;
        const ext = FileUtils.getFileExt(new File(file));
        let fileId = UUIDGen.generate();
        if (ext != undefined) {
            fileId = fileId + ext;
        }
        const ossPath = `${config.root}/${path}/${fileId}`;
        const handler = ossManager.upload(config.bucket, ossPath, file);
        const rsp = await Promise.all([
            handler.onCompleter,
            ImageUtils.getImageSize(file).catch((/*ignore*/) => {
                return undefined;
            }),
        ]);

        const uploadResult = rsp[0];
        if (uploadResult!.error) throw uploadResult!.error;

        const size = rsp[1];
        // const url = config.hostPrefix + "/" + ossPath;
        return {
            // url: uploadResult!.url,
            url: uploadResult?.url,
            filePath: file,
            size: size,
        };
    }

    static async uploadImageV2(options: OssProxyToken, file: string): Promise<UploadImageInfo> {
        const token = await FileUploader.initOssProxyToken(options);
        const ext = FileUtils.getFileExt(new File(file));
        let fileId = UUIDGen.generate();
        if (ext != undefined) {
            fileId = fileId + ext;
        }
        const ossPath = `${token.fileDir}/${fileId}`;
        const handler = ossManager.upload(token.bucket, ossPath, file);
        const rsp = await Promise.all([
            handler.onCompleter,
            ImageUtils.getImageSize(file).catch((/*ignore*/) => {
                return undefined;
            }),
        ]);

        const uploadResult = rsp[0];
        if (uploadResult!.error) throw uploadResult!.error;

        const size = rsp[1];
        const url = token.abcBaseUrlPrefix + "/" + fileId;
        // 请求签名url
        const signFile = await OssSignAgent.getFileSignUrl(url);
        return {
            url: signFile.signFileUrl,
            filePath: file,
            size: size,
        };
    }
}
