import { ABCApiNetwork } from "../../../net";

export class SignFile {
    originalFileUrl?: string;
    signFileUrl?: string;
}

export class OssSignAgent {
    /**
     * 获取文件签名路径
     */
    static getFileSignUrl(fileUrl: string): Promise<SignFile> {
        return ABCApiNetwork.get("interfaces/oss-proxy/sign-file-url", {
            queryParameters: {
                fileUrl,
            },
            clazz: SignFile,
        });
    }
}
