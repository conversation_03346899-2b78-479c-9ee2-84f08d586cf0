/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/8/7
 *
 * @description
 */
import { JsonProperty } from "../../../common-base-module/json-mapper/json-mapper";
import { ABCApiNetwork } from "../../../net";

export class OssToken {
    @JsonProperty({ name: "AccessKeySecret" })
    accessKeySecret!: string;
    @JsonProperty({ name: "AccessKeyId" })
    accessKeyId!: string;
    @JsonProperty({ name: "Expiration" })
    expiration!: string;

    @JsonProperty({ name: "SecurityToken" })
    securityToken!: string;

    bucket!: string;

    endpoint!: string;
}

export class OssProxyRes {
    abcBaseUrlPrefix?: string;
    accessKeyId?: string;
    accessKeySecret?: string;
    bucket!: string;
    endpoint!: string;
    expiration?: string;
    fileDir?: string;
    internalEndpoint?: string;
    isPrivateBucket?: number;
    region!: string;
    securityToken?: string;
}

export interface OssProxyToken {
    businessModule: string;
    businessScene: string;
}

export class OssTokenAgent {
    /**
     * 获取oss token
     */
    static getOssToken(): Promise<OssToken> {
        return ABCApiNetwork.get("interfaces/oss/getosstoken", {
            clazz: OssToken,
        });
    }

    static getOssProxyToken(body: OssProxyToken): Promise<OssProxyRes> {
        return ABCApiNetwork.post("interfaces/oss-proxy/sts-token", {
            body,
            clazz: OssProxyRes,
        });
    }
}
