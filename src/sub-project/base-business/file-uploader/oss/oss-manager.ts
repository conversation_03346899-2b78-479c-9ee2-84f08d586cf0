import { callNativeWithPromise, HippyEventEmitter } from "@hippy/react";
import { UUIDGen } from "../../../common-base-module/utils";
import { Completer } from "../../../common-base-module/async/completer";
import { Subject } from "rxjs";
import { OssProxyRes } from "./oss-token-agent";

/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/8/7
 *
 * @description
 */

interface UploadProgress {
    currentSize: number;
    totalSize: number;
}

interface UploadCompleteRsp {
    url?: string;
    error?: string;
}

const kModuleName = "OSS";

export interface UploadHandler {
    cancel: () => void;

    onProgress: Subject<UploadProgress>;
    onCompleter: Promise<UploadCompleteRsp>;
}

class OssManager {
    init(params: { accessKeyId: string; accessKeySecret: string; securityToken: string; endpoint: string }): Promise<boolean> {
        //前端定义，避免mobile-proxy版本不同导致无法上传
        params.endpoint = "oss-cn-chengdu.aliyuncs.com";
        return callNativeWithPromise(kModuleName, "init", params);
    }

    initProxy(params: OssProxyRes): Promise<boolean> {
        return callNativeWithPromise(kModuleName, "init", params);
    }

    /**
     * oss文件上传
     * @param bucketName
     * @param objectKey
     * @param file 要上传的文件
     * @return 文件上传handler,可用于取消，监听上传进度等
     */
    upload(bucketName: string, objectKey: string, file: string): UploadHandler {
        const taskId = UUIDGen.generate();

        let hippyEventEmitterHandler: any;
        const completer = new Completer<UploadCompleteRsp>();
        const handler: UploadHandler = {
            cancel: () => {
                hippyEventEmitterHandler?.remove();
                hippyEventEmitterHandler = undefined;
                this.cancelUpload(taskId);
            },

            onCompleter: completer.promise,
            onProgress: new Subject(),
        };

        const hippyEventEmitter = new HippyEventEmitter();
        hippyEventEmitterHandler = hippyEventEmitter.addListener(
            "OSSCallback",
            (evt: { taskId: string; methodName: string; args: any }) => {
                const { taskId: callbackTaskId, methodName, args } = evt;
                if (taskId !== callbackTaskId) return;
                if (methodName == "onProgress") {
                    const { currentSize, totalSize } = args;
                    handler.onProgress.next({
                        currentSize: currentSize,
                        totalSize: totalSize,
                    });
                } else if (methodName == "onSuccess") {
                    const { url } = args;

                    completer.resolve({
                        url: url,
                    });
                } else if (methodName == "onFailure") {
                    const { error } = args;
                    completer.reject({
                        error: error,
                    });
                }
            }
        );

        callNativeWithPromise(kModuleName, "upload", {
            bucketName: bucketName,
            objectKey: objectKey,
            file: file,
            id: taskId,
        });

        return handler;
    }

    cancelUpload(taskId: string) {
        return callNativeWithPromise(kModuleName, "cancelUpload").then(() => taskId);
    }
}

const ossManager = new OssManager();
export { ossManager };
