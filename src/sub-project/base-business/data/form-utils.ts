/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020/5/22
 *
 * @description
 *
 */
import { FormCheckBox, FormDataDetail, FormDetail, FromValues } from "./form-beans";

export interface QAItem {
    question: string;
    answer: string;
}

export class FormUtils {
    /**
     * 从表单中抽取问答
     * @param formDetail
     * @param formDataDetail
     */
    static extractQAList(formDetail: FormDetail, formDataDetail?: FormDataDetail): QAItem[] {
        let values: FromValues | undefined = undefined;
        if (formDataDetail) {
            values = formDataDetail.values;
        }

        return formDetail?.components.map((item) => {
            if (!values)
                return {
                    question: item.labelWithType,
                    answer: "",
                };

            let value = values![item._id].formatValue;

            if (item instanceof FormCheckBox) {
                value = value.split("|").join("、");
            }
            return {
                question: item.label,
                answer: value,
            };
        });
    }
}
