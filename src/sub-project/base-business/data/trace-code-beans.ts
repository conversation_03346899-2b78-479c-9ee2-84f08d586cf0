import { <PERSON><PERSON><PERSON>ap<PERSON>, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { GoodsInfo, GoodsSubType, GoodsType, GoodsTypeId, KeywordTraceableCode, TraceableCodeList } from "./beans";
import { ChargeSourceFormType } from "../../charge/data/charge-beans";
import { DispensingFormItem } from "../../pharmacy/data/pharmacy-bean";
import { userCenter } from "../../user-center";
import { UUIDGen } from "../../common-base-module/utils";
import { GoodsAgent } from "../../data/goods/goods-agent";
import { cloneDeep } from "lodash";

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export const isNull = (val?: any): boolean => {
    return val === null || val === undefined || val === "";
};

export const TraceCodeScenesEnum = Object.freeze({
    PHARMACY: 10,
    INVENTORY: 20,
});

// 追溯码类型
export const TraceableCodeTypeEnum = Object.freeze({
    HAS_CODE: 0,
    NO_CODE: 10,
});

// 标识码操作类型
export const TraceableCodeOpTypeEnum = Object.freeze({
    BIND: 0,
    UN_BUNDLING: 10,
});

// 拆零标记
export const TraceableCodeDismountingFlagEnum = Object.freeze({
    // 不拆零
    NO_DISMOUNTING: 0,
    // 系统强制拆零-医保地区强制拆零
    SYSTEM_FORCE_DISMOUNTING: 0x01,
    // 库存批次不够导致拆零
    SYSTEM_BATCH_DISMOUNTING: 0x02,
    // 系统计算拆零-发药批次拆分导致拆零
    SYSTEM_CALCULATE_DISMOUNTING: 0x04,
});

// 医保拆零标记
export const ShebaoTraceableCodeDismountingFlagEnum = Object.freeze({
    // 不拆零不允许编辑
    NO_DISMOUNTING_NO_EDIT: 0,
    // 拆零不允许编辑
    DISMOUNTING_NO_EDIT: 0b01,
    // 不拆零允许编辑
    NO_DISMOUNTING_EDIT: 0b10,
    // 拆零允许编辑
    DISMOUNTING_EDIT: 0b11,
});

// 追溯码使用场景
export const SceneTypeEnum = Object.freeze({
    NONE: "NONE", // 放开各处校验
    GOODS_IN: "goodsIn", // 采购入库
    GOODS_OUT: "goodsOut", // 退货出库
    GOODS_CHECK: "goodsCheck", // 盘点
    GOODS_TAKE: "goodsTake", // 药店收货
    CHARGE: "charge", // 收费
    PHARMACY_CHARGE: "pharmacy_charge", // 药店收费
    PHARMACY: "pharmacy", // 药房
    SELL: "sell", // 收费，药房等销售场景
});

//追溯码码上放心级别 null/0未知 1小 2中 3大
export const AliPackageLevelEnum = Object.freeze({
    SMALL: 1,
    MEDIUM: 2,
    BIG: 3,
});

export const AliPackageLevelLabel = Object.freeze({
    [AliPackageLevelEnum.SMALL]: "小码",
    [AliPackageLevelEnum.MEDIUM]: "中码",
    [AliPackageLevelEnum.BIG]: "大码",
});

export const DISABLED = "DISABLED"; // 不允许社保支付

export class TraceNoListItem {
    hisPackageCount?: number;
    traceCodeLockId?: string;
    no?: string;
    hisPieceCount?: number;
    trdnFlag?: number; // 拆零标志，-1表示未计算
}
export class TraceCodeListItem {
    goodsId?: string;
    goodsSubType?: number;
    goodsType?: number;
    keyId?: string;
    limitUnitType?: number;
    medicineCadn?: string;
    medicineNmpn?: string;
    @JsonProperty({ type: Array, clazz: TraceNoListItem })
    noList?: TraceNoListItem[];
    packageCount?: number;
    packageUnit?: string;
    patientOrderId?: string;
    pieceCount?: number;
    pieceNum?: number;
    pieceUnit?: string;
    shebaoMedicineName?: string;
    shebaoMedicineNmpn?: string;
    shebaoPackageUnit?: string;
    shebaoPieceNum?: number;
    shebaoPieceUnit?: string;
    shebaoPriceLimit?: number;
}
// 获取追溯码应采数量请求参数
export class TraceCodeReq {
    @JsonProperty({ type: Array, clazz: TraceCodeListItem })
    list?: TraceCodeListItem[];
    patientOrderId?: string;
    scene?: number;
    shebaoHisType?: string;
    shebaoRegion?: string;
}
export class TraceCodeCollectItem {
    hisLeftTotalPieceCount?: number;
    hisPackageCount?: number;
    newTraceCodeNo?: boolean;
    no?: string;
    hisPieceCount?: number;
    hisLeftTotalPackageCount?: number;
}
export class TraceCodeCollectRsp {
    collectCountTransFactor?: number;
    goodsId?: string;
    isTransformable?: number;
    keyId?: string;
    pacakgeCount?: number;
    pieceNum?: number;
    policy?: number;
    shebaoPackageUnit?: string;
    shebaoPieceNum?: number;
    shebaoPieceUnit?: string;
    traceableCodeNum?: number; // 应采数量
    transPackageCount?: number;
    transPackageMatchCondition?: string;
    transPieceCount?: number;
    transPieceMatchCondition?: string;
    transformWarn?: number;
    useLimitPriceTargetUnit?: number;
    @JsonProperty({ type: Array, clazz: TraceCodeCollectItem })
    list?: TraceCodeCollectItem[];
    shebaoDismountingFlag?: number;
}

export class TraceCode {
    // 支持采集追溯码的商品类型
    static usableTraceCodeGoodsTypeIdList = [
        GoodsTypeId.medicineWest,
        GoodsTypeId.medicineChinesePatent,
        GoodsTypeId.materialMedical,
        GoodsTypeId.MATERIAL_DISINFECTANT,
    ];
    /**
     * @desc 判断是否支持追溯码
     * <AUTHOR>
     * @date 2024/8/31 下午12:19
     */
    static isSupportTraceCode(typeId?: number): boolean {
        if (!typeId) return false;
        return this.usableTraceCodeGoodsTypeIdList.includes(typeId);
    }
    /**
     * @desc 判断无码商品是否补码
     * <AUTHOR>
     * @date 2024/8/31 下午12:19
     */
    static isSupplementNoCodeGoods(goods: GoodsInfo): boolean {
        return TraceCode.isNoTraceCodeGoods(goods) && !TraceCode.isNullCodeGoods(goods) && TraceCode.isSupportTraceCode(goods?.typeId);
    }
    /**
     * @desc 获取不同地区的药品无码标识，若地区没有固定值不上报，需要自行处理为无需采集提示给用户
     * <AUTHOR>
     * @date 2024/9/10 下午2:48
     * @return {string} 无码标识-默认是药品
     */
    static getDefaultNoCodeIdentification(goodsInfo?: GoodsInfo): string {
        const {
            drug = "", // 药品默认追溯码
            material = "", // 耗材默认追溯码
        } = {};

        return goodsInfo?.type === ChargeSourceFormType.material ? material : drug;
    }
    /**
     * @desc 判断药品有码还是无码药品
     * <AUTHOR>
     * @date 2024/9/10 上午9:49
     */
    static isNoTraceCodeGoods(goodsInfo?: GoodsInfo): boolean {
        const { traceableCodeNoInfoList } = goodsInfo || {};
        return !!(traceableCodeNoInfoList || []).find((item) => item.type === TraceableCodeTypeEnum.NO_CODE);
    }

    // 特殊无码商品-无固定值后端默认是空字符串
    static isNullCodeGoods(goodsInfo?: GoodsInfo): boolean {
        return TraceCode.isNoTraceCodeGoods(goodsInfo) && isNull(TraceCode.getDefaultNoCodeIdentification(goodsInfo));
    }
    static getTraceCodeLockId(sceneType: string, item: DispensingFormItem): string | undefined {
        const idMap = {
            [SceneTypeEnum.PHARMACY]: item.sourceFormItemId,
            [SceneTypeEnum.CHARGE]: item.id,
        };

        return idMap[sceneType];
    }

    /**
     * @desc 判断formItem是否支持追溯码
     * <AUTHOR> Yang
     * @date 2024-09-06 15:47:44
     */
    static formItemSupportTraceCode(formItem: DispensingFormItem): boolean {
        const { productInfo, productType, productSubType, composeChildren } = formItem;

        if (!productInfo) {
            if (
                productType === GoodsType.medicine &&
                [GoodsSubType.medicineWestern, GoodsSubType.medicineChinesePatent].indexOf(productSubType ?? 0) > -1
            ) {
                return true;
            }

            return productType === GoodsType.material && [GoodsSubType.materialMedical].indexOf(productSubType ?? 0) > -1;
        }

        const { typeId } = productInfo || {};
        if (typeId === GoodsTypeId.decoctionFee) {
            return !!composeChildren?.some((it) => this.formItemSupportTraceCode(it));
        }
        return this.usableTraceCodeGoodsTypeIdList.includes(typeId ?? 0);
    }

    static isShebaoDismountingFlag(shebaoDismountingFlag: number): boolean {
        return [
            ShebaoTraceableCodeDismountingFlagEnum.DISMOUNTING_NO_EDIT,
            ShebaoTraceableCodeDismountingFlagEnum.DISMOUNTING_EDIT,
        ].includes(shebaoDismountingFlag);
    }

    /**
     * @desc goods是否支持采集
     * <AUTHOR> Yang
     * @date 2024-11-06 19:24:37
     */
    static supportCollect(goods?: GoodsInfo): boolean {
        const { shebao } = goods || {};
        if (!shebao) return false;
        if (!shebao.nationalCode || shebao.nationalCode === DISABLED) return false;
        return !TraceCode.isNullCodeGoods(goods);
    }

    /**
     * @desc 是否应该使用小单位
     * @param traceCode
     */
    static isShouldApplyPieceUnit(traceCode?: TraceableCodeList): boolean {
        const { hisPieceCount } = traceCode ?? {};
        return !!hisPieceCount;
    }

    static async queryTraceCodeCharge(selectGoods: GoodsInfo, traceableCodeList?: KeywordTraceableCode[]): Promise<GoodsInfo | undefined> {
        const goods = cloneDeep(selectGoods);
        // 是否开启拆零不采功能
        const hasEnableDismountingMode = userCenter.inventoryClinicConfig?.traceCodeConfig?.shebaoDismountingCollectStrategy == 0;
        // 是否开通码上放心平台
        // const hasCodeSafeOpened = userCenter.inventoryClinicConfig?.traceCodeConfig?.aliHealth?.status == 2;
        if (
            !(
                !TraceCode.isNoTraceCodeGoods(goods) &&
                TraceCode.isSupportTraceCode(goods?.typeId) &&
                (hasEnableDismountingMode || !!traceableCodeList?.length)
            )
        )
            return;
        const traceCodeReqList = [goods].map((item) => {
            const productInfo = item;
            const shebao = productInfo?.shebao || {};
            let pieceCount, packageCount;
            if (!!productInfo?.dismounting) {
                pieceCount = 1;
            } else {
                packageCount = 1;
            }

            return JsonMapper.deserialize(TraceCodeListItem, {
                goodsId: productInfo?.goodsId,
                goodsSubType: productInfo?.subType,
                goodsType: productInfo?.type,
                keyId: item.keyId ?? UUIDGen.generate(),
                limitUnitType: shebao?.limitUnitType,
                medicineCadn: productInfo?.medicineCadn,
                medicineNmpn: productInfo?.medicineNmpn,
                packageCount: packageCount,
                packageUnit: productInfo?.packageUnit,
                pieceCount: pieceCount,
                pieceNum: productInfo?.pieceNum,
                pieceUnit: productInfo?.pieceUnit,
                shebaoMedicineName: shebao?.shebaoMedicineNmpn,
                shebaoMedicineNmpn: shebao?.shebaoMedicineNmpn,
                shebaoPackageUnit: shebao?.shebaoPackageUnit,
                shebaoPieceNum: shebao?.shebaoPieceNum,
                shebaoPieceUnit: shebao?.shebaoPieceUnit,
                shebaoPriceLimit: shebao?.specialPriceLimit,
                noList: traceableCodeList?.map((e) => {
                    return JsonMapper.deserialize(TraceNoListItem, {
                        no: e?.no,
                    });
                }),
            });
        });

        // 调用接口
        const traceCodeReq = JsonMapper.deserialize(TraceCodeReq, {
            list: traceCodeReqList,
            scene: TraceCodeScenesEnum.PHARMACY, // PHARMACY 场景
            shebaoHisType: userCenter.shebaoConfig?.basicInfo?.hospitalType ?? "1",
            shebaoRegion: userCenter.shebaoConfig?.region,
        });
        const result = await GoodsAgent.getCollectCodeCountList(traceCodeReq);
        if (result && !!result?.length) {
            result?.forEach((traceItem) => {
                const shebaoDismountingFlag = traceItem?.shebaoDismountingFlag,
                    traceListItem = traceItem?.list?.[0];
                goods._shebaoDismountingFlag = shebaoDismountingFlag;
                /**
                 * 医保拆零标志 shebaoDismountingFlag 二进制
                 * 第0位：是否允许拆零 0-不拆零 1-拆零 在10中0是第0位
                 * 第1位：是否允许编辑是否拆零 0-不允许 1-允许 在10中1是第1位
                 * 10->2 不拆零允许编辑; 11->3 拆零允许编辑;00->0 不拆零不允许编辑; 01->1 拆零不允许编辑
                 */
                if (hasEnableDismountingMode && shebaoDismountingFlag == ShebaoTraceableCodeDismountingFlagEnum.DISMOUNTING_NO_EDIT) {
                    goods._keywordTraceableCode = undefined;
                } else {
                    const isDismounting = TraceCode.isShouldApplyPieceUnit(traceListItem);
                    goods._keywordTraceableCode = goods._keywordTraceableCode?.map((trace) => {
                        return {
                            ...trace,
                            count: traceListItem ? (isDismounting ? traceListItem.hisPieceCount : traceListItem.hisPackageCount) : 1,
                            hisPackageCount: traceListItem?.hisPackageCount,
                            hisPieceCount: traceListItem?.hisPieceCount,
                            hisLeftTotalPieceCount: traceListItem?.hisLeftTotalPieceCount,
                        };
                    });
                }
            });
        }
        return goods;
    }
}
