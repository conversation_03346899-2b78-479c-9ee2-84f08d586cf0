import { fromJsonToDate, JsonProperty } from "../../../common-base-module/json-mapper/json-mapper";
import { ShebaoCardInfo } from "../shebao/bean";
import { DentistryMedicalRecordItem, ExamItems, ExtendDiagnosisInfosItem, EyeExamination, Patient } from "../beans";
import { ChargeInvoiceDetailData, ChargeSheetSummary, ChargeTransaction } from "../../../charge/data/charge-beans";

export enum HospitalRegisterDetailStatus {
    none = 0, //无状态/未登记
    inpatient = 1, //在院
    discharge = 2, //出院/已结算
    cancel = 3, //取消登记
}
export enum ThroatCutStatus {
    nocut = 0, //否
}

export enum HospitalSheetDetailStatus {}
export enum HospitalSheetDetailSettleStatus {}

export enum InpatientWayEnum {
    UNKNOWN = 0, // 未知
    BU_XING = 1, // 不行
    FU_ZHU = 2, // 扶助
    LUN_YI = 3, // 轮椅
    PING_CHE = 4, // 平车
}

export enum DiagnosisTreatmentTypeEnum {
    UN_KNOW = 0, // 未知
    MEDICINE = 1, // 药品
    INSPECTION = 10, // 检查
    ASSAY = 20, // 检验
    BLOOD_TRANSFUSION = 30, // 输血
    /**
     * 会诊，已废弃，会诊功能开发时，王晓东未使用预留的会诊类型，而是自己定义了一个 100 的 CONSULTATION 类型
     */
    DOCTORS_CONSULTATION = 40, // 会诊
    DIAGNOSIS = 50, // 诊断
    NURSE = 60, // 护理
    SURGERY = 70, // 手术
    TRANSFER_DEPARTMENT = 80, // 转科
    DISCHARGE_HOSPITAL = 90, // 出院
    CONSULTATION = 100, // 会诊
    MATERIALS = 110, // 物资
}

export enum AdvicesCreatedFromEnum {
    OrderPage = 0, // 医生站医嘱页面
    ConsultationPage = 1, // 医生站会诊页面
}

export enum AdviceRuleTypeEnum {
    medicineWestern = 0, // 西成药
    medicineChinesePiece = 10, // 中药饮片
    medicineChineseGranule = 20, // 中药颗粒
    examinationTest = 30, // 检查
    examination = 40, // 检验
    nurse = 50, // 护理
    nurseLeve = 51, // 护理等级
    treatment = 60, // 治疗
    physiotherapy = 70, // 理疗
    transfer = 80, // 转科
    hospitalDischarge = 90, // 出院
    hospitalTransfer = 91, // 转院
}

export enum AdviceAstFlagEnum {
    noSkin = 0, // 免试
    needSkin = 1, // 皮试
    continuedUse = 2, // 续用
}

export enum AdviceMedicalFeeGrade {
    empty = 0, // 空（未知）
    classA = 1, // 甲类
    classB = 2, // 乙类
    selPaid = 3, // 自费
}
// 医疗费用等级 0.空（未知） 1.甲类  2.乙类  3.自费

export enum DoctorMedicalPrescriptionTypeEnum {
    NORMAL = 0, // 正常医嘱
    WRITTEN = 1, // 手写嘱托
}

export enum AdvicesStatusEnum {
    ISSUE = 0, // 下达
    VERIFIED = 10, // 已核对
    EXECUTED = 20, // 已执行
    STOPPED = 30, // 已停止
    REVOKED = 90, // 已撤销
}

export class HospitalRegisterShebaoInfo {
    assessmentLevel?: string; // 评估等级
    doctorCode?: string; // 登记医师编号
    nurseCode?: string; // 登记护士编号
    inHospitalNo?: string; // 住院流水号
    patientNo?: string; // 人员编号
    userCategory?: string; // 人员类型
}

export class HospitalSheetDetail {
    id?: string;
    chainId?: string;
    clinicId?: string;
    patientId?: string;
    hospitalOrderId?: string;
    receivedPrice?: number;
    refundedPrice?: number;
    totalPrice?: number;
    settleStatus?: HospitalSheetDetailSettleStatus;
    status?: HospitalSheetDetailStatus;

    lastModifiedBy?: string;
    createdBy?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    lastModified?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;
}

export class HospitalRegisterDetail {
    id?: string;
    patientId?: string;
    registerDoctorId?: string;
    registerDoctorName?: string; //登记医生
    directDoctorId?: string;
    directDoctorName?: string; //责任医生
    registerNurseId?: string;
    registerNurseName?: string; //登记护士
    serviceType?: string; // 服务类型
    applyCategory?: string;
    chargeType?: string; //结算类型
    bedType?: string;
    bedInfo?: string;
    inpatientDays?: number; //在院天数
    amountTotal?: number; //累计费用
    isCutThroat?: ThroatCutStatus; //气管切割
    hospitalSheetId?: string;
    dischargeReason?: string; //在院状态
    inpatientWard?: string;

    status?: HospitalRegisterDetailStatus;

    @JsonProperty({ fromJson: fromJsonToDate })
    registerTime?: Date; //入院时间

    @JsonProperty({ fromJson: fromJsonToDate })
    dischargeTime?: Date; //出院时间

    @JsonProperty({ fromJson: fromJsonToDate })
    leaveTime?: Date;

    @JsonProperty({ type: ShebaoCardInfo })
    shebaoCardInfo?: ShebaoCardInfo;

    @JsonProperty({ type: Array, clazz: ExtendDiagnosisInfosItem })
    extendDiagnosisInfos?: ExtendDiagnosisInfosItem[];

    @JsonProperty({ type: Patient })
    patient?: Patient;

    @JsonProperty({ type: HospitalRegisterShebaoInfo })
    shebaoInfo?: HospitalRegisterShebaoInfo;

    @JsonProperty({ type: HospitalSheetDetail })
    hospitalSheet?: HospitalSheetDetail;

    get hasThroatCut(): boolean {
        return !!this.isCutThroat;
    }

    get statusDisplayName(): string {
        return this.status != HospitalRegisterDetailStatus.inpatient ? "出院" : "在院";
    }

    //是否是长护门诊
    get clinicTypeName(): string {
        return !!this?.id ? "长护门诊" : "普通门诊";
    }

    @JsonProperty({ name: "id" })
    __id?: string;
}

export class HospitalCumulativePreview {
    id?: string;
    chainId?: string;
    clinicId?: string;
    hospitalOrderId?: string;
    @JsonProperty({ type: ChargeSheetSummary })
    hospitalSheetSummary?: ChargeSheetSummary;
    hospitalTransactions?: ChargeTransaction[];
    @JsonProperty({ type: Array, clazz: ChargeInvoiceDetailData })
    rows?: ChargeInvoiceDetailData[];
    settleStatus?: number;
    settleStatusName?: string;
    status?: number;
    statusName?: string;
    total?: number;
    patientId?: string;
}

/**
 * 医嘱列表
 */
export enum AdvicesTypeEnum {
    temporary = 0, // 临时
    chronic = 10, // 长期
    carry = 20, // 带药
}
export class GoodsSnapshotVersionShebao {
    goodsId?: string;
    goodsType?: number;
    payMode?: number;
    isDummy?: number;
    medicineNum?: string;
    medicalFeeGrade?: number;
    nationalCode?: string;
    nationalCodeId?: string;
}
class SelfPayProp {
    key?: number;
}

export class DeviceInfo {
    clinicId?: string;
    deviceId?: number;
    shortId?: string;
    model?: string;
    name?: string;
    deviceModeId?: number;
    deviceUuid?: string;
    manufacture?: string;
    iconUrl?: string;
    inspectDeviceModel?: string;
    deviceType?: number;
    deviceTypeName?: string;
    usageType?: number;
    usageTypeName?: string;
    deviceStatus?: number;
    deviceStatusName?: string;
    innerFlag?: number;
}
export class GoodsSnapshotVersion {
    goodsVersion?: number; // goods基本信息的版本号
    disable?: number; // 门店是否停用
    piecePrice?: number; // 制剂价格 门店价格
    packagePrice?: number; // 整包价格 门店价格
    packageCostPrice?: number; // 成本价(有库存的商品，成本价存在stock中) 门店的最近成本价
    position?: string; // 门店的货架
    typeId?: number; // Goods类型Id
    customTypeId?: number; // 二级分类ID
    bizRelevantId?: string; // 护理 等级  0 特级 1
    shebao?: GoodsSnapshotVersionShebao; // 社保对码和Code
    selfPayProp?: SelfPayProp; // 自付比例
    deviceInfo?: DeviceInfo; // 检查检验项目项目 goods
    medicalFeeGrade?: number;
    subType?: number;
    priceType?: number;
    isPreciousDevice?: number;
    componentContentNum?: number;
    componentContentUnit?: string;
    medicineDosageNum?: number;
    medicineDosageUnit?: string;
    pieceNum?: number;
    pieceUnit?: string;
    packageUnit?: string;
    composeUseDismounting?: number;
    composeSort?: number;
    composeFractionPrice?: number;
    composeUnitCount?: number;
    feeCategoryId?: number;
    hospitalNeedExecutive?: number;
    cmspec?: string;
}
export class AdvicesAdviceGoodsItems {
    id?: number; // 主键ID
    chainId?: string; // 连锁ID
    clinicId?: string; // 门店ID
    wardAreaId?: number; // 病区ID
    departmentId?: string; // 科室ID
    patientOrderId?: string; // 住院单ID
    adviceId?: number; // 医嘱ID
    adviceRuleId?: number; // 医嘱规则ID
    goodsId?: string; // 商品ID
    goodsName?: string; // 商品名称
    goodsSpec?: string; // 商品规格
    unitCount?: number; // 中药医嘱：每剂处方的含量；非中药医嘱：每次执行医嘱任务的含量
    unit?: string; // 含量单位
    unitPrice?: number; // 单位价格
    goodsType?: number; // 商品类型
    goodsSubType?: number; // 商品子类型
    goodsTypeId?: number; // 商品类型ID
    type?: number; // 类型; 0:医嘱项目;1:医嘱项目关联项;2:用法关联项;3:皮试关联项；4:护士补充项;5:加工费
    primaryItemId?: number; // 关联的主项id
    primaryItemUnitCount?: number; // 关联的主项unitCount
    chargeFlag?: number; // 收费标识 0 正常收费 1：自备
    goodsSnapshotVersion?: GoodsSnapshotVersion; // 商品信息快照
    isDismounting?: number; // 是否使用拆零(包括价格、规格等)
    pharmacyNo?: number; // 药房号
    pharmacyType?: number; // 商品绑定的检查检验设备类型
    pharmacyInfo?: HospitalPatientOrderDetailAdvicesPharmacyInfo; // 药房快照信息
    goodsExamDeviceType?: number; // 商品绑定的检查检验设备类型
    supplementOperator?: string; // 补充项操作人
    supplementEffectiveStartTime?: string; // 护士补费用开始生效时间
    usageRuleType?: number; // 用法关联项的使用规则类型
    remark?: string; // 备注
    goodsExtendSpec?: string; // goods扩展规格:中药为用户输入的扩展规格;检查:0普通检查 10RIS检查 20眼科检查
    feeComposeType?: number; // 费用类型
    feeTypeId?: number; // 费用类型ID
    medicalInsurancePayType?: number; // 医保支付方式；0/空：不指定明确的方式，即有医保就用医保，无医保就自费；10：强制自费
    currGoodsItem?: string; // 当前商品实时信息，目前只有处方打印会返回该字段
}
export class HospitalPatientOrderDetailAdvicesPharmacyInfo {
    chainId?: string;
    clinicId?: string;
    no?: number;
    name?: string;
    type?: number;
    typeName?: string;
    stockCutType?: number;
    sort?: number;
    innerFlag?: number;
    dispenseFlag?: number;
    status?: number;
    isDeleted?: number;
    defaultGoodsTypeIdList?: number[]; // 当前药房是哪些类型的默认药房,12西药(代表中西成药 ) 14中药饮片 15 中药颗粒 2 材料 7 商品
    extendInfo?: [];
}
export class HospitalPatientOrderDetailAdvicesProcessInfo {
    usageType?: number;
    usageSubType?: string;
    displayName?: string;
    perDosageBagCount?: string;
    totalBagCount?: string;
    price?: string;
    remark?: string;
    feeTypeId?: string;
    feeCategoryId?: string;
}
export class SimpleExamSheet {
    clinicId?: string;
}
export class Tags {
    category?: number; // 标签分类{@link
    name?: string; // 名称{@link AdviceTagType}
    type?: number; // 类型{@link AdviceTagType}
}
export class Surgery {
    id?: number;
    name?: string; // 手术名称
    surgeryDate?: string; // 手术日期
}
export class AstResultOperators {
    employeeId?: string; // 用户ID
    employeeName?: string; // 用户姓名
    employeeHandSign?: string; // 用户手写签名
}
export class AstResult {
    operatorId?: string; // 操作人ID，需求改成皮试可
    operatorIds?: string[]; // 操作人Id集合
    operatorTime?: string; // 操作时间
    result?: string; // 结果
    description?: string; // 描述
    @JsonProperty({ clazz: AstResultOperators, type: Array })
    operators?: AstResultOperators[]; // 皮试操作人集合;
}
export class HospitalPatientOrderDetailAdvices {
    id?: string; // 说明Id
    chainId?: string; // 连锁ID
    clinicId?: string; // 门店ID
    wardAreaId?: string; // 病区ID
    departmentId?: string; // 科室ID
    patientId?: string; // 患者ID
    patientOrderId?: string; // 住院单ID
    type?: AdvicesTypeEnum; // 医嘱类型 （0 临时 10长期 20带药）
    diagnosisTreatmentType?: DiagnosisTreatmentTypeEnum; // 诊疗类型
    name?: string; // 名称
    status?: AdvicesStatusEnum; // 状态；0：下达；10：已核对；20：已执行；30：已停止；90：已撤销；
    usage?: string; // 用法
    ivgtt?: number; // 输液滴速
    freq?: string; // 频次
    freqInfo?: FreqInfo; // 频次信息快照
    days?: number; // 天数
    @JsonProperty({ fromJson: fromJsonToDate })
    startTime?: Date; // 开始时间
    @JsonProperty({ fromJson: fromJsonToDate })
    stopTime?: Date; // 停止时间
    createdBy?: string; // 开立医生ID
    createdByName?: string; // 开立医生名称
    createdByHandSign?: string; // 开立医生手写签名
    stopDoctorId?: string; // 停止医生id
    stopDoctorName?: string; // 停止医生名称
    @JsonProperty({ clazz: Tags, type: Array })
    tags?: Tags[]; // 医嘱标签
    groupId?: number; // 分组id；多个医嘱分为一组时，分组id使用最早的医嘱id
    groupSort?: number; // 分组内排序
    checkedOperateId?: number; // 核对操作id，一次核对多个医
    createdType?: DoctorMedicalPrescriptionTypeEnum; // 创建来源。0：医生站医嘱页面； 1：医生站会诊页面
    remark?: string; // 备注
    createdFrom?: AdvicesCreatedFromEnum; // 创建来源。0：医生站医嘱页面； 1：医生站会诊页面
    adviceRuleType?: AdviceRuleTypeEnum; // 类型 (0:西成药; 10:中药饮片; 20:中药颗粒; 30:检查; 40:检验; 50:护理; 51:护理等级; 60:治疗; 70:理疗; 80:转科; 90:出院; 91:转院;)
    astFlag?: AdviceAstFlagEnum; // 皮试标识；0：免试;1：需要; 2:续用;
    astResult?: AstResult; // 皮试结果
    singleDosageCount?: string; // 单次用量。对于中药类型的比
    singleDosageUnit?: string; // 单次用量单位
    dosageCount?: number; // 总量
    dosageUnit?: string; // 总量单位
    pharmacyNo?: number; // 药房号
    pharmacyType?: number; // 药房类型
    pharmacyInfo?: HospitalPatientOrderDetailAdvicesPharmacyInfo; // 药房快照信息
    processInfo?: HospitalPatientOrderDetailAdvicesProcessInfo; // 代加工信息
    treatmentSites?: string[]; // 治疗穴位、部位
    chargeFlag?: number; // 是否自备标识;0:非自备;1:自备
    dischargeHospitalReason?: string; // 出院原因
    isGoodsDefaultPharmacy?: number; // 是否医嘱项目的默认药房
    westernPrimaryItemSpec?: string; // 西药主项规格
    medicalFeeGrade?: AdviceMedicalFeeGrade; // 医疗费用等级 0.空（未知） 1.甲类  2.乙类  3.自费
    examApplySheetPurpose?: string; // 检查、检验申请单目的
    examApplySheet?: string; // 检查检验简要信息
    simpleExamSheet?: SimpleExamSheet; // 检查检验简要信息
    isNeedRefundDispensing?: number;
    isNeedHospitalExecute?: number;
    @JsonProperty({ clazz: AdvicesAdviceGoodsItems, type: Array })
    adviceGoodsItems?: AdvicesAdviceGoodsItems[]; // type=AdviceRuleItemType.ADVICE_GOODS items
    surgery?: Surgery; // 手术单信息
}

export class FreqInfoIntervalTime {
    month?: number;
    week?: number;
    day?: number;
}
export class FreqInfoDailyTimings {
    weekday?: number; // 指定周几。从1-7 表示每周
    timings?: string[]; // 每天的时间点
}
export class FreqInfo {
    description?: string; // 描述信息
    firstDayFrequency?: number; // 首日次数
    firstDayTimings?: string[]; // 首日的时间点
    intervalTime?: FreqInfoIntervalTime; // 间隔时间。像Qnd(n天1次)
    @JsonProperty({ clazz: FreqInfoDailyTimings, type: Array })
    dailyTimings?: FreqInfoDailyTimings[]; // 时间点
    startExecuteTiming?: string; // nid开始执行时间
    endExecuteTiming?: string; // nid结束执行时间
}

export class HospitalPatientOrderDetail {
    id?: number;
    chainId?: string; // 连锁ID
    clinicId?: string; // 门店ID
    wardAreaId?: number; // 病区ID
    departmentId?: string; // 科室ID
    patientId?: string; // 患者ID
    patientOrderId?: string; // 住院单ID
    type?: AdvicesTypeEnum; // 类型；0：临时； 10：长期； 20：出院带药；
    diagnosisTreatmentType?: DiagnosisTreatmentTypeEnum; // 诊疗类型;1:药品;10:检查;20:检验;30:输血;40:会诊;50:诊断;60:护理;70:手术;80:转科;90:出院;100:会诊;110:物资
    status?: AdvicesStatusEnum; // 状态；0：下达；10：已核对；20：已执行；30：已停止；90：已撤销；
    usage?: string; // 用法
    ivgtt?: number; // 输液滴速
    freq?: string; // 频次
    freqInfo?: FreqInfo; // 频次信息快照
    days?: number; // 天数
    @JsonProperty({ fromJson: fromJsonToDate })
    startTime?: Date; // 开始时间
    @JsonProperty({ fromJson: fromJsonToDate })
    stopTime?: Date; // 停止时间
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date; // 下达时间
    createdBy?: string; // 开立医生Id
    @JsonProperty({ clazz: Tags, type: Array })
    tags?: Tags[]; // 医嘱标签信息
    createdByName?: string; // 开立医生名称
    createdByHandSign?: string; // 开立医生手写签名
    stopDoctorId?: string; // 停止医生Id
    stopDoctorName?: string; // 停止医生名称
    stopDoctorHandSign?: string; // 停止医生手写签名
    checkedOperatorId?: string; // 核对人Id
    checkedOperatorName?: string; // 核对人名称
    checkedOperatorHandSign?: string; // 核对人手写签名
    @JsonProperty({ fromJson: fromJsonToDate })
    checkedTime?: Date; // 核对时间
    @JsonProperty({ clazz: HospitalPatientOrderDetailAdvices, type: Array })
    advices?: HospitalPatientOrderDetailAdvices[]; // 医嘱集合
    stopConfirmOperatorId?: string; // 医嘱停止确认人id
    stopConfirmOperatorName?: string; // 医嘱停止确认人名称
    stopConfirmOperatorHandSign?: string; // 核对人手写签名
    executeOperatorId?: string; // 医嘱执行人人id
    executeOperatorName?: string; // 医嘱执行人名称
    executeOperatorHandSign?: string; // 执行人手写签名
    @JsonProperty({ fromJson: fromJsonToDate })
    executeTime?: Date; // 执行时间

    // 医嘱-类型
    get _advicesTypeStr(): string {
        // 0 临时 10长期 20带药
        let str = "";
        switch (this.type) {
            case AdvicesTypeEnum.temporary:
                str = "临时";
                break;
            case AdvicesTypeEnum.chronic:
                str = "长期";
                break;
            case AdvicesTypeEnum.carry:
                str = "带药";
        }
        return str;
    }
}

/**
 * 诊断列表
 */
export class TcmSyndromeType {
    code?: string;
    name?: string;
}
export enum HospitalDoctorDiagnosisTypeEnum {
    PRELIMINARY = 0, // 初步诊断
    ADMISSION = 10, // 入院诊断
    DISCHARGE = 20, // 出院诊断
}
export class HospitalDoctorDiagnosisDetail {
    id?: string;
    patientOrderId?: string;
    outpatientOrderId?: string;
    departmentId?: string;
    doctorId?: string;
    doctorName?: string;
    patientId?: string;
    type?: HospitalDoctorDiagnosisTypeEnum; // 诊断类型(0:初步诊断 10:入院诊断 20:出院诊断)
    category?: number;
    diseaseName?: string;
    diseaseCode?: string;
    diseaseCategory?: string;
    isPrimary?: number; // 0:普通 10:主诊
    diagnosedStatus?: number;
    sickTime?: string;
    diagnosedTime?: string;
    typeName?: string;
    sort?: number;
    @JsonProperty({ clazz: TcmSyndromeType, type: Array })
    tcmSyndromeType?: TcmSyndromeType[];
}
export class HospitalDoctorDiagnosisRsp {
    @JsonProperty({ clazz: HospitalDoctorDiagnosisDetail, type: Array })
    data?: HospitalDoctorDiagnosisDetail[];
}

/**
 * 住院信息
 */
export class PreDiagnosisInfosValue {
    code?: string;
    diseaseType?: string;
    hint?: string;
    name?: string;
}
export class PreDiagnosisInfos {
    toothNos?: number[];
    @JsonProperty({ clazz: PreDiagnosisInfosValue, type: Array })
    value?: PreDiagnosisInfosValue[];
}
export class PrimaryDiagnosisInfosValue {
    code?: string;
    name?: string;
    diseaseType?: string;
    hint?: string;
}

export class PrimaryDiagnosisInfos {
    toothNos?: number[];
    @JsonProperty({ clazz: PrimaryDiagnosisInfosValue, type: Array })
    value?: PrimaryDiagnosisInfosValue[];
}

class DischargeDiagnosisInfos extends PrimaryDiagnosisInfos {}

export class HospitalizationInformationTags {
    id?: string;
    name?: string;
}

export class DepartmentTransferRecord {
    departmentId?: string;
    departmentName?: string;
    escortEmployeeId?: string;
    escortEmployeeName?: string;
    escortWay?: number;
    executorId?: string;
    executorName?: string;
    id?: string;
    isEscort?: number;
    remark?: string;
    status?: number;
    @JsonProperty({ fromJson: fromJsonToDate })
    transferTime?: Date;
    wardId?: string;
    wardName?: string;
}
export class HospitalizationInformationDetail {
    id?: string; // 住院状态 0 未登记 1 入院中 2 门诊出院-待结算 3 撤销 4 出院-结算完成
    chainId?: string;
    clinicId?: string;
    patient?: Patient;
    no?: string;
    status?: number;
    departmentId?: string;
    departmentName?: string;
    inDepartmentId?: string;
    inDepartmentName?: string;
    wardId?: string;
    wardName?: string;
    bedId?: string;
    bedNo?: string;
    adviceDeposit?: string;
    feeTypeName?: string;
    inpatientWay?: number;
    inpatientSource?: string;
    inpatientCondition?: string;
    outpatientDoctorId?: string;
    outpatientDoctorName?: string;
    outpatientDoctorHandSign?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    firstInDiagnosedTime?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    inpatientTimeRequest?: Date;
    @JsonProperty({ clazz: PreDiagnosisInfos, type: Array })
    preDiagnosisInfos?: PreDiagnosisInfos[];
    @JsonProperty({ clazz: DischargeDiagnosisInfos, type: Array })
    dischargeDiagnosisInfos?: DischargeDiagnosisInfos[];
    @JsonProperty({ clazz: PrimaryDiagnosisInfos, type: Array })
    primaryDiagnosisInfos?: PrimaryDiagnosisInfos[];
    doctorName?: string;
    doctorHandSign?: string;
    nurseId?: string;
    nurseName?: string;
    nurseHandSign?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    inpatientTime?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    dischargeTime?: Date;
    dischargeReason?: string;
    inpatientDays?: string;
    times?: string;
    timesOfYear?: string;
    inpatientYear?: string;
    totalPrice?: string;
    depositAvailableFee?: string;
    @JsonProperty({ clazz: HospitalizationInformationTags, type: Array })
    tags?: HospitalizationInformationTags[];
    departmentTransferRecord?: DepartmentTransferRecord;
    nurseLevel?: string;
    shebaoCardInfo?: ShebaoCardInfo;
    bedRoomName?: string;
    dischargeMethod?: string;

    get inpatientWayNameDisplay(): string {
        let str = "";
        switch (this.inpatientWay) {
            case InpatientWayEnum.UNKNOWN:
                str = "未知";
                break;
            case InpatientWayEnum.BU_XING:
                str = "步行";
                break;
            case InpatientWayEnum.FU_ZHU:
                str = "扶助";
                break;
            case InpatientWayEnum.LUN_YI:
                str = "轮椅";
                break;
            case InpatientWayEnum.PING_CHE:
                str = "平车";
                break;
        }

        return str;
    }

    formatToothNos2Text(toothNos: number[]): string {
        if (!toothNos) return "";
        return toothNos
            .slice()
            .sort((a, b) => a - b)
            .join("、");
    }

    formatDentistry2Text(arr: DischargeDiagnosisInfos[]): string {
        if (!arr) return "";
        const result: string[] = [];
        arr?.forEach((item) => {
            if (item.value) {
                let str = "";
                if (item.toothNos && item.toothNos.length) {
                    str += `${this.formatToothNos2Text(item.toothNos)} `;
                }
                if (Array.isArray(item.value)) {
                    str += item.value.map((it) => it.name).join("，");
                } else {
                    str += item.value;
                }
                result.push(str);
            }
        });
        return result.join("，");
    }
}
/**
 * 获取门诊摘要信息
 */
export class OutpatientControllerMedicalRecordInfos {
    code?: string;
    name?: string;
    diseaseType?: string;
    hint?: string;
}
export class OutpatientControllerMedicalRecordAttachments {
    fileName?: string;
    fileSize?: string;
    id?: string;
    sort?: number;
    url?: string;
}

export class OutpatientControllerMedicalRecord {
    id?: string;
    patientOrderId?: string;
    outpatientSheetId?: string;
    patientId?: string;
    clinicId?: string;
    chainId?: string;
    departmentId?: string;
    doctorId?: string;
    type?: number;
    chiefComplaint?: string;
    pastHistory?: string;
    allergicHistory?: string;
    familyHistory?: string;
    personalHistory?: string;
    presentHistory?: string;
    physicalExamination?: string;
    doctorAdvice?: string;
    syndrome?: string;
    syndromeTreatment?: string;
    therapy?: string;
    chineseExamination?: string;
    birthHistory?: string;
    oralExamination?: string;
    epidemiologicalHistory?: string;
    obstetricalHistory?: string;
    auxiliaryExamination?: string;
    @JsonProperty({ clazz: DentistryMedicalRecordItem, type: Array })
    auxiliaryExaminations?: DentistryMedicalRecordItem[];
    chinesePrescription?: string;
    diagnosis?: string;
    @JsonProperty({ clazz: OutpatientControllerMedicalRecordInfos, type: Array })
    diagnosisInfos?: OutpatientControllerMedicalRecordInfos[];
    @JsonProperty({ clazz: OutpatientControllerMedicalRecordInfos, type: Array })
    extendDiagnosisInfos?: OutpatientControllerMedicalRecordInfos[];
    @JsonProperty({ clazz: OutpatientControllerMedicalRecordAttachments, type: Array })
    attachments?: OutpatientControllerMedicalRecordAttachments[];
    @JsonProperty({ clazz: DentistryMedicalRecordItem, type: Array })
    dentistryExaminations?: DentistryMedicalRecordItem;
    @JsonProperty({ clazz: DentistryMedicalRecordItem, type: Array })
    treatmentPlans?: DentistryMedicalRecordItem[];
    @JsonProperty({ clazz: DentistryMedicalRecordItem, type: Array })
    disposals?: DentistryMedicalRecordItem[];
    @JsonProperty({ clazz: ExamItems, type: Array })
    examItems?: ExamItems[];
    wearGlassesHistory?: string;
    eyeExamination?: EyeExamination;
    target?: string;
    prognosis?: string;
    symptomTime?: string;
}
export class OutpatientControllerInformationDetail {
    id?: string;
    patientOrderId?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    diagnosedDate?: Date;
    doctorId?: string;
    doctorName?: string;
    departmentId?: string;
    departmentName?: string;
    clinicId?: string;
    chainId?: string;
    isCopyWrite?: number;
    status?: number;
    medicalRecord?: OutpatientControllerMedicalRecord;
}
