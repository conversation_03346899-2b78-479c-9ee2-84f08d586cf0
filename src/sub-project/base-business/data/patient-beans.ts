/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/6/17
 *
 * @description
 */
import { HistoryPermissionModuleType } from "./beans";

export class PatientInfoMethod {
    /**
     * 对手机号进行加密处理
     * 11位手机号，隐藏中间四位，如：138****1234
     * 小于等于10位的，从第二位开始隐藏，隐藏3位，如：138****234
     * @param params
     */
    static encryptThePhoneNumber(mobile?: string): string {
        if (!mobile) return "";
        const mobileLength = mobile.length;
        const start = mobile.substring(0, mobileLength <= 10 ? 1 : 3);
        const end = mobile.substring(mobileLength <= 10 ? 4 : mobileLength - 4, mobileLength);
        const middle = mobile.substring(mobileLength <= 10 ? 1 : 3, mobileLength <= 10 ? 4 : mobileLength - 4).replace(/\d/g, "*");
        return start + middle + end;
    }

    /**
     * 不同模块下是否能显示患者手机号
     * @param params
     */
    static canSeePatientMobile(params: {
        type?: HistoryPermissionModuleType;
        canSeePatientMobileInRegister?: boolean;
        canSeePatientMobileInOutpatient?: boolean;
        canSeePatientMobileInCashier?: boolean;
        canSeePatientMobileInPharmacy?: boolean;
        canSeePatientMobileInExecution?: boolean;
    }): boolean {
        const {
            type,
            canSeePatientMobileInRegister = true,
            canSeePatientMobileInOutpatient = true,
            canSeePatientMobileInCashier = true,
            canSeePatientMobileInPharmacy = true,
            canSeePatientMobileInExecution = true,
        } = params;
        if (!type) return true;
        let isShowFullMobile = true;
        switch (type) {
            case HistoryPermissionModuleType.pharmacy:
                isShowFullMobile = canSeePatientMobileInPharmacy;
                break;
            case HistoryPermissionModuleType.outpatient:
                isShowFullMobile = canSeePatientMobileInOutpatient;
                break;
            case HistoryPermissionModuleType.cashier:
                isShowFullMobile = canSeePatientMobileInCashier;
                break;
            case HistoryPermissionModuleType.registration:
                isShowFullMobile = canSeePatientMobileInRegister;
                break;
            case HistoryPermissionModuleType.execution:
                isShowFullMobile = canSeePatientMobileInExecution;
                break;
            default:
                break;
        }
        return isShowFullMobile;
    }
}
