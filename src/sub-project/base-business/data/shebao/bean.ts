import { fromJsonToDateTime, JsonProperty } from "../../../common-base-module/json-mapper/json-mapper";

export class ShebaoCardInfoExtend {}

export class ShebaoCardInfo {
    name?: string;
    sex?: string;
    mobile?: string;
    birthday?: string;
    idCardNo?: string;
    cardNo?: string;
    group?: any;
    feeType?: string;
    company?: string;
    personalCode?: string;
    isJoinedCommercialInsurance?: number;
    @JsonProperty({ type: ShebaoCardInfoExtend })
    extend?: ShebaoCardInfoExtend;
    @JsonProperty({ fromJson: fromJsonToDateTime })
    lastModified?: Date;
}
