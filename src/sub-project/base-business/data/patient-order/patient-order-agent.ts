/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2022/9/6
 * @Copyright 成都字节流科技有限公司© 2022
 */

import {
    PatientOrderBusinessScene,
    PatientOrderLockDetail,
    PatientOrderLockType,
    PatientOrdersInfo,
    PatientOrderValue,
} from "./patient-order-bean";
import { ABCApiNetwork } from "../../../net";
import { JsonMapper } from "../../../common-base-module/json-mapper/json-mapper";

export class PatientOrderAgent {
    /**
     * 获取锁单详情
     * @param orderId
     * @param options
     */
    static async getPatientOrderLockDetail(
        orderId: string,
        options: {
            businessKey: PatientOrderLockType;
        }
    ): Promise<PatientOrderLockDetail> {
        return ABCApiNetwork.get(`patientorders/${orderId}/lock`, { queryParameters: options, clazz: PatientOrderLockDetail });
    }

    /**
     * 添加详情锁
     * @param orderId
     * @param options
     */
    static async postPatientOrderLock(
        orderId: string,
        options: {
            businessKey: PatientOrderLockType;
            businessScene?: PatientOrderBusinessScene; // 不传的话，默认为0
        }
    ): Promise<PatientOrderLockDetail> {
        const finalOptions = {
            ...options,
            businessScene: options?.businessScene ?? PatientOrderBusinessScene.OUTPATIENT_LOCK_CHARGE_SHEET,
        };
        return ABCApiNetwork.post(`patientorders/${orderId}/lock`, { queryParameters: finalOptions, clazz: PatientOrderLockDetail });
    }

    /**
     * 业务锁续期
     * @param orderId
     * @param options
     */
    static async putPatientOrderLockRenew(
        orderId: string,
        options: {
            businessKey: PatientOrderLockType;
            identity: string;
            businessLockValue?: PatientOrderValue;
        }
    ): Promise<PatientOrderLockDetail> {
        return ABCApiNetwork.put(`patientorders/${orderId}/lock/renew`, {
            queryParameters: {
                businessKey: options.businessKey,
                identity: options.identity,
            },
            body: options.businessLockValue,
            clazz: PatientOrderLockDetail,
        });
    }

    /**
     * 业务锁解锁
     * @param orderId
     * @param options
     */
    static async postPatientOrderLockUnlock(
        orderId: string,
        options: {
            businessKey: PatientOrderLockType;
            identity: string;
            businessLockValue?: PatientOrderValue;
        }
    ): Promise<PatientOrderLockDetail> {
        return ABCApiNetwork.post(`patientorders/${orderId}/unlock`, {
            queryParameters: {
                businessKey: options.businessKey,
                identity: options.identity,
            },
            body: options.businessLockValue,
            clazz: PatientOrderLockDetail,
        });
    }

    /**
     * 根据就诊单号查询所有就诊单
     */
    static async getPatientOrdersByPatientOrderNo(options: { no: string; clinicId: string }): Promise<PatientOrdersInfo> {
        return ABCApiNetwork.get(`patientorders/by-patientorder-no`, {
            queryParameters: options,
            clazz: PatientOrdersInfo,
        });
    }

    /**
     * 查询就诊单锁单列表（门诊、药房需要调用，执行和收费会在详情单上返回，所以不需要调用）
     * @param patientOrderId
     * @param businessKeys
     */
    static async getPatientOrdersListLocks(
        patientOrderId: string,
        businessKeys: PatientOrderLockType[]
    ): Promise<PatientOrderLockDetail[]> {
        const rsp: { rows: PatientOrderLockDetail[] } = await ABCApiNetwork.post(`patientorders/${patientOrderId}/list-locks`, {
            body: { businessKeys },
        });
        return rsp?.rows?.map((item) => JsonMapper.deserialize(PatientOrderLockDetail, { ...item })) ?? [];
    }
}
