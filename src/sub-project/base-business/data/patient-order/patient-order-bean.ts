/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2022/9/6
 * @Copyright 成都字节流科技有限公司© 2022
 */

import { fromJsonToDate, JsonProperty } from "../../../common-base-module/json-mapper/json-mapper";

export enum PatientOrderLockType {
    outpatientSheet = "outpatient.sheet",
    chargeSheet = "charge.sheet", // 收费锁（包含门诊医生编辑医嘱、收费支付锁、退费锁）
    // 下面两个是自定义的，主要是用来门诊锁单用set判断收费锁里面是收费支付锁、退费锁
    chargeSheetPay = "chargeSheet.pay",
    chargeSheetRefund = "chargeSheet.refund",
}

export class PatientOrderLockBaseInfo {
    businessKey?: PatientOrderLockType; //业务key
    expire?: number; //超时时间
    identity?: string; //锁识别
    key?: string;
    result?: number; //操作结果code 0 操作失败 1操作成功
}
export enum PatientOrderBusinessScene {
    OUTPATIENT_LOCK_CHARGE_SHEET = 0, // 门诊锁收费单
    CHARGE_SHEET_PAY = 1, // 收费锁收费单
    CHARGE_SHEET_REFUND = 2, // 退费锁收费单
}
export class PatientOrderBusinessDetail {
    addedLockStatus?: number;
    chargePayTransactionId?: string;
    chargeSheetId?: string;
}
export class PatientOrderValue {
    @JsonProperty({ type: PatientOrderBusinessDetail })
    businessDetail?: PatientOrderBusinessDetail; // 只有收费支付锁才会有这个字段信息
    businessScene?: PatientOrderBusinessScene; // 用来区分当前锁单类别(目前功能只有收费需要区分，门诊不用)
    employeeId?: string;
    id?: string;
    //  门诊编辑处方锁单
    get doctorEditPrescription(): boolean {
        return this.businessScene == PatientOrderBusinessScene.OUTPATIENT_LOCK_CHARGE_SHEET;
    }
    // 收费支付锁单
    get chargeInProgress(): boolean {
        return this.businessScene == PatientOrderBusinessScene.CHARGE_SHEET_PAY;
    }
    //     退费锁收费单
    get chargeRefundOrder(): boolean {
        return this.businessScene == PatientOrderBusinessScene.CHARGE_SHEET_REFUND;
    }
}
export class PatientOrderLockDetail extends PatientOrderLockBaseInfo {
    employeeId?: string; //当前持锁人姓名
    employeeName?: string; //当前持锁人姓名
    status?: number; //当前锁状态 0 未锁 1 已锁
    @JsonProperty({ type: PatientOrderValue })
    value?: PatientOrderValue;
}

export class PatientOrdersInfo {
    id?: string;
    chainId?: string;
    clinicId?: string;
    patientId?: string;
    patientName?: string;
    patientSex?: string;
    patientMobile?: string;
    patientNamePy?: string;
    patientNamePyFirst?: string;
    patientBirthday?: string;
    isMember?: number;
    patientAge?: {
        year?: number;
        month?: number;
        day?: number;
    };
    no?: string;
    source?: number;
    sourceClientType?: number;
    @JsonProperty({ fromJson: fromJsonToDate })
    reserveTime?: Date;
    visitSourceFrom?: string;
    visitSourceRemark?: string;
    revisitStatus?: number;
    shebaoCardInfo?: string;
    hospitalPatientOrderId?: string;
    importFlag?: number;
    shebaoChargeType?: number;
    referralFlag?: number;
    type?: number;
    isDeleted?: number;
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;
    createdBy?: string;
    visitSourceName?: string;
    visitSourceFromName?: string;
    visitSourceRelatedType?: number;
    referralPatientOrder?: string;
    visitSourceParentId?: string;
    visitSourceParentName?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    ageLockTime?: Date;
    visitSourceId?: string;
}
