import { JsonMapper, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { LogUtils } from "../../common-base-module/log";
import _ from "lodash";

/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/5/22
 *
 * @description
 */

type ComponentType = "Checkbox" | "Radio" | "ImageCheckbox" | "ImageRadio" | "Input";

const typeNameMaps = new Map<string, string>([
    ["Checkbox", "多选"],
    ["Radio", "单选"],
    ["ImageRadio", "单选"],
    ["ImageCheckbox", "多选"],
    ["Input", "输入"],
]);

class CheckOption {
    _id?: string;
    name?: string;
    value?: string | number;
}

export class Component {
    _id!: string;
    kind!: ComponentType;
    key!: string;
    required!: boolean;
    isPop!: boolean;
    label!: string;

    get labelWithType(): string {
        let label = this.label;
        const kindValue = typeNameMaps.get(this.kind);
        if (!_.isEmpty(kindValue)) {
            label += `(${kindValue})`;
        }

        return label;
    }
}

export class FormCheckBox extends Component {
    options!: CheckOption[];
}

export class FormInput extends Component {}

export class FormRadio extends Component {}

export class FormImageCheckbox extends Component {}

export class FormImageRadio extends Component {}

export interface ComponentValue {
    kind: ComponentType;
    componentId: string;
    formatValue: string;
    score: number;
}

const creatorMap = {
    Checkbox: FormCheckBox,
    Radio: FormRadio,
    Input: FormInput,
    ImageCheckbox: FormCheckBox,
    ImageRadio: FormImageRadio,
};

function parseComponentFromJson(json: any): Component | null {
    const component = json as Component;
    const clz = creatorMap[component.kind!];
    if (clz) {
        return JsonMapper.deserialize(clz, json);
    }

    return null;
}

function parseComponentsFromJson(json: any[]): Component[] {
    if (!json) return [];
    const components: Component[] = [];
    json.forEach((item) => {
        const component = parseComponentFromJson(item);
        if (component) {
            components.push(component);
        } else {
            LogUtils.e("parseComponentsFromJson unknown form type " + item.kind);
        }
    });

    return components;
}

export class FormDetail {
    formType?: number; // 1: 简单表单；2: 嵌套表单-主表； 3:嵌套表单-子表
    version?: number; //版本号
    _id?: string; // 表单ID
    name?: string;
    formId?: string;

    @JsonProperty({ fromJson: parseComponentsFromJson })
    components!: Component[];
}

export interface FromValues {
    [key: string]: ComponentValue;
}

export class FormDataDetail {
    _id?: string;
    patientId?: string;
    formId?: string;
    version?: number;
    values?: FromValues;
    pops?: any;
}
