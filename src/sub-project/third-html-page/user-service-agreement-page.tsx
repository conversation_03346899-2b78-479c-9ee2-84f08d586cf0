import React from "react";
import { ScrollView } from "@hippy/react";
import { BasePage } from "../base-ui";
import { Colors, Sizes, TextStyles } from "../theme";
import { WordUtils } from "./word-utils";
import { serviceAgreementPageContent } from "./service-page";

/**
 * desc:用户服务协议页面
 */

export class UserServiceAgreementPage extends BasePage {
    getAppBarTitle(): string {
        return "用户服务协议";
    }

    renderContent(): JSX.Element {
        return (
            <ScrollView style={{ flexGrow: 1, backgroundColor: Colors.white, paddingHorizontal: Sizes.dp16, paddingVertical: Sizes.dp16 }}>
                {WordUtils.createTitleView("ABC医疗云用户服务协议", [TextStyles.t18MT1, { alignSelf: "center", marginBottom: Sizes.dp32 }])}
                {WordUtils.createPageContentView(serviceAgreementPageContent)}
            </ScrollView>
        );
    }
}
