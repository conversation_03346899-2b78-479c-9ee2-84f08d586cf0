/**
 * create by <PERSON><PERSON><PERSON>
 * desc:
 * create date 2022/3/15
 */
import React from "react";
import { Style, View } from "@hippy/react";
import { AbcText } from "../base-ui/views/abc-text";
import { flattenStyles, Sizes, TextStyles } from "../theme";

export class WordUtils {
    static bottomSpacing = Sizes.dp10;
    static createTitleView(text: string, style?: Style | Style[]): JSX.Element {
        return (
            <AbcText
                style={[
                    TextStyles.t16MT1.copyWith({ lineHeight: Sizes.dp24 }),
                    flattenStyles(style),
                    { marginBottom: WordUtils.bottomSpacing },
                ]}
            >
                {text}
            </AbcText>
        );
    }

    static createSubTitleView(text: string, style?: Style | Style[]): JSX.Element {
        return (
            <AbcText
                style={[
                    TextStyles.t14MT1.copyWith({ lineHeight: Sizes.dp22 }),
                    flattenStyles(style),
                    { marginBottom: WordUtils.bottomSpacing },
                ]}
            >
                {text}
            </AbcText>
        );
    }

    static createContentView(text: string, style?: Style | Style[]): JSX.Element {
        return (
            <AbcText
                style={[
                    TextStyles.t14NT1.copyWith({ lineHeight: Sizes.dp22 }),
                    flattenStyles(style),
                    { marginBottom: WordUtils.bottomSpacing },
                ]}
            >
                {text}
            </AbcText>
        );
    }

    static createPageContentView(page: { type: string; content: string }[]): JSX.Element {
        const view: JSX.Element[] = [];
        page.forEach((item) => {
            switch (item.type) {
                case "title": {
                    view.push(this.createTitleView(item.content));
                    break;
                }
                case "subTitle": {
                    view.push(this.createSubTitleView(item.content));
                    break;
                }
                case "content": {
                    view.push(this.createContentView(item.content));
                    break;
                }
            }
        });
        return <View>{view}</View>;
    }
}
