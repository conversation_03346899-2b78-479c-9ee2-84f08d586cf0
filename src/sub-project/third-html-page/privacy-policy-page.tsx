import React from "react";
import { ScrollView } from "@hippy/react";
import { BasePage } from "../base-ui";
import { Colors, Sizes, TextStyles } from "../theme";
import { WordUtils } from "./word-utils";
import { privacyProtectionPageContent } from "./service-page";

/**
 * desc:隐私保护政策页面
 */

export class PrivacyPolicyPage extends BasePage {
    getAppBarTitle(): string {
        return "隐私保护政策";
    }

    renderContent(): JSX.Element {
        return (
            <ScrollView style={{ flexGrow: 1, backgroundColor: Colors.white, paddingHorizontal: Sizes.dp16, paddingVertical: Sizes.dp16 }}>
                {WordUtils.createTitleView("ABC医疗云隐私保护政策", [TextStyles.t18MT1, { alignSelf: "center", marginBottom: Sizes.dp32 }])}
                {WordUtils.createSubTitleView("版本：V1.0", [{ alignSelf: "center", marginBottom: Sizes.dp32 }])}
                {WordUtils.createSubTitleView("发布日期：2019年11月26日", [{ alignSelf: "center", marginBottom: Sizes.dp32 }])}

                {WordUtils.createPageContentView(privacyProtectionPageContent)}
            </ScrollView>
        );
    }
}
