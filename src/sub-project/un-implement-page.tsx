/**
 * create by dengjie
 * desc:
 * create date 2020/8/18
 */
import React from "react";
import { Text, View } from "@hippy/react";
import { BasePage, SizedBox } from "./base-ui";
import { AssetImageView } from "./base-ui/views/asset-image-view";
import { Colors, Sizes, TextStyles } from "./theme";

interface UnImplementPageProps {
    title: string;
}

export class UnImplementPage extends BasePage<UnImplementPageProps> {
    constructor(props: UnImplementPageProps) {
        super(props);
    }

    getAppBarTitle(): string {
        return this.props.title;
    }

    renderContent(): JSX.Element | undefined {
        return (
            <View style={{ flex: 1, backgroundColor: Colors.white }}>
                <SizedBox height={Sizes.dp80} />
                <View style={{ alignItems: "center" }}>
                    <AssetImageView
                        name="unimlement"
                        style={{
                            width: Sizes.dp80,
                            height: Sizes.dp80,
                        }}
                    />
                </View>
                <SizedBox height={Sizes.dp16} />
                <Text style={[TextStyles.t14NT3, { textAlign: "center" }]}>功能即将上线，敬请期待！</Text>
            </View>
        );
    }
}
