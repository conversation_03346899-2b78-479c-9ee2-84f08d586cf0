/**
 * create by deng<PERSON>e
 * desc: 消息中心
 * create date 2020/7/21
 */
import React from "react";
import { ScrollView, Text, View } from "@hippy/react";
import { BasePage, SizedBox } from "../base-ui";
import { BaseComponent } from "../base-ui/base-component";
import { ABCStyles, Color, Colors, Sizes, TextStyles } from "../theme";
import { AssetImageView } from "../base-ui/views/asset-image-view";
import { Badge } from "../base-ui/badge/badge";
import { TimeUtils } from "../common-base-module/utils";
import { GetLatestMsgSummaryRsp, MsgCategory, MsgType, NotificationDataManager } from "../data/notification";
import { sharedPreferences } from "../base-business/preferences/shared-preferences";
import { AbcView } from "../base-ui/views/abc-view";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { NotificationListPage } from "./notification-list-page";
import { userCenter } from "../user-center";
import { getDistributeConfig } from "../views-distribute/utils";

interface NotificationPageProps {}

export class NotificationPage extends BasePage<NotificationPageProps> {
    msgSummary?: GetLatestMsgSummaryRsp;

    constructor(props: NotificationPageProps) {
        super(props);
    }

    pageName(): string | undefined {
        return "NotificationPage";
    }

    getAppBarBgColor(): Color {
        return getDistributeConfig().viewFeature.homeV2 ? Colors.retail_theme_topbar : Colors.white;
    }

    getStatusBarColor(): Color {
        return getDistributeConfig().viewFeature.homeV2 ? Colors.retail_theme_topbar : Colors.white;
    }

    getAppBarTitleColor(): Color {
        return getDistributeConfig().viewFeature.homeV2 ? Colors.white : Colors.black;
    }

    getBackgroundColor(): Color {
        return getDistributeConfig().viewFeature.homeV2 ? Colors.white : Colors.window_bg;
    }

    componentDidMount(): void {
        super.componentDidMount();
        this.msgSummary = NotificationDataManager.sObserver.value;
        sharedPreferences.setInt(NotificationDataManager.PREF_LAST_FETCH_MSG_TIME, Date.now());
        const notificationDisposable = NotificationDataManager.sObserver.subscribe((rsp) => {
            this.msgSummary = rsp;
            this.forceUpdate();
        });

        this.addDisposable(notificationDisposable);
        //
        // //触发一次消息拉取
        NotificationDataManager.getLatestMsgSummary().then();
    }

    getAppBarTitle(): string {
        return "消息中心";
    }

    getAppBarBackIconStatus(): boolean {
        return false;
    }

    getShowBottomSafeArea(): boolean {
        return false;
    }
    updateCategoryReadTime(msgCategory: number): void {
        NotificationDataManager.updateCategoryReadTime(msgCategory);
    }

    navigateToPage(options: { msgType: number; title: string }): void {
        const { msgType, title } = options;
        ABCNavigator.navigateToPage(<NotificationListPage msgType={msgType} title={title} />).then();
    }

    // 将“0”转化为“今”
    private zeroDayDisplayToday(title = ""): string {
        return title.replace(/(?:^|[^0-9])(0)(?!\d)/g, "今");
    }

    renderContent(): JSX.Element | undefined {
        const systemItem = this.msgSummary?.getItemByCategoryType(MsgCategory.categorySystem);
        const todoItem = this.msgSummary?.getItemByCategoryType(MsgCategory.categoryTodo);
        const workReportItem = this.msgSummary?.getItemByCategoryType(MsgCategory.categoryWorkReport);
        const stateIncomeItem = this.msgSummary?.getItemByCategoryType(MsgCategory.categoryStatIncome);
        if (this.msgSummary == undefined) return <View />;
        let notificationWorkStatSubtitle = "工作日报";
        let notificationWorkStatDate = !!workReportItem?.message?.created
            ? new Date(workReportItem?.message?.created).format("yyyy-MM-dd")
            : "";
        if (workReportItem?.message?.messageBody?.data?.msgType == MsgType.reservedDailyReport) {
            notificationWorkStatSubtitle = `今日预约${workReportItem?.message?.messageBody?.data.countStat?.count}人`;
        } else if (workReportItem?.message?.messageBody?.data?.msgType == MsgType.reservedWeeklyReport) {
            notificationWorkStatSubtitle = `下周预约${workReportItem?.message?.messageBody?.data.countStat?.count}人`;
            const stringTime = workReportItem?.message?.messageBody?.data?.date;
            const getNextWeekStartDateFormat = new Date(Date.parse(stringTime ?? new Date().toString())); // 字符串转Date类型
            const getNextWeekEndDate = new Date(
                getNextWeekStartDateFormat.getFullYear(),
                getNextWeekStartDateFormat.getMonth(),
                getNextWeekStartDateFormat.getDate() + 6
            );
            notificationWorkStatDate = `${getNextWeekStartDateFormat?.format("MM-dd")}~${getNextWeekEndDate?.format("MM-dd")}`;
        }
        const isDrugstoreButler = userCenter.clinic?.isDrugstoreButler; // 药店管家不展示待办通知
        const notificationTypeList = [
            {
                title: "系统通知",
                icon: "notification_system",
                badge: systemItem?.newMessageCount ?? 0,
                detail: this.zeroDayDisplayToday(systemItem?.message?.messageBody?.title),
                latestTime: systemItem?.message?.created,
                onClick: () => {
                    this.updateCategoryReadTime(MsgCategory.categorySystem);
                    this.navigateToPage({ msgType: MsgCategory.categorySystem, title: "系统通知" });
                },
                show: true,
            },
            {
                title: "待办通知",
                icon: "notification_todolist",
                badge: todoItem?.newMessageCount ?? 0,
                detail: todoItem?.message?.messageBody?.title,
                latestTime: todoItem?.message?.created,
                onClick: () => {
                    this.updateCategoryReadTime(MsgCategory.categoryTodo);
                    this.navigateToPage({ msgType: MsgCategory.categoryTodo, title: "待办通知" });
                },
                show: !isDrugstoreButler,
            },
            {
                title: "工作日报",
                icon: "notification_work_stat",
                badge: workReportItem?.newMessageCount ?? 0,
                detail: !!workReportItem?.message?.created ? `${notificationWorkStatSubtitle} ${notificationWorkStatDate}` : "",
                latestTime: workReportItem?.message?.created,
                onClick: () => {
                    this.updateCategoryReadTime(MsgCategory.categoryWorkReport);
                    this.navigateToPage({ msgType: MsgCategory.categoryWorkReport, title: "工作日报" });
                },
                show: !isDrugstoreButler,
            },
            {
                title: "经营日报",
                icon: "notification_business_stat",
                badge: stateIncomeItem?.newMessageCount ?? 0,
                detail: !!stateIncomeItem?.message?.created
                    ? `经营日报 ${new Date(stateIncomeItem?.message?.created).format("yyyy-MM-dd")}`
                    : "",
                latestTime: stateIncomeItem?.message?.created,
                onClick: () => {
                    this.updateCategoryReadTime(MsgCategory.categoryStatIncome);
                    this.navigateToPage({ msgType: MsgCategory.categoryStatIncome, title: "经营日报" });
                },
                show: true,
            },
        ];

        return (
            <ScrollView>
                {notificationTypeList.map((item, index) => {
                    if (!item.show) return <View />;
                    return (
                        <_NotificationTypeItemView
                            key={index}
                            title={item.title}
                            icon={item.icon}
                            badge={item.badge}
                            detail={item.detail}
                            latestTime={item.latestTime}
                            onClick={item.onClick}
                        />
                    );
                })}
            </ScrollView>
        );
    }
}

interface _NotificationTypeItemViewProps {
    badge?: number;
    icon?: string;
    title?: string;
    detail?: string;
    latestTime?: string | Date;
    onClick?: () => void;
}

class _NotificationTypeItemView extends BaseComponent<_NotificationTypeItemViewProps> {
    constructor(props: _NotificationTypeItemViewProps) {
        super(props);
    }

    render() {
        const { icon, badge, title, detail, latestTime, onClick } = this.props;
        return (
            <AbcView
                style={{ backgroundColor: Colors.white }}
                onClick={() => {
                    onClick?.();
                }}
            >
                <View
                    style={[
                        ABCStyles.rowAlignCenter,
                        {
                            ...ABCStyles.bottomLine,
                            borderColor: getDistributeConfig().viewFeature.homeV2 ? Colors.retail_border_light : undefined,
                        },
                        { height: Sizes.dp80, marginLeft: Sizes.dp16 },
                    ]}
                >
                    <View>
                        <Badge dot={(badge ?? 0) > 0} size={Sizes.dp6}>
                            <AssetImageView style={{ width: Sizes.dp40, height: Sizes.dp40 }} name={icon} />
                        </Badge>
                    </View>
                    <SizedBox width={Sizes.dp12} />
                    <View style={{ flex: 1, paddingRight: Sizes.dp16 }}>
                        <View style={ABCStyles.rowAlignCenter}>
                            <Text style={[TextStyles.t16MB, { flex: 1 }]} numberOfLines={1}>
                                {title ?? ""}
                            </Text>
                            <Text style={TextStyles.t12NT4}>
                                {latestTime
                                    ? TimeUtils.formatDatetimeAsRecent(latestTime as Date, {
                                          year: true,
                                          time: true,
                                      })
                                    : ""}
                            </Text>
                        </View>
                        <SizedBox height={Sizes.dp4} />
                        <Text style={[TextStyles.t14NT2, { flexShrink: 1 }]} numberOfLines={1}>
                            {detail ?? "没有新消息"}
                        </Text>
                    </View>
                </View>
            </AbcView>
        );
    }
}
