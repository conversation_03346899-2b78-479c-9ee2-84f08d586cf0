/**
 * create by dengjie
 * desc:
 * create date 2020/7/21
 */

import { Bloc, BlocEvent } from "../bloc";
import React from "react";
import { EventName } from "../bloc/bloc";
import { ABCUtils } from "../base-ui/utils/utils";
import { GetMsgListRsp, MessageListItem, MsgCategory, MsgType, NotificationDataManager } from "../data/notification";
import _ from "lodash";
import { TimeUtils } from "../common-base-module/utils";
import { SizedBox } from "../base-ui";
import { Sizes } from "../theme";
import { NotificationBusinessStat, NotificationSystemCardView, NotificationTodoCardView, NotificationWorkStat } from "./notification-view";
import { View } from "@hippy/react";
import { _TimeView } from "./notification-list-page";
import { LoadingWithTextView } from "../base-ui/views/loading-view";
import { Subject } from "rxjs";
import { switchMap } from "rxjs/operators";
import { ABCError } from "../common-base-module/common-error";

class State {
    lastResult?: GetMsgListRsp | null;
    loading = true;
    loadError?: any;

    detailInfo?: Array<MessageListItem> = [];

    msgViews?: JSX.Element[] = [];

    get hasMore(): boolean {
        return (
            !this.lastResult ||
            ((this.lastResult.messageList?.length ?? 0) == (this.lastResult?.limit ?? 0) &&
                (this.lastResult.messageList?.length ?? 0) < (this.lastResult?.total ?? 0))
        );
    }

    get isEmpty(): boolean {
        return _.isEmpty(this.msgViews);
    }

    clone(): State {
        return Object.assign(new State(), this);
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {}

class _EventLoadMore extends _Event {}

class _EventRetryLoad extends _Event {}

class NotificationListPageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<NotificationListPageBloc | undefined>(undefined);
    private msgType: number;

    static fromContext(context: NotificationListPageBloc): NotificationListPageBloc {
        return context;
    }

    constructor(msgType: number) {
        super();
        this.msgType = msgType;

        this.dispatch(new _EventInit());
    }

    private _innerState!: State;
    private _loadDataTrigger = new Subject();
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventLoadMore, this._mapEventLoadMore);
        map.set(_EventRetryLoad, this._mapEventRetryLoad);

        return map;
    }

    async *_mapEventInit(/*ignored: _EventInit*/): AsyncGenerator<State> {
        NotificationDataManager.markAllNotificationDataAsRead(this.msgType).then();
        this._loadDataTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.loading = true;
                    this.innerState.loadError = null;
                    this.update();

                    if (this.innerState.msgViews?.length) {
                        this.innerState.msgViews?.push(<LoadingWithTextView text={"加载中..."} />);
                    }

                    const offset = (this.innerState.lastResult?.offset ?? 0) + (this.innerState.lastResult?.messageList?.length ?? 0);
                    return NotificationDataManager.getNewMsgList({
                        category: this.msgType,
                        offset: offset,
                    })
                        .catch((e) => new ABCError(e))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                this.innerState.loading = false;
                if (ABCUtils.last(this.innerState.msgViews ?? []) instanceof LoadingWithTextView) {
                    this.innerState.msgViews?.pop();
                }
                if (rsp instanceof ABCError) {
                    this.innerState.loadError = rsp;
                    this.innerState.msgViews?.push(<LoadingWithTextView text={"加载失败"} showLoading={false} />);
                    return;
                }

                this.innerState.lastResult = rsp;
                if (rsp != null && !ABCUtils.isEmpty(rsp.messageList)) {
                    let lastItem;
                    const length = this.innerState.msgViews?.length ?? 0;
                    if (length > 0) {
                        lastItem = this.innerState.detailInfo?.[length - 2];
                        _.remove(this.innerState.msgViews ?? [], (item, index, self) => {
                            return self.length == index + 1;
                        }); //删除最后一条时间分割item
                    }

                    const dataToView = (item: MessageListItem) => {
                        switch (this.msgType) {
                            case MsgCategory.categorySystem: {
                                return <NotificationSystemCardView cardInfo={item} />;
                            }
                            case MsgCategory.categoryTodo: {
                                return <NotificationTodoCardView cardInfo={item} />;
                            }
                            case MsgCategory.categoryWorkReport: {
                                return <NotificationWorkStat cardInfo={item} />;
                            }
                            case MsgCategory.categoryStatIncome: {
                                return <NotificationBusinessStat cardInfo={item} />;
                            }
                            default: {
                                return <View />;
                            }
                        }
                    };

                    for (const item of rsp.messageList ?? []) {
                        if (item?.messageBody?.data?.msgType == MsgType.inventoryWarning) {
                            if (
                                !item?.messageBody?.data?.date ||
                                (!!item?.messageBody?.data?.date && !TimeUtils.isToday(new Date(item?.messageBody?.data?.date)))
                            ) {
                                continue; //库存预警只显示当天日期,需要过滤掉非当天及没有日期的库存预警数据
                            }
                        }
                        const delta = lastItem != null ? TimeUtils.difference(item.created, lastItem.created) : null;
                        if (_.isEmpty(this.innerState.msgViews)) this.innerState.msgViews?.push(<SizedBox height={Sizes.dp16} />); //添加顶底间隔

                        if (Math.abs(delta?.inMinutes ?? 0) <= 3) {
                            //三分钟内，分为一组
                            if (!_.isEmpty(this.innerState.msgViews)) this.innerState.msgViews?.push(<SizedBox height={Sizes.dp16} />);
                            this.innerState.msgViews?.push(dataToView(item));
                        } else {
                            if (!!lastItem && this.isMeetShowTimeCondition(lastItem)) {
                                this.innerState.msgViews?.push(<_TimeView time={lastItem?.created} />);
                            }
                            this.innerState.msgViews?.push(dataToView(item));
                        }

                        lastItem = item;
                    }
                    if (!!lastItem && this.isMeetShowTimeCondition(lastItem)) {
                        this.innerState.msgViews?.push(<_TimeView time={lastItem?.created} />);
                    }
                }

                if (!this.innerState.hasMore && this.innerState.msgViews?.length) {
                    this.innerState.msgViews?.push(<LoadingWithTextView text={"没有更多数据了"} showLoading={false} />);
                }

                this.innerState.detailInfo = this.innerState.detailInfo?.concat(rsp?.messageList ?? []);
                this.update();
            })
            .addToDisposableBag(this);

        this._loadDataTrigger.next();
    }

    // 时间显示条件
    private isMeetShowTimeCondition(item: MessageListItem): boolean {
        if (!item) return false;
        return (
            item?.messageBody?.data?.msgType != MsgType.inventoryWarning ||
            (item?.messageBody?.data?.msgType == MsgType.inventoryWarning &&
                !!item?.messageBody?.data?.date &&
                TimeUtils.isToday(new Date(item?.messageBody?.data?.date)))
        );
    }

    async *_mapEventUpdate(/*ignored: _EventUpdate*/): AsyncGenerator<State> {
        yield this.innerState;
    }

    async *_mapEventLoadMore(/*ignored: _EventUpdate*/): AsyncGenerator<State> {
        if (this.innerState.loading) return;
        if (!this.innerState.hasMore) return;
        this._loadDataTrigger.next();
    }

    async *_mapEventRetryLoad(/*ignored: _EventUpdate*/): AsyncGenerator<State> {
        if (this.innerState.loading) return;
        this.innerState.msgViews = [];
        this.innerState.detailInfo = [];
        this._loadDataTrigger.next();
    }

    update(): void {
        this.dispatch(new _EventUpdate());
    }

    //加载更多
    requestLoadingMore(): void {
        this.dispatch(new _EventLoadMore());
    }

    //在出错时，点击重试
    requestRetryLoad(): void {
        this.dispatch(new _EventRetryLoad());
    }
}

export { NotificationListPageBloc, State };
