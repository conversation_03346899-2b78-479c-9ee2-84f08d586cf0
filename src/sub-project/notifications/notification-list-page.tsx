/**
 * create by dengjie
 * desc:
 * create date 2020/7/21
 */
import React from "react";
import { Text, View } from "@hippy/react";
import { NotificationListPageBloc } from "./notification-list-page-bloc";
import { ABCStyles, Color, Colors, Sizes, TextStyles } from "../theme";
import { ABCNetworkPageContentStatus, BaseBlocNetworkPage } from "../base-ui/base-page";
import ReverseListView from "../base-ui/views/reverse-list-view";
import { BaseComponent } from "../base-ui/base-component";
import { TimeUtils } from "../common-base-module/utils";
import { ABCEmptyView } from "../base-ui/views/empty-view";

interface NotificationListPageProps {
    title: string;
    msgType: number;
}

export class NotificationListPage extends BaseBlocNetworkPage<NotificationListPageProps, NotificationListPageBloc> {
    constructor(props: NotificationListPageProps) {
        super(props);
        this.bloc = new NotificationListPageBloc(props.msgType);
    }

    getAppBarTitle(): string {
        return this.props.title;
    }

    getBackgroundColor(): Color {
        return Colors.prescriptionBg;
    }

    emptyContent(): JSX.Element {
        return <ABCEmptyView tips={"没有新消息"} />;
    }

    componentDidMount(): void {
        super.componentDidMount();

        this.setContentStatus(ABCNetworkPageContentStatus.loading, null);
        const stateObserver = this.bloc.state.subscribe((state) => {
            let status = ABCNetworkPageContentStatus.show_data;
            let error: any;
            if (state.loading) {
                if (!state.msgViews?.length) {
                    status = ABCNetworkPageContentStatus.loading;
                }
            } else if (state.isEmpty) {
                status = ABCNetworkPageContentStatus.empty;
            } else if (state.loadError) {
                status = ABCNetworkPageContentStatus.error;
                error = state.loadError;
            }

            this.setContentStatus(status, error);
        });

        this.addDisposable(stateObserver);
    }

    renderContent(): JSX.Element {
        const state = this.bloc.currentState;
        if (!state.msgViews?.length) return <View />;
        return (
            <View style={{ paddingHorizontal: Sizes.listHorizontalMargin, flex: 1 }}>
                <ReverseListView
                    showScrollIndicator={false}
                    numberOfRows={this.bloc.currentState.msgViews?.length ?? 0}
                    dataSource={this.bloc.currentState.msgViews ?? []}
                    scrollEventThrottle={300}
                    getRowKey={(index) => index.toString()}
                    renderRow={(view) => {
                        return view;
                    }}
                    onEndReached={() => {
                        this.bloc.requestLoadingMore();
                    }}
                />
            </View>
        );
    }
}

interface _TimeViewProps {
    time?: Date;
}

export class _TimeView extends BaseComponent<_TimeViewProps> {
    constructor(props: _TimeViewProps) {
        super(props);
    }

    render(): JSX.Element {
        const { time } = this.props;
        return (
            <View style={[ABCStyles.rowAlignCenter, { justifyContent: "center", height: Sizes.dp50 }]}>
                <View
                    style={[
                        ABCStyles.rowAlignCenter,
                        {
                            textAlign: "center",
                        },
                    ]}
                >
                    <Text style={TextStyles.t12NT2.copyWith({ color: Colors.t2, lineHeight: Sizes.dp16 })}>
                        {TimeUtils.formatDatetimeAsRecent(time)}
                    </Text>
                </View>
            </View>
        );
    }
}
