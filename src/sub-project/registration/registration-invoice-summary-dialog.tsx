/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2022/8/3
 * @Copyright 成都字节流科技有限公司© 2022
 */
import React from "react";
import { Text, View } from "@hippy/react";
import { OldReserveInfo, PayStatusV2, RegistrationInvoiceType, RegistrationStatusV2 } from "./data/bean";
import { showBottomSheet } from "../base-ui/dialog/bottom_sheet";
import { ABCNetworkPageContentStatus, BaseBlocNetworkView } from "../base-ui/base-page";
import { RegistrationInvoicePageBloc } from "./registration-invoice-page-bloc";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { SafeAreaBottomView } from "../base-ui/safe_area_view";
import { AbcToolBar, AbcToolBarButtonStyle1, AbcToolBarButtonStyle2 } from "../base-ui/abc-app-library/tool-bar-button/tool-bar";
import { IconFontView, SizedBox } from "../base-ui";
import { BlocHelper } from "../bloc/bloc-helper";
import { RegistrationPatientCardInfoView } from "./views/registration-patient-card-info-view";
import _ from "lodash";
import { TimeUtils } from "../common-base-module/utils";
import { userCenter } from "../user-center";
import { PrecisionLimitFormatter } from "../base-ui/utils/formatter";
import { CustomInput } from "../base-ui/input/custom-input";
import { ABCUtils } from "../base-ui/utils/utils";
import { AbcView } from "../base-ui/views/abc-view";
import UiUtils, { pxToDp } from "../base-ui/utils/ui-utils";
import { RegistrationFeeDetailView } from "./views/views";
import abcI18Next from "../language/config";
import { HistoryPermissionModuleType } from "../base-business/data/beans";
import { ChargePromotionUseType } from "../charge/data/charge-beans";
import { DentistryConfig } from "./dentistry/data/bean";
import { RegistrationInvoicePageOldVersionBloc } from "./registration-invoice-page-old-version-bloc";
import { AbcBannerTips } from "../base-ui/abc-banner-tips/abc-banner-tips";
import { PatientOrderLockType } from "../base-business/data/patient-order/patient-order-bean";
import { DeviceUtils } from "../base-ui/utils/device-utils";

interface BaseRegistrationInvoiceSummaryDialogProps {
    id?: string;
    doctorId?: string;
    oldReserveInfo?: OldReserveInfo;
    statusName?: string;
}

// 基础组件，包含共同的 UI 和逻辑
export abstract class BaseRegistrationInvoiceSummaryDialog<
    P extends BaseRegistrationInvoiceSummaryDialogProps = BaseRegistrationInvoiceSummaryDialogProps,
    B extends RegistrationInvoicePageBloc | RegistrationInvoicePageOldVersionBloc =
        | RegistrationInvoicePageBloc
        | RegistrationInvoicePageOldVersionBloc
> extends BaseBlocNetworkView<P, B> {
    protected abstract useBloc(): B;

    constructor(props: P) {
        super(props);
        this.bloc = this.useBloc();
    }

    componentDidMount(): void {
        super.componentDidMount();
        this.contentStatus = ABCNetworkPageContentStatus.loading;
        BlocHelper.connectLoadingStatus(this.bloc as any, this);
    }

    private _contentHeight = pxToDp(322) + UiUtils.safeAreaBottomHeight();

    errorContent(): JSX.Element {
        return <View style={[ABCStyles.panelTopStyle, { height: this._contentHeight }]}>{super.errorContent()}</View>;
    }

    renderContent(): JSX.Element {
        if (!this.bloc.currentState.detail) return <View style={{ height: this._contentHeight }} />;
        const { chargeSheet } = this.bloc.currentState.detail;
        const { orderIsLocking, lockCopy } = chargeSheet || {};
        // 如果本单有收费异常内容，又正在收费中，仅展示收费中横幅
        const isExistChargeInProgress =
            chargeSheet?.copyPatientOrderLocks?.find((t) => t.businessKey == PatientOrderLockType.chargeSheetPay && !!t.status) ||
            chargeSheet?.chargeLockOrder;
        const shebaoRefundException = this.bloc.currentState.shebaoRefundException;
        const isOhos = DeviceUtils.isOhos();

        // 为不同平台创建完全分离的样式对象
        let containerStyle;
        if (isOhos) {
            // 鸿蒙系统样式
            containerStyle = {
                // 鸿蒙系统不设置背景色和圆角，由底层容器提供
                ...(this.contentStatus == ABCNetworkPageContentStatus.loading ? { height: this._contentHeight } : {}),
            };
        } else {
            // 其他平台样式
            containerStyle = {
                ...ABCStyles.panelTopStyle,
                backgroundColor: Colors.white,
                ...(this.contentStatus == ABCNetworkPageContentStatus.loading ? { height: this._contentHeight } : {}),
            };
        }

        return (
            <View style={containerStyle}>
                {this._renderPatientView()}
                {!isExistChargeInProgress && chargeSheet?.isSheBaoAbnormal && (
                    <AbcBannerTips tips={`医保${shebaoRefundException ? "退费" : "收费"}异常，请前往客户端处理`} />
                )}
                {orderIsLocking && !!lockCopy?.tips && (
                    <AbcBannerTips
                        tips={lockCopy.tips}
                        cancelText={lockCopy.isCancel ? "支付遇到问题？" : ""}
                        cancelOperate={() => this.bloc.requestCancelPayment()}
                    />
                )}
                {this._renderRegistrationDetail()}
                {this._renderBottomGroup()}
                <SafeAreaBottomView />
            </View>
        );
    }

    protected _renderPatientView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail,
            patient = detail?.patient;
        const { statusName: _statusName } = this.props;
        let statusName = _statusName;
        if (detail?.registrationFormItem?.statusV2 == RegistrationStatusV2.diagnosing) {
            statusName = "诊中";
        } else if (detail?.registrationFormItem?.statusV2 == RegistrationStatusV2.continueDiagnose) {
            statusName = "回诊";
        } else if (detail?.registrationFormItem?.statusV2 == RegistrationStatusV2.waitingDiagnose) {
            statusName = "待诊";
        }
        const isLockingOrder = detail?.chargeSheet?.orderIsLocking;
        return (
            <RegistrationPatientCardInfoView
                patientSwitchable={false}
                patient={patient}
                isEditing={false}
                diagnoseCount={state.diagnoseCount}
                isShowLine={true}
                isCanSeePatientHistoryInRegister={state.canViewDiagnoseHistory}
                type={HistoryPermissionModuleType.registration}
                canSeePatientMobileInRegister={state.canSeePatientPhone}
                panelBgColor={
                    statusName == "待签"
                        ? Colors.errorBorderBg
                        : statusName == "待诊" || statusName == "已诊" || statusName == "回诊" || statusName == "已退"
                        ? Colors.bluePanelBg
                        : undefined
                }
                textColor={
                    statusName == "待签"
                        ? Colors.Y2
                        : statusName == "待诊" || statusName == "已诊" || statusName == "回诊" || statusName == "已退"
                        ? Colors.B1
                        : undefined
                }
                lineColor={
                    statusName == "待签"
                        ? Colors.errorLine
                        : statusName == "待诊" || statusName == "已诊" || statusName == "回诊" || statusName == "已退"
                        ? Colors.blueLineColor
                        : undefined
                }
                onChange={(patient) => this.bloc.requestUpdatePatient(patient)}
                canEditPatientInfo={!isLockingOrder}
            />
        );
    }

    /**
     * 来源视图
     * @private
     */
    protected _renderSourceView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail,
            referralSource = detail?.referralSource;
        if (!referralSource) return <View />;
        return (
            <View style={[ABCStyles.rowAlignCenter, { paddingVertical: Sizes.dp8 }]}>
                <Text style={[TextStyles.t16NT6]}>来源：</Text>
                <Text style={[TextStyles.t16NT1]}>
                    {`${state.detail?.displayReferralSourceDoctorDepartName} ${
                        referralSource?.referralTime ? new Date(referralSource?.referralTime).format("yyyy-MM-dd") : ""
                    } 转出`}
                </Text>
            </View>
        );
    }

    /**
     * 医生视图
     * @private
     */
    protected _renderDoctorView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail;
        const registrationFormItem = detail?.registrationFormItem;
        return (
            <View style={[ABCStyles.rowAlignCenter, { paddingVertical: Sizes.dp8, alignItems: "flex-start", flex: 1 }]}>
                <Text style={[TextStyles.t16NT6]}>医生：</Text>
                <View style={{ flex: 1 }}>
                    <Text
                        style={[TextStyles.t16NT1]}
                    >{`${registrationFormItem?.displayDoctorDepartName} ${detail?.__revisitStatusName}`}</Text>
                </View>
            </View>
        );
    }
    /**
     * 号种视图
     * @private
     */
    protected _renderRegCategoryView(): JSX.Element {
        if (!userCenter.clinic?.isNormalHospital) return <View />;
        const state = this.bloc.currentState,
            detail = state.detail;
        const registrationFormItem = detail?.registrationFormItem;
        return (
            <View style={[ABCStyles.rowAlignCenter, { paddingVertical: Sizes.dp8 }]}>
                <Text style={[TextStyles.t16NT6]}>号种：</Text>
                <Text style={[TextStyles.t16NT1]}>{registrationFormItem?.registrationCategoryDisplay ?? ""}</Text>
            </View>
        );
    }

    /**
     * 时间视图
     * @private
     */
    protected _renderTimeView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail;
        const registrationFormItem = detail?.registrationFormItem;
        const { statusName } = this.props;
        if (!registrationFormItem?.reserveDate) return <View />;
        //固定模式---具体编号
        const fixedOrderNumInfo =
            (!!registrationFormItem?.timeOfDay ? registrationFormItem?.timeOfDay : "") +
            (this.isSignInGetOrderNo() && registrationFormItem?.statusV2 == RegistrationStatusV2.waitingSignIn
                ? ""
                : registrationFormItem?.displayOrderNumber);

        let statusDisplayName = statusName;
        if (registrationFormItem?.statusV2 == RegistrationStatusV2.diagnosing) {
            statusDisplayName = "诊中";
        } else if (registrationFormItem?.statusV2 == RegistrationStatusV2.continueDiagnose) {
            statusDisplayName = "回诊";
        } else if (registrationFormItem?.statusV2 == RegistrationStatusV2.waitingDiagnose) {
            statusDisplayName = "待诊";
        }
        return (
            <View style={[this.isFlexibleMode() ? ABCStyles.rowAlignCenter : { flexDirection: "row" }, { paddingVertical: Sizes.dp8 }]}>
                <Text style={[TextStyles.t16NT6]}>时间：</Text>
                <View style={[this.isFlexibleMode() ? ABCStyles.rowAlignCenter : {}, { alignSelf: "flex-start" }]}>
                    <Text style={[TextStyles.t16NT1, { lineHeight: Sizes.dp22 }]}>
                        {`${TimeUtils.formatDate(registrationFormItem?.reserveDate, "MM月dd日")} ${TimeUtils.getDayOfWeek(
                            registrationFormItem?.reserveDate,
                            "周"
                        )} ${this.isFixedMode() ? fixedOrderNumInfo : ""}`}
                    </Text>
                    <Text style={[TextStyles.t16NT1, { lineHeight: Sizes.dp22 }]}>
                        {`${(registrationFormItem?._registrationReserveTime?.start || registrationFormItem?.reserveTime?.start) ?? ""}~${
                            (registrationFormItem?._registrationReserveTime?.end || registrationFormItem?.reserveTime?.end) ?? ""
                        }`}
                    </Text>
                </View>

                <SizedBox width={Sizes.dp4} />
                {!!statusName && (
                    <Text
                        style={[
                            TextStyles.t16NY2.copyWith({
                                color:
                                    registrationFormItem.statusV2 == RegistrationStatusV2.diagnosing
                                        ? "rgba(30, 199, 97,1)"
                                        : statusName == "待签"
                                        ? Colors.Y2
                                        : statusName == "已退"
                                        ? Colors.T6
                                        : statusName == "待诊"
                                        ? Colors.B2
                                        : statusName == "已诊"
                                        ? Colors.T3
                                        : Colors.mainColor,
                            }),
                        ]}
                    >
                        {statusDisplayName}
                    </Text>
                )}
            </View>
        );
    }

    /**
     * 诊室视图
     * @private
     */
    protected _renderRoomView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail;
        const registrationFormItem = detail?.registrationFormItem;
        return (
            <View style={{ paddingVertical: Sizes.dp8 }}>
                <View style={{ flexDirection: "row", flex: 1 }}>
                    <Text style={[TextStyles.t16NT6]}>诊室：</Text>
                    <Text style={[TextStyles.t16NT1, { flexShrink: 1 }]}>{registrationFormItem?.consultingRoomName ?? "无"}</Text>
                </View>
            </View>
        );
    }

    /**
     * 项目视图
     * @private
     */
    protected _renderProjectView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail;
        const registrationFormItem = detail?.registrationFormItem;
        if (
            !(
                userCenter.isAllowedRegUpgrade &&
                !!this.registrationConfig()?.isOpenReserveProduct &&
                !!registrationFormItem?.registrationProductName
            )
        )
            return <View />;
        return (
            <View style={{ paddingVertical: Sizes.dp8 }}>
                <View style={{ flexDirection: "row", flex: 1 }}>
                    <Text style={[TextStyles.t16NT6]}>项目：</Text>
                    <Text style={[TextStyles.t16NT1, { flexShrink: 1 }]}>{registrationFormItem?.registrationProductName ?? ""}</Text>
                </View>
            </View>
        );
    }

    /**
     * 备注视图
     * @private
     */
    protected _renderRemarkView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail;
        if (!detail?.visitSourceRemark) return <View />;
        return (
            <View
                style={{
                    paddingVertical: Sizes.dp8,
                }}
            >
                <View style={{ flexDirection: "row", flex: 1 }}>
                    <Text style={[TextStyles.t16NT6]}>备注：</Text>
                    <Text style={[TextStyles.t16NT1, { flexShrink: 1, lineHeight: Sizes.dp22 }]} numberOfLines={3}>
                        {detail?.visitSourceRemark ?? ""}
                    </Text>
                </View>
            </View>
        );
    }

    /**
     * 推荐视图
     * @private
     */
    protected _renderRecommendView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail;
        if (!detail?.__visitSourceDisplayName) return <View />;
        return (
            <View
                style={{
                    paddingVertical: Sizes.dp8,
                }}
            >
                <View style={{ flexDirection: "row", flex: 1 }}>
                    <Text style={[TextStyles.t16NT6]}>推荐：</Text>
                    <Text style={[TextStyles.t16NT1, { flexShrink: 1, lineHeight: Sizes.dp22 }]}>
                        {detail?.__visitSourceDisplayName ?? ""}
                    </Text>
                </View>
            </View>
        );
    }

    /**
     * 预诊视图
     * @private
     */
    protected _renderPreDiagnosisView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail,
            registrationFormItem = detail?.registrationFormItem;
        if (registrationFormItem?.registrationCategory == 2) return <View />;
        if (!detail?.dentistryPreDiagnosisInfo) return <View />;
        return (
            <View
                style={{
                    paddingVertical: Sizes.dp8,
                }}
            >
                <View style={{ flexDirection: "row", flex: 1 }}>
                    <Text style={[TextStyles.t16NT6]}>预诊：</Text>
                    <Text style={[TextStyles.t16NT1, { flexShrink: 1, lineHeight: Sizes.dp22 }]} numberOfLines={5}>
                        {detail?.dentistryPreDiagnosisInfo ?? ""}
                    </Text>
                </View>
            </View>
        );
    }

    // 是否有可用优惠
    protected hasAvailableDiscount(): boolean {
        const state = this.bloc.currentState,
            detail = state.detail;
        const { couponPromotions, patientCardPromotions, promotions, giftRulePromotions, patientPointsInfo } = detail?.chargeSheet || {};
        const hasAvailableCoupon = !!couponPromotions?.some((t) => t.isCanBeUsed == ChargePromotionUseType.canUsed),
            hasAvailablePromotions = !!promotions?.some((t) => t.isCannotBeUsed == ChargePromotionUseType.canUsed),
            hasAvailableGiftRulePromotions = !!giftRulePromotions?.some((t) => t.isCanBeUsed == ChargePromotionUseType.canUsed),
            hasAvailablePatientCardPromotions = !!patientCardPromotions?.some((t) => {
                !!t.deductItems?.some((k) => !k.availableDeductTotalCount);
            }),
            hasAvailablePoints = !!patientPointsInfo?.maxDeductionPrice;
        return (
            hasAvailableCoupon ||
            hasAvailablePromotions ||
            hasAvailableGiftRulePromotions ||
            hasAvailablePatientCardPromotions ||
            hasAvailablePoints
        );
    }

    /**
     * 费用视图
     * @private
     */
    protected _renderFeeView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail;
        const registrationFormItem = detail?.registrationFormItem;
        const discountFee = detail?.chargeSheet?.chargeSheetSummary?.discountFee,
            receivedFee = detail?.chargeSheet?.chargeSheetSummary?.receivedFee,
            refundFee = Math.abs(detail?.chargeSheet?.chargeSheetSummary?.refundFee ?? 0);
        //是否是全部退费或者部分退费
        const isRefund =
            registrationFormItem?.payStatusV2 == PayStatusV2.refunded || registrationFormItem?.payStatusV2 == PayStatusV2.partedRefunded;
        let feeDisplay = 0;
        let statusText: string | number | string[] | undefined = undefined;
        let statusTextColor: string | undefined = undefined;
        if (registrationFormItem?.payStatusV2 == PayStatusV2.paid) {
            statusText = "已收";
            statusTextColor = Colors.canAppointmentColor;
            feeDisplay = receivedFee ?? 0;
        } else if (isRefund) {
            statusText = "已退";
            statusTextColor = Colors.T2;
            feeDisplay = 0;
        } else if (registrationFormItem?.payStatusV2 == PayStatusV2.partedPaid) {
            statusText = "欠收";
            statusTextColor = Colors.Y2;
            feeDisplay = detail?.chargeSheet?.chargeSheetSummary?.needPayFee ?? 0;
        } else if (registrationFormItem?.payStatusV2 == PayStatusV2.notPaid) {
            statusText = "未收";
            statusTextColor = Colors.Y2;
            feeDisplay = state?.pay?.receivable ?? 0;
        } else {
            feeDisplay = state?.pay?.fee ?? 0;
        }
        const isLockingOrder = detail?.chargeSheet?.orderIsLocking;
        return (
            <View style={[ABCStyles.rowAlignCenter]}>
                <Text style={[TextStyles.t16NT6]}>费用：</Text>
                {state.disabledEditCharge && (
                    <View style={{ flexDirection: "row", flex: 1, alignItems: "center" }}>
                        <Text style={[TextStyles.t16NT1]}>
                            {`${abcI18Next.t("￥") + (isRefund ? receivedFee : feeDisplay)?.toFixed(2)} `}
                        </Text>
                        <Text style={{ color: statusTextColor, fontSize: Sizes.dp16 }}>
                            {!!statusText ? statusText : ""}
                            {registrationFormItem?.payStatusV2 == PayStatusV2.partedRefunded
                                ? "(" + abcI18Next.t("￥") + refundFee?.toFixed(2) + ")"
                                : ""}
                        </Text>
                        <Text>{` `}</Text>
                        {!isLockingOrder && registrationFormItem?.payStatusV2 != PayStatusV2.notPaid && (
                            <IconFontView
                                name={"info_bold"}
                                size={Sizes.dp13}
                                color={Colors.bdColor}
                                onClick={() => {
                                    // 挂号-费用-费用说明
                                    RegistrationFeeDetailView.show({
                                        detail: detail,
                                        isCanSeeModifyPayMode: state.canSeeModifyPayMode,
                                        payModeTypeStr: state.dataPermission?.registrationModifyPayModeTypeStr,
                                    });
                                }}
                            />
                        )}
                    </View>
                )}
                {!state.disabledEditCharge && (
                    <View
                        style={[
                            ABCStyles.rowAlignCenterSpaceBetween,
                            Sizes.paddingLTRB(Sizes.dp12, Sizes.dp8),
                            { flex: 1, backgroundColor: Colors.whiteSmoke, borderRadius: Sizes.dp4 },
                        ]}
                    >
                        <Text
                            style={[
                                TextStyles.t16NT1.copyWith({
                                    color: !state.shouldRegisteredBargain || state.isHasEditCharge ? Colors.T2 : Colors.T1,
                                }),
                            ]}
                        >
                            {`${!!statusText ? statusText : ""} ${abcI18Next.t("￥")}`}
                        </Text>
                        <CustomInput
                            style={{
                                flex: 1,
                                textAlign: "left",
                            }}
                            borderType={"none"}
                            type={"input"}
                            value={ABCUtils.formatPrice(feeDisplay ?? 0)}
                            placeholder={"费用"}
                            autoFocus={false}
                            formatter={PrecisionLimitFormatter(2)}
                            textStyle={{ ...TextStyles.t16NT1, height: Sizes.dp22 }}
                            onChange={(value) => this.bloc.requestModifyRegistrationFee(value)}
                            disable={!state.shouldRegisteredBargain || state.isHasEditCharge || isLockingOrder}
                            containerStyle={{ backgroundColor: state.hasCharged ? Colors.whiteSmoke : undefined }}
                            disableStyle={{ fontSize: Sizes.dp16 }}
                        />
                        <AbcView onClick={() => this.bloc.requestDiscountDetail()}>
                            {!this.hasAvailableDiscount() && <Text style={TextStyles.t14NT4}>{"无可用优惠"}</Text>}
                            {this.hasAvailableDiscount() && (
                                <View style={[ABCStyles.rowAlignCenter]}>
                                    <Text style={[TextStyles.t14NT1]}>优惠：</Text>
                                    <Text style={[TextStyles.t14NR2]}>{ABCUtils.formatPriceWithRMB(discountFee ?? 0, false)}</Text>
                                </View>
                            )}
                        </AbcView>
                    </View>
                )}
            </View>
        );
    }

    /**
     * 创建视图
     * @private
     */
    protected _renderCreateView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail;
        const registrationFormItem = detail?.registrationFormItem;
        const createTime = registrationFormItem?.created?.format("yyyy-MM-dd") ?? "";
        const createDisplayStr = `${registrationFormItem?.displayCreatedByName ?? ""}${!!createTime ? `(${createTime})` : ""}`;
        if (!createDisplayStr) return <View />;
        return (
            <View
                style={{
                    paddingVertical: Sizes.dp8,
                }}
            >
                <View style={{ flexDirection: "row", flex: 1 }}>
                    <Text style={[TextStyles.t16NT6]}>创建：</Text>
                    <Text style={[TextStyles.t16NT1, { flexShrink: 1, lineHeight: Sizes.dp22 }]}>{createDisplayStr}</Text>
                </View>
            </View>
        );
    }

    protected _renderRegistrationDetail(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail;
        const registrationFormItem = detail?.registrationFormItem;
        if (_.isEmpty(registrationFormItem)) return <View />;
        return (
            <View style={[Sizes.paddingLTRB(Sizes.dp17, Sizes.dp8, Sizes.dp16, Sizes.dp16)]}>
                {this._renderSourceView()}
                {this._renderDoctorView()}
                {this._renderRegCategoryView()}
                {this._renderTimeView()}
                {this._renderRoomView()}
                {this._renderProjectView()}
                {this._renderRemarkView()}
                {this._renderRecommendView()}
                {this._renderPreDiagnosisView()}
                {this._renderFeeView()}
                {this._renderCreateView()}
            </View>
        );
    }

    protected _renderBottomGroup(): JSX.Element {
        const state = this.bloc.currentState;
        const registration = state.detail?.registrationFormItem;
        const chargeSheet = state.detail?.chargeSheet;
        const isFunded =
            registration?.statusV2 != RegistrationStatusV2.refunded ||
            (registration?.statusV2 == RegistrationStatusV2.refunded && registration?.payStatusV2 === PayStatusV2.partedRefunded);
        // 老版本的挂号预约不受新增、修改挂号预约权限控制
        const canModifyRegistration = this.bloc instanceof RegistrationInvoicePageBloc ? state?.canModifyRegistration : true; // 门诊挂号编辑、收费、预诊、退费
        if (!canModifyRegistration) return <View />;
        const isLockingOrder = chargeSheet?.orderIsLocking;
        if (!isFunded) {
            if (state.canBackDirectRegistration) {
                return (
                    <AbcToolBar showMoreBtn={true} showCount={4} direction={"right"} lineHeight={1} key={"returnDiagnosisRoot"}>
                        <AbcToolBarButtonStyle2
                            key={"DirectRegistration"}
                            text={"回诊"}
                            disable={isLockingOrder}
                            onClick={() => {
                                this.bloc.requestBackDirectRegistration();
                            }}
                        />
                    </AbcToolBar>
                );
            }
            return <View />;
        }
        const isRefundFee =
            (registration?.payStatusV2 === PayStatusV2.paid ||
                registration?.payStatusV2 === PayStatusV2.partedPaid ||
                registration?.payStatusV2 === PayStatusV2.partedRefunded) &&
            (chargeSheet?.chargeSheetSummary?.receivedFee ?? 0) > 0;

        const buttons: JSX.Element[] = [];
        //退号后，去掉编辑
        if (registration?.statusV2 != RegistrationStatusV2.refunded) {
            buttons.push(
                <AbcToolBarButtonStyle2
                    key={"edit"}
                    text={`编辑`}
                    onClick={() => this.bloc.requestUpdateRegistrationInfo()}
                    disable={isLockingOrder}
                />
            );
        }

        if (!state.hasCharged) {
            buttons.push(
                <AbcToolBarButtonStyle2
                    style={{ textAlign: "center" }}
                    key={"charge"}
                    text={`收费`}
                    disable={isLockingOrder}
                    onClick={() => {
                        this.bloc.requestRegistrationCharge();
                    }}
                />
            );
        }

        if (registration?.statusV2 == RegistrationStatusV2.waitingSignIn) {
            buttons.push(
                <AbcToolBarButtonStyle1
                    key={"sign"}
                    text={"签到"}
                    disable={isLockingOrder}
                    onClick={() => {
                        this.bloc.requestRegistrationSignIn();
                    }}
                />
            );
        }

        if (state.canBackDirectRegistration) {
            buttons.push(
                <AbcToolBarButtonStyle2
                    key={"DirectRegistration"}
                    text={"回诊"}
                    disable={isLockingOrder}
                    onClick={() => {
                        this.bloc.requestBackDirectRegistration();
                    }}
                />
            );
        }

        if ((registration?.statusV2 ?? 0) < RegistrationStatusV2.diagnosed && !registration?.isConvenient) {
            buttons.push(
                <AbcToolBarButtonStyle2
                    key={"preDianosis"}
                    text={"预诊"}
                    disable={isLockingOrder}
                    onClick={() => this.bloc.requestCompletePreDiagnosis()}
                />
            );
        }

        if (!state.isCreate && state.chargeFromRegistration) {
            if (isRefundFee) {
                buttons.push(
                    <AbcToolBarButtonStyle2
                        key={"refund"}
                        text={"退费"}
                        disable={isLockingOrder}
                        onClick={() => {
                            this.bloc.requestRefundedRegistrationFee();
                        }}
                    />
                );
            } else if (registration?.statusV2 != RegistrationStatusV2.diagnosed) {
                // 已诊不能退号
                buttons.push(
                    <AbcToolBarButtonStyle2
                        key={"cancel"}
                        text={`${this.isFlexibleMode() ? "取消预约" : "退号"}`}
                        disable={isLockingOrder}
                        onClick={() => {
                            this.bloc.requestRefundedRegistration();
                        }}
                    />
                );
            }
        }

        // if (registration?.statusV2 && registration?.statusV2 <= RegistrationStatusV2.refunded) {
        //     buttons.push(
        //         <AbcToolBarButtonStyle1
        //             key={UniqueKey()}
        //             text={"保存"}
        //             onClick={
        //                 state.hasChange
        //                     ? () => {
        //                           this.bloc.requestSaveRegistration();
        //                       }
        //                     : undefined
        //             }
        //         />
        //     );
        // }
        if (!buttons.length) return <View />;

        return (
            <AbcToolBar showMoreBtn={true} showCount={4} direction={"right"} lineHeight={1}>
                {buttons}
            </AbcToolBar>
        );
    }

    protected isFlexibleMode(): boolean {
        if (this.bloc instanceof RegistrationInvoicePageBloc) {
            return this.bloc.currentState.isFlexibleMode;
        }
        return false;
    }

    protected isFixedMode(): boolean {
        if (this.bloc instanceof RegistrationInvoicePageBloc) {
            return this.bloc.currentState.isFixedMode;
        }
        return false;
    }

    protected registrationConfig(): DentistryConfig | undefined {
        if (this.bloc instanceof RegistrationInvoicePageBloc) {
            return this.bloc.currentState.registrationConfig;
        }
        return undefined;
    }

    protected isSignInGetOrderNo(): boolean {
        if (this.bloc instanceof RegistrationInvoicePageBloc) {
            return this.bloc.currentState.isSignInGetOrderNo;
        }
        return false;
    }
}

// 老版本组件
export class RegistrationInvoiceSummaryDialogLegacy extends BaseRegistrationInvoiceSummaryDialog<
    BaseRegistrationInvoiceSummaryDialogProps,
    RegistrationInvoicePageOldVersionBloc
> {
    protected useBloc(): RegistrationInvoicePageOldVersionBloc {
        const { id, doctorId, oldReserveInfo } = this.props;
        return new RegistrationInvoicePageOldVersionBloc({
            id,
            doctorId,
            oldReserveInfo,
        });
    }

    static async show(options?: BaseRegistrationInvoiceSummaryDialogProps): Promise<void> {
        return showBottomSheet(<RegistrationInvoiceSummaryDialogLegacy {...options} />);
    }
}

// 新版本组件
export class RegistrationInvoiceSummaryDialogUpgrade extends BaseRegistrationInvoiceSummaryDialog<
    BaseRegistrationInvoiceSummaryDialogProps,
    RegistrationInvoicePageBloc
> {
    protected useBloc(): RegistrationInvoicePageBloc {
        const { id, doctorId, oldReserveInfo } = this.props;
        return new RegistrationInvoicePageBloc({
            id,
            doctorId,
            oldReserveInfo,
            type: RegistrationInvoiceType.dialog,
        });
    }

    static async show(options?: BaseRegistrationInvoiceSummaryDialogProps): Promise<void> {
        return showBottomSheet(<RegistrationInvoiceSummaryDialogUpgrade {...options} />);
    }
}

// 导出一个默认的组件，根据条件选择使用哪个版本
export class RegistrationInvoiceSummaryDialog extends React.Component<
    BaseRegistrationInvoiceSummaryDialogProps & { isUpgradeBloc?: boolean }
> {
    static defaultProps = {
        isUpgradeBloc: true,
    };

    static async show(options?: BaseRegistrationInvoiceSummaryDialogProps & { isUpgradeBloc?: boolean }): Promise<void> {
        const { isUpgradeBloc, ...rest } = options || {};

        if (isUpgradeBloc == false && !userCenter.isAllowedRegUpgrade) {
            return RegistrationInvoiceSummaryDialogLegacy.show(rest);
        } else {
            return RegistrationInvoiceSummaryDialogUpgrade.show(rest);
        }
    }

    render(): JSX.Element {
        const { isUpgradeBloc, ...rest } = this.props;

        if (isUpgradeBloc == false && !userCenter.isAllowedRegUpgrade) {
            return <RegistrationInvoiceSummaryDialogLegacy {...rest} />;
        } else {
            return <RegistrationInvoiceSummaryDialogUpgrade {...rest} />;
        }
    }
}
