/**
 * create by <PERSON><PERSON><PERSON><PERSON><PERSON>
 * desc: 理疗预约成功对话框
 * create date 2021/02/01
 */
import React from "react";
import { View, Text } from "@hippy/react";
import { BaseComponent } from "../../base-ui/base-component";
import { Dialog<PERSON><PERSON>er, DialogButtonBuilder, DialogIndex } from "../../base-ui/dialog/dialog-builder";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { IconFontView } from "../../base-ui";
import { TimeUtils } from "../../common-base-module/utils";
import { TherapyAppointmentSheet } from "./data/appointment-bean";
export enum TherapySuccessDialogType {
    finish = 1,
    signIn,
}

interface TherapySuccessDialogProps {
    type?: TherapySuccessDialogType;
    detail: TherapyAppointmentSheet;
}

export class TherapySuccessDialog extends BaseComponent<TherapySuccessDialogProps> {
    static async show(props: TherapySuccessDialogProps): Promise<DialogIndex> {
        const view = <TherapySuccessDialog {...props} />;
        const builder = new DialogBuilder();
        const buttons = new DialogButtonBuilder();

        buttons.appendNegativeButton("确定");

        builder.button = buttons;
        builder.content = view;
        builder.contentPadding = Sizes.paddingLTRB(Sizes.dp24, Sizes.dp24);
        return await builder.show();
    }

    constructor(props: TherapySuccessDialogProps) {
        super(props);
    }

    private _renderDialogBar(): JSX.Element {
        const { type } = this.props;
        return (
            <View style={[ABCStyles.rowAlignCenter]}>
                <IconFontView style={{ paddingRight: Sizes.dp8 }} name={"Chosen"} size={Sizes.dp24} color={Colors.mainColor} />
                <Text style={TextStyles.t18MT1.copyWith({ lineHeight: Sizes.dp28 })}>
                    {`${type == TherapySuccessDialogType.signIn ? "签到" : "预约"}成功`}
                </Text>
            </View>
        );
    }

    private _renderContentItem(title: string, content: string, showBottomLine = true): JSX.Element {
        return (
            <View style={[{ flexDirection: "row", marginBottom: showBottomLine ? Sizes.dp6 : undefined }]}>
                <View style={{ flexDirection: "row", width: Sizes.dp64, justifyContent: "space-between" }}>
                    {title.split("").map((item, index) => (
                        <Text key={index} style={TextStyles.t16NT2.copyWith({ lineHeight: Sizes.dp24 })}>
                            {item}
                        </Text>
                    ))}
                </View>
                <Text style={TextStyles.t16NT2.copyWith({ lineHeight: Sizes.dp24 })}>：</Text>
                <Text style={[TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp24 }), { flex: 1 }]}>{content}</Text>
            </View>
        );
    }

    render(): JSX.Element {
        const { detail } = this.props;
        const time = detail.reserveDate;
        const timeDisplay = time ? `${TimeUtils.formatDate(time, "MM-dd", "")}  ${detail?.reserveStart} ~ ${detail?.reserveEnd}` : "";
        const consultingRoomName = detail?.consultingRoomName ?? "-"; // 诊室
        return (
            <View>
                {this._renderDialogBar()}
                <View style={{ marginTop: Sizes.dp16 }}>
                    {this._renderContentItem("理疗师", `${detail.doctorName?.length ? detail?.doctorName : "未指定里理疗师"}`)}
                    {this._renderContentItem("时间", timeDisplay ?? "")}
                    {this._renderContentItem("诊室", consultingRoomName, false)}
                </View>
            </View>
        );
    }
}
