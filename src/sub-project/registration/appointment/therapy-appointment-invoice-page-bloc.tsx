/**
 * 成都字节星球科技公司
 * <AUTHOR>
 * @date 2020-01-18
 *
 * @description
 */

import React from "react";
import { Bloc, BlocEvent } from "../../bloc";
import { EventName } from "../../bloc/bloc";
import { Range } from "../../base-ui/utils/value-holder";
import _ from "lodash";
import { BaseLoadingState } from "../../bloc/bloc-helper";
import {
    TherapyAppointmentSheet,
    TherapyDetailReq,
    TherapyDoctorShiftsListItem,
    TherapyDoctorsInfo,
    TherapyRegistration,
    TherapySignInStatus,
    TherapyStatus,
} from "./data/appointment-bean";
import { ClinicAgent, EmployeesMeConfig, RegAndTherapyConfig } from "../../base-business/data/clinic-agent";
import { MedicalRecord, Patient } from "../../base-business/data/beans";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import { Subject } from "rxjs";
import { switchMap } from "rxjs/operators";
import { TherapyAppointmentAgent } from "./data/therapy-appointment-agent";
import { ABCError } from "../../common-base-module/common-error";
import { CrmAgent } from "../../patients/data/crm-agent";
import { showOptionsBottomSheet } from "../../base-ui/dialog/bottom_sheet";
import { DialogIndex, showQueryDialog } from "../../base-ui/dialog/dialog-builder";
import { FocusItemKeys, PayStatusV2, RegistrationStatusV2 } from "../data/bean";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { OutpatientAgent } from "../../outpatient/data/outpatient";
import { Toast } from "../../base-ui/dialog/toast";
import { TherapyAppointmentDoctorListPage } from "./therapy-appointment-doctor-list-page";
import { errorToStr, TimeUtils } from "../../common-base-module/utils";
import { LoadingDialog } from "../../base-ui/dialog/loading-dialog";
import { TherapySuccessDialog } from "./therapy-success-dialog";
import { Doctor } from "../data/registration";
import { showBottomPanel } from "../../base-ui/abc-app-library/panel";
import { TherapyRemarkInputPage } from "./views/therapy-remark-input-page";
import { Sizes } from "../../theme";

export enum TherapyAppointmentInvoiceType {
    existence = 1, // 预约单
    addition = 2, //  新增预约
}

export class State extends BaseLoadingState {
    detail?: TherapyAppointmentSheet; // 理疗预约单详情

    employeeConfig?: RegAndTherapyConfig; // 员工配置
    pay = { memberId: "" };
    hasChange = false;
    focusKey = "";
    showErrorHint = false; // 显示错误提示

    shiftsRows?: TherapyDoctorShiftsListItem[];

    employeesMeConfig?: EmployeesMeConfig;
    //能够查看患者手机号
    get canSeePatientPhone(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.registration?.isCanSeePatientMobile;
    }

    _currentTherapyDoctors: Array<TherapyDoctorsInfo> = []; // 理疗师（id/name）
    set currentTherapyDoctorsList(list: Array<TherapyDoctorsInfo> | undefined) {
        this._currentTherapyDoctors = list ?? [];
    }
    // 选择理疗师列表名单
    get currentTherapyDoctorsList(): Array<TherapyDoctorsInfo> | undefined {
        let list: Doctor[] = [];
        list = list.concat(this._currentTherapyDoctors);
        return list;
    }

    //挂号预约能查看就诊历史
    get canViewDiagnoseHistory(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.registration?.medicalHistory;
    }

    type: TherapyAppointmentInvoiceType = TherapyAppointmentInvoiceType.addition;

    get isCreate(): boolean {
        return this.type == TherapyAppointmentInvoiceType.addition; // 新增预约
    }
    // 仅待签到状态下课可签到
    get isCanSignIn(): boolean {
        return this.detail?.therapyRegistration?.status == TherapyStatus.waitingSignIn;
    }

    // 已退号状态下不显示底部退号按钮
    get isCanCanceled(): boolean {
        return this.detail?.therapyRegistration?.status == TherapyStatus.canceled;
    }

    get chargeFromRegistration(): boolean {
        return this.detail?.chargeSheet?.type === 1;
    }

    get hasCharged(): boolean {
        const registrationFormItem = this.detail?.therapyRegistration;
        return !(
            (registrationFormItem?.payStatusV2 === PayStatusV2.notPaid || registrationFormItem?.payStatusV2 === PayStatusV2.partedPaid) &&
            (registrationFormItem.statusV2 ?? 0) < RegistrationStatusV2.refunded &&
            this.chargeFromRegistration
        );
    }
    // 病史档案
    get medicalRecord(): MedicalRecord {
        const detail = this.detail;
        return JsonMapper.deserialize(MedicalRecord, {
            chiefComplaint: detail?.chiefComplaint, // 主诉
            presentHistory: detail?.presentHistory, // 现病史
            pastHistory: detail?.pastHistory, // 既往病史
            physicalExamination: detail?.physicalExamination, // 体格检查
        });
    }

    clone(): State {
        return Object.assign(new State(), this);
    }
}

export class ScrollToErrorViewState extends State {
    static fromState(state: State): ScrollToErrorViewState {
        const newState = new ScrollToErrorViewState();
        Object.assign(newState, state);
        return newState;
    }
}

export class TherapyAppointmentInvoicePageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<TherapyAppointmentInvoicePageBloc | undefined>(undefined);

    static fromContext(context: TherapyAppointmentInvoicePageBloc): TherapyAppointmentInvoicePageBloc {
        return context;
    }

    // private _updatingPatientInformationTrigger: Subject<string> = new Subject<string>(); // 更新患者信息触发器
    private _appointmentDetailsTrigger: Subject<string> = new Subject<string>(); // 预约详情触发器

    private id?: string;
    // 默认预约时间选项
    private defaultAppointmentTimeOptions: {
        id: number;
        title: string;
        timeOfDay: string;
        timeOfDayCount?: number;
        range: Range<string>;
    }[] = [
        { id: 1, title: "今天", timeOfDay: "上午", timeOfDayCount: undefined, range: new Range<string>("00:00", "12:00") },
        { id: 2, title: "今天", timeOfDay: "下午", timeOfDayCount: undefined, range: new Range<string>("12:00", "18:00") },
        { id: 3, title: "今天", timeOfDay: "晚上", timeOfDayCount: undefined, range: new Range<string>("18:00", "24:00") },
    ];

    constructor(options?: { id?: string }) {
        super();

        this.id = options?.id;
        this.dispatch(new _EventInit()).then();
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit); // 拉取患者信息
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventModifyAppointmentPatient, this._mapEventModifyAppointmentPatient); // 修改预约患者
        map.set(_EventModifyAppointmentDoctor, this._mapEventModifyAppointmentDoctor); // 修改预约理疗师
        map.set(_EventModifyAppointmentTime, this._mapEventModifyAppointmentTime); // 修改预约时间
        map.set(_EventCancelPatientAppointment, this._mapEventCancelPatientAppointment); // 理疗预约（退号）
        map.set(_EventFinishTherapyAppointment, this._mapEventFinishTherapyAppointment); // 完成理疗预约
        map.set(_EventSignInAppointment, this._mapEventAppointmentSignIn); // 预约签到
        map.set(_EventModifyRemark, this._mapEventModifyRemark); // 修改备注

        return map;
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    private async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        this.innerState.employeesMeConfig = await ClinicAgent.getEmployeesMeConfig().catchIgnore();
        // 预约详情 触发器
        this._appointmentDetailsTrigger
            .pipe(
                switchMap((id) => {
                    this.innerState.startLoading();
                    this.update();
                    return TherapyAppointmentAgent.getTherapyDetail(id) // 拉取理疗预约详情
                        .catch((e) => new ABCError(e))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (rsp instanceof ABCError) {
                    this.innerState.setLoadingError(rsp);
                } else {
                    this.innerState.loading = false;
                    this.innerState.detail = rsp;
                }
                this.update();
            })
            .addToDisposableBag(this);

        Promise.all([TherapyAppointmentAgent.getAllTherapyDoctorList(), ClinicAgent.getEmployeesMeConfig()])
            .catchIgnore()
            .toObservable()
            .subscribe((rsp) => {
                this.innerState.currentTherapyDoctorsList = rsp?.[0]; // 预约理疗师数据提供程序 (拉取全部理疗师)
                this.innerState.employeeConfig = rsp?.[1]?.clinicInfo?.config; // 员工配置
                this.update();
            })
            .addToDisposableBag(this);

        if (this.id) {
            this.innerState.type = TherapyAppointmentInvoiceType.existence; // 预约单（已存在）
            this._appointmentDetailsTrigger.next(this.id);
        } else {
            this.innerState.detail = new TherapyAppointmentSheet();
            this.innerState.detail.therapyRegistration = new TherapyRegistration();
            // 默认选中时间
            const hours = new Date().getHours();
            let defaultReserve;
            if (hours >= 0 && hours < 12) {
                defaultReserve = this.defaultAppointmentTimeOptions[0];
            } else if (hours >= 12 && hours < 18) {
                defaultReserve = this.defaultAppointmentTimeOptions[1];
            } else {
                defaultReserve = this.defaultAppointmentTimeOptions[2];
            }

            // this.innerState.detail!.therapyRegistration!.reserveDate = new Date(); // 默认显示时间
            this.innerState.detail!.therapyRegistration!.reserveTime = defaultReserve.range;

            this.innerState.stopLoading();

            yield this.innerState.clone();
        }
    }

    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    private _formatPatientAddress(patient: Patient): void {
        _.assign(patient, patient.address);
    }
    /**
     * 新建预约 修改患者信息（修改/编辑患者信息）
     * @param event
     * @private
     */
    private async *_mapEventModifyAppointmentPatient(event: _EventModifyAppointmentPatient): AsyncGenerator<State> {
        this._formatPatientAddress(event.patient);
        if (this.innerState.detail?.patient?.id && this.innerState.detail?.patient?.id == event.patient.id) {
            this.innerState.detail!.patient = event.patient;
            this.update();
            return;
        }
        this.innerState.hasChange = true;
        if (event.patient?.id) {
            CrmAgent.getPatientById(event.patient.id)
                .then((rsp) => {
                    const patient = rsp;
                    this.innerState.detail!.patient = patient;
                    this.innerState.pay.memberId = event.patient.id ?? "";
                    this.update();
                })
                .then(() => {
                    // 获取pastHistoryInfo
                    OutpatientAgent.getPastHistory(event.patient.id!).then((rsp) => {
                        this.innerState.detail!.pastHistory = rsp;
                        this.update();
                    });
                });
        } else {
            this.innerState.detail!.patient = event.patient;
            this.update();
        }
    }

    /**
     * 选择预约理疗师 (选择理疗师)
     * @private
     */
    private async *_mapEventModifyAppointmentDoctor(): AsyncGenerator<State> {
        const doctorIndex = this.innerState.currentTherapyDoctorsList?.findIndex((item) => {
            return item.doctorId == this.innerState.detail?.therapyRegistration?.doctorId;
        });

        const result = await showOptionsBottomSheet({
            title: "选择理疗师",
            options: this.innerState.currentTherapyDoctorsList?.map((item) => item.doctorName ?? ""),
            initialSelectIndexes: !_.isUndefined(doctorIndex) ? new Set<number>([doctorIndex]) : undefined,
        });

        if (!result || _.isEmpty(result)) return;

        this.innerState.hasChange = true;

        const currentDoctor = this.innerState.currentTherapyDoctorsList?.[result[0]];

        if (!this.innerState.detail?.therapyRegistration) {
            this.innerState.detail!.therapyRegistration = new TherapyRegistration();
        }
        this.innerState.detail!.therapyRegistration.doctorId = currentDoctor?.doctorId;
        this.innerState.detail!.therapyRegistration.doctorName = currentDoctor?.doctorId ? currentDoctor?.doctorName : undefined;
        this.innerState.detail!.therapyRegistration.reserveDate = undefined; // 清空预约日期
        this.innerState.detail!.therapyRegistration.timeOfDay = "";
        this.innerState.detail!.therapyRegistration.reserveTime = undefined;
        this.innerState.detail!.therapyRegistration.orderNos = undefined;

        yield this.innerState.clone();
    }

    /**
     * 预约理疗师时间 (新增预约-选择时间)
     * @private
     */

    private async *_mapEventModifyAppointmentTime(): AsyncGenerator<State> {
        const result = await TherapyAppointmentDoctorListPage.showTherapy(this.innerState.detail?.therapyRegistration);
        if (!result || _.isEmpty(result)) return;
        this.innerState.hasChange = true;
        this.innerState.detail!.therapyRegistration = result;

        yield this.innerState.clone();
    }

    /**
     * 理疗预约（退号）
     * @private
     */
    private async *_mapEventCancelPatientAppointment(): AsyncGenerator<State> {
        const patient = this.innerState.detail?.patient;
        const registration = this.innerState.detail?.therapyRegistration;
        const result = await showQueryDialog(
            "",
            registration?.signInStatus == TherapySignInStatus.signed
                ? `${patient?.name} 已接受预约，确定要退号？`
                : "退号后不可恢复，是否继续？"
        );
        if (result == DialogIndex.positive) {
            TherapyAppointmentAgent.cancelPatientAppointment(this.innerState.detail!.id!).then(() => {
                Toast.show("退号成功", { success: true }).then(() => ABCNavigator.pop());
            });
            TherapyAppointmentAgent.changeObserver.next();
        }
    }

    /**
     * 理疗预约单（签到）
     * @private
     */
    private async *_mapEventAppointmentSignIn(): AsyncGenerator<State> {
        const detail = this.innerState.detail;
        if (!detail || !detail.id) return;
        const { therapyRegistration } = detail;

        if (TimeUtils.formatDate(therapyRegistration?.reserveDate) != TimeUtils.formatDate(new Date())) {
            const result = await showQueryDialog(
                "",
                `患者预约时间（${TimeUtils.formatDate(therapyRegistration?.reserveDate)}）不是今天。签到后会更改预约时间，是否确定`
            );
            if (result != DialogIndex.positive) {
                return;
            }
        }

        const rsp = await TherapyAppointmentAgent.therapyAppointmentSignIn(detail.id).catchIgnore();

        if (rsp) {
            this.innerState.detail = rsp;
            this.innerState.type = TherapyAppointmentInvoiceType.existence;
            this.innerState.hasChange = false;
            if (this.innerState.hasCharged) {
                Toast.show("签到成功", { success: true }).then(() => {
                    ABCNavigator.pop();
                });
                TherapyAppointmentAgent.changeObserver.next();
            }
        }
    }

    /**
     * 完成预约 (效验:姓名、性别、年龄、电话、职业、身份证、详细地址、来源)
     * @private
     */
    private async *_mapEventFinishTherapyAppointment(): AsyncGenerator<State> {
        const detail = this.innerState.detail; // 理疗预约详情单
        if (!detail) return;
        // 内容完整性校验
        if (!detail.patient || !detail.patient.name) {
            // 无患者
            this.innerState.focusKey = FocusItemKeys.patient;
            this.innerState.showErrorHint = true;
            yield ScrollToErrorViewState.fromState(this.innerState);
            return;
        } else if (!detail.therapyRegistration?.doctorId) {
            this.innerState.focusKey = FocusItemKeys.doctorId;
            this.innerState.showErrorHint = true;
            yield ScrollToErrorViewState.fromState(this.innerState);
            return;
        } else if (!detail.therapyRegistration.reserveDate) {
            this.innerState.focusKey = FocusItemKeys.reserveDate;
            this.innerState.showErrorHint = true;
            yield ScrollToErrorViewState.fromState(this.innerState);
            return;
        }
        const req = JsonMapper.deserialize(TherapyDetailReq, {
            reserveDate: detail?.therapyRegistration.reserveDate, // 预约日期 (-年 -月 -日)
            doctorId: detail.therapyRegistration.doctorId, // 理疗师 ID
            doctorName: detail.therapyRegistration.doctorName, // 理疗师 姓名
            orderNos: detail.therapyRegistration.orderNos, // 签到号数 时段
            patient: detail.patient,
            remark: detail.therapyRegistration?.remark,
        });

        const dialog = new LoadingDialog();
        dialog.show(300);
        const rsp = await TherapyAppointmentAgent.createTherapySheet(req).catch((e) => new ABCError(e));
        await dialog.hide();
        if (rsp instanceof ABCError) {
            await showQueryDialog("预约失败", errorToStr(rsp));
        } else if (rsp) {
            this.innerState.detail = rsp;
            this.innerState.type = TherapyAppointmentInvoiceType.existence;
            this.innerState.hasChange = false;
            await TherapySuccessDialog.show({ detail: this.innerState.detail });
            ABCNavigator.pop();
            yield this.innerState.clone();
        }
    }

    private async *_mapEventModifyRemark(): AsyncGenerator<State> {
        const result = await showBottomPanel<string>(
            <TherapyRemarkInputPage remark={this.innerState.detail?.therapyRegistration?.remark} />,
            {
                topMaskHeight: Sizes.dp160,
            }
        );
        if (_.isUndefined(result)) return;
        if (this.innerState.detail?.therapyRegistration) {
            this.innerState.detail.therapyRegistration.remark = result;
            yield this.innerState;
        }
    }

    /**
     * 修改预约患者信息
     */
    public _requestModifyAppointmentPatient(patient: Patient): void {
        this.dispatch(new _EventModifyAppointmentPatient(patient));
    }

    /**
     * 修改预约理疗师
     */
    public requestModifyAppointmentDoctor(): void {
        this.dispatch(new _EventModifyAppointmentDoctor());
    }

    /**
     * 修改预约时间
     */
    public requestModifyAppointmentTime(): void {
        this.dispatch(new _EventModifyAppointmentTime());
    }

    /**
     * 完成预约
     */
    public requestFinishTherapyAppointment(): void {
        this.dispatch(new _EventFinishTherapyAppointment());
    }

    /**
     * 理疗预约 （退号）
     */
    public requestCancelPatientAppointment(): void {
        this.dispatch(new _EventCancelPatientAppointment());
    }

    /**
     * 保存预约单修改信息
     */
    public requestSaveAppointment(): void {
        this.dispatch(new _EventSaveAppointment());
    }

    /**
     * 修改预诊信息
     * @param medicalRecord
     */
    public requestModifyMedicalRecord(medicalRecord: MedicalRecord): void {
        this.dispatch(new _EventModifyMedicalRecordCard(medicalRecord));
    }

    /**
     * 预约签到
     */
    public requestSignInAppointment(): void {
        this.dispatch(new _EventSignInAppointment());
    }
    public requestModifyRemark(): void {
        this.dispatch(new _EventModifyRemark());
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}
// 修改预约患者信息
class _EventModifyAppointmentPatient extends _Event {
    patient: Patient;

    constructor(patient: Patient) {
        super();
        this.patient = patient;
    }
}
// 修改预约理疗师
class _EventModifyAppointmentDoctor extends _Event {}
// 修改预约时间
class _EventModifyAppointmentTime extends _Event {}
// 完成预约
class _EventFinishTherapyAppointment extends _Event {}
// 取消预约（退号）
class _EventCancelPatientAppointment extends _Event {}
// 保存预约单修改记录
class _EventSaveAppointment extends _Event {}
// 修改病历卡
class _EventModifyMedicalRecordCard extends _Event {
    medicalRecord: MedicalRecord;

    constructor(medicalRecord: MedicalRecord) {
        super();
        this.medicalRecord = medicalRecord;
    }
}
// 理疗预约签到
class _EventSignInAppointment extends _Event {}
class _EventModifyRemark extends _Event {}
