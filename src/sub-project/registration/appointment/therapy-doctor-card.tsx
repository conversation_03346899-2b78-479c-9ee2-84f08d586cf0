/**
 * Create By <PERSON><PERSON><PERSON><PERSON>
 * Desc: 理疗预约医生信息卡片(理疗师-时间（MM-DD 15:00 ~ 16:00 下午 3号）)
 * Create Date 2021/01/20
 */
import React from "react";
import { BaseComponent } from "../../base-ui/base-component";
import { FocusItemKeys } from "../data/bean";
import { View, Text } from "@hippy/react";
import { TimeUtils } from "../../common-base-module/utils";
import { ListSettingItem, ListSettingItemStyle } from "../../base-ui/views/list-setting-item";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { IconFontView, SizedBox } from "../../base-ui";
import { TherapyRegistration, TherapyStatus } from "./data/appointment-bean";
import { UIUtils } from "../../base-ui/utils";
import { OverlayTips } from "../../base-ui/dialog/overlay-tips";
import { AbcView } from "../../base-ui/views/abc-view";

interface TherapyDoctorCardProps {
    isEditing?: boolean;
    showErrorHint?: boolean;
    therapyRegistration?: TherapyRegistration;

    onChangeType?(index: number): void;

    onChangeDepartment?(): void;

    onChangeDoctor?(): void;

    onChangeTime?(): void;
    onModifyRemark?(): void;
}

export class TherapyDoctorCard extends BaseComponent<TherapyDoctorCardProps> {
    private _iconViewRef?: View | null;

    constructor(props: TherapyDoctorCardProps) {
        super(props);
    }

    private _formatReserveInfoType(reason?: number): string {
        let str = "";
        switch (reason) {
            case 1:
                str = "提前签到";
                break;
            case 2:
                str = "延后签到";
                break;
            case 3:
                str = "提前理疗";
                break;
            case 4:
                str = "延后理疗";
                break;
        }
        return str;
    }

    async _showTip(): Promise<void> {
        const therapyInfo = this.props.therapyRegistration;
        const oldSimpleInfo = this.props.therapyRegistration!.oldSimpleInfo!;
        if (!oldSimpleInfo) return;
        const layout = await UIUtils.measureInWindow(this._iconViewRef);
        OverlayTips.show(
            `预计理疗时间：${oldSimpleInfo.reserveDate} ${oldSimpleInfo.reserveStart} \n实际理疗时间：${TimeUtils.formatDate(
                therapyInfo?.reserveDate
            )} ${therapyInfo?.reserveStart}`,
            layout,
            {
                preferPosition: "center",
                backgroundColor: Colors.black,
                textStyle: { ...TextStyles.t14NW, paddingHorizontal: Sizes.dp10 },
                arrowSize: Sizes.dp10,
                borderRadius: Sizes.dp4,
            }
        ).then();
    }

    render(): JSX.Element {
        const { isEditing } = this.props;
        return <View>{isEditing ? this._renderCanEditCard() : this._renderNotEditCard()}</View>;
    }
    /**
     * 渲染不编辑卡的理疗预约卡
     */
    private _renderNotEditCard(): JSX.Element {
        const { therapyRegistration } = this.props;
        let text = "";
        let textStyle = TextStyles.t12NT4.copyWith({ lineHeight: Sizes.dp24 }); // 已退、已签、 默认为T3
        let borderColor = textStyle.color;
        // 已预约
        if (therapyRegistration?.status == TherapyStatus.reserved) {
            textStyle = textStyle.copyWith({ color: Colors.B1 });
            text = "已预约";
            borderColor = Colors.B1;
            // 待签到
        } else if (therapyRegistration?.status == TherapyStatus.waitingSignIn) {
            textStyle = textStyle.copyWith({ color: Colors.Y2 });
            text = "待签到";
            borderColor = Colors.Y2;
        } else if (therapyRegistration?.status == TherapyStatus.signed) {
            textStyle = textStyle.copyWith({ color: Colors.T3 });
            text = "已签到";
            borderColor = Colors.T3;
        } else if (therapyRegistration?.status == TherapyStatus.canceled) {
            textStyle = textStyle.copyWith({ color: Colors.T3 });
            text = "已退";
            borderColor = Colors.T3;
        }
        const defaultStyle = TextStyles.t18MT2; // 默认预约卡片样式
        const time = therapyRegistration?.reserveDate; // 预约日期 (-年 -月 -日)

        return (
            <View style={[ABCStyles.bottomLine]}>
                <View style={[ABCStyles.centerChild, Sizes.paddingLTRB(Sizes.dp16), { backgroundColor: Colors.white }]}>
                    <Text style={defaultStyle}>{therapyRegistration?.doctorName ?? ""}</Text>
                    <Text style={defaultStyle}>{`${TimeUtils.formatDate(time, "MM-dd")} ${therapyRegistration?.reserveStart ?? ""} ~ ${
                        therapyRegistration?.reserveEnd ?? ""
                    }`}</Text>

                    <Text style={defaultStyle}>{therapyRegistration?.displayOrderNo ?? ""}</Text>
                </View>

                <View style={[ABCStyles.centerChild, { position: "absolute", right: Sizes.dp12, top: Sizes.dp14 }]}>
                    {therapyRegistration?.oldSimpleInfo && (
                        <View
                            ref={(ref) => {
                                this._iconViewRef = ref;
                            }}
                        >
                            <AbcView
                                style={{ flexDirection: "row" }}
                                onClick={() => {
                                    this._showTip().then();
                                }}
                            >
                                <Text style={[TextStyles.t14NT2, { alignSelf: "center" }]}>
                                    {this._formatReserveInfoType(therapyRegistration.oldSimpleInfo.reason)}
                                </Text>
                                <IconFontView
                                    name={"information"}
                                    size={Sizes.dp16}
                                    color={Colors.T2}
                                    style={{ ...Sizes.paddingLTRB(Sizes.dp4, 0) }}
                                />
                            </AbcView>
                        </View>
                    )}

                    {text.length !== 0 && (
                        <Text
                            style={[
                                textStyle,
                                Sizes.paddingLTRB(Sizes.dp6, 0),
                                Sizes.marginLTRB(0, Sizes.dp2),
                                {
                                    borderWidth: Sizes.dp1,
                                    borderRadius: Sizes.dp12,
                                    borderColor: borderColor,
                                    textAlign: "center",
                                    lineHeight: Sizes.dp20,
                                },
                            ]}
                        >
                            {text}
                        </Text>
                    )}
                </View>

                <SizedBox height={Sizes.dp8} />
            </View>
        );
    }

    /**
     * 新增理疗预约卡(理疗师、预约时间、预约号)
     * @private
     */
    private _renderCanEditCard(): JSX.Element {
        const { therapyRegistration, showErrorHint, onChangeDoctor, onChangeTime, onModifyRemark } = this.props;
        const time = therapyRegistration?.reserveDate; // 选择预约排班日期

        const timeDisplay = time
            ? `${TimeUtils.formatDate(time, "MM-dd" ?? "")} ${therapyRegistration?.reserveTime?.start} ~ ${
                  therapyRegistration?.reserveTime?.end
              } ${therapyRegistration?.timeOfDay} ${therapyRegistration?.orderNos?.[0].orderNo}号`
            : "";

        const itemStyle = ListSettingItemStyle.expandIcon;
        return (
            <View style={[ABCStyles.bottomLine, { backgroundColor: Colors.white }]}>
                <View style={[{ paddingLeft: Sizes.dp16 }]}>
                    <ListSettingItem
                        style={{ paddingRight: Sizes.dp16 }}
                        itemStyle={itemStyle}
                        bottomLine={true}
                        title={"理疗师"}
                        contentHint={"选择理疗师"}
                        content={therapyRegistration?.doctorName}
                        contentStyle={[
                            ABCStyles.rowAlignCenter,
                            {
                                paddingVertical: Sizes.dp14,
                                justifyContent: "flex-end",
                            },
                        ]}
                        onClick={() => onChangeDoctor?.()}
                    />
                </View>
                <View style={[showErrorHint && !timeDisplay ? ABCStyles.errorBorder : {}, { paddingLeft: Sizes.dp16 }]}>
                    <ListSettingItem
                        ref={FocusItemKeys.reserveDate}
                        style={{ paddingRight: Sizes.dp16 }}
                        itemStyle={itemStyle}
                        bottomLine={true}
                        title={"时间"}
                        contentHint={"选择时间"}
                        content={timeDisplay}
                        contentStyle={[
                            ABCStyles.rowAlignCenter,
                            {
                                paddingVertical: Sizes.dp14,
                                justifyContent: "flex-end",
                            },
                        ]}
                        onClick={() => onChangeTime?.()}
                    />
                </View>
                <View style={[{ paddingLeft: Sizes.dp16 }]}>
                    <ListSettingItem
                        style={{ paddingRight: Sizes.dp16 }}
                        itemStyle={itemStyle}
                        title={"备注"}
                        contentHint={"本次就诊备注"}
                        content={therapyRegistration?.remark ?? ""}
                        contentStyle={[
                            ABCStyles.rowAlignCenter,
                            {
                                paddingVertical: Sizes.dp14,
                                justifyContent: "flex-end",
                            },
                        ]}
                        onClick={() => onModifyRemark?.()}
                    />
                </View>
            </View>
        );
    }
}
