import { BaseBlocNetworkPage } from "../../base-ui/base-page";
import { ScrollToErrorViewState, TherapyAppointmentInvoicePageBloc } from "./therapy-appointment-invoice-page-bloc";
import BlocBuilder from "../../bloc/bloc-builder";
import { <PERSON><PERSON>elper } from "../../bloc/bloc-helper";
import { ScrollView, Text, View } from "@hippy/react";
import { FocusItemKeys } from "../data/bean";
import { SizedBox, ToolBar, ToolBarButtonStyle1, ToolBarButtonStyle2, UniqueKey } from "../../base-ui";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import React from "react";
import { PatientInfoViewWithModifyAndHistory } from "../../views/patient-info-view";
import { TherapyDoctorCard } from "./therapy-doctor-card";
import { userCenter } from "../../user-center";
import { HistoryPermissionModuleType } from "../../base-business/data/beans";

const kFirstChild = "AppointmentInvoicePage.firstChild";
const kLastChild = "AppointmentInvoicePage.lastChild";

interface AppointmentOldSimpleInfo {}

interface AppointmentInvoicePageProps {
    id?: string;
    oldSimpleInfo?: AppointmentOldSimpleInfo;
}

export class AppointmentInvoicePage extends BaseBlocNetworkPage<AppointmentInvoicePageProps, TherapyAppointmentInvoicePageBloc> {
    private _scrollView?: ScrollView | null;

    constructor(props: AppointmentInvoicePageProps) {
        super(props);
        this.bloc = new TherapyAppointmentInvoicePageBloc({ id: props.id });
    }

    getAppBarTitle(): string {
        return this.bloc.currentState.isCreate ? "新增预约" : "预约单";
    }

    getAppBar(): JSX.Element | undefined {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }

    componentDidMount(): void {
        BlocHelper.connectLoadingStatus(this.bloc, this);

        this.bloc.state.subscribe((state) => {
            if (state instanceof ScrollToErrorViewState) {
                this._scrollView?.scrollChildToVisible(state.focusKey, kFirstChild, kLastChild);
            }
        });
    }
    renderContent(): JSX.Element | undefined {
        const state = this.bloc.currentState,
            detail = state.detail,
            showErrorHint = state.showErrorHint;
        return (
            <View style={{ flex: 1 }}>
                <ScrollView
                    style={{ flex: 1 }}
                    showsVerticalScrollIndicator={false}
                    ref={(ref) => {
                        this._scrollView = ref;
                    }}
                >
                    <View ref={kFirstChild} collapsable={false} />
                    {this._renderPatientView()}
                    <TherapyDoctorCard
                        isEditing={state.isCreate}
                        showErrorHint={showErrorHint}
                        therapyRegistration={detail?.therapyRegistration}
                        onChangeDoctor={() => {
                            this.bloc.requestModifyAppointmentDoctor(); // 选择理疗师
                        }}
                        onChangeTime={() => {
                            this.bloc.requestModifyAppointmentTime(); // 选择预约理疗师时间
                        }}
                        onModifyRemark={() => {
                            this.bloc.requestModifyRemark(); // 修改备注
                        }}
                    />

                    <View ref={kLastChild} collapsable={false} />
                </ScrollView>
                {this._renderBottomGroup()}
            </View>
        );
    }

    private _renderPatientView(): JSX.Element {
        const state = this.bloc.currentState,
            config = state.employeeConfig,
            detail = state.detail,
            showErrorHint = state.showErrorHint,
            patient = detail?.patient,
            therapyRegistration = detail?.therapyRegistration;

        const regShowView: JSX.Element[] = [];
        if (!config?.therapyHiddenIdCard) {
            regShowView.push(
                <Text key={"idCard"} style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp22 })]}>
                    {patient?.idCard?.length ? patient?.idCard : "暂无身份证号"}
                </Text>
            );
        }
        if (!config?.therapyHiddenSource) {
            regShowView.push(
                <Text key={"source"} style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp22 })]}>
                    {patient?.patientSource?.sourceDisplay.length ? patient?.patientSource?.sourceDisplay : "暂无来源"}
                </Text>
            );
        }
        if (!config?.therapyHiddenAddress) {
            regShowView.push(
                <Text key={"address"} style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp22 })]}>
                    {patient?.address?.addressDisplay.length ? patient?.address?.addressDisplay : "暂无详细地址"}
                </Text>
            );
        }
        if (!config?.therapyHiddenProfession) {
            regShowView.push(
                <Text key={""} style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp22 })]}>
                    {patient?.profession ? patient?.profession : "暂无职业"}
                </Text>
            );
        }
        if (!config?.therapyHiddenSn) {
            regShowView.push(
                <Text key={"sn"} style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp22 })]}>
                    {patient?.sn ?? "暂无档案号"}
                </Text>
            );
        }
        if (!!therapyRegistration?.remark) {
            regShowView.push(
                <Text
                    key={"therapy-remark"}
                    style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp22 }), { flexShrink: 1 }]}
                    numberOfLines={1}
                >
                    {`备注:${therapyRegistration?.remark}`}
                </Text>
            );
        }
        return (
            <View ref={FocusItemKeys.patient}>
                <View style={[showErrorHint && !patient ? ABCStyles.errorBorder : {}]}>
                    <PatientInfoViewWithModifyAndHistory
                        patient={detail?.patient}
                        hintSex={"男"}
                        bottomLine={false}
                        patientSwitchable={state.isCreate}
                        showPatientSource={true}
                        showIDInfo={true}
                        showAddressInfo={true}
                        showSN={true}
                        copyEnable={false}
                        showAll={true}
                        requirePatientSource={userCenter.clinicConfig?.regsRequiredSource ?? false} // 患者来源是否必填
                        requireMobile={userCenter.clinicConfig?.regsRequiredMobile ?? false} // 手机号是否必填
                        onChanged={(patient) => {
                            this.bloc._requestModifyAppointmentPatient(patient); // 预约患者信息详情
                        }}
                        showHistoryBtn={state.canViewDiagnoseHistory}
                        type={HistoryPermissionModuleType.registration}
                        canSeePatientMobileInRegister={state.canSeePatientPhone}
                    />
                    {!!patient && !!regShowView.length && (
                        <View style={[{ backgroundColor: Colors.white }, Sizes.paddingLTRB(Sizes.dp16, Sizes.dp8)]}>{regShowView}</View>
                    )}
                </View>
                <SizedBox height={Sizes.dp8} />
            </View>
        );
    }

    private _renderBottomGroup(): JSX.Element {
        const state = this.bloc.currentState;

        const buttons: JSX.Element[] = [];

        if (state) {
            if (!state.isCreate && !state.isCanCanceled) {
                buttons.push(
                    <ToolBarButtonStyle2
                        style={{ width: state.isCanSignIn ? Sizes.dp78 : undefined }}
                        key={UniqueKey()}
                        text={"退号"}
                        onClick={() => {
                            this.bloc.requestCancelPatientAppointment();
                        }}
                    />
                );
            }
        }

        if (state) {
            if (state.isCreate) {
                buttons.push(
                    <ToolBarButtonStyle1 key={UniqueKey()} text={"完成预约"} onClick={() => this.bloc.requestFinishTherapyAppointment()} />
                );
            } else if (state.isCanSignIn) {
                buttons.push(<ToolBarButtonStyle1 key={UniqueKey()} text={"签到"} onClick={() => this.bloc.requestSignInAppointment()} />);
            }
        }

        if (buttons.length !== 0) {
            return <ToolBar>{buttons}</ToolBar>;
        }
        return <View />;
    }
}
