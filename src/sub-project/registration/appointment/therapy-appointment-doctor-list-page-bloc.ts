/**
 * create by <PERSON><PERSON><PERSON><PERSON><PERSON>
 * desc:
 * create date 2021/1/28
 */

import React from "react";
import _ from "lodash";
import { of, Subject } from "rxjs";
import { debounce, switchMap } from "rxjs/operators";
import { BaseLoadingState } from "../../bloc/bloc-helper";
import {
    TherapyAppointmentOrderNo,
    TherapyDoctorShiftsListItem,
    TherapyDoctorShiftsWithTime,
    TherapyDoctorsInfo,
    TherapyRegistration,
    TherapyShiftCells,
} from "./data/appointment-bean";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import { TimeUtils } from "../../common-base-module/utils";
import { Bloc, BlocEvent } from "../../bloc";
import { TherapyAppointmentAgent } from "./data/therapy-appointment-agent";
import { delayed } from "../../common-base-module/rxjs-ext/rxjs-ext";
import { RegistrationDoctorSearchPage, RegistrationDoctorSearchPageType } from "../doctor-search/registration-doctor-search-page";
import { ABCError } from "../../common-base-module/common-error";
import { EventName } from "../../bloc/bloc";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { TherapyDoctorShiftTimeDialog } from "./views/therapy-shift-time-dialog";
import { TherapyDoctorShiftPage } from "./therapy-doctor-shift-page";
import { Range } from "../../base-ui/utils/value-holder";

export class State extends BaseLoadingState {
    timeListLoading = false;
    doctorShiftsList: TherapyDoctorShiftsListItem[] = [];

    therapyRegistration: TherapyRegistration = new TherapyRegistration();
    shiftCells: TherapyShiftCells = new TherapyShiftCells();

    therapyDoctors?: TherapyDoctorsInfo[];

    clone(): State {
        return Object.assign(new State(), this);
    }

    /**
     * 按时间排列的理疗师轮班表
     */
    get doctorShiftsListByTime(): Map<Date, TherapyDoctorShiftsWithTime[]> {
        const therapyDoctorShiftsListByTimeList: Map<Date, TherapyDoctorShiftsWithTime[]> = new Map();
        this.doctorShiftsList?.forEach((therapyDoctorInfo) => {
            therapyDoctorInfo.shifts?.forEach((shift) => {
                const newData = JsonMapper.deserialize(TherapyDoctorShiftsWithTime, _.assign(therapyDoctorInfo, shift));
                if (shift.workingDate && new Date(shift.workingDate).getTime() > TimeUtils.getTodayStart().getTime()) {
                    if (therapyDoctorShiftsListByTimeList.has(shift.workingDate)) {
                        therapyDoctorShiftsListByTimeList.get(shift.workingDate)?.push(newData);
                        therapyDoctorShiftsListByTimeList.get(shift.workingDate)?.sort((item) => {
                            return item.restCountToday ? -1 : 1;
                        });
                        if (this.therapyRegistration.doctorId) {
                            therapyDoctorShiftsListByTimeList.get(shift.workingDate)?.sort((item) => {
                                return item.doctorId == this.therapyRegistration.doctorId ? -1 : 1;
                            });
                        }
                    } else {
                        therapyDoctorShiftsListByTimeList.set(shift.workingDate, [newData]);
                    }
                }
            });
        });
        return therapyDoctorShiftsListByTimeList;
    }
}

/**
 * 理疗预约医生列表 (当前星期 有号、未排班、无号更多号源)
 */
export class TherapyAppointmentDoctorListPageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<TherapyAppointmentDoctorListPageBloc | undefined>(undefined);

    private _loadDoctorShiftsList: Subject<number> = new Subject<number>();
    private _therapyRegistration: TherapyRegistration | undefined;
    private firstLoad = true;

    static fromContext(context: TherapyAppointmentDoctorListPageBloc): TherapyAppointmentDoctorListPageBloc {
        return context;
    }

    constructor(params: { therapyRegistration?: TherapyRegistration }) {
        super();
        this._therapyRegistration = params.therapyRegistration;
        this.innerState.therapyRegistration = params.therapyRegistration ?? new TherapyRegistration();

        this.dispatch(new _EventInit()).then();
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventLoadMoreDoctorShift, this._mapEventLoadMoreDoctorShift); // 加载更多医生排班
        map.set(_EventCheckDoctor, this._mapEventCheckDoctor); // 检查医生排班
        map.set(_EventSearchTherapyDoctor, this._mapEventSearchTherapyDoctor); // 搜索理疗医生
        map.set(_EventShowCurrentDoctorShifts, this._mapEventShowCurrentDoctorShifts); // 显示当前医生轮班

        return map;
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    private async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        this.innerState.loading = true;
        this.update();
        this._loadDoctorShiftsList
            .pipe(
                debounce(() => {
                    if (this.innerState.doctorShiftsList?.length) {
                        return delayed(1000);
                    } else {
                        return of(0);
                    }
                }),
                switchMap(() => {
                    const startTime = _.findLast([...this.innerState.doctorShiftsListByTime.keys()]);
                    return TherapyAppointmentAgent.getTherapyDoctorShiftsList(
                        undefined,
                        startTime ? new Date(new Date(startTime).getTime() + 7 * 24 * 3600 * 1000) : undefined
                    )
                        .catch((e) => new ABCError(e))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (rsp instanceof ABCError) {
                } else {
                    rsp?.map((item) => {
                        const sameIdItem = this.innerState.doctorShiftsList?.find((doctorShifts) => item.doctorId == doctorShifts.doctorId);
                        if (sameIdItem) {
                            sameIdItem.shifts = sameIdItem.shifts?.concat(item.shifts ?? []);
                        } else {
                            this.innerState.doctorShiftsList?.push(item);
                        }
                    });
                    // 置顶当前选中医生
                    if (this.innerState.therapyRegistration.doctorId) {
                        this.innerState.doctorShiftsList.sort((item) =>
                            item.doctorId == this.innerState.therapyRegistration.doctorId ? -1 : 1
                        );
                    }
                    if (this.firstLoad) {
                        this.firstLoad = false;
                        this._loadDoctorShiftsList.next();
                    } else {
                        this.innerState.loading = false;
                    }
                }
                this.innerState.timeListLoading = false;
                this.update();
            })
            .addToDisposableBag(this);
        this._loadDoctorShiftsList.next();
        /**
         * 拉取全部理疗师
         */
        TherapyAppointmentAgent.getAllTherapyDoctorList().then((rsp) => {
            this.innerState.therapyDoctors = rsp;
        });
    }

    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    private async *_mapEventLoadMoreDoctorShift(): AsyncGenerator<State> {
        this.innerState.timeListLoading = true;
        yield this.innerState.clone();
        this._loadDoctorShiftsList.next();
    }

    /**
     * 检查所有理疗师排班情况
     * @param event
     * @private
     */
    private async *_mapEventCheckDoctor(event: _EventCheckDoctor): AsyncGenerator<State> {
        let therapyDoctor = event.doctor;
        if (therapyDoctor.canReserve) {
            // 能预约
            if (therapyDoctor.restCountToday) {
                // 有剩余预约时间 选择到店时间
                therapyDoctor = await TherapyAppointmentAgent.getDesignatedTherapyDoctorShiftsList(
                    therapyDoctor.doctorId,
                    therapyDoctor.workingDate
                ).then((rsp) => {
                    rsp.doctorName = therapyDoctor.doctorName;
                    return rsp;
                });

                const result = await TherapyDoctorShiftTimeDialog.show(therapyDoctor);

                if (result) {
                    const orderNos = result.map((item) => {
                        return {
                            orderNo: item.restOrderNos?.[0],
                            ...item,
                        } as TherapyAppointmentOrderNo;
                    });
                    this.innerState.therapyRegistration.doctorId = therapyDoctor.doctorId;
                    this.innerState.therapyRegistration.doctorName = therapyDoctor.doctorName;
                    this.innerState.therapyRegistration.reserveDate = therapyDoctor.workingDate; // 排班日期
                    this.innerState.therapyRegistration.timeOfDay = result?.[0].timeOfDay; // 排班时段
                    this.innerState.therapyRegistration.orderNos = orderNos; // 预约号数
                    this.innerState.therapyRegistration.reserveTime = new Range<string>(
                        _.first(result)?.reserveStart,
                        _.last(result)?.reserveEnd
                    ); // 服务时长
                    ABCNavigator.pop(this.innerState.therapyRegistration);
                }
            } else if (therapyDoctor.restCountToday == 0) {
                // 无号 - 当前医生的其他号源信息
                this.dispatch(new _EventShowCurrentDoctorShifts(therapyDoctor.doctorId!, therapyDoctor));
            }
        }
    }

    private _searchDoctor = false;

    /**
     * 医生排班 (理疗师名字)
     * @private
     */
    private async *_mapEventSearchTherapyDoctor(/*event: _EventSearchTherapyDoctor*/): AsyncGenerator<State> {
        RegistrationDoctorSearchPage.show({
            popAnimation: false,
            type: RegistrationDoctorSearchPageType.therapy,
            onClickItem: (info: TherapyDoctorsInfo) => {
                if (info) {
                    this._searchDoctor = true;
                    this.dispatch(new _EventShowCurrentDoctorShifts(info.doctorId!, info));
                }
            },
        }).then();
    }

    /**
     * 无号显示当前理疗师全部排班
     * @param event
     * @private
     */
    private async *_mapEventShowCurrentDoctorShifts(event: _EventShowCurrentDoctorShifts): AsyncGenerator<State> {
        const therapyDoctor = event.therapyDoctorInfo;

        const result = await TherapyDoctorShiftPage.show({
            doctorId: event.doctorId,
            doctorName: event.therapyDoctorInfo?.doctorName ?? "",
        });
        if (result) {
            const orderNos = result.result.map((item) => {
                return {
                    orderNo: item.restOrderNos?.[0],
                    ...item,
                } as TherapyAppointmentOrderNo;
            });
            const { doctor, result: shifts } = result;
            this.innerState.therapyRegistration.doctorId = doctor.doctorId;
            this.innerState.therapyRegistration.doctorName = therapyDoctor?.doctorName;
            this.innerState.therapyRegistration.reserveDate = doctor.workingDate;
            this.innerState.therapyRegistration.timeOfDay = shifts?.[0].timeOfDay;
            this.innerState.therapyRegistration.orderNos = orderNos;
            this.innerState.therapyRegistration.reserveTime = new Range<string>(_.first(shifts)?.reserveStart, _.last(shifts)?.reserveEnd);
            if (this._searchDoctor) {
                ABCNavigator.pop(this.innerState.therapyRegistration, false);
            }
            ABCNavigator.pop(this.innerState.therapyRegistration);
        }

        yield this.innerState.clone();
    }

    public requestLoadMoreDoctorShift(): void {
        this.dispatch(new _EventLoadMoreDoctorShift());
    }

    public requestCheckDoctor(doctor: TherapyDoctorShiftsWithTime): void {
        this.dispatch(new _EventCheckDoctor(doctor));
    }

    public requestSearchTherapyDoctor(): void {
        this.dispatch(new _EventSearchTherapyDoctor());
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventLoadMoreDoctorShift extends _Event {}

class _EventCheckDoctor extends _Event {
    doctor: TherapyDoctorShiftsWithTime;

    constructor(doctor: TherapyDoctorShiftsWithTime) {
        super();
        this.doctor = doctor;
    }
}

class _EventSearchTherapyDoctor extends _Event {}

class _EventShowCurrentDoctorShifts extends _Event {
    doctorId: string;
    therapyDoctorInfo?: TherapyDoctorsInfo;

    constructor(doctorId: string, therapyDoctorInfo?: TherapyDoctorsInfo) {
        super();
        this.doctorId = doctorId;
        this.therapyDoctorInfo = therapyDoctorInfo;
    }
}
