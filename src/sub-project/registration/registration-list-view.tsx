/**
 * create by deng<PERSON>e
 * desc:
 * create date 2021/1/6
 */
import React from "react";
import { Text, View } from "@hippy/react";
import { ABCNetworkPageContentStatus, BaseBlocNetworkView } from "../base-ui/base-page";
import { RegistrationListViewBloc } from "./registration-list-view-bloc";
import { Filters } from "../base-ui/searchBar/search-bar-bean";
import { ABCUtils } from "../base-ui/utils/utils";
import { AbcListView } from "../base-ui/list/abc-list-view";
import { UniqueKey } from "../base-ui";
import _ from "lodash";
import { DataHolder } from "../views/list-data-holder";
import { RegistrationDetail, RegistrationStatusV2, RegistrationTabType } from "./data/bean";
import { PatientListItemStatusTextStyle, PatientListItemView } from "../outpatient/views/patient-list-item-view";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { Sizes, TextStyles } from "../theme";
import { ABCEmptyView } from "../base-ui/views/empty-view";
import { TimeUtils } from "../common-base-module/utils";
import { AppointmentInvoicePage } from "./appointment/appointment-invoice-page";
import { TherapyAppointmentSheet } from "./appointment/data/appointment-bean";
import { StringUtils } from "../base-ui/utils/string-utils";
import { RegistrationInvoiceSummaryDialog } from "./registration-invoice-summary-dialog";
import { RegistrationPatientListItemView } from "./dentistry/dentistry-list-view";
import { userCenter } from "../user-center";

interface RegistrationListViewProps {
    type?: number;
    searchMode?: boolean;
}

export class RegistrationListView extends BaseBlocNetworkView<RegistrationListViewProps, RegistrationListViewBloc> {
    constructor(props: RegistrationListViewProps) {
        super(props);
        this.bloc = new RegistrationListViewBloc({ type: props.type, searchMode: props.searchMode });
    }

    componentDidMount(): void {
        super.componentDidMount();

        this.bloc.state
            .subscribe((state) => {
                let status = ABCNetworkPageContentStatus.show_data;
                if (state.loading) {
                    if (state.dataList.length) {
                    } else {
                        status = ABCNetworkPageContentStatus.loading;
                    }
                } else if (state.loadError) {
                    status = ABCNetworkPageContentStatus.error;
                } else if (_.isEmpty(state.dataList) && _.isEmpty(state.kanbanDetail)) {
                    status = ABCNetworkPageContentStatus.empty;
                }

                this.setContentStatus(status, state.loadError);
            })
            .addToDisposableBag(this);
    }

    public changeFilter(filter: Filters): void {
        this.bloc.requestChangeFilter(filter);
    }

    public changeViewMode(isKanban: boolean): void {
        this.bloc.requestChangeViewMode(isKanban);
    }

    public searchByKeyword(keyword: string): void {
        this.bloc.requestSearchPatient(keyword);
    }

    reloadData(): void {
        this.bloc.requestReload();
    }

    emptyContent(): JSX.Element {
        if (this.props.type == RegistrationTabType.registration) {
            return <ABCEmptyView tips={"暂无挂号/预约记录"} />;
        } else {
            return <ABCEmptyView tips={"暂无理疗预约记录"} />;
        }
    }

    renderContent(): JSX.Element {
        const state = this.bloc.currentState;
        if (ABCUtils.isEmpty(state.dataList)) return <View />;
        const itemCount = state.dataList.length ?? 0;
        const _dataSource = state.dataList.map((item) =>
            this.props.type == RegistrationTabType.registration
                ? new _RegistrationListItemHolder(item, this.props.searchMode)
                : new _AppointmentRegistrationListItemHolder(item, this.props.searchMode)
        );
        return (
            <View style={{ flex: 1 }}>
                <AbcListView
                    loading={state.loading}
                    scrollEventThrottle={300}
                    initialListSize={20}
                    preloadItemNumber={7}
                    getRowKey={(index) => `${state.dataList[index].id ?? state.dataList[index].registrationId ?? UniqueKey()} `}
                    renderRow={(item) => item.createView() ?? <View />}
                    numberOfRows={itemCount}
                    dataSource={_dataSource}
                    finished={!state.hasMore}
                    onRefresh={() => {
                        this.bloc.requestReload();
                    }}
                    onEndReached={() => {
                        this.bloc.requestLoadMore();
                    }}
                />
            </View>
        );
    }
}

class _RegistrationListItemHolder extends DataHolder<RegistrationDetail> {
    private _searchMode?: boolean;

    constructor(props: RegistrationDetail, searchMode?: boolean) {
        super(props);
        this._searchMode = searchMode;
    }

    createView(): JSX.Element {
        const itemInfo = this.data?.registrationFormItem;
        const statusName = itemInfo?.statusName;
        let statusTextStyle = PatientListItemStatusTextStyle.visited;
        switch (itemInfo?.statusV2) {
            case RegistrationStatusV2.waitingSignIn:
                statusTextStyle = PatientListItemStatusTextStyle.Style4;
                break;
            case RegistrationStatusV2.waitingDiagnose:
            case RegistrationStatusV2.continueDiagnose:
                statusTextStyle = PatientListItemStatusTextStyle.draft;
                break;
            case RegistrationStatusV2.diagnosed:
            case RegistrationStatusV2.expired:
            case RegistrationStatusV2.refunded:
            case RegistrationStatusV2.canceled:
                statusTextStyle = PatientListItemStatusTextStyle.visited;
                break;
        }

        // 医助能查看医助的医生 显示姓名、号数 ，没有选择医生不做显示
        // 医生查看自己接诊列表 仅显示时间号数
        // 管理员可查看所有医生姓名
        let timeOverride;
        const doctorName = itemInfo?.doctorName?.length ? itemInfo?.doctorName : "--";
        const departmentName: string = itemInfo?.departmentName?.length ? itemInfo?.departmentName : "其他";

        timeOverride = itemInfo?.orderNoStr;

        //TODO 灵活模式显示预约时间 timeOverride
        if (userCenter.clinic?.isDentistryClinic && userCenter.dentistryConfig?.isFlexibleMode) {
            timeOverride = `${itemInfo?.reserveStart ?? ""}`;
        }

        if (userCenter.dentistryConfig?.isSignInGetOrderNo && (itemInfo?.statusV2 ?? 0) < RegistrationStatusV2.waitingSignIn) {
            timeOverride = "";
        }

        return (
            <RegistrationPatientListItemView
                patient={this.data?.patient}
                extraIconBeforeTime={() => {
                    return !!doctorName ? (
                        <View style={{ flexDirection: "row", alignItems: "center", flexShrink: 1, marginRight: Sizes.dp14 }}>
                            <Text style={[TextStyles.t14NT1, { flexShrink: 1 }]} numberOfLines={1}>
                                {!!departmentName?.length ? departmentName : ""}
                            </Text>
                            <Text style={[TextStyles.t14NT1, { flexShrink: 1 }]} numberOfLines={1}>
                                {`${departmentName?.length ? "/" : ""}${doctorName ?? ""}`}
                            </Text>
                        </View>
                    ) : (
                        <View />
                    );
                }}
                time={itemInfo?.created}
                timeOverride={timeOverride}
                timeStyle={TextStyles.t14NT1}
                status={statusName}
                statusTextStyle={statusTextStyle}
                isReserved={itemInfo?.isReserved}
                onClick={() => {
                    RegistrationInvoiceSummaryDialog.show({
                        id: this.data?.id,
                        doctorId: itemInfo?.doctorId,
                        oldReserveInfo: this.data?.registrationFormItem?.oldReserveInfo,
                        statusName: statusName,
                        isUpgradeBloc: false,
                    });
                }}
                anonymousName={"匿名患者"}
                isHasAbnormal={this.data?.isSheBaoAbnormal || this.data?.isNotSheBaoAbnormal}
                isReferral={this.data?.registrationFormItem?.isReferral}
            />
        );
    }
}

class _AppointmentRegistrationListItemHolder extends DataHolder<TherapyAppointmentSheet> {
    private _searchMode?: boolean;

    constructor(props: TherapyAppointmentSheet, searchMode?: boolean) {
        super(props);
        this._searchMode = searchMode;
    }

    createView(): JSX.Element {
        const itemInfo = this.data?.therapyRegistration;
        let statusName = itemInfo?.statusName,
            statusTextStyle = PatientListItemStatusTextStyle.visited;
        if (!statusName) {
            switch (itemInfo?.status) {
                case 0:
                    statusName = "已预约";
                    statusTextStyle = PatientListItemStatusTextStyle.draft;
                    break;
                case 10:
                    statusName = "待签";
                    statusTextStyle = PatientListItemStatusTextStyle.Style4;
                    break;
                case 20:
                    statusName = "已签";
                    break;
                case 91:
                    statusName = "已退";
                    break;
            }
        }
        return (
            <PatientListItemView
                patient={this.data?.patient}
                status={statusName}
                anonymousName={"匿名患者"}
                statusTextStyle={statusTextStyle}
                extraIconBeforeTime={() => (
                    <Text style={[TextStyles.t12NT1, { flexShrink: 1, marginRight: Sizes.dp8 }]} numberOfLines={1}>
                        {itemInfo?.doctorName?.length ? itemInfo?.doctorName : "不指定理疗师"}
                    </Text>
                )}
                timeStyle={TextStyles.t12NT1}
                timeOverride={
                    !this._searchMode
                        ? `${itemInfo?.orderNos?.[0]?.timeOfDay} ${StringUtils.zeroFill(_.first(itemInfo!.orderNos)!.orderNo!, 2)}`
                        : TimeUtils.formatDatetimeAsRecent(itemInfo?.reserveDate, { time: false }) ?? ""
                }
                time={itemInfo?.created}
                onClick={() => {
                    ABCNavigator.navigateToPage(
                        <AppointmentInvoicePage
                            id={this.data?.registrationId}
                            oldSimpleInfo={this.data?.therapyRegistration?.oldSimpleInfo}
                        />
                    ).then();
                }}
            />
        );
    }
}
