/**
 * create by dengjie
 * desc:
 * create date 2021/1/6
 */

import React from "react";
import { View } from "@hippy/react";
import { BasePage } from "../base-ui";
import { RegistrationListViewBloc } from "./registration-list-view-bloc";
import { AppSearchBar } from "../base-ui/app-bar";
import { RegistrationListView } from "./registration-list-view";
import { Color, Colors } from "../theme";
import { RegistrationTabType } from "./data/bean";
import { GetRegistrationListRsp, RegistrationAgent } from "./data/registration-agent";
import { TherapyAppointmentAgent } from "./appointment/data/therapy-appointment-agent";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { RegistrationInvoicePage } from "./registration-invoice-page";
import { AppointmentInvoicePage } from "./dentistry/appointment/appointment-invoice-page";

interface RegistrationSearchPageProps {
    type: RegistrationTabType;
}

export class RegistrationSearchPage extends BasePage<RegistrationSearchPageProps, RegistrationListViewBloc> {
    private _registrationListViewRef?: RegistrationListView | null;

    constructor(props: RegistrationSearchPageProps) {
        super(props);
    }

    pageName(): string | undefined {
        return "搜索患者";
    }

    _jumpRelativePage(rsp?: GetRegistrationListRsp): void {
        const data = rsp?.rows;
        if (data && data?.length == 1) {
            const itemInfo = data[0]?.registrationFormItem;
            if (this.props.type == RegistrationTabType.registration) {
                ABCNavigator.navigateToPage(
                    <RegistrationInvoicePage
                        id={data?.[0]?.id}
                        doctorId={itemInfo?.doctorId}
                        oldReserveInfo={data?.[0]?.registrationFormItem?.oldReserveInfo}
                    />
                ).then();
            } else {
                ABCNavigator.navigateToPage(
                    <AppointmentInvoicePage id={data?.[0]?.registrationId} oldSimpleInfo={data[0]?.therapyRegistration?.oldSimpleInfo} />
                ).then();
            }
        } else {
            this._registrationListViewRef?.searchByKeyword(rsp?.keyword ?? "");
        }
    }

    getAppBar(): JSX.Element {
        return (
            <AppSearchBar
                placeholder={"输入姓名/手机号"}
                rightPart={this.getRightAppBarIcons()}
                onBackClick={this.onBackClick.bind(this)}
                autoFocus={true}
                onChangeText={(value) => {
                    this._registrationListViewRef?.searchByKeyword(value);
                }}
                isNeedAutoFillText={true}
                isShowScanIcon={true}
                qrScanGenerator={
                    this.props.type == RegistrationTabType.registration
                        ? RegistrationAgent.getRegistrationList
                        : TherapyAppointmentAgent.getTherapyPatientList
                }
                qrScanRsp={(rsp) => this._jumpRelativePage(rsp)}
            />
        );
    }

    getBackgroundColor(): Color {
        return Colors.white;
    }

    renderContent(): JSX.Element {
        const { type } = this.props;
        return (
            <View style={{ flex: 1 }}>
                <RegistrationListView ref={(ref) => (this._registrationListViewRef = ref)} type={type} searchMode={true} />
            </View>
        );
    }
}
