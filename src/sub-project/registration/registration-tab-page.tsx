/**
 * create by deng<PERSON>e
 * desc:
 * create date 2021/1/6
 */
import React from "react";
import { BasePage, Tab, Tabs } from "../base-ui";
import { CommonFilterId } from "../base-ui/searchBar/search-bar";
import { FilterItem, Filters } from "../base-ui/searchBar/search-bar-bean";
import { View } from "@hippy/react";
import { ABCStyles, Color, Colors, Sizes, TextStyles } from "../theme";
import IconFontView from "../base-ui/iconfont/iconfont-view";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { RegistrationListView } from "./registration-list-view";
import { RegistrationStatus, RegistrationTabType } from "./data/bean";
import { RegistrationSearchPage } from "./registration-search-page";
import { RegistrationInvoicePage } from "./registration-invoice-page";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { TimeUtils } from "../common-base-module/utils";
import { SearchBarWithDropDownFilter } from "../base-ui/searchBar/filter-view";
import { RegistrationAgent, RegistrationsDoctorListItem } from "./data/registration-agent";
import { FilterUtils } from "../base-ui/searchBar/filter-utils";
import { TherapyAppointmentAgent } from "./appointment/data/therapy-appointment-agent";
import { AppointmentInvoicePage } from "./appointment/appointment-invoice-page";
import { TherapyStatDetail, TherapyStatus } from "./appointment/data/appointment-bean";
import { PatientOrderDataAgent } from "../nurse-station/data/nurse-station-data";
import { runFuncBeforeCheckExpired } from "../views/clinic-edition";
import { DeviceUtils } from "../base-ui/utils/device-utils";
import { delayed } from "../common-base-module/rxjs-ext/rxjs-ext";
import { FilterModifyState } from "./registration-list-view-bloc";

interface RegistrationTabPageProps {
    tabIndex?: number;
}

export class RegistrationTabPage extends BasePage<RegistrationTabPageProps> {
    tabsFilterInfo: Map<RegistrationTabType, Filters> = new Map([
        [RegistrationTabType.registration, RegistrationListFilters()],
        [RegistrationTabType.appointment, TherapyListFilters()],
    ]);
    private currentTabIndex: number;
    private enableAppointment: number; // 理疗预约开关 1为开启 0关闭，（患者可预约理疗师或理疗项目）
    private _listViewRef: Map<number, RegistrationListView> = new Map<number, RegistrationListView>();
    private filterDate: Map<number, Date> = new Map<number, Date>();
    private _tabPage?: Tabs | null;

    get listViewRef(): RegistrationListView | undefined {
        return this._listViewRef.get(this.currentTabIndex);
    }

    static defaultProps = {
        tabIndex: 0,
    };

    constructor(props: RegistrationTabPageProps) {
        super(props);
        this.currentTabIndex = 0;
        this.enableAppointment = 0;
        this.filterDate.set(RegistrationTabType.registration, new Date());
        this.filterDate.set(RegistrationTabType.appointment, new Date());
    }

    pageName(): string | undefined {
        return "挂号预约列表";
    }

    getAppBar(): JSX.Element | undefined {
        return undefined;
    }

    getStatusBarColor(): Color {
        return Colors.mainColor;
    }

    componentDidMount(): void {
        this.modifyDoctorFilterGroup().then();
        if (this.currentTabIndex == RegistrationTabType.registration) {
            //更新医生选中项目
            RegistrationAgent.changeObserver
                .subscribe(() => {
                    const blocState = this.listViewRef?.bloc.currentState;
                    this.modifyDoctorFilterGroup(blocState?.queryParams?.date, blocState?.queryParams.doctorId).then();
                })
                .addToDisposableBag(this);
        }
        // 是否启用理疗预约功能
        PatientOrderDataAgent.getTherapyAppointmentSetting()
            .then((rsp) => {
                this.enableAppointment = rsp.enableAppointment;
                this.setState({});
            })
            .then(() => {
                if (DeviceUtils.isAndroid()) {
                    delayed(70)
                        .subscribe(() => {
                            this._tabPage?.setPage(this.props.tabIndex ?? 0, false);
                            this.setState({});
                        })
                        .addToDisposableBag(this);
                }
            })
            .catchIgnore();
        delayed(2000)
            .subscribe(() => {
                [...this._listViewRef.values()].map((item) => {
                    item.bloc.state
                        .subscribe((state) => {
                            if (state instanceof FilterModifyState) {
                                //更新筛选条件
                                this.modifyDoctorFilterGroup(state.queryParams.date, state.queryParams.doctorId).then();
                            }
                        })
                        .addToDisposableBag(this);
                });
            })
            .addToDisposableBag(this);
    }

    /**
     * 显示导航栏全部医师列表 （医生/理疗师）
     * @param data
     * @param doctorId
     */
    async modifyDoctorFilterGroup(data?: Date, doctorId?: string): Promise<void> {
        let doctorList: RegistrationsDoctorListItem[] | TherapyStatDetail[] | undefined = [];

        if (this.currentTabIndex == RegistrationTabType.appointment) {
            doctorList = await TherapyAppointmentAgent.getTherapyDoctorList(data)
                .then((rsp) => rsp.rows)
                .catch(() => []);
        } else if (this.currentTabIndex == RegistrationTabType.registration) {
            doctorList = await RegistrationAgent.getRegistrationsDoctorList(data)
                .then((rsp) => rsp.rows)
                .catch(() => []);
        }
        const tabFilter = this.tabsFilterInfo.get(this.currentTabIndex);
        const doctorFilter = tabFilter!.filters?.[2];
        doctorFilter.filters = [
            JsonMapper.deserialize(FilterItem, {
                title: this.currentTabIndex == RegistrationTabType.appointment ? "全部理疗师" : "全部医生",
                id: CommonFilterId.others,
                isDefault: true,
                select: !doctorId,
                exclusive: true,
            }),
        ];
        /**
         * 显示导航栏指定的理疗师的预约患者列表（指定理疗师）
         */
        doctorList?.forEach((item: RegistrationsDoctorListItem | TherapyStatDetail | undefined, index: number) => {
            if (item instanceof RegistrationsDoctorListItem) {
                doctorFilter.filters?.push(
                    JsonMapper.deserialize(FilterItem, {
                        title: item.name,
                        id: CommonFilterId.others + index + 1,
                        exclusive: true,
                        info: item,
                        select: item.id == doctorId,
                    })
                );
            } else if (item instanceof TherapyStatDetail) {
                doctorFilter.filters?.push(
                    JsonMapper.deserialize(FilterItem, {
                        title: item.doctorName,
                        id: CommonFilterId.others + index + 1,
                        exclusive: true,
                        info: item,
                        select: item.doctorId == doctorId,
                    })
                );
            }
        });
        tabFilter!.filters![2] = doctorFilter;
        this.forceUpdate();
    }

    @runFuncBeforeCheckExpired()
    createRegistrationInvoice(): void {
        if (this.currentTabIndex == RegistrationTabType.appointment) {
            ABCNavigator.navigateToPage(<AppointmentInvoicePage />);
        } else if (this.currentTabIndex == RegistrationTabType.registration) {
            ABCNavigator.navigateToPage(<RegistrationInvoicePage />).then();
        }
    }

    renderContent(): JSX.Element {
        return (
            <View style={{ position: "relative", flex: 1 }}>
                <Tabs
                    ref={(ref) => {
                        this._tabPage = ref;
                    }}
                    tabsStyle={{
                        justifyContent: "center",
                        paddingHorizontal: Sizes.listHorizontalMargin,
                        backgroundColor: Colors.mainColor,
                        height: Sizes.dp44,
                        ...ABCStyles.bottomLine,
                    }}
                    tabStyle={{
                        marginRight: Sizes.dp28,
                        ...TextStyles.t16NW,
                        color: "rgba(255,255,255,0.7)",
                    }}
                    lineColor={Colors.white}
                    lazy={false}
                    currentStyle={{
                        ...TextStyles.t16MW,
                    }}
                    leftSuffix={() => {
                        return (
                            <IconFontView
                                name={"back"}
                                size={Sizes.dp20}
                                color={Colors.white}
                                style={[
                                    DeviceUtils.isOhos()
                                        ? { marginLeft: Sizes.dp14 }
                                        : Sizes.paddingLTRB(Sizes.dp16, Sizes.dp14, Sizes.dp16, Sizes.dp10),
                                ]}
                                onClick={() => {
                                    ABCNavigator.pop();
                                }}
                            />
                        );
                    }}
                    rightSuffix={() => {
                        return (
                            <IconFontView
                                name={"plus_circle_glyph"}
                                style={[
                                    DeviceUtils.isOhos()
                                        ? { marginRight: Sizes.dp14 }
                                        : Sizes.paddingLTRB(Sizes.dp16, Sizes.dp14, Sizes.dp16, Sizes.dp10),
                                ]}
                                size={Sizes.dp20}
                                color={Colors.white}
                                onClick={() => {
                                    this.createRegistrationInvoice();
                                }}
                            />
                        );
                    }}
                    onChange={(index) => {
                        this.currentTabIndex = index;
                        this.modifyDoctorFilterGroup().then();
                        this.setState({});
                    }}
                >
                    <Tab title={"门诊挂号"}>
                        {this._renderSearchFilterView(RegistrationTabType.registration)}
                        <RegistrationListView
                            ref={(ref) => {
                                if (ref) this._listViewRef.set(RegistrationTabType.registration, ref);
                            }}
                            type={RegistrationTabType.registration}
                        />
                    </Tab>
                    {this.enableAppointment == 1 && (
                        <Tab title={"理疗预约"}>
                            {this._renderSearchFilterView(RegistrationTabType.appointment)}
                            <RegistrationListView
                                ref={(ref) => {
                                    if (ref) this._listViewRef.set(RegistrationTabType.appointment, ref);
                                }}
                                type={RegistrationTabType.appointment}
                            />
                        </Tab>
                    )}
                </Tabs>
            </View>
        );
    }

    /**
     * 页面搜索导航栏
     * @param tabIndex
     * @private
     */
    private _renderSearchFilterView(tabIndex: number): JSX.Element {
        return (
            <SearchBarWithDropDownFilter
                placeholder={"搜索患者"}
                filters={this.tabsFilterInfo.get(tabIndex)}
                onChange={() => {
                    ABCNavigator.navigateToPage(<RegistrationSearchPage type={this.currentTabIndex} />).then();
                }}
                onFilterChange={(filters) => {
                    const _date = filters.filters?.[0].filters?.[0].date ?? FilterUtils.filterId2Time(filters.filters?.[0].filters?.[0].id);
                    if (
                        _date &&
                        TimeUtils.getStartOfDate(this.filterDate.get(tabIndex) ?? new Date()).getTime() !=
                            TimeUtils.getStartOfDate(_date).getTime()
                    ) {
                        this.filterDate.set(tabIndex, _date);
                        this.modifyDoctorFilterGroup(_date).then();
                    }

                    this.listViewRef?.changeFilter(filters);
                }}
                onSwitchMode={(isKanban) => {
                    this.listViewRef?.changeViewMode(isKanban);
                }}
            />
        );
    }
}

const RegistrationListFilters = () =>
    Filters.createFilters([
        {
            id: "time",
            filters: [
                {
                    title: "今天",
                    id: CommonFilterId.timeToday,
                    isDefault: true,
                    select: true,
                    exclusive: true,
                    time: new Date(),
                },
                {
                    title: "明天",
                    id: CommonFilterId.timeTomorrow,
                    exclusive: true,
                    time: TimeUtils.tomorrow(),
                },
                {
                    title: "后天",
                    id: CommonFilterId.timeAfterTomorrow,
                    exclusive: true,
                    time: TimeUtils.afterTomorrow(),
                },
                {
                    title: "指定时间",
                    id: CommonFilterId.time,
                    exclusive: true,
                    maxDate: new Date(2100, 1, 1),
                },
            ],
        },
        {
            id: "status",
            filters: [
                {
                    title: "全部状态",
                    id: CommonFilterId.allStatus,
                    isDefault: true,
                    select: true,
                    exclusive: true,
                },
                {
                    title: "已签",
                    id: CommonFilterId.allStatus + RegistrationStatus.signed,
                    exclusive: true,
                },
                {
                    title: "待签",
                    id: CommonFilterId.allStatus + RegistrationStatus.waitSign,
                    exclusive: true,
                },
                {
                    title: "待诊",
                    id: CommonFilterId.allStatus + RegistrationStatus.wait,
                    exclusive: true,
                },
                {
                    title: "已诊",
                    id: CommonFilterId.allStatus + RegistrationStatus.visited,
                    exclusive: true,
                },
                {
                    title: "已退",
                    id: CommonFilterId.allStatus + RegistrationStatus.refunded,
                    exclusive: true,
                },
            ],
        },
        {
            id: "doctor",
            filters: [
                {
                    title: "全部医生",
                    id: CommonFilterId.others,
                    isDefault: true,
                    select: true,
                    exclusive: true,
                },
            ],
        },
    ]);
const TherapyListFilters = () =>
    Filters.createFilters([
        {
            id: "time",
            filters: [
                {
                    title: "今天",
                    id: CommonFilterId.timeToday,
                    isDefault: true,
                    select: true,
                    exclusive: true,
                    time: new Date(),
                },
                {
                    title: "明天",
                    id: CommonFilterId.timeTomorrow,
                    exclusive: true,
                    time: TimeUtils.tomorrow(),
                },
                {
                    title: "后天",
                    id: CommonFilterId.timeAfterTomorrow,
                    exclusive: true,
                    time: TimeUtils.afterTomorrow(),
                },
                {
                    title: "指定时间",
                    id: CommonFilterId.time,
                    exclusive: true,
                    maxDate: new Date(2100, 1, 1),
                },
            ],
        },
        {
            id: "status",
            filters: [
                {
                    title: "全部状态",
                    id: CommonFilterId.allStatus + TherapyStatus.all,
                    isDefault: true,
                    select: true,
                    exclusive: true,
                },
                {
                    title: "待签",
                    id: CommonFilterId.allStatus + TherapyStatus.waitingSignIn,
                    exclusive: true,
                },
                {
                    title: "已签",
                    id: CommonFilterId.allStatus + TherapyStatus.signed,
                    exclusive: true,
                },
                {
                    title: "已退",
                    id: CommonFilterId.allStatus + TherapyStatus.canceled,
                    exclusive: true,
                },
            ],
        },
        {
            id: "doctor",
            filters: [
                {
                    title: "全部理疗师",
                    id: CommonFilterId.others,
                    isDefault: true,
                    select: true,
                    exclusive: true,
                },
            ],
        },
    ]);
