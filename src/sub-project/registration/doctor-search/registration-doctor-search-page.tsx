/**
 * create by deng<PERSON>e
 * desc:
 * create date 2021/1/18
 */
import React from "react";
import { ListView, Text } from "@hippy/react";
import { BaseBlocNetworkPage } from "../../base-ui/base-page";
import { RegistrationDoctorSearchPageBloc } from "./registration-doctor-search-page-bloc";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { BlocHelper } from "../../bloc/bloc-helper";
import { AppSearchBar } from "../../base-ui/app-bar";
import { ABCStyles, ABCStyleSheet, Colors, Sizes, TextStyles } from "../../theme";
import IconFontView from "../../base-ui/iconfont/iconfont-view";
import { ClinicDoctorInfo } from "../../base-business/data/beans";
import _ from "lodash";
import { AbcView } from "../../base-ui/views/abc-view";
import { RegistrationTabType } from "../data/bean";

const styles = ABCStyleSheet.create({
    itemStyle: {
        ...ABCStyles.rowAlignCenter,
        ...ABCStyles.bottomLine,
        ...ABCStyles.listHorizontalPadding,
        justifyContent: "space-between",
        backgroundColor: Colors.white,
        height: Sizes.listItemHeight,
    },
});

export enum RegistrationDoctorSearchPageType {
    registration = "registration",
    therapy = "therapy",
}

interface RegistrationDoctorSearchPageProps {
    searchHint?: string;
    doctorId?: string;
    cancelSelectText?: string; //如果为空，将不显示"不选择xxx",
    popAnimation?: boolean;

    type?: RegistrationDoctorSearchPageType;

    onClickItem?(info: ClinicDoctorInfo): void;
}

export class RegistrationDoctorSearchPage extends BaseBlocNetworkPage<RegistrationDoctorSearchPageProps, RegistrationDoctorSearchPageBloc> {
    private currentTabIndex: number;

    static show(param?: RegistrationDoctorSearchPageProps): Promise<ClinicDoctorInfo | undefined> {
        return ABCNavigator.navigateToPage(<RegistrationDoctorSearchPage {...param} />);
    }

    constructor(props: RegistrationDoctorSearchPageProps) {
        super(props);
        this.currentTabIndex =
            props.type == RegistrationDoctorSearchPageType.registration
                ? RegistrationTabType.registration
                : RegistrationTabType.appointment;
        this.bloc = new RegistrationDoctorSearchPageBloc(!_.isEmpty(this.props.cancelSelectText), props.popAnimation, props.type);
        this.addDisposable(this.bloc);
    }

    public componentDidMount(): void {
        super.componentDidMount();
        BlocHelper.connectLoadingStatus(this.bloc, this, (state) => !state.matchDoctors.length);
    }

    public reloadData(): void {
        this.bloc.requestReloadData();
    }

    getAppBar(): JSX.Element {
        if (this.currentTabIndex == RegistrationTabType.registration) {
            return (
                <AppSearchBar
                    placeholder={this.props.searchHint ?? "请输入医生名字"}
                    rightPart={this.getRightAppBarIcons()}
                    onBackClick={this.onBackClick.bind(this)}
                    autoFocus={true}
                    onChangeText={(value) => this.bloc.requestSearch(value)}
                />
            );
        } else {
            return (
                <AppSearchBar
                    placeholder={this.props.searchHint ?? "请输入理疗师名字"}
                    rightPart={this.getRightAppBarIcons()}
                    onBackClick={this.onBackClick.bind(this)}
                    autoFocus={true}
                    onChangeText={(value) => this.bloc.requestSearch(value)}
                />
            );
        }
    }

    public renderContent(): JSX.Element {
        const { matchDoctors } = this.bloc.currentState;
        return (
            <ListView
                dataSource={matchDoctors}
                numberOfRows={matchDoctors.length}
                getRowKey={(index) => index.toString()}
                renderRow={(data /*, unknown, index*/) => this._renderRow(data)}
                scrollEventThrottle={300}
            />
        );
    }

    private _renderRow(data: ClinicDoctorInfo /*, ignore: number | undefined*/) {
        const name = data?.doctorName;
        const departmentName = data?.departmentName;
        return (
            <AbcView
                style={styles.itemStyle}
                onClick={() => {
                    if (this.props.onClickItem) {
                        this.props.onClickItem(data);
                        return;
                    }

                    this.bloc.requestSelectItem(data);
                }}
            >
                <Text style={TextStyles.t14NT1}>{name ?? ""}</Text>
                {this.props.type == RegistrationDoctorSearchPageType.registration && (
                    <Text style={[TextStyles.t12NT2, { flex: 1, marginLeft: Sizes.dp12 }]} numberOfLines={1}>
                        {departmentName?.length ? departmentName : "其他"}
                    </Text>
                )}
                {data.doctorId === this.props.doctorId && <IconFontView name={"Positive_Selected"} size={14} color={Colors.mainColor} />}
            </AbcView>
        );
    }
}
