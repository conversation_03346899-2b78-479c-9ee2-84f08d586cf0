/**
 * create by deng<PERSON>e
 * desc:
 * create date 2021/1/18
 */
import { Bloc, BlocEvent } from "../../bloc";
import React from "react";
import { EventName } from "../../bloc/bloc";
import { BaseLoadingState } from "../../bloc/bloc-helper";
import { ClinicDoctorInfo } from "../../base-business/data/beans";
import { of, Subject } from "rxjs";
import { debounce, switchMap } from "rxjs/operators";
import { ClinicAgent } from "../../base-business/data/clinic-agent";
import { ABCError } from "../../common-base-module/common-error";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { TherapyAppointmentAgent } from "../appointment/data/therapy-appointment-agent";
import { RegistrationDoctorSearchPageType } from "./registration-doctor-search-page";
import { delayed } from "../../common-base-module/rxjs-ext/rxjs-ext";
import _ from "lodash";

export class State extends BaseLoadingState {
    keyword?: string;
    allDoctors: ClinicDoctorInfo[] = [];
    matchDoctors: ClinicDoctorInfo[] = [];

    clone(): State {
        return Object.assign(new State(), this);
    }
}

export class RegistrationDoctorSearchPageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<RegistrationDoctorSearchPageBloc | undefined>(undefined);

    private readonly _cancelable: boolean;
    private readonly _popAnimation: boolean;
    private readonly _type: RegistrationDoctorSearchPageType;
    private _loadDataTrigger = new Subject<number>();

    static fromContext(context: RegistrationDoctorSearchPageBloc): RegistrationDoctorSearchPageBloc {
        return context;
    }

    constructor(cancelable: boolean, popAnimation?: boolean, type?: RegistrationDoctorSearchPageType) {
        super();
        this._cancelable = cancelable;
        this._popAnimation = popAnimation ?? true;
        this._type = type ?? RegistrationDoctorSearchPageType.registration;
        this.dispatch(new _EventInit()).then();
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventSearch, this._mapEventSearch);
        map.set(_EventSelectItem, this._mapEventSelectItem);

        return map;
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    private async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        this._loadDataTrigger
            .pipe(
                debounce((/*ignored*/) => {
                    if (_.isEmpty(this.innerState.keyword)) return of(0);
                    return delayed(200);
                }),
                switchMap(() => {
                    if (this._type == RegistrationDoctorSearchPageType.registration) {
                        return Promise.all([ClinicAgent.getClinicAllDoctorsRegfee(0), ClinicAgent.getClinicAllDoctorsRegfee(1, 1)])
                            .catch((e) => new ABCError(e))
                            .toObservable();
                    } else if (this._type == RegistrationDoctorSearchPageType.therapy) {
                        return Promise.all([TherapyAppointmentAgent.getAllTherapyDoctorList()])
                            .catch((e) => new ABCError(e))
                            .toObservable();
                    } else {
                        return of(null);
                    }
                })
            )
            .subscribe((data) => {
                this.innerState.loading = false;
                if (data instanceof ABCError) {
                    this.innerState.loadError = data.detailError;
                    this.update();
                } else {
                    this.innerState.allDoctors = this.innerState.allDoctors.concat(data?.[0] ?? [], data?.[1] ?? []) ?? [];
                    this._collectMatchData();
                    this.update();
                }
            })
            .addToDisposableBag(this);
        this._loadDataTrigger.next(0);
    }

    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    private async *_mapEventSearch(event: _EventSearch): AsyncGenerator<State> {
        this.innerState.keyword = event.keyword;
        this._collectMatchData();
        yield this.innerState.clone();
    }

    private async *_mapEventSelectItem(event: _EventSelectItem): AsyncGenerator<State> {
        ABCNavigator.pop(event.data, this._popAnimation);
    }

    //过滤出符合条件的项
    private _collectMatchData() {
        const keyword = (this.innerState.keyword ?? "").toLowerCase();
        if (keyword.length === 0) {
            this.innerState.matchDoctors = this.innerState.allDoctors;
        }

        this.innerState.matchDoctors = this.innerState.allDoctors.filter(
            (item) =>
                (item.doctorName?.toLowerCase().indexOf(keyword) ?? -1) >= 0 ||
                (item.doctorNamePyFirst?.toLowerCase().indexOf(keyword) ?? -1) >= 0 ||
                (item.doctorNamePy?.toLowerCase().indexOf(keyword) ?? -1) >= 0 ||
                (item.namePy?.toLowerCase().indexOf(keyword) ?? -1) >= 0 ||
                (item.namePyFirst?.toLowerCase().indexOf(keyword) ?? -1) >= 0
        );

        //
        if (this._cancelable) this.innerState.matchDoctors.splice(0, 0);
    }

    public requestReloadData(): void {
        return;
    }

    public requestSearch(keyword: string): void {
        this.dispatch(new _EventSearch(keyword));
    }

    public requestSelectItem(doctor: ClinicDoctorInfo): void {
        this.dispatch(new _EventSelectItem(doctor));
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventSearch extends _Event {
    keyword: string;

    constructor(keyword: string) {
        super();
        this.keyword = keyword;
    }
}

class _EventSelectItem extends _Event {
    data: ClinicDoctorInfo;

    constructor(data: ClinicDoctorInfo) {
        super();
        this.data = data;
    }
}
