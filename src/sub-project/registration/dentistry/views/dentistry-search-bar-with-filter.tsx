import { SearchBarWithFilter } from "../../../base-ui/searchBar/search-bar";
import { View } from "@hippy/react";
import { ABCStyles, Colors, flattenStyles, Sizes } from "../../../theme";
import { IconFontView, Spacer } from "../../../base-ui";
import React from "react";
import { DropDownFilterView } from "../../../base-ui/searchBar/filter-view";
import { AbcView } from "../../../base-ui/views/abc-view";

export class DentistrySearchBarWithFilter extends SearchBarWithFilter {
    render(): JSX.Element {
        const { style } = this.props;
        return (
            <View
                style={[ABCStyles.bottomLine, ABCStyles.rowAlignCenter, { minHeight: Sizes.dp44 }, flattenStyles(style)]}
                collapsable={false}
                ref={(ref) => (this._iconRef = ref)}
                onLayout={(layoutInfo) => {
                    //@ts-ignore
                    this.viewHeight = layoutInfo.layout.height;
                }}
            >
                <View
                    style={{
                        flex: 1,
                        flexDirection: "row",
                        paddingHorizontal: Sizes.dp2,
                    }}
                >
                    <View style={[ABCStyles.rowAlignCenter, { justifyContent: "space-between" }]}>
                        {this.props.filters?.filters.map((item, index, self) => (
                            <DropDownFilterView
                                key={index}
                                selectMode={1}
                                iconRef={this._iconRef}
                                style={[index == self.length - 1 ? { flexShrink: 1 } : {}]}
                                viewHeight={this.viewHeight}
                                filterGroup={item}
                                filters={this.props.filters!}
                                defaultShowText={"暂无医生"}
                                onChange={(filters) => {
                                    this.props.onFilterChange?.(filters);
                                }}
                            />
                        ))}
                    </View>
                    <Spacer />
                    {this._renderDentistrySearchBar()}
                    {/*暂时注释掉--等做看板功能时，再放开*/}
                    {/*{this._renderModeSwitchIcon()}*/}
                </View>
            </View>
        );
    }

    private _renderDentistrySearchBar(): JSX.Element {
        const { onChange } = this.props;
        return (
            <AbcView onClick={() => onChange?.("")} style={ABCStyles.rowAlignCenter}>
                <IconFontView name="Search" size={Sizes.dp20} color={Colors.t2} style={{ paddingHorizontal: Sizes.dp12 }} />
            </AbcView>
        );
    }

    private _renderModeSwitchIcon(): JSX.Element {
        const { hasKanbanMode, onSwitchMode } = this.props;
        return (
            <AbcView onClick={() => onSwitchMode?.(!!hasKanbanMode)} style={[ABCStyles.rowAlignCenter, { paddingHorizontal: Sizes.dp12 }]}>
                <IconFontView name={!!hasKanbanMode ? "kanban" : "single row"} size={Sizes.dp20} color={Colors.t2} />
            </AbcView>
        );
    }
}
