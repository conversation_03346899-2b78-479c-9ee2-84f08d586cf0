import React from "react";
import { View, Text } from "@hippy/react";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../theme";
import { AbcWheel, AbcWheelProps } from "../../../base-ui/abc-app-library/wheel/abc-wheel";
import { BaseComponent } from "../../../base-ui/base-component";
import { ABCNavigator } from "../../../base-ui/views/abc-navigator";
import { AbcText } from "../../../base-ui/views/abc-text";
import { pxToDp } from "../../../base-ui/utils/ui-utils";
import FontWeight from "../../../theme/font-weights";
import { AssetImageView } from "../../../base-ui/views/asset-image-view";

interface FixedSourceTimeProps extends AbcWheelProps {
    list?: { cont: string; type?: number; residueCount?: string }[];
    enableLeaveForMember?: boolean; //是否显示会员预留号标识
    enableLeaveForPC?: boolean; //是否显示现场预留号标识
}
export class FixedSourceTime extends BaseComponent<FixedSourceTimeProps> {
    private selectIndex?: number;
    private _timeWheel?: AbcWheel | null;
    private timeList: JSX.Element[] = [];
    constructor(props: FixedSourceTimeProps) {
        super(props);
        this.selectIndex = this.props.initialIndex;
        this.timeList = [];
        props.list?.map((item, index) => {
            this.timeList.push(
                <View style={[ABCStyles.rowAlignCenter, { paddingVertical: Sizes.dp10, justifyContent: "center" }]} key={index}>
                    {!!props?.enableLeaveForMember && item?.type == 2 && (
                        <AssetImageView
                            name={"charge_invoice_patient_member"}
                            style={{
                                marginRight: Sizes.dp4,
                                width: Sizes.dp16,
                                height: Sizes.dp16,
                            }}
                        />
                    )}
                    {!!props?.enableLeaveForPC && item?.type == 1 && (
                        <AssetImageView
                            name={"reserve_no"}
                            style={{
                                marginRight: Sizes.dp4,
                                width: Sizes.dp16,
                                height: Sizes.dp16,
                            }}
                        />
                    )}
                    {!item?.type && <View style={{ width: Sizes.dp20 }} />}
                    <Text
                        style={[
                            TextStyles.t16NT2.copyWith({
                                color: this.props.initialIndex == index ? Colors.T1 : Colors.t2,
                                fontWeight: this.props.initialIndex == index ? FontWeight.medium : undefined,
                            }),
                        ]}
                    >
                        {item?.cont ?? ""}
                    </Text>
                    <Text style={TextStyles.t16NT3}>{item?.residueCount ?? ""} </Text>
                </View>
            );
        });
    }
    private getCurrentSelectIndex(index?: number): void {
        this.selectIndex = index;
        this.timeList = [];
        this.props.list?.map((item, kIndex) => {
            this.timeList.push(
                <View style={[ABCStyles.rowAlignCenter, { paddingVertical: Sizes.dp10, justifyContent: "center" }]} key={index}>
                    {!!this.props?.enableLeaveForMember && item?.type == 2 && (
                        <AssetImageView
                            name={"charge_invoice_patient_member"}
                            style={{
                                marginRight: Sizes.dp4,
                                width: Sizes.dp16,
                                height: Sizes.dp16,
                            }}
                        />
                    )}
                    {!!this.props?.enableLeaveForPC && item?.type == 1 && (
                        <AssetImageView
                            name={"reserve_no"}
                            style={{
                                marginRight: Sizes.dp4,
                                width: Sizes.dp16,
                                height: Sizes.dp16,
                            }}
                        />
                    )}
                    {!item?.type && <View style={{ width: Sizes.dp20 }} />}
                    <Text
                        style={[
                            TextStyles.t16NT2.copyWith({
                                color: kIndex == index ? Colors.T1 : Colors.t2,
                                fontWeight: kIndex == index ? FontWeight.medium : undefined,
                            }),
                        ]}
                    >
                        {item?.cont ?? ""}
                    </Text>
                    <Text style={TextStyles.t16NT3}>{item?.residueCount ?? ""} </Text>
                </View>
            );
        });
        this.setState({});
    }
    render(): JSX.Element {
        return (
            <View style={[ABCStyles.panelTopStyle, { backgroundColor: Colors.white, height: pxToDp(268) }]}>
                <View style={[ABCStyles.rowAlignCenterSpaceBetween, Sizes.paddingLTRB(Sizes.dp16, Sizes.dp14), ABCStyles.bottomLine]}>
                    <AbcText style={[TextStyles.t14MT2.copyWith({ color: Colors.T6 })]} onClick={() => ABCNavigator.pop()}>
                        取消
                    </AbcText>
                    <AbcText style={[TextStyles.t14MM]} onClick={() => ABCNavigator.pop(this.selectIndex)}>
                        确定
                    </AbcText>
                </View>
                <View style={{ paddingHorizontal: Sizes.dp10, flex: 1 }}>
                    <AbcWheel
                        ref={(ref) => {
                            this._timeWheel = ref;
                        }}
                        dataList={this.timeList}
                        initialIndex={this.selectIndex}
                        onSelectChanged={(index) => this.getCurrentSelectIndex(index)}
                    />
                </View>
            </View>
        );
    }
}
