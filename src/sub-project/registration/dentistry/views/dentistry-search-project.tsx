import React from "react";
import { BaseBlocPage } from "../../../base-ui/base-page";
import { DentistrySearchProjectBloc } from "./dentistry-search-project-bloc";
import { Text, View } from "@hippy/react";
import { ABCStyles, Color, Colors, FontSizes, Sizes, TextStyles } from "../../../theme";
import { AppSearchBar } from "../../../base-ui/app-bar";
import { ABCUtils } from "../../../base-ui/utils/utils";
import { AbcListView } from "../../../base-ui/list/abc-list-view";
import { IconFontView, SizedBox } from "../../../base-ui";
import { DentistryClinicProduct, RegistrationType } from "../data/bean";
import { ignore } from "../../../common-base-module/global";
import _ from "lodash";
import { Abc<PERSON>iew } from "../../../base-ui/views/abc-view";
import { BlocBuilder } from "../../../bloc";
import { RegistrationProducts } from "../../data/bean";
import abcI18Next from "../../../language/config";

interface DentistrySearchProjectProps {
    doctorId?: string;
    defaultProducts?: RegistrationProducts[]; //默认选中的项目id集合
    registrationType?: RegistrationType;
}

export class DentistrySearchProject extends BaseBlocPage<DentistrySearchProjectProps, DentistrySearchProjectBloc> {
    private productList?: DentistryClinicProduct[] = [];
    constructor(props: DentistrySearchProjectProps) {
        super(props);
        this.bloc = new DentistrySearchProjectBloc({ ...props });
    }

    getAppBar(): any {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => this._getAppBar()} />;
    }

    _getAppBar(): JSX.Element {
        return (
            <View style={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp16, Sizes.dp6, Sizes.dp4), { backgroundColor: Colors.white }]}>
                <AppSearchBar
                    placeholder={"输入项目名称"}
                    rightPart={this.getRightAppBarIcons()}
                    onBackClick={this.onBackClick.bind(this)}
                    autoFocus={true}
                    showBackIcon={false}
                    onChangeText={(value) => {
                        this.bloc.requestSearch(value);
                    }}
                    bottomLine={false}
                    maxLength={Sizes.dp50}
                    searchContainerStyle={{ backgroundColor: Colors.bg1 }}
                    inputStyle={{ backgroundColor: Colors.bg1, height: Sizes.dp36, fontSize: FontSizes.size14 }}
                />
            </View>
        );
    }

    getRightAppBarIcons(): JSX.Element[] {
        const { selectProduct } = this.bloc.currentState;
        return [
            <AbcView key={"product_count"} onClick={() => this.bloc.requestConfirm()}>
                <Text style={[TextStyles.t14MM]}>{`确定(${selectProduct.size})`}</Text>
            </AbcView>,
        ];
    }

    getBackgroundColor(): Color {
        return Colors.white;
    }

    getShowStatusBar(): boolean {
        return false;
    }

    private _renderRow(data: DentistryClinicProduct, _ignore: number | undefined, checked: boolean): JSX.Element {
        ignore(_ignore);
        return (
            <AbcView
                key={data.id}
                style={[
                    ABCStyles.rowAlignCenterSpaceBetween,
                    Sizes.paddingLTRB(Sizes.dp8, Sizes.dp16, 0, Sizes.dp16),
                    ABCStyles.bottomLine,
                    { flex: 1 },
                ]}
                onClick={() => {
                    this.bloc.requestChangeSelectState(data);
                }}
            >
                <Text numberOfLines={1} style={[TextStyles.t16NT1, { flexShrink: 1 }]}>
                    {data.name ?? ""}
                </Text>
                <View style={[ABCStyles.rowAlignCenter]}>
                    <Text style={[TextStyles.t16NT6]}>
                        {abcI18Next.t("¥")}
                        {ABCUtils.formatPrice(data?.price ?? 0)}
                    </Text>
                    <SizedBox width={Sizes.dp8} />
                    <View>
                        {checked ? (
                            <IconFontView name={"Chosen"} size={Sizes.dp16} color={Colors.mainColor} />
                        ) : (
                            <View
                                style={{
                                    width: Sizes.dp16,
                                    height: Sizes.dp16,
                                    borderWidth: Sizes.dp1,
                                    borderRadius: Sizes.dp8,
                                    borderColor: Colors.P1,
                                }}
                            />
                        )}
                    </View>
                </View>
            </AbcView>
        );
    }

    renderContent(): JSX.Element {
        const state = this.bloc.currentState;
        const { productList, selectProduct } = state;
        if (_.isEmpty(productList)) return <View />;
        return (
            <View style={{ flex: 1, backgroundColor: Colors.white, paddingHorizontal: Sizes.dp16 }}>
                <AbcListView
                    style={{ flex: 1 }}
                    loading={state.loading}
                    scrollEventThrottle={300}
                    dataSource={productList ?? []}
                    numberOfRows={productList!.length}
                    getRowKey={(index) => productList?.[index].id ?? ""}
                    renderRow={(data, index) => this._renderRow(data, index, selectProduct?.has(data))}
                />
            </View>
        );
    }
}
