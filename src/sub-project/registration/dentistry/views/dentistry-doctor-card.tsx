/**
 * create by dengjie
 * desc: 预约的医生信息卡片展示
 * create date 2021/1/7
 */
import React from "react";
import { FocusItemKeys, RegistrationFormItem, RegistrationReferralSource } from "../../data/bean";
import { BaseComponent } from "../../../base-ui/base-component";
import { View, Style } from "@hippy/react";
import { UIUtils } from "../../../base-ui/utils";
import { OverlayTips } from "../../../base-ui/dialog/overlay-tips";
import { TimeUtils } from "../../../common-base-module/utils";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../theme";
import { ListSettingItem, ListSettingItemStyle, ListSettingRadiosItem } from "../../../base-ui/views/list-setting-item";
import { ignore } from "../../../common-base-module/global";
import _ from "lodash";
import { DeviceUtils } from "../../../base-ui/utils/device-utils";

interface DentistryDoctorCardProps {
    isEditing?: boolean;
    showErrorHint?: boolean;
    registrationInfo?: RegistrationFormItem;
    revisitStatusName?: string;
    disabledEditRevisit?: boolean;
    showRegistrationCategory?: boolean;

    onChangeType?(index: number): void;

    onChangeDepartment?(): void;

    onChangeDoctor?(): void;

    onChangeTime?(): void;

    onChangeRevisit?(): void;
    isEditDiagnoseType?: boolean; //是否可编辑初/复诊类型
    referralSource?: RegistrationReferralSource; // 转诊来源信息
    onChangeRegistrationCategory?(): void;
}
const renderItemWithErrorBorder = (showErrorHint?: boolean, hasError?: boolean, children?: React.ReactNode, style?: Style | Style[]) => {
    if (DeviceUtils.isOhos()) {
        // 鸿蒙系统特殊处理
        return (
            <View style={style}>
                {children}
                {showErrorHint && hasError && (
                    <View
                        style={{
                            position: "absolute",
                            left: 0,
                            right: 0,
                            top: 0,
                            bottom: 0,
                            borderWidth: 1,
                            borderColor: Colors.errorBorder,
                            pointerEvents: "none",
                        }}
                    />
                )}
            </View>
        );
    } else {
        // 其他平台正常处理
        return <View style={[showErrorHint && hasError ? ABCStyles.errorBorder : {}, style]}>{children}</View>;
    }
};
export class DentistryDoctorCard extends BaseComponent<DentistryDoctorCardProps> {
    private _iconViewRef?: View | null;

    constructor(props: DentistryDoctorCardProps) {
        super(props);
    }
    static defaultProps = {
        isEditDiagnoseType: true,
    };

    async _showTip(): Promise<void> {
        const registrationInfo = this.props.registrationInfo;
        const oldReserveInfo = registrationInfo?.oldReserveInfo;
        if (!oldReserveInfo) return;
        const layout = await UIUtils.measureInWindow(this._iconViewRef);
        OverlayTips.show(
            `预计就诊时间：${oldReserveInfo.reserveDate} ${oldReserveInfo.reserveStart} \n实际就诊时间：${TimeUtils.formatDate(
                registrationInfo?.reserveDate
            )} ${registrationInfo?.reserveTime?.start}`,
            layout,
            {
                preferPosition: "center",
                backgroundColor: Colors.black,
                textStyle: { ...TextStyles.t14NW, paddingHorizontal: Sizes.dp10 },
                arrowSize: Sizes.dp10,
                borderRadius: Sizes.dp4,
            }
        ).then();
    }

    private _renderCanEditCard(): JSX.Element {
        const {
            registrationInfo,
            showErrorHint,
            showRegistrationCategory,
            onChangeDepartment,
            onChangeDoctor,
            onChangeType,
            // onChangeTime,
            revisitStatusName,
            disabledEditRevisit,
            onChangeRevisit,
            onChangeRegistrationCategory,
            isEditing,
            isEditDiagnoseType,
            referralSource,
        } = this.props;

        const departmentName = _.isUndefined(registrationInfo?.departmentName)
            ? undefined
            : registrationInfo?.departmentName.length
            ? registrationInfo?.departmentName
            : "其他";

        const type = ["预约", "挂号"];
        const itemStyle = !!isEditing ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal;

        const referralDoctorInfo = `${referralSource?.doctorName ?? ""}${
            referralSource?.departmentName ? `-${referralSource?.departmentName}` : ""
        } ${referralSource?.referralTime ? new Date(referralSource?.referralTime).format("yyyy-MM-dd") : ""} 转出`;

        return (
            <View style={[ABCStyles.bottomLine, { backgroundColor: Colors.white }]}>
                {referralSource && (
                    <View style={[{ paddingLeft: Sizes.dp16 }]}>
                        <ListSettingItem
                            style={{ marginRight: Sizes.dp16 }}
                            itemStyle={ListSettingItemStyle.normal}
                            bottomLine={true}
                            title={"转诊"}
                            content={referralDoctorInfo}
                            contentStyle={[
                                ABCStyles.rowAlignCenter,
                                {
                                    paddingVertical: Sizes.dp16,
                                    justifyContent: "flex-end",
                                },
                            ]}
                            titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                            leftAlignment={{ alignSelf: undefined }}
                        />
                    </View>
                )}

                <ListSettingRadiosItem
                    style={{ marginHorizontal: Sizes.dp16, justifyContent: "space-between", alignItems: "center", height: Sizes.dp54 }}
                    bottomLine={true}
                    marginBetweenItem={Sizes.dp40}
                    title={"类型"}
                    options={type.reverse()}
                    check={type[registrationInfo?.isReserved ?? 0]}
                    enable={!!isEditing}
                    onChanged={(option, index) => {
                        ignore(option);
                        onChangeType?.(index);
                    }}
                    titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                    leftAlignment={{ alignSelf: undefined }}
                />
                {renderItemWithErrorBorder(
                    showErrorHint,
                    !registrationInfo?.departmentName,
                    <ListSettingItem
                        ref={FocusItemKeys.department}
                        style={{ marginRight: Sizes.dp16 }}
                        itemStyle={itemStyle}
                        bottomLine={true}
                        title={"科室"}
                        contentHint={"选择科室"}
                        content={departmentName}
                        contentStyle={[
                            ABCStyles.rowAlignCenter,
                            {
                                paddingVertical: Sizes.dp16,
                                justifyContent: "flex-end",
                            },
                        ]}
                        onClick={!!isEditing ? () => onChangeDepartment?.() : undefined}
                        titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                        leftAlignment={{ alignSelf: undefined }}
                    />,
                    {
                        paddingLeft: Sizes.dp16,
                    }
                )}
                {renderItemWithErrorBorder(
                    showErrorHint,
                    !registrationInfo?.doctorName,
                    <ListSettingItem
                        style={{ marginRight: Sizes.dp16 }}
                        itemStyle={itemStyle}
                        bottomLine={true}
                        title={"医生"}
                        contentHint={"医生"}
                        content={registrationInfo?.doctorName}
                        contentStyle={[
                            ABCStyles.rowAlignCenter,
                            {
                                paddingVertical: Sizes.dp16,
                                justifyContent: "flex-end",
                            },
                        ]}
                        onClick={!!isEditing ? () => onChangeDoctor?.() : undefined}
                        titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                        leftAlignment={{ alignSelf: undefined }}
                    />,
                    {
                        paddingLeft: Sizes.dp16,
                    }
                )}

                <View style={[{ paddingLeft: Sizes.dp16 }]}>
                    <ListSettingItem
                        style={{ marginRight: Sizes.dp16 }}
                        title={"初诊/复诊"}
                        bottomLine={showRegistrationCategory}
                        itemStyle={!disabledEditRevisit ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal}
                        contentStyle={[
                            ABCStyles.rowAlignCenter,
                            {
                                paddingVertical: Sizes.dp16,
                                justifyContent: "flex-end",
                            },
                        ]}
                        content={revisitStatusName}
                        onClick={!disabledEditRevisit ? () => onChangeRevisit?.() : undefined}
                        titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                        leftAlignment={{ alignSelf: undefined }}
                    />
                </View>
                {showRegistrationCategory && (
                    <View style={[{ paddingLeft: Sizes.dp16 }]}>
                        <ListSettingItem
                            style={{ marginRight: Sizes.dp16 }}
                            title={"号种"}
                            bottomLine={false}
                            itemStyle={!!isEditing || isEditDiagnoseType ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal}
                            contentStyle={[
                                ABCStyles.rowAlignCenter,
                                {
                                    paddingVertical: Sizes.dp16,
                                    justifyContent: "flex-end",
                                },
                            ]}
                            content={registrationInfo?.registrationCategoryDisplay ?? ""}
                            onClick={!!isEditing || isEditDiagnoseType ? () => onChangeRegistrationCategory?.() : undefined}
                            titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                            leftAlignment={{ alignSelf: undefined }}
                        />
                    </View>
                )}
            </View>
        );
    }

    render(): JSX.Element {
        return <View>{this._renderCanEditCard()}</View>;
    }
}
