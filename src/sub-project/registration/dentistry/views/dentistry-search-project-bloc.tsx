import { Bloc, BlocEvent } from "../../../bloc";
import { actionEvent, EventName } from "../../../bloc/bloc";
import { DentistryClinicProduct, RegistrationType } from "../data/bean";
import { DentistryAgent } from "../data/dentistry-agent";
import { AbcSet } from "../../../base-ui/utils/abc-set";
import { Subject } from "rxjs";
import { switchMap } from "rxjs/operators";
import { ABCError } from "../../../common-base-module/common-error";
import { ABCNavigator } from "../../../base-ui/views/abc-navigator";
import { RegistrationProducts } from "../../data/bean";
import _ from "lodash";

class State {
    loading = false;
    loadError: any;
    keyword?: string;
    productList?: DentistryClinicProduct[] = [];
    selectProduct: AbcSet<DentistryClinicProduct> = new AbcSet<DentistryClinicProduct>(undefined, (item) => item.id!);

    clone(): State {
        return Object.assign(new State(), this);
    }
}
class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventChangeSelectState extends _Event {
    data?: DentistryClinicProduct;
    constructor(data?: DentistryClinicProduct) {
        super();
        this.data = data;
    }
}

class _EventConfirm extends _Event {}

class _EventSearch extends _Event {
    value?: string;
    constructor(value?: string) {
        super();
        this.value = value;
    }
}

export class DentistrySearchProjectBloc extends Bloc<_Event, State> {
    static fromContext(context: DentistrySearchProjectBloc): DentistrySearchProjectBloc {
        return context;
    }
    private doctorId?: string;
    private defaultProducts?: RegistrationProducts[];
    private _registrationType?: RegistrationType;
    _loadDataTrigger: Subject<number> = new Subject<number>();

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    constructor(options: { doctorId?: string; defaultProducts?: RegistrationProducts[]; registrationType?: RegistrationType }) {
        super();
        this.doctorId = options?.doctorId;
        this.defaultProducts = options?.defaultProducts;
        this._registrationType = options?.registrationType ?? RegistrationType.outpatientRegistration;
        this.dispatch(new _EventInit()).then();
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        return map;
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    @actionEvent(_EventInit)
    private async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        if (!_.isEmpty(this.defaultProducts)) {
            this.defaultProducts?.map((it) => {
                this.innerState.selectProduct.add(it);
            });
            this.update();
        }
        this._loadDataTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.loading = true;
                    this.innerState.loadError = null;
                    this.innerState.productList = [];
                    this.update();
                    return DentistryAgent.getDentistryClinicProduct({
                        employeeId: this.doctorId,
                        registrationType: this._registrationType,
                        keywords: this.innerState.keyword ?? "",
                    })
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe(
                (rsp) => {
                    this.innerState.loading = false;
                    if (rsp instanceof ABCError) {
                        this.innerState.loadError = rsp;
                        this.update();
                        return;
                    } else if (rsp) {
                        this.innerState.productList = rsp;
                        this.innerState.productList?.sort((item) => (this.innerState.selectProduct.has(item) ? -1 : 1));
                        this.update();
                    }
                },
                (error) => {
                    this.innerState.loading = false;
                    this.innerState.loadError = error;
                    this.update();
                }
            )
            .addToDisposableBag(this);
        this._loadDataTrigger.next(0);
    }

    @actionEvent(_EventUpdate)
    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    @actionEvent(_EventChangeSelectState)
    private async *_mapEventChangeSelectState(event: _EventChangeSelectState): AsyncGenerator<State> {
        if (!event.data) return;
        const selectProduct = this.innerState.selectProduct;
        if (selectProduct.has(event.data)) {
            selectProduct.delete(event.data);
        } else {
            selectProduct.add(event.data);
        }
        this.update();
    }

    @actionEvent(_EventSearch)
    private async *_mapEventSearch(event: _EventSearch): AsyncGenerator<State> {
        this.innerState.keyword = event.value;
        this._loadDataTrigger.next();
    }

    @actionEvent(_EventConfirm)
    private async *_mapEventConfirm(): AsyncGenerator<State> {
        //需要匹配当前就诊项目完整信息，否则更改项目后，获取不到项目时长，导致就诊时长不对
        let selProductList = this.innerState.selectProduct.values();
        selProductList = selProductList?.map((item) => {
            this.innerState.productList?.forEach((sub) => {
                if (item.id == sub.id) {
                    item = sub;
                }
            });
            return item;
        });

        ABCNavigator.pop(selProductList);
    }

    /**
     * 更改选中的状态
     * @param data
     */
    requestChangeSelectState(data?: DentistryClinicProduct): void {
        this.dispatch(new _EventChangeSelectState(data));
    }

    //确定最终选择的项目
    requestConfirm(): void {
        this.dispatch(new _EventConfirm());
    }

    //搜索项目
    requestSearch(value?: string): void {
        this.dispatch(new _EventSearch(value));
    }
}
