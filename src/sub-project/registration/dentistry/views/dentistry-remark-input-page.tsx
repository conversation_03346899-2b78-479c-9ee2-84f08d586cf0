import React from "react";
import { BasePage, DividerLine, SizedBox } from "../../../base-ui";
import { ScrollView, Text, View } from "@hippy/react";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../theme";
import { AbcText } from "../../../base-ui/views/abc-text";
import { AbcTextInput } from "../../../base-ui/views/abc-text-input";
import { DeviceUtils } from "../../../base-ui/utils/device-utils";
import { AbcIntelligenceChip } from "../../../outpatient/outpatient-views";
import { AnyType } from "../../../common-base-module/common-types";
import { DentistryAgent } from "../data/dentistry-agent";
import { DentistryRemarkInfo, RegistrationType } from "../data/bean";
import { ABCNavigator } from "../../../base-ui/views/abc-navigator";
import _ from "lodash";

interface DentistryRemarkInputPageProps {
    remark?: string;
    registrationType?: RegistrationType;
}
export class DentistryRemarkInputPage extends BasePage<DentistryRemarkInputPageProps, any> {
    inputHeight = DeviceUtils.isOhos() ? 20 : 0;
    private remarkValue?: string;
    private _textInputRef?: AbcTextInput | null;
    private _remarkList?: DentistryRemarkInfo[] = [];
    private _remarkValStr?: string[];

    getAppBar(): JSX.Element | undefined {
        return undefined;
    }

    getShowStatusBar(): boolean {
        return false;
    }

    constructor(props: DentistryRemarkInputPageProps) {
        super(props);
        this.remarkValue = props.remark;
        this._remarkValStr = !!this.remarkValue ? this.remarkValue.split("，") : [];
    }

    componentDidMount(): void {
        this.queryRemarkTemplate().then();
    }

    private async queryRemarkTemplate(): Promise<void> {
        this._remarkList = await DentistryAgent.queryDentistryRemarkTemplates(this.props.registrationType).catchIgnore();
        this.setState({});
    }

    private _createInput(): JSX.Element {
        return (
            <View style={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp22, Sizes.dp16, Sizes.dp20)]}>
                <View style={[ABCStyles.rowAlignCenter, { justifyContent: "space-between" }]}>
                    <Text style={[TextStyles.t16MT1, { lineHeight: Sizes.dp22 }]}>{"备注"}</Text>
                    <AbcText
                        style={[TextStyles.t14MM, { lineHeight: Sizes.dp22 }]}
                        onClick={() => {
                            this.remarkConfirm();
                        }}
                    >
                        {"确定"}
                    </AbcText>
                </View>
                <SizedBox height={Sizes.dp17} />
                <AbcTextInput
                    style={{
                        ...TextStyles.t16NB,
                        ...(DeviceUtils.isOhos()
                            ? { minHeight: this.inputHeight }
                            : {
                                  flexGrow: 1,
                                  backgroundColor: Colors.transparent,
                                  underlineColorAndroid: Colors.white,
                                  height: this.inputHeight,
                                  paddingHorizontal: DeviceUtils.isAndroid() ? -6 : undefined,
                              }),
                    }}
                    ref={(ref) => {
                        this._textInputRef = ref;
                    }}
                    enableDefaultToolBar={true}
                    defaultValue={this.remarkValue}
                    multiline={true}
                    placeholder={"请输入备注"}
                    returnKeyType={"done"}
                    selectTextOnFocus={false}
                    placeholderTextColor={Colors.t4}
                    onContentSizeChange={(event) => {
                        this.inputHeight = Math.min(event.contentSize.height, Sizes.dp95);
                        this.forceUpdate();
                    }}
                    onChangeText={this.changeRemark.bind(this)}
                    maxLength={50}
                />
            </View>
        );
    }

    changeRemark(text?: string): void {
        this._remarkValStr = text != "" ? text?.split("，") : [];
        this.remarkValue = text;
        this.forceUpdate();
    }

    remarkConfirm(): void {
        ABCNavigator.pop(this.remarkValue);
    }

    handleSelectChip({ info }: AnyType): void {
        this.remarkValue = info;
        this.remarkValue = this.remarkValue?.substring(0, 50);
        this.forceUpdate();
    }

    _createItem(list: Array<DentistryRemarkInfo>): JSX.Element {
        return (
            <View
                style={{
                    flexDirection: "row",
                    flexWrap: "wrap",
                }}
            >
                {list.map((item, index) => (
                    <AbcIntelligenceChip
                        key={index}
                        text={item?.content}
                        onClick={(item) => {
                            if (this._remarkValStr?.find((_item) => _item == item.info)) return; // 不可重复添加
                            this._remarkValStr?.push(item.info);
                            this.remarkValue = this._remarkValStr?.join("，");
                            this.remarkValue = this.remarkValue?.substring(0, 50);
                            this._textInputRef?.setValue(this.remarkValue ?? "");
                            this.forceUpdate();
                        }}
                    />
                ))}
            </View>
        );
    }

    private _renderRemarkContView(): JSX.Element {
        return (
            <View
                style={[
                    Sizes.paddingLTRB(Sizes.dp16, /* DeviceUtils.isIOS() ? Sizes.dp20 : Sizes.dp10,*/ Sizes.dp20, Sizes.dp16),
                    { backgroundColor: Colors.white },
                ]}
            >
                <View>{this._createItem(this._remarkList!)}</View>
            </View>
        );
    }

    renderContent(): JSX.Element {
        return (
            <View
                style={{
                    flex: 1,
                    backgroundColor: Colors.white,
                }}
            >
                {this._createInput()}
                <DividerLine
                    lineHeight={Sizes.dpHalf}
                    color={Colors.dividerLineColor}
                    style={{ marginHorizontal: Sizes.listHorizontalMargin }}
                />
                <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
                    {!_.isEmpty(this._remarkList) && this._renderRemarkContView()}
                </ScrollView>
            </View>
        );
    }
}
