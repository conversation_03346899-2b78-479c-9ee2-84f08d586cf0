import React from "react";
import { BaseComponent } from "../../../../base-ui/base-component";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../../theme";
import { pxToDp } from "../../../../base-ui/utils/ui-utils";
import { View, Text, ScrollView } from "@hippy/react";
import { BottomSheetHelper } from "../../../../base-ui/abc-app-library";
import { ABCNavigator } from "../../../../base-ui/views/abc-navigator";
import { NewStyleNumberStepperInputView } from "../../../../base-ui/views/number-stepper-input-view";
import { SizedBox } from "../../../../base-ui";
import { Range } from "../../../../base-ui/utils/value-holder";
import { showBottomSheet } from "../../../../base-ui/dialog/bottom_sheet";
import { GridView } from "../../../../base-ui/views/grid-view";
import { AbcTag } from "../../../../base-ui/abc-app-library/common/abc-tag";
import { SafeAreaBottomView } from "../../../../base-ui/safe_area_view";
import { RegistrationDesignatedTime, RegistrationDesignatedTimeItem } from "../../../data/bean";
import { TimeUtils } from "../../../../common-base-module/utils";
import _ from "lodash";
import moment from "moment";

interface TherapyTimePickerDialogProps {
    serviceMinMinutes: number;
    date?: Range<Date>;
    schedulingTimeList?: RegistrationDesignatedTime;
    serviceMaxMinutes?: number;
}
interface TherapyTimePickerDialogState {
    serviceTime: number;
    selTimeRangeList?: RegistrationDesignatedTimeItem[];
}
export class TherapyTimePickerDialog extends BaseComponent<TherapyTimePickerDialogProps, TherapyTimePickerDialogState> {
    timeRangeList?: RegistrationDesignatedTimeItem[];
    static async show<T>(params: TherapyTimePickerDialogProps): Promise<{ date: Range<Date>; timeOfDay?: string }> {
        return showBottomSheet(<TherapyTimePickerDialog {...params} />);
    }

    constructor(props: TherapyTimePickerDialogProps) {
        super(props);
        this.timeRangeList = props.schedulingTimeList?.getScheduleIntervals()?.reduce<RegistrationDesignatedTimeItem[]>((prev, next) => {
            const list = next.list;
            if (list) {
                prev.push(...list);
            }
            return prev;
        }, []);
        //勾选当前选中的时间段
        const selectTimeRangeList: RegistrationDesignatedTimeItem[] = [];
        const selectStart = TimeUtils.formatDate(props.date?.start, "HH:mm"),
            selectEnd = TimeUtils.formatDate(props.date?.end, "HH:mm");
        if (!!props.date?.start && !!props.date?.end) {
            this.timeRangeList?.forEach((item) => {
                if (
                    TimeUtils.strTime2MinuteUnit(item?.start) >= TimeUtils.strTime2MinuteUnit(selectStart) &&
                    (TimeUtils.strTime2MinuteUnit(item?.end) <= TimeUtils.strTime2MinuteUnit(selectEnd) ||
                        TimeUtils.strTime2MinuteUnit(item.start) < TimeUtils.strTime2MinuteUnit(selectEnd))
                ) {
                    selectTimeRangeList.push(item);
                }
            });
        }
        const selectMinMinutes = TimeUtils.strTime2MinuteUnit(selectEnd) - TimeUtils.strTime2MinuteUnit(selectStart);

        this.state = {
            serviceTime: (!!selectMinMinutes ? selectMinMinutes : props.serviceMinMinutes) ?? 30,
            selTimeRangeList: selectTimeRangeList,
        };
    }

    onCountInputValueChanged(value: number): void {
        this.setState({
            serviceTime: value,
            selTimeRangeList: [],
        });
    }
    _updateTimeState(info: RegistrationDesignatedTimeItem): void {
        const serviceTime = this.state.serviceTime;
        const tmp: RegistrationDesignatedTimeItem[] = [];
        const timeRange = new Range<number>(
            TimeUtils.strTime2MinuteUnit(info.start),
            TimeUtils.strTime2MinuteUnit(info.start) + serviceTime
        );

        this.timeRangeList?.map((item) => {
            if (
                TimeUtils.strTime2MinuteUnit(item.start) >= timeRange.start! &&
                (TimeUtils.strTime2MinuteUnit(item.end) <= timeRange.end! || TimeUtils.strTime2MinuteUnit(item.start) < timeRange.end!)
            ) {
                tmp.push(item);
            }
        });
        this.setState({ selTimeRangeList: tmp });
    }

    splitShiftCells(): Range<number>[] {
        const rangeList: Range<number>[] = [];
        this.timeRangeList?.forEach((item) => {
            if (!item.available) {
                return;
            } else {
                const startTimeNum = TimeUtils.strTime2MinuteUnit(item.start);
                const endTimeNum = TimeUtils.strTime2MinuteUnit(item.end);
                let lastRangeItem = _.last(rangeList);
                if (!lastRangeItem || lastRangeItem.end != startTimeNum) {
                    rangeList.push(new Range<number>());
                }
                lastRangeItem = _.last(rangeList)!;
                if (!lastRangeItem.start) {
                    lastRangeItem.start = startTimeNum;
                }
                lastRangeItem.end = endTimeNum;
            }
        });

        return rangeList;
    }

    _checkServiceTimeRange(item: RegistrationDesignatedTimeItem): boolean {
        const startTime = TimeUtils.strTime2MinuteUnit(item.start);
        const serviceTime = this.state.serviceTime; // 最小服务时长(分钟)
        const sectionsStartTime = this.splitShiftCells();
        let status = false;
        sectionsStartTime.map((timeSlot) => {
            // 开始时间 不包含 分段列表每个可选分段的end时间
            if (startTime !== timeSlot.end) {
                // 可选段start时间 <= startTime  < startTime + 服务时间 < 可选段end时间   A<B<C<D = A<C + D<B
                if (timeSlot.start! <= startTime && startTime + serviceTime <= timeSlot.end!) {
                    status = true;
                }
            }
        });
        return status;
    }

    _selTimeStatus(info: RegistrationDesignatedTimeItem): boolean {
        let select = false;
        const currentShift = this.state.selTimeRangeList;
        currentShift?.map((item) => {
            if (info.start == item.start) {
                select = true;
            }
        });
        return select;
    }

    getCurrentSelTime(): void {
        const { selTimeRangeList } = this.state;

        ABCNavigator.pop({
            date: new Range<Date>(
                moment(selTimeRangeList?.[0]?.start, "LT").toDate(),
                moment(selTimeRangeList?.[selTimeRangeList?.length - 1]?.end, "LT").toDate()
            ),
            timeOfDay: selTimeRangeList?.[0]?.timeOfDay,
        });
    }

    renderHeaderView(): JSX.Element {
        const { serviceMinMinutes, serviceMaxMinutes } = this.props;
        const { serviceTime } = this.state;
        let maxCount = serviceMaxMinutes;
        //最大时长---为最小时长的整数倍
        if (serviceMaxMinutes && serviceMinMinutes) {
            maxCount = Math.ceil(serviceMaxMinutes / serviceMinMinutes) * serviceMinMinutes;
        }
        return (
            <View style={[ABCStyles.rowAlignCenterSpaceBetween, ABCStyles.bottomLine, { paddingVertical: Sizes.dp16 }]}>
                <Text style={[TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp22 })]}>服务时长</Text>
                <View style={[ABCStyles.rowAlignCenter]}>
                    <View
                        onClick={() => ({})}
                        style={{
                            width: Sizes.dp114,
                            marginLeft: Sizes.dp20,
                        }}
                    >
                        <NewStyleNumberStepperInputView
                            stepCount={serviceMinMinutes}
                            maxCount={maxCount}
                            minCount={serviceMinMinutes}
                            value={serviceTime}
                            onChanged={(value) => this.onCountInputValueChanged?.(value)}
                        />
                    </View>
                    <SizedBox width={Sizes.dp4} />
                    <Text style={[TextStyles.t16NT2.copyWith({ color: Colors.t2, lineHeight: Sizes.dp22 })]}>分钟</Text>
                </View>
            </View>
        );
    }
    renderTimeListView(): JSX.Element {
        const { selTimeRangeList } = this.state;
        if (!this.timeRangeList?.length) return <View />;
        return (
            <View style={{ paddingTop: Sizes.dp16, flex: 1 }}>
                <ScrollView showsVerticalScrollIndicator={false} style={{ flex: 1 }}>
                    <GridView itemHeight={Sizes.dp36} crossAxisCount={2} crossAxisSpacing={Sizes.dp8} mainAxisSpacing={Sizes.dp8}>
                        {this.timeRangeList?.map((item, index) => {
                            const isSelect = selTimeRangeList?.find((t) => t?.start == item?.start);
                            const isDisable = !item.available || !item.restCount || !this._checkServiceTimeRange(item);
                            return (
                                <AbcTag
                                    key={index}
                                    text={item.start + "-" + item.end}
                                    textStyle={[
                                        TextStyles.t14NT1.copyWith({
                                            color: isSelect ? Colors.mainColor : isDisable ? Colors.T4 : Colors.t2,
                                        }),
                                        { flexShrink: 1 },
                                    ]}
                                    style={[
                                        ABCStyles.rowAlignCenter,
                                        Sizes.marginLTRB(0),
                                        Sizes.paddingLTRB(0, Sizes.dp8),
                                        {
                                            justifyContent: "center",
                                            backgroundColor: isSelect ? Colors.theme2_08 : Colors.bg1,
                                            flexShrink: 1,
                                            borderColor: undefined,
                                        },
                                    ]}
                                    textNumberOfLines={1}
                                    value={item}
                                    onClick={(value) => {
                                        !isDisable && this._updateTimeState(value);
                                    }}
                                />
                            );
                        })}
                    </GridView>
                </ScrollView>
            </View>
        );
    }
    render(): JSX.Element {
        const hasCheck = !!this.state.selTimeRangeList?.length;
        return (
            <View style={[ABCStyles.dialogBaseTopRadius, { backgroundColor: Colors.white, height: pxToDp(468) }]}>
                {BottomSheetHelper.createDefaultHandleBar({
                    onClick: () => {
                        hasCheck ? this.getCurrentSelTime() : undefined;
                    },
                    color: !hasCheck ? Colors.T4 : undefined,
                })}
                <View style={{ paddingHorizontal: Sizes.dp16, flex: 1 }}>
                    {this.renderHeaderView()}
                    {this.renderTimeListView()}
                </View>
                <SafeAreaBottomView bottomSafeAreaColor={Colors.white} />
            </View>
        );
    }
}
