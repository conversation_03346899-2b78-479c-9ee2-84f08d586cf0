/**
 * create by <PERSON><PERSON><PERSON><PERSON><PERSON>
 * desc:
 * create date 2021/02/03
 */

import React from "react";
import { Text, View } from "@hippy/react";
import { TherapyDoctorShiftsWithTime } from "../data/appointment-bean";
import { BaseComponent } from "../../../../base-ui/base-component";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../../theme";
import { AbcView } from "../../../../base-ui/views/abc-view";
import { SizedBox } from "../../../../base-ui";

interface TherapyDoctorShiftsDetailsProps {
    therapyDoctor: TherapyDoctorShiftsWithTime;

    onClick?(arg1: TherapyDoctorShiftsWithTime): void;
}

export class TherapyDoctorShiftsDetails extends BaseComponent<TherapyDoctorShiftsDetailsProps> {
    renderTherapyRestCountDisplayView(): JSX.Element {
        const { therapyDoc<PERSON> } = this.props;
        let restCountDisplay = "";
        let restCountDisplayStyle = TextStyles.t16NT1;
        if (therapyDoctor.canReserve) {
            // 可预约
            if (therapyDoctor.restCountToday) {
                // 有剩余号数（无排班时，剩余号数为空）
                return (
                    <View style={[ABCStyles.rowAlignCenter]}>
                        <SizedBox width={Sizes.dp8} />
                        <Text style={[TextStyles.t16NB1, { lineHeight: Sizes.dp24 }]}>有号</Text>
                    </View>
                );
                // 有排班
            } else if (therapyDoctor?.isScheduled !== 0) {
                return (
                    <View style={[ABCStyles.rowAlignCenter]}>
                        <Text style={[TextStyles.t16NT2, { lineHeight: Sizes.dp24 }]}>无号</Text>
                        <SizedBox width={Sizes.dp8} />
                        <Text style={TextStyles.t16NM.copyWith({ color: Colors.mainColor })}>更多号源</Text>
                    </View>
                );
            } else {
                restCountDisplayStyle = restCountDisplayStyle.copyWith({ color: Colors.T4 });
                restCountDisplay = "未排班";
            }
        } else {
            restCountDisplayStyle = restCountDisplayStyle.copyWith({ color: Colors.T2 });
            restCountDisplay = "不可预约";
        }
        restCountDisplayStyle = restCountDisplayStyle.copyWith({ lineHeight: Sizes.dp24 });
        return <Text style={[restCountDisplayStyle]}>{restCountDisplay}</Text>;
    }

    render(): JSX.Element {
        const { therapyDoctor, onClick } = this.props;
        return (
            <AbcView
                style={[
                    ABCStyles.rowAlignCenter,
                    ABCStyles.bottomLine,
                    {
                        justifyContent: "space-between",
                        height: Sizes.dp48,
                        paddingHorizontal: Sizes.dp16,
                    },
                ]}
                onClick={() => {
                    onClick?.(therapyDoctor);
                }}
            >
                <Text style={[{ minWidth: Sizes.dp96 }, TextStyles.t16MT1.copyWith({ lineHeight: Sizes.dp24 })]} numberOfLines={1}>
                    {therapyDoctor.doctorName ?? ""}
                </Text>
                {this.renderTherapyRestCountDisplayView()}
            </AbcView>
        );
    }
}
