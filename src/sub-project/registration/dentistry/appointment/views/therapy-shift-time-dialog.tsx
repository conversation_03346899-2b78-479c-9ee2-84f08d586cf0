/**
 * create by <PERSON><PERSON><PERSON><PERSON><PERSON>
 * desc:
 * create date 2021/1/14
 */
import React from "react";
import { ScrollView, Text, View } from "@hippy/react";
import { TherapyDoctorShiftsWithTime, TherapyShiftCells } from "../data/appointment-bean";
import { BaseComponent } from "../../../../base-ui/base-component";
import { SafeAreaBottomView } from "../../../../base-ui/safe_area_view";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../../theme";
import { BottomSheetCloseButton, showBottomSheet } from "../../../../base-ui/dialog/bottom_sheet";
import { AbcView } from "../../../../base-ui/views/abc-view";
import { pxToDp } from "../../../../base-ui/utils/ui-utils";
import { ABCNavigator } from "../../../../base-ui/views/abc-navigator";
import { IconFontView, Spacer, ToolBar, ToolBarButtonStyle1 } from "../../../../base-ui";
import { GridView } from "../../../../base-ui/views/grid-view";
import sizes from "../../../../theme/sizes";
import { TimeUtils } from "../../../../common-base-module/utils";
import { Range } from "../../../../base-ui/utils/value-holder";
import _ from "lodash";
import { Toast } from "../../../../base-ui/dialog/toast";

interface TherapyDoctorShiftTimeDialogProps {
    therapyDoctor: TherapyDoctorShiftsWithTime; // 理疗预约-实际时间
    onClick?: () => void;
}
interface TherapyDoctorShiftTimeDialogState {
    currentShift?: TherapyShiftCells[]; // 当前班次
    serviceTime: number; // 服务时间
}

export class TherapyDoctorShiftTimeDialog extends BaseComponent<TherapyDoctorShiftTimeDialogProps, TherapyDoctorShiftTimeDialogState> {
    constructor(props: TherapyDoctorShiftTimeDialogProps) {
        super(props);

        this.state = {
            currentShift: [],
            serviceTime: props.therapyDoctor.serviceMinMinutes ?? 30,
        };
    }

    static async show(therapyDoctor: TherapyDoctorShiftsWithTime): Promise<TherapyShiftCells[] | undefined> {
        return await showBottomSheet(<TherapyDoctorShiftTimeDialog therapyDoctor={therapyDoctor} />);
    }

    render(): JSX.Element {
        const hasCheck = !!this.state.currentShift?.length;

        return (
            <View style={[{ backgroundColor: Colors.white }]}>
                <View style={[{ height: pxToDp(468) }]}>
                    {this._renderDialogTitle()}
                    {this._renderSelectServiceTime()}
                    {this._renderShiftTime()}
                    <ToolBar>
                        {this._showSelectServiceTimeRange()}
                        <Spacer />
                        <ToolBarButtonStyle1
                            style={{ width: Sizes.dp100 }}
                            text={"下一步"}
                            onClick={
                                hasCheck
                                    ? () => {
                                          ABCNavigator.pop(this.state.currentShift);
                                      }
                                    : undefined
                            }
                        />
                    </ToolBar>
                </View>
                <SafeAreaBottomView />
            </View>
        );
    }

    /**
     * _选择到店时间
     * @param info
     */
    private _renderShiftTimeItem(info: TherapyShiftCells): JSX.Element {
        let select = false;
        const currentShift = this.state.currentShift;
        currentShift?.map((item) => {
            if (info.reserveStart == item.reserveStart) {
                select = true;
            }
        });
        const borderColor = select ? Colors.B1 : Colors.dividerLineColor;
        const textDefaultColor = Colors.T2;
        const textSelectColor = Colors.B1;
        const disable = !info.isAvailable || !this._checkServiceTimeRange(info); // 单元格不可用 或不在服务时间范围

        return (
            <AbcView
                key={info.reserveStart}
                style={[
                    sizes.marginLTRB(0, Sizes.dp4, Sizes.dp8, Sizes.dp4),
                    sizes.paddingLTRB(Sizes.dp8, Sizes.dp4),
                    { borderWidth: 1, borderRadius: Sizes.dp3 },
                    disable
                        ? { borderColor: borderColor, backgroundColor: Colors.window_bg }
                        : { borderColor: borderColor, backgroundColor: Colors.white },
                ]}
                onClick={() => {
                    if (disable) return;
                    this._countServiceAppointmentEndTime(info);
                }}
            >
                <Text
                    style={[
                        TextStyles.t14NT2.copyWith({
                            lineHeight: Sizes.dp20,
                            color: select ? textSelectColor : textDefaultColor,
                        }),
                        { textAlign: "center" },
                    ]}
                >
                    {info.reserveStart ?? ""}
                </Text>
            </AbcView>
        );
    }

    /**
     * 到店时间宫格
     * @private
     */
    private _renderShiftTime(): JSX.Element {
        const { shiftCells } = this.props.therapyDoctor;
        if (!shiftCells?.length) return <View />;
        return (
            <ScrollView showsVerticalScrollIndicator={false}>
                <View
                    style={[ABCStyles.rowAlignCenter, { alignItems: "center", paddingHorizontal: Sizes.dp16, paddingVertical: Sizes.dp16 }]}
                >
                    <Text style={[TextStyles.t16MT1, { flex: 1, lineHeight: Sizes.dp24 }]}>选择到店时间</Text>
                </View>
                <GridView itemHeight={Sizes.dp36} crossAxisCount={4} style={[Sizes.paddingLTRB(Sizes.dp16, 0), { flex: 1 }]}>
                    {shiftCells?.map((item) => this._renderShiftTimeItem(item))}
                </GridView>
            </ScrollView>
        );
    }

    /**
     * 对话框标题 (理疗师/时间)
     * @private
     */
    private _renderDialogTitle(): JSX.Element {
        const { therapyDoctor } = this.props;

        return (
            <View style={[ABCStyles.rowAlignCenter, ABCStyles.bottomLine, { height: Sizes.dp48, paddingLeft: Sizes.dp16 }]}>
                <Text style={[TextStyles.t14NT4, { flex: 1 }]} numberOfLines={1}>{`${
                    therapyDoctor?.doctorName ?? ""
                } ${TimeUtils.formatDate(therapyDoctor.workingDate, "MM-dd")} (${therapyDoctor.dayOfWeek})`}</Text>
                <BottomSheetCloseButton onTap={() => ABCNavigator.pop()} />
            </View>
        );
    }

    /**
     * 选择理疗服务时长 (最小30分钟)
     * @private
     */
    private _renderSelectServiceTime(): JSX.Element {
        const serviceTime = this.state.serviceTime;
        const serviceMinMinutes = this.props.therapyDoctor.serviceMinMinutes ?? 30;
        const serviceMaxMinutes = this.props.therapyDoctor.serviceMaxMinutes;
        return (
            <View style={[ABCStyles.rowAlignCenter, ABCStyles.bottomLine, { height: Sizes.dp56, padding: Sizes.dp16 }]}>
                <Text style={[TextStyles.t16MT1, { flex: 1, lineHeight: Sizes.dp24 }]} numberOfLines={1}>
                    服务时长（分钟）
                </Text>
                <View
                    style={[
                        ABCStyles.rowAlignCenter,
                        {
                            justifyContent: "space-between",
                            height: Sizes.dp24,
                            minWidth: Sizes.dp113,
                            borderColor: Colors.P1,
                            borderWidth: Sizes.dpHalf,
                            borderRadius: Sizes.dp56 / 2,
                        },
                    ]}
                >
                    <AbcView
                        style={{
                            borderRightColor: Colors.P1,
                            borderRightWidth: Sizes.dpHalf,
                            marginLeft: Sizes.dp10,
                            paddingRight: Sizes.dp8,
                        }}
                        onClick={() => {
                            this._setServiceTime(-(this.props.therapyDoctor.serviceMinMinutes ?? 30));
                        }}
                    >
                        <IconFontView
                            name={"Minus"}
                            size={pxToDp(16)}
                            color={serviceTime - serviceMinMinutes < serviceMinMinutes! ? Colors.T2 : Colors.black}
                        />
                    </AbcView>
                    <Text style={{ marginHorizontal: Sizes.dp8 }}>{`${serviceTime ?? ""} 分钟`}</Text>
                    <AbcView
                        style={{
                            borderLeftColor: Colors.P1,
                            borderLeftWidth: Sizes.dpHalf,
                            paddingLeft: Sizes.dp8,
                            paddingRight: Sizes.dp10,
                            borderRadius: Sizes.dp3, // 解决左上 框架显示BUG
                        }}
                        onClick={() => {
                            this._setServiceTime(this.props.therapyDoctor.serviceMinMinutes ?? 30);
                        }}
                    >
                        <IconFontView
                            name={"Plus"}
                            size={pxToDp(16)}
                            color={serviceTime + serviceMinMinutes > (serviceMaxMinutes ?? 240) ? Colors.T2 : Colors.black}
                        />
                    </AbcView>
                </View>
            </View>
        );
    }

    /**
     * 设置服务分钟时长 ( 30 ~ 240 minute )
     * @param minute
     * @private
     */
    private _setServiceTime(minute: number): void {
        const serviceTime = this.state.serviceTime;
        const serviceMinMinutes = this.props.therapyDoctor.serviceMinMinutes;
        const serviceMaxMinutes = this.props.therapyDoctor.serviceMaxMinutes;
        // 如果服务时长 + 增加分钟数 < 服务最小时长  或 服务时长 + 增加分钟数 > 240
        if (serviceTime + minute < serviceMinMinutes!) {
            return;
        } else if (serviceTime + minute > (serviceMaxMinutes ?? 240)) {
            Toast.show(`服务时长最多可选择${serviceMaxMinutes ?? 240}分钟`, { warning: true }).then();
            return;
        }

        this.setState({ serviceTime: serviceTime + minute, currentShift: [] });
    }

    /**
     * 拆分可服务时间段
     * @private
     */
    private splitShiftCells() {
        const { shiftCells } = this.props.therapyDoctor;
        const rangeList: Range<number>[] = [];
        // 截取（上午/下午）
        shiftCells?.forEach((item) => {
            if (!item.isAvailable) {
                return;
            } else {
                const startTimeNum = TimeUtils.strTime2MinuteUnit(item.reserveStart);
                const endTimeNum = TimeUtils.strTime2MinuteUnit(item.reserveEnd);
                let lastRangeItem = _.last(rangeList);
                if (!lastRangeItem || lastRangeItem.end != startTimeNum) {
                    rangeList.push(new Range<number>());
                }
                lastRangeItem = _.last(rangeList)!;
                if (!lastRangeItem.start) {
                    lastRangeItem.start = startTimeNum;
                }
                lastRangeItem.end = endTimeNum;
            }
        });

        return rangeList;
    }

    /**
     * 检查可服务时间范围 (8:00~11:30 13:00~22:00 拆分时间段)
     * @private
     * @param item
     */
    private _checkServiceTimeRange(item: TherapyShiftCells): boolean {
        const startTime = TimeUtils.strTime2MinuteUnit(item.reserveStart);
        const serviceTime = this.state.serviceTime; // 最小服务时长(分钟)
        if (item.serviceMaxMinutes && item.serviceMaxMinutes < serviceTime) return false;
        const sectionsStartTime = this.splitShiftCells();
        let status = false;
        sectionsStartTime.map((timeSlot) => {
            // 开始时间 不包含 分段列表每个可选分段的end时间
            if (startTime !== timeSlot.end) {
                // 可选段start时间 <= startTime  < startTime + 服务时间 < 可选段end时间   A<B<C<D = A<C + D<B
                if (timeSlot.start! <= startTime && startTime + serviceTime <= timeSlot.end!) {
                    status = true;
                }
            }
        });
        return status;
    }

    /**
     * 计算增加服务时长的预约时间段
     * @param info
     */
    private _countServiceAppointmentEndTime(info: TherapyShiftCells): void {
        const serviceTime = this.state.serviceTime;
        const tmp: TherapyShiftCells[] = [];
        const timeRange = new Range<number>(
            TimeUtils.strTime2MinuteUnit(info.reserveStart),
            TimeUtils.strTime2MinuteUnit(info.reserveStart) + serviceTime
        );
        const { shiftCells } = this.props.therapyDoctor;

        shiftCells?.map((item) => {
            if (
                TimeUtils.strTime2MinuteUnit(item.reserveStart) >= timeRange.start! &&
                (TimeUtils.strTime2MinuteUnit(item.reserveEnd) <= timeRange.end! ||
                    TimeUtils.strTime2MinuteUnit(item.reserveStart) < timeRange.end!)
            ) {
                tmp.push(item);
            }
        });
        this.setState({ currentShift: tmp });
    }

    /**
     * 显示底部选择预约时间 (MM-dd reserveStart ~ reserveEnd )
     * @private
     */
    private _showSelectServiceTimeRange(): JSX.Element {
        const { therapyDoctor } = this.props;
        const currentShift = this.state.currentShift;
        return (
            <AbcView style={[ABCStyles.rowAlignCenter, { minWidth: Sizes.dp213, flexShrink: 1 }]}>
                <Text style={[TextStyles.t14NT2, { lineHeight: Sizes.dp20 }]}>预约时间：</Text>
                <Text style={[TextStyles.t14NT1, { lineHeight: Sizes.dp20 }]}>
                    {` ${TimeUtils.formatDate(therapyDoctor.workingDate, "MM-dd" ?? "")} `}
                    {currentShift?.length
                        ? `${_.first(this.state.currentShift)?.reserveStart ?? ""} ~ ${_.last(this.state.currentShift)?.reserveEnd ?? ""}`
                        : ""}
                </Text>
            </AbcView>
        );
    }
}
