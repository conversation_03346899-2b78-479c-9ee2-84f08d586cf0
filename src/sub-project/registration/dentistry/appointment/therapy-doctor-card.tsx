/**
 * Create By <PERSON><PERSON><PERSON><PERSON><PERSON>
 * Desc: 理疗预约医生信息卡片(理疗师-时间（MM-DD 15:00 ~ 16:00 下午 3号）)
 * Create Date 2021/01/20
 */
import React from "react";
import { BaseComponent } from "../../../base-ui/base-component";
import {
    FocusItemKeys,
    RegistrationDesignatedTime,
    RegistrationFormItem,
    RegistrationProducts,
    RegistrationStatusV2,
} from "../../data/bean";
import { Text, View } from "@hippy/react";
import { TimeUtils } from "../../../common-base-module/utils";
import { ListSettingItem, ListSettingItemStyle } from "../../../base-ui/views/list-setting-item";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../theme";
import { IconFontView, SizedBox } from "../../../base-ui";
import { AbcView } from "../../../base-ui/views/abc-view";
import { AbcBasePanel } from "../../../base-ui/abc-app-library";
import FontWeight from "../../../theme/font-weights";
import { AbcTimeRangeView } from "../../../base-ui/abc-app-library/time/time-range";
import { Range } from "../../../base-ui/utils/value-holder";
import { pxToDp } from "../../../base-ui/utils/ui-utils";
import { RegistrationLocalTimeCountRsp, RegistrationModeType } from "../data/bean";

interface TherapyDoctorCardProps {
    isEditing?: boolean;
    showErrorHint?: boolean;
    registrationFormItem?: RegistrationFormItem;

    onChangeType?(index: number): void;

    onChangeDoctor?(): void;

    modeType?: RegistrationModeType;
    localTimeCountList?: RegistrationLocalTimeCountRsp[];
    isOpenReserveProduct?: boolean; //是否开通预约项目
    flexibleChangeDate?(): void; //灵活模式下改变日期
    flexibleSelectTimeRange?(date?: Range<Date>, timeOfDay?: string): void; //灵活模式下改变时间
    modifyFixedSourceDate?(): void; //固定模式下改变日期
    modifyFixedSourceTime?(): void; //固定模式下选择号数
    modifyFixedTimeRange?(): void; //固定模式下切换时间段
    flexibleSearchProject?(registrationProducts?: RegistrationProducts[]): void; //预约项目搜索
    remark?: string;
    modifyRegistrationVisitSourceRemark?(remark?: string): void;
    isCreate?: boolean; //是否是新增单
    serviceMinMinutes?: number; //最小服务时长
    serviceMaxMinutes?: number; //最大服务时长
    schedulingTimeList?: RegistrationDesignatedTime; //排班时间列表（灵活模式）
    canEditTimeOfFlexible?: boolean; //灵活模式下，时间是否可编辑
}

export class TherapyDoctorCard extends BaseComponent<TherapyDoctorCardProps> {
    private _iconViewRef?: View | null;

    constructor(props: TherapyDoctorCardProps) {
        super(props);
    }

    render(): JSX.Element {
        return <AbcBasePanel panelStyle={Sizes.marginLTRB(Sizes.dp8, Sizes.dp18, Sizes.dp8, 0)}>{this._renderCanEditCard()}</AbcBasePanel>;
    }

    private _formatReserveInfoType(reason?: number): string {
        let str = "";
        switch (reason) {
            case 1:
                str = "提前签到";
                break;
            case 2:
                str = "延后签到";
                break;
            case 3:
                str = "提前理疗";
                break;
            case 4:
                str = "延后理疗";
                break;
        }
        return str;
    }

    //灵活模式---挂号与预约时间相关组件
    private _renderFlexibleTimeView(): JSX.Element {
        const registrationInfo = this.props.registrationFormItem;
        if (!registrationInfo) return <View />;
        const time = registrationInfo?.reserveDate;
        const { localTimeCountList, isCreate, serviceMinMinutes, schedulingTimeList, serviceMaxMinutes, showErrorHint } = this.props;
        let dateStr: string | undefined;
        //预约1，挂号0
        if (!!time) {
            dateStr =
                TimeUtils.getStartOfDate(time).getTime() == TimeUtils.getStartOfDate(new Date()).getTime()
                    ? "今天"
                    : TimeUtils.formatDate(time);
        } else {
            dateStr = TimeUtils.formatDate(new Date());
        }
        //是否需要限制选择时间(1、挂号需要限制；2、预约：小于当天日期需要限制),编辑单不限制时间
        const isLimitSelTime =
            isCreate && !(TimeUtils.getStartOfDate(time ?? new Date()).getTime() > TimeUtils.getStartOfDate(new Date()).getTime());

        const dateTime = (registrationInfo?.reserveDate ?? new Date())?.format("yyyy/MM/dd");
        const localTimeCountRange = localTimeCountList
            ?.filter((t) => !!t.count)
            ?.map((item) => {
                return {
                    date: new Date(`${dateTime} ${item.localTime}`),
                    render: () => {
                        return (
                            <View style={[ABCStyles.rowAlignCenter, { paddingLeft: Sizes.dp29 }]}>
                                <IconFontView name={"patient"} size={Sizes.dp12} color={Colors.T6} />
                                <SizedBox width={Sizes.dp3} />
                                <Text style={[TextStyles.t12NT6.copyWith({ fontWeight: FontWeight.medium })]}>{item?.count ?? ""}</Text>
                            </View>
                        );
                    },
                };
            });

        //判断是否可编辑(类型、科室、医生、初/复诊、时间、备注、就诊来源)
        //待诊、待签、已签均可编辑；已诊只可编辑初/复诊；已退均不可编辑
        let isEditing = true;
        if (
            registrationInfo?.statusV2 == RegistrationStatusV2.waitingSignIn ||
            registrationInfo?.statusV2 == RegistrationStatusV2.waitingDiagnose
        ) {
            isEditing = true;
        } else if (
            registrationInfo?.statusV2 == RegistrationStatusV2.refunded ||
            registrationInfo?.statusV2 == RegistrationStatusV2.diagnosed
        ) {
            isEditing = false;
        }
        //详情页，时间组件有值的情况下只可查看
        const isCanUpdateTime = !!this.props?.canEditTimeOfFlexible;

        return (
            <View>
                <ListSettingItem
                    ref={FocusItemKeys.reserveDate + "date"}
                    style={{ marginHorizontal: Sizes.dp16 }}
                    itemStyle={ListSettingItemStyle.normal}
                    bottomLine={true}
                    title={"日期"}
                    contentHint={"选择日期"}
                    contentBuilder={() => {
                        return (
                            <View
                                style={[
                                    ABCStyles.rowAlignCenter,
                                    {
                                        justifyContent: "flex-end",
                                    },
                                ]}
                            >
                                <AbcView
                                    style={{
                                        backgroundColor: Colors.whiteSmoke,
                                        width: Sizes.dp126,
                                        justifyContent: "center",
                                        alignItems: "center",
                                        borderRadius: Sizes.dp4,
                                        height: Sizes.dp38,
                                    }}
                                    onClick={() => !!isEditing && this.props.flexibleChangeDate?.()}
                                >
                                    <Text style={[TextStyles.t16NT1]}>{dateStr ?? ""}</Text>
                                </AbcView>
                            </View>
                        );
                    }}
                    titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                    leftAlignment={{ alignSelf: undefined }}
                />
                <ListSettingItem
                    ref={FocusItemKeys.reserveDate + "time"}
                    style={{ paddingHorizontal: Sizes.dp16 }}
                    itemStyle={ListSettingItemStyle.normal}
                    bottomLine={false}
                    title={"时间"}
                    contentHint={"选择时间"}
                    contentBuilder={() => {
                        return (
                            <View
                                style={[
                                    ABCStyles.rowAlignCenter,
                                    {
                                        justifyContent: "flex-end",
                                    },
                                ]}
                            >
                                <AbcTimeRangeView
                                    style={[
                                        !registrationInfo?.reserveTime ? Sizes.paddingLTRB(Sizes.dp4, Sizes.dp8) : {},
                                        {
                                            width: Sizes.dp126,
                                            height: Sizes.dp38,
                                        },
                                    ]}
                                    date={
                                        !registrationInfo?.reserveTime
                                            ? undefined
                                            : new Range<Date>(
                                                  new Date(
                                                      !!registrationInfo?.reserveTime?.start
                                                          ? `${dateTime} ${registrationInfo?.reserveTime?.start}`
                                                          : `${dateTime}`
                                                  ),
                                                  new Date(
                                                      !!registrationInfo?.reserveTime?.end
                                                          ? `${dateTime} ${registrationInfo?.reserveTime?.end}`
                                                          : `${dateTime}`
                                                  )
                                              )
                                    }
                                    subTitle={localTimeCountRange}
                                    onChange={(date, timeOfDay) => this.props.flexibleSelectTimeRange?.(date, timeOfDay)}
                                    limitNow={isLimitSelTime}
                                    enable={isCanUpdateTime}
                                    special={true}
                                    serviceMinMinutes={serviceMinMinutes}
                                    serviceMaxMinutes={serviceMaxMinutes}
                                    schedulingTimeList={schedulingTimeList}
                                    isCustomTime={false}
                                    showErrorBorder={!registrationInfo?.reserveTime && showErrorHint}
                                />
                            </View>
                        );
                    }}
                    titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                    leftAlignment={{ alignSelf: undefined }}
                />
            </View>
        );
    }

    //固定号源模式--挂号与预约时间相关组件
    private _renderFixedSourceTimeView(): JSX.Element {
        const { showErrorHint } = this.props;
        const registrationInfo = this.props?.registrationFormItem;
        const time = registrationInfo?.reserveDate;
        const dateStr = time ? TimeUtils.formatDate(time) : undefined;
        const periodTimeStr = registrationInfo?.timeOfDay ?? "";
        const timeStr = !!registrationInfo?.reserveTime
            ? `${registrationInfo?.displayOrderNumber ?? ""} ${registrationInfo?.reserveTime?.start} ~ ${
                  registrationInfo?.reserveTime?.end
              }`
            : "";

        //判断是否可编辑(类型、科室、医生、初/复诊、时间、备注、就诊来源)
        //待诊、待签、已签均可编辑；已诊只可编辑初/复诊；已退均不可编辑
        let isEditing = true;
        if (
            registrationInfo?.statusV2 == RegistrationStatusV2.waitingSignIn ||
            registrationInfo?.statusV2 == RegistrationStatusV2.waitingDiagnose
        ) {
            isEditing = true;
        } else if (
            registrationInfo?.statusV2 == RegistrationStatusV2.refunded ||
            registrationInfo?.statusV2 == RegistrationStatusV2.diagnosed
        ) {
            isEditing = false;
        }

        return (
            <View>
                <ListSettingItem
                    ref={FocusItemKeys.reserveDate + "date"}
                    style={{ marginHorizontal: Sizes.dp16 }}
                    itemStyle={ListSettingItemStyle.normal}
                    bottomLine={true}
                    title={"日期"}
                    contentBuilder={() => {
                        return (
                            <View
                                style={[
                                    ABCStyles.rowAlignCenter,
                                    {
                                        justifyContent: "flex-end",
                                    },
                                ]}
                            >
                                <AbcView
                                    style={[
                                        !!periodTimeStr
                                            ? { width: Sizes.dp109 }
                                            : {
                                                  width: pxToDp(172),
                                              },
                                        {
                                            backgroundColor: Colors.whiteSmoke,
                                            justifyContent: "center",
                                            alignItems: "center",
                                            borderRadius: Sizes.dp4,
                                            height: Sizes.dp38,
                                        },
                                    ]}
                                    onClick={() => {
                                        !!isEditing && this.props.modifyFixedSourceDate?.();
                                    }}
                                >
                                    <Text style={[TextStyles.t16NT1.copyWith({ color: !!dateStr ? Colors.T1 : Colors.T4 })]}>
                                        {dateStr ?? "选择日期"}
                                    </Text>
                                </AbcView>
                                {!!periodTimeStr && (
                                    <AbcView
                                        style={[
                                            Sizes.paddingLTRB(Sizes.dp12, 0),
                                            {
                                                backgroundColor: Colors.whiteSmoke,
                                                marginLeft: Sizes.dp7,
                                                height: Sizes.dp38,
                                                borderRadius: Sizes.dp4,
                                                justifyContent: "center",
                                                alignItems: "center",
                                            },
                                        ]}
                                        onClick={() => !!isEditing && this.props.modifyFixedTimeRange?.()}
                                    >
                                        <Text style={[TextStyles.t16NT1]}>{periodTimeStr}</Text>
                                    </AbcView>
                                )}
                            </View>
                        );
                    }}
                    titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                    leftAlignment={{ alignSelf: undefined }}
                />
                <ListSettingItem
                    ref={FocusItemKeys.reserveDate + "time"}
                    style={{ paddingHorizontal: Sizes.dp16 }}
                    itemStyle={ListSettingItemStyle.normal}
                    bottomLine={false}
                    title={"号数"}
                    contentHint={"选择号数"}
                    onClick={() => !!timeStr && this.props.modifyFixedSourceTime?.()}
                    contentBuilder={() => {
                        return (
                            <View
                                style={[
                                    ABCStyles.rowAlignCenter,

                                    {
                                        justifyContent: "flex-end",
                                    },
                                ]}
                            >
                                <View
                                    style={[
                                        {
                                            width: pxToDp(172),

                                            backgroundColor: Colors.whiteSmoke,
                                            justifyContent: "center",
                                            alignItems: "center",
                                            borderRadius: Sizes.dp4,
                                            height: Sizes.dp38,
                                        },
                                    ]}
                                >
                                    <Text
                                        style={[
                                            TextStyles.t16NT1.copyWith({
                                                color: !!timeStr ? Colors.T1 : showErrorHint ? Colors.Y2 : Colors.T4,
                                            }),
                                        ]}
                                    >
                                        {!!timeStr ? timeStr : "无可用号源"}
                                    </Text>
                                </View>
                            </View>
                        );
                    }}
                    titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                    leftAlignment={{ alignSelf: undefined }}
                />
            </View>
        );
    }

    /**
     * 新增理疗预约卡(理疗师、预约时间、预约号)
     * @private
     */
    private _renderCanEditCard(): JSX.Element {
        const { registrationFormItem, onChangeDoctor, modeType, isOpenReserveProduct, isEditing, remark } = this.props;

        const itemStyle = ListSettingItemStyle.expandIcon;
        return (
            <View style={[ABCStyles.bottomLine, { backgroundColor: Colors.prescriptionBg }]}>
                <AbcBasePanel>
                    <View style={[{ paddingLeft: Sizes.dp16 }]}>
                        <ListSettingItem
                            style={{ paddingRight: Sizes.dp16 }}
                            itemStyle={itemStyle}
                            bottomLine={true}
                            title={"理疗师"}
                            contentHint={"选择理疗师"}
                            content={registrationFormItem?.doctorName}
                            contentStyle={[
                                ABCStyles.rowAlignCenter,
                                {
                                    paddingVertical: Sizes.dp14,
                                    justifyContent: "flex-end",
                                },
                            ]}
                            contentTextStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                            onClick={() => onChangeDoctor?.()}
                        />
                    </View>
                </AbcBasePanel>

                {modeType == RegistrationModeType.flexibleMode && !!isOpenReserveProduct && (
                    <AbcBasePanel panelStyle={Sizes.marginLTRB(0, Sizes.dp18, 0, 0)}>
                        <View style={[{ paddingLeft: Sizes.dp16 }]}>
                            <ListSettingItem
                                style={{ marginRight: Sizes.dp16, justifyContent: "center", height: Sizes.dp55 }}
                                itemStyle={isEditing ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal}
                                bottomLine={true}
                                title={"项目"}
                                contentHint={"选择项目"}
                                content={registrationFormItem?.registrationProducts?.map((item) => item.displayName).join("、")}
                                contentStyle={[
                                    ABCStyles.rowAlignCenter,
                                    {
                                        paddingVertical: Sizes.dp16,
                                        justifyContent: "flex-end",
                                    },
                                ]}
                                onClick={() => {
                                    !!isEditing && this.props.flexibleSearchProject?.(registrationFormItem?.registrationProducts);
                                }}
                                contentNumberOfLine={1}
                                titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                                leftAlignment={{ alignSelf: undefined }}
                            />
                        </View>
                    </AbcBasePanel>
                )}

                <AbcBasePanel panelStyle={Sizes.marginLTRB(0, Sizes.dp18, 0, 0)}>
                    {modeType == RegistrationModeType.flexibleMode && this._renderFlexibleTimeView()}
                    {modeType == RegistrationModeType.fixedMode && this._renderFixedSourceTimeView()}
                </AbcBasePanel>
                <AbcBasePanel panelStyle={Sizes.marginLTRB(0, Sizes.dp18, 0, 0)}>
                    <ListSettingItem
                        itemStyle={isEditing ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal}
                        title={"备注"}
                        style={{ paddingHorizontal: Sizes.listHorizontalMargin }}
                        contentStyle={[ABCStyles.rowAlignCenter, { paddingVertical: Sizes.dp16, justifyContent: "flex-end" }]}
                        content={remark}
                        bottomLine={false}
                        onClick={() => {
                            if (isEditing) this.props.modifyRegistrationVisitSourceRemark?.(remark);
                        }}
                        contentNumberOfLine={1}
                        titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                        leftAlignment={{ alignSelf: undefined }}
                    />
                </AbcBasePanel>
            </View>
        );
    }
}
