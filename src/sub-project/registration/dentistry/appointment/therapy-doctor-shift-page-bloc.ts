/**
 * create by <PERSON><PERSON><PERSON><PERSON><PERSON>
 * desc:
 * create date 2021/1/28
 */
import React from "react";
import _ from "lodash";
import { Subject } from "rxjs";
import { switchMap } from "rxjs/operators";
import { JsonMapper } from "../../../common-base-module/json-mapper/json-mapper";
import { Bloc, BlocEvent } from "../../../bloc";
import { Range } from "../../../base-ui/utils/value-holder";
import { ABCError } from "../../../common-base-module/common-error";
import { EventName } from "../../../bloc/bloc";
import { BaseLoadingState } from "../../../bloc/bloc-helper";
import { ABCNavigator } from "../../../base-ui/views/abc-navigator";
import { TherapyDoctorShiftTimeDialog } from "./views/therapy-shift-time-dialog";
import { GetTherapyRangeScheduleItem, TherapyDoctorShiftsWithTime } from "./data/appointment-bean";
import { TherapyAppointmentAgent } from "./data/therapy-appointment-agent";

export class State extends BaseLoadingState {
    pageNo = 1;
    loading = true;
    shiftsLoading = false;
    shiftsRange: Range<Date> = new Range(new Date());
    scheduleList: GetTherapyRangeScheduleItem[] = []; // (理疗师id、排班日期、是否可预约、最小服务时间、号源列表[]) // shiftCells 号源列表 （时段、剩余号数列表、预约开始/结束时间、当前单元规格是否可用）
    therapyDoctor?: TherapyDoctorShiftsWithTime;
    // therapyDoctors?: TherapyDoctorsInfo[];

    // get therapyDoctorShiftsListByTime(): Map<Date, GetTherapyRangeScheduleItem[]> {
    //     const therapyDoctorShiftsListByTimeList: Map<Date, GetTherapyRangeScheduleItem[]> = new Map();
    //     this.scheduleList?.forEach((therapyDoctorInfo) => {
    //         therapyDoctorInfo.shiftCells?.forEach((shift) => {
    //             const newInfo = JsonMapper.deserialize(GetTherapyRangeScheduleItem, _.assign(therapyDoctorInfo, shift));
    //         });
    //     });
    //     return therapyDoctorShiftsListByTimeList;
    // }

    clone(): State {
        return Object.assign(new State(), this);
    }
}

export class TherapyDoctorShiftPageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<TherapyDoctorShiftPageBloc | undefined>(undefined);
    private _doctorId: string;
    private _loadShiftsTrigger: Subject<number> = new Subject<number>();

    constructor(options: { doctorId: string }) {
        super();
        this._doctorId = options.doctorId;
        this.dispatch(new _EventInit()).then();
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    static fromContext(context: TherapyDoctorShiftPageBloc): TherapyDoctorShiftPageBloc {
        return context;
    }

    initialState(): State {
        return this.innerState;
    }

    /**
     * 创建事件处理程序
     */
    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventCheckDoctor, this._mapEventCheckDoctor); // 检查理疗师排班
        // map.set(_EventCheckDoctor, this._mapEventAppointDoctor); // 指定无号理疗师 排班
        map.set(_EventLoadMore, this._mapEventLoadMore);
        return map;
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    public requestGetAppointTherapyDoctorShifts(shift: GetTherapyRangeScheduleItem): void {
        this.dispatch(new _EventGetAppointTherapyDoctor(shift));
    }

    public requestCheckDoctor(shift: TherapyDoctorShiftsWithTime): void {
        this.dispatch(new _EventCheckDoctor(shift));
    }

    public requestLoadMore(): void {
        this.dispatch(new _EventLoadMore());
    }

    private _pageNo2Date(): Date {
        return new Date(new Date().getTime() + this.innerState.pageNo * 60 * 60 * 24 * 1000 * 20);
    }

    private async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        this._loadShiftsTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.shiftsLoading = true;
                    if (this.innerState.shiftsRange.end) {
                        this.innerState.shiftsRange.start = this.innerState.shiftsRange.end;
                        this.innerState.shiftsRange.end = this._pageNo2Date();
                    } else {
                        this.innerState.shiftsRange.end = this._pageNo2Date();
                    }
                    // 获取指定时间范围医生排班预约号源
                    return TherapyAppointmentAgent.getTherapyDoctorShiftsListForApp({
                        doctorId: this._doctorId,
                        timeRange: this.innerState.shiftsRange,
                    })
                        .catch((e) => new ABCError(e))
                        .toObservable();
                })
            )
            .subscribe((doctorInfo) => {
                this.innerState.shiftsLoading = false;
                if (doctorInfo instanceof ABCError) {
                } else {
                    this.innerState.loading = false;
                    doctorInfo.scheduleList?.forEach((shift) => {
                        const newData = JsonMapper.deserialize(
                            GetTherapyRangeScheduleItem,
                            _.assign(
                                {
                                    doctorId: this._doctorId,
                                },
                                shift
                            )
                        );
                        this.innerState.scheduleList.push(newData);
                    });
                }
                this.update();
            })
            .addToDisposableBag(this);
        this._loadShiftsTrigger.next();
    }

    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    private async *_mapEventLoadMore(): AsyncGenerator<State> {
        if (this.innerState.pageNo == 10) return;
        this.innerState.pageNo += 1;
        this._loadShiftsTrigger.next();
    }

    /**
     * 检查更多号源理疗师排班
     * @param event
     * @private
     */
    private async *_mapEventCheckDoctor(event: _EventCheckDoctor): AsyncGenerator<State> {
        const therapyDoctor = event.therapyDoctor;
        let status = false;
        // 可预约
        if (therapyDoctor.canReserve) {
            // 有号 选择时间
            if (therapyDoctor.shiftCells?.length !== 0) {
                status = !!therapyDoctor?.shiftCells?.some((item) => !!item.isAvailable);
            }
        }
        if (status) {
            // 获取理疗师详细排班表 (选择理疗时间)
            const doctorShifts = await TherapyAppointmentAgent.getDesignatedTherapyDoctorShiftsList(
                therapyDoctor.doctorId,
                therapyDoctor.workingDate
            ).then((rsp) => rsp.shiftCells);
            // format
            const req = JsonMapper.deserialize(TherapyDoctorShiftsWithTime, {
                shiftCells: doctorShifts,
                canReserve: therapyDoctor.canReserve, // 是否可预约 1
                dayOfWeek: therapyDoctor.dayOfWeek, // 周几
                doctorId: therapyDoctor.doctorId, // 理疗师 ID
                doctorName: therapyDoctor.doctorName, // 理疗师 ID
                serviceMinMinutes: therapyDoctor.serviceMinMinutes, // 最小服务分钟数
                workingDate: therapyDoctor.workingDate, // 排班日期
                restCountToday: therapyDoctor.restCountToday, // 剩余号数，无排班时，剩余号数为空
                isScheduled: therapyDoctor.isScheduled, // 是否排班
            });
            // 展示弹窗
            const result = await TherapyDoctorShiftTimeDialog.show(req);
            if (result) {
                ABCNavigator.pop({ doctor: therapyDoctor, result: result });
            }
        }
        yield this.innerState.clone();
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventCheckDoctor extends _Event {
    therapyDoctor: TherapyDoctorShiftsWithTime;

    constructor(doctor: TherapyDoctorShiftsWithTime) {
        super();
        this.therapyDoctor = doctor;
    }
}

class _EventGetAppointTherapyDoctor extends _Event {
    appointDoctor: GetTherapyRangeScheduleItem;

    constructor(doctor: GetTherapyRangeScheduleItem) {
        super();
        this.appointDoctor = doctor;
    }
}

class _EventLoadMore extends _Event {}
