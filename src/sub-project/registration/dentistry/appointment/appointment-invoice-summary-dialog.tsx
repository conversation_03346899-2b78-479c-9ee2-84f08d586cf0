/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2022/11/29
 * @Copyright 成都字节流科技有限公司© 2022
 */
import React from "react";
import { Text, View } from "@hippy/react";
import { ABCNetworkPageContentStatus, BaseBlocNetworkView } from "../../../base-ui/base-page";
import { showBottomSheet } from "../../../base-ui/dialog/bottom_sheet";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../theme";
import { BlocHelper } from "../../../bloc/bloc-helper";
import { TherapyAppointmentInvoicePageBloc } from "./therapy-appointment-invoice-page-bloc";
import { RegistrationPatientCardInfoView } from "../../views/registration-patient-card-info-view";
import { SafeAreaBottomView } from "../../../base-ui/safe_area_view";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "../../../base-ui";
import { AbcToolBar, AbcToolBarButtonStyle1, AbcToolBarButtonStyle2 } from "../../../base-ui/abc-app-library/tool-bar-button/tool-bar";
import { TimeUtils } from "../../../common-base-module/utils";
import { PayStatusV2, RegistrationStatusV2 } from "../../data/bean";
import UiUtils, { pxToDp } from "../../../base-ui/utils/ui-utils";
import { HistoryPermissionModuleType } from "../../../base-business/data/beans";

interface AppointmentInvoiceSummaryDialogProps {
    id?: string;
    statusName?: string;
}

export class AppointmentInvoiceSummaryDialog extends BaseBlocNetworkView<
    AppointmentInvoiceSummaryDialogProps,
    TherapyAppointmentInvoicePageBloc
> {
    constructor(props: AppointmentInvoiceSummaryDialogProps) {
        super(props);
        this.bloc = new TherapyAppointmentInvoicePageBloc({ id: props.id });
    }

    static async show(options?: AppointmentInvoiceSummaryDialogProps): Promise<void> {
        return showBottomSheet(<AppointmentInvoiceSummaryDialog {...options} />);
    }

    componentDidMount(): void {
        super.componentDidMount();

        BlocHelper.connectLoadingStatus(this.bloc, this);
    }

    private _contentHeight = pxToDp(322) + UiUtils.safeAreaBottomHeight();

    errorContent(): JSX.Element {
        return <View style={[ABCStyles.panelTopStyle, { height: this._contentHeight }]}>{super.errorContent()}</View>;
    }

    renderContent(): JSX.Element {
        if (!this.bloc.currentState.detail) return <View style={{ height: this._contentHeight }} />;
        return (
            <View
                style={[
                    ABCStyles.panelTopStyle,
                    this.contentStatus == ABCNetworkPageContentStatus.loading ? { height: this._contentHeight } : {},
                    { backgroundColor: Colors.white },
                ]}
            >
                {this._renderPatientView()}
                {this._renderDetail()}
                {this._renderBottomGroup()}
                <SafeAreaBottomView />
            </View>
        );
    }

    /**
     * 理疗师渲染视图
     * @private
     */
    protected _renderDoctorDetailView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail;
        const therapyRegistration = detail?.registrationFormItem;
        return (
            <View style={[ABCStyles.rowAlignCenter, { paddingVertical: Sizes.dp8 }]}>
                <Text style={[TextStyles.t16NT6]}>医生：</Text>
                <Text style={[TextStyles.t16NT1]}>
                    {`${!!therapyRegistration?.doctorName ? therapyRegistration?.doctorName : "不指定理疗师"}`}
                </Text>
            </View>
        );
    }

    /**
     * 预约视图
     * @private
     */
    protected _renderReserveDetailView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail;
        const therapyRegistration = detail?.registrationFormItem;
        const time = therapyRegistration?.reserveDate; // 预约日期 (-年 -月 -日)
        const { statusName } = this.props;
        //固定模式---具体编号
        const fixedOrderNumInfo =
            (!!therapyRegistration?.timeOfDay ? therapyRegistration?.timeOfDay : "") + therapyRegistration?.displayOrderNumber;
        return (
            <View style={[{ flexDirection: "row" }, { paddingVertical: Sizes.dp8 }]}>
                <Text style={[TextStyles.t16NT6]}>时间：</Text>
                <View style={[{ alignSelf: "flex-start" }]}>
                    <Text style={[TextStyles.t16NT1, { lineHeight: Sizes.dp22 }]}>
                        {`${TimeUtils.formatDate(time, "MM月dd日")} ${TimeUtils.getDayOfWeek(therapyRegistration?.reserveDate, "周")} ${
                            state.isFixedMode ? fixedOrderNumInfo : ""
                        }`}
                    </Text>
                    <Text style={[TextStyles.t16NT1, { lineHeight: Sizes.dp22 }]}>
                        {`${therapyRegistration?.reserveTime?.start}~${therapyRegistration?.reserveTime?.end}`}
                    </Text>
                </View>

                <Spacer />
                {!!statusName && (
                    <Text
                        style={[
                            TextStyles.t16NY2.copyWith({
                                color:
                                    statusName == "待签"
                                        ? Colors.Y2
                                        : statusName == "已退"
                                        ? Colors.T6
                                        : statusName == "待诊"
                                        ? Colors.B2
                                        : statusName == "已诊"
                                        ? Colors.T3
                                        : Colors.mainColor,
                            }),
                        ]}
                    >
                        {statusName ?? ""}
                    </Text>
                )}
            </View>
        );
    }

    /**
     * 项目视图
     * @private
     */
    protected _renderProductDetailView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail,
            registrationFormItem = detail?.registrationFormItem;
        return (
            <View>
                {!!state.registrationConfig?.isOpenReserveProduct && !!registrationFormItem?.registrationProductName && (
                    <View style={{ paddingVertical: Sizes.dp8 }}>
                        <View style={{ flexDirection: "row", flex: 1 }}>
                            <Text style={[TextStyles.t16NT6]}>项目：</Text>
                            <Text style={[TextStyles.t16NT1, { flexShrink: 1 }]}>
                                {registrationFormItem?.registrationProductName ?? ""}
                            </Text>
                        </View>
                    </View>
                )}
            </View>
        );
    }

    /**
     * 诊室视图
     */
    protected _renderConsultingRoomView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail,
            registrationFormItem = detail?.registrationFormItem;
        return (
            <View style={{ paddingVertical: Sizes.dp8 }}>
                <View style={{ flexDirection: "row", flex: 1 }}>
                    <Text style={[TextStyles.t16NT6]}>诊室：</Text>
                    <Text style={[TextStyles.t16NT1, { flexShrink: 1 }]}>{registrationFormItem?.consultingRoomName ?? "无"}</Text>
                </View>
            </View>
        );
    }

    /**
     * 备注视图
     * @private
     */
    protected _renderRemarkDetailView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail;
        return (
            <View>
                {!!detail?.visitSourceRemark && (
                    <View
                        style={{
                            paddingVertical: Sizes.dp8,
                        }}
                    >
                        <View style={{ flexDirection: "row", flex: 1 }}>
                            <Text style={[TextStyles.t16NT6]}>备注：</Text>
                            <Text style={[TextStyles.t16NT1, { flexShrink: 1, lineHeight: Sizes.dp22 }]}>
                                {detail?.visitSourceRemark ?? ""}
                            </Text>
                        </View>
                    </View>
                )}
            </View>
        );
    }

    /**
     * 创建人视图
     * @protected
     */
    protected _renderCreateView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail;
        const registrationFormItem = detail?.registrationFormItem;
        const createTime = registrationFormItem?.created?.format("yyyy-MM-dd") ?? "";
        const createDisplayStr = `${registrationFormItem?.displayCreatedByName ?? ""}${!!createTime ? `(${createTime})` : ""}`;
        if (!createDisplayStr) return <View />;
        return (
            <View
                style={{
                    paddingVertical: Sizes.dp8,
                }}
            >
                <View style={{ flexDirection: "row", flex: 1 }}>
                    <Text style={[TextStyles.t16NT6]}>创建：</Text>
                    <Text style={[TextStyles.t16NT1, { flexShrink: 1, lineHeight: Sizes.dp22 }]}>{createDisplayStr}</Text>
                </View>
            </View>
        );
    }

    protected _renderDetail(): JSX.Element {
        return (
            <View style={[Sizes.paddingLTRB(Sizes.dp17, Sizes.dp8, Sizes.dp16, Sizes.dp16)]}>
                {this._renderDoctorDetailView()}
                {this._renderReserveDetailView()}
                {this._renderProductDetailView()}
                {this._renderConsultingRoomView()}
                {this._renderRemarkDetailView()}
                {this._renderCreateView()}
            </View>
        );
    }

    private _renderPatientView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail,
            patient = detail?.patient;
        return (
            <RegistrationPatientCardInfoView
                patientSwitchable={false}
                patient={patient}
                isEditing={false}
                isShowLine={true}
                diagnoseCount={state.diagnoseCount}
                type={HistoryPermissionModuleType.registration}
                isCanSeePatientHistoryInRegister={state.canViewDiagnoseHistory}
                canSeePatientMobileInRegister={state.canSeePatientPhone}
                onChange={(patient) => this.bloc.requestUpdatePatientInfo(patient)}
            />
        );
    }

    private _renderBottomGroup(): JSX.Element {
        const state = this.bloc.currentState;
        const registration = state.detail?.registrationFormItem;
        const isFunded =
            ((registration?.statusV2 ?? 0) < RegistrationStatusV2.refunded && (registration?.payStatusV2 ?? 0) < PayStatusV2.refunded) ||
            (registration?.statusV2 === RegistrationStatusV2.refunded && registration?.payStatusV2 === PayStatusV2.partedRefunded);
        const canModifyRegistration = state?.canModifyRegistration; // 理疗预约-编辑、签到、取消预约受权限控制

        if (!isFunded || !canModifyRegistration) return <View />;

        const buttons: JSX.Element[] = [];

        buttons.push(<AbcToolBarButtonStyle2 key={UniqueKey()} text={`编辑`} onClick={() => this.bloc.requestUpdateAppointmentInfo()} />);

        if (registration?.statusV2 == RegistrationStatusV2.waitingSignIn) {
            buttons.push(<AbcToolBarButtonStyle1 key={UniqueKey()} text={"签到"} onClick={() => this.bloc.requestSignInAppointment()} />);
        }
        if (state) {
            if (!state.isCreate && !state.isCanCanceled) {
                buttons.push(
                    <AbcToolBarButtonStyle2
                        key={UniqueKey()}
                        text={"取消预约"}
                        onClick={() => {
                            this.bloc.requestCancelPatientAppointment();
                        }}
                    />
                );
            }

            if (state.isCreate) {
                buttons.push(
                    <AbcToolBarButtonStyle1
                        key={UniqueKey()}
                        text={"完成预约"}
                        onClick={() => this.bloc.requestFinishTherapyAppointment()}
                    />
                );
            }
        }

        if (!buttons.length) return <View />;

        return (
            <AbcToolBar showMoreBtn={true} showCount={4} direction={"right"} lineHeight={1}>
                {buttons}
            </AbcToolBar>
        );
    }
}
