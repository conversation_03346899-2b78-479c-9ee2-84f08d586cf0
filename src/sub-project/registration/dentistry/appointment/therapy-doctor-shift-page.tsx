/**
 * create by <PERSON><PERSON><PERSON><PERSON><PERSON>
 * desc:
 * create date 2021/1/28
 */
import React from "react";
import { View, Text } from "@hippy/react";
import { BaseComponent } from "../../../base-ui/base-component";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../theme";
import { AbcView } from "../../../base-ui/views/abc-view";
import { ABCNavigator } from "../../../base-ui/views/abc-navigator";
import { AbcListView } from "../../../base-ui/list/abc-list-view";
import { BlocHelper } from "../../../bloc/bloc-helper";
import { BaseBlocNetworkPage } from "../../../base-ui/base-page";
import { TimeUtils } from "../../../common-base-module/utils";
import { TherapyDoctorShiftPageBloc } from "./therapy-doctor-shift-page-bloc";
import { TherapyDoctorShiftsWithTime, TherapyShiftCells } from "./data/appointment-bean";

interface TherapyDoctorShiftPageProps {
    doctorId: string;
    doctorName: string;
}

export class TherapyDoctorShiftPage extends BaseBlocNetworkPage<TherapyDoctorShiftPageProps, TherapyDoctorShiftPageBloc> {
    constructor(props: TherapyDoctorShiftPageProps) {
        super(props);
        this.bloc = new TherapyDoctorShiftPageBloc({ doctorId: props.doctorId });
    }

    static async show(
        options: TherapyDoctorShiftPageProps
    ): Promise<{ doctor: TherapyDoctorShiftsWithTime; result: TherapyShiftCells[] } | undefined> {
        return ABCNavigator.navigateToPage(<TherapyDoctorShiftPage {...options} />);
    }

    componentDidMount(): void {
        super.componentDidMount();
        BlocHelper.connectLoadingStatus(this.bloc, this);
    }

    getAppBarTitle(): string {
        return "理疗师排班";
    }

    renderContent(): JSX.Element {
        const state = this.bloc.currentState,
            { scheduleList } = state;
        const doctorName = this.props.doctorName;
        return (
            <View style={{ backgroundColor: Colors.white, flex: 1 }}>
                <View style={[ABCStyles.rowAlignCenter, Sizes.paddingLTRB(Sizes.dp16), ABCStyles.bottomLine]}>
                    {/*<View*/}
                    {/*    style={[*/}
                    {/*        ABCStyles.borderOne,*/}
                    {/*        { width: Sizes.dp56, height: Sizes.dp56, borderRadius: Sizes.dp6, overflow: "hidden", marginRight: Sizes.dp12 },*/}
                    {/*    ]}*/}
                    {/*>*/}
                    {/*    <AssetImageView src={doctorDetail?.headImgUrl} />*/}
                    {/*</View>*/}
                    <View style={{ flex: 1 }}>
                        <Text style={[TextStyles.t16MT1.copyWith({ lineHeight: Sizes.dp24 }), { flexShrink: 1 }]} numberOfLines={1}>
                            {doctorName}
                        </Text>
                    </View>
                </View>
                {!scheduleList.length ? (
                    <View style={[ABCStyles.centerChild, { flex: 1 }]}>
                        <Text style={[TextStyles.t14NT2]}>该医生暂无可用号源</Text>
                    </View>
                ) : (
                    <View style={[{ flex: 1 }]}>
                        <AbcListView
                            style={{ flex: 1 }}
                            numberOfRows={scheduleList.length}
                            dataSource={scheduleList}
                            scrollEventThrottle={300}
                            initialListSize={20}
                            getRowKey={(index) => index.toString()}
                            renderRow={(data) => (
                                <_ShiftsListItem
                                    therapyDoctor={data}
                                    onClick={() => {
                                        this.bloc.requestCheckDoctor(data);
                                    }}
                                />
                            )}
                            onEndReached={() => {
                                this.bloc.requestLoadMore();
                            }}
                        />
                    </View>
                )}
            </View>
        );
    }
}

interface _ShiftsListItemProps {
    therapyDoctor: TherapyDoctorShiftsWithTime;

    onClick?(arg1: TherapyDoctorShiftsWithTime): void;
}

/**
 * 更多号源班次列表
 */
class _ShiftsListItem extends BaseComponent<_ShiftsListItemProps> {
    renderTherapyRestCountDisplayView(): JSX.Element {
        const therapyDoctor = this.props.therapyDoctor;
        let status = false;

        let restCountDisplay = "";
        let restCountColor = Colors.T4; // 未排班、不可预约默认为T4

        let restCountDisplayStyle = TextStyles.t16NT1;

        // 可预约
        if (therapyDoctor.canReserve) {
            // 有排班
            if (therapyDoctor?.shiftCells?.length !== 0) {
                status = !!therapyDoctor?.shiftCells?.some((item) => !!item.isAvailable);
                if (status) {
                    restCountColor = Colors.T1;
                    restCountDisplay = "有号";
                } else {
                    restCountColor = Colors.T2;
                    restCountDisplay = "无号";
                }
            } else {
                restCountDisplay = "未排班";
            }
        } else {
            restCountDisplay = "不可预约";
        }
        restCountDisplayStyle = restCountDisplayStyle.copyWith({ lineHeight: Sizes.dp24 });
        return <Text style={[restCountDisplayStyle.copyWith({ color: restCountColor })]}>{restCountDisplay}</Text>;
    }

    render(): JSX.Element {
        const { therapyDoctor, onClick } = this.props;
        return (
            <AbcView
                style={[
                    ABCStyles.rowAlignCenter,
                    ABCStyles.bottomLine,
                    {
                        height: Sizes.dp48,
                        paddingHorizontal: Sizes.dp16,
                    },
                ]}
                onClick={() => {
                    onClick?.(therapyDoctor);
                }}
            >
                <Text style={[{ width: Sizes.dp96 }, TextStyles.t16MT1.copyWith({ lineHeight: Sizes.dp24 })]} numberOfLines={1}>
                    {TimeUtils.formatDate(therapyDoctor.workingDate, "MM-dd") ?? ""}
                </Text>
                <Text style={[{ flex: 1 }, TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp20 })]}>{therapyDoctor.dayOfWeek ?? ""}</Text>
                {this.renderTherapyRestCountDisplayView()}
            </AbcView>
        );
    }
}
