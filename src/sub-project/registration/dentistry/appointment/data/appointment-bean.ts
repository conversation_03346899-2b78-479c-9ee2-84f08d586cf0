import { Patient } from "../../../../base-business/data/beans";
import { fromJsonToDate, fromJsonToMap, JsonProperty } from "../../../../common-base-module/json-mapper/json-mapper";
import { DataBase } from "../../../../views/list-data-holder";
import { Range } from "../../../../base-ui/utils/value-holder";

import { ChargeInvoiceDetailData } from "../../../../charge/data/charge-beans";
import { DoctorShifts } from "../../../data/registration-agent";

/**
 * 理疗预约 签到状态（ 0:无需签到； 1:待签到； 2：已签到 ）
 */
export enum TherapySignInStatus {
    none = 0, // 已预约（无需签到）
    waitingSignIn = 1, // 待签
    signed = 2, // 已签
}

/**
 * 理疗预约 状态（ 0:已预约；10:待签到；20:已签到；91:已退 ）
 */
export enum TherapyStatus {
    all = -1, // 所有 （终端自定义）
    reserved = 0, // 已预约（无需签到）
    waitingSignIn = 10, // 待签
    signed = 20, // 已签
    canceled = 91, // 已退
}

/**
 * 理疗预约（签到号数）
 */
export class TherapyAppointmentOrderNo {
    orderNo?: number; // 签到号数
    reserveStart?: string; // 预约开始时间 (-年 -月 -日)
    reserveEnd?: string; // 预约结束时间 (-年 -月 -日)
    timeOfDay?: string; // 一天中的时间（上午/下午）
}

/**
 * 理疗预约（理疗注册）
 */
export class TherapyRegistration {
    id?: string; // 预约 ID
    chainId?: string; // 连锁 ID
    clinicId?: string; // 门店 ID
    patientId?: string; // 患者 ID
    msgId?: string; // 消息 ID
    doctorId?: string; // 理疗师 ID
    doctorName?: string; // 理疗师 姓名
    consultingRoomId?: string; // 诊室 ID
    consultingRoomName?: string; // 诊室名称
    @JsonProperty({ type: Array, clazz: TherapyAppointmentOrderNo })
    orderNos?: TherapyAppointmentOrderNo[]; // 订单时间
    timeOfDay?: string; // 一天中的时间（上午/下午）

    @JsonProperty({ fromJson: fromJsonToDate })
    reserveDate?: Date; // 预约日期 (-年 -月 -日)

    sourceType?: number; // 预约来源 (0:PC；1:微诊所)
    status?: number; // 状态 (0:已预约；10:待签到；20:已签到；91:已取消)
    signInStatus?: number; // 签到状态 (0:无需签到；1：待签到；2：已签到)
    statusTextStyle?: string; // 签到样式

    signInTime?: string; // null 签到时间
    isExpired?: number; // 0 已过期
    isRevisited?: number; //  1
    code?: number; // 144364
    createdBy?: string;

    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date; // 时间戳 "2021-01-19T11:00:58Z"
    printCount?: number; // 0
    isSignTime?: number; // 0 签到状态是否是待签到并且当天是否是预约日期 (0：否；1：是)
    displayOrderNo?: string;
    oldSimpleInfo?: AppointmentOldSimpleInfo; // 旧预约信息

    // 添加
    statusV2?: number;
    statusName?: string;
    departmentId?: string; // 科室 ID
    departmentName?: string; // 部门 名称

    // isReserved?: number; // 1 预约、 0 挂号

    fee?: number;
    type?: number;
    @JsonProperty({ fromJson: fromJsonToDate })
    estimatedDiagnoseTime?: Date;
    orderNoStr?: string;
    patientOrderId?: string;
    payStatusV2?: number;
    registrationSheetId?: string;
    // oldReserveInfo: null;
    @JsonProperty({
        fromJson: (json) => {
            return new Range<string>(json.start, json.end);
        },
    })
    reserveTime?: Range<string>;
    // signInTime: null;
    orderNo?: number; // 签到号数
    reserveStart?: string;
    reserveEnd?: string;

    @JsonProperty({ fromJson: fromJsonToMap })
    timeOfDayTotalCountMap?: Map<string, number>;
}
/**
 * 完成预约 Req
 */
export class AppointmentDetailOrderNosReq {
    orderNo?: number; // 签到号数
    timeOfDay?: string; // 时段
    reserveStart?: string;
    reserveEnd?: string;
}
export class TherapyDetailReq {
    @JsonProperty({ fromJson: fromJsonToDate })
    reserveDate?: Date; // 预约日期 (-年 -月 -日)
    doctorId?: string;
    doctorName?: string;

    @JsonProperty({ type: Array, clazz: AppointmentDetailOrderNosReq })
    orderNos?: AppointmentDetailOrderNosReq[]; // 签到号数 时段

    // @JsonProperty({ type: Array, clazz: TherapyRegistration })
    therapyRegistration?: TherapyRegistration;

    @JsonProperty({ type: Patient })
    patient?: Patient;
}
/**
 * 理疗预约单
 */
export class TherapyAppointmentSheet extends DataBase {
    id?: string; // 患者 ID

    @JsonProperty({ type: Patient })
    patient?: Patient;
    registrationId?: string; // 理疗预约列表使用 ID
    @JsonProperty({ type: TherapyRegistration })
    therapyRegistration?: TherapyRegistration; // 理疗登记表
    // 添加
    chiefComplaint?: string; // 主诉
    presentHistory?: string; // 现病史
    pastHistory?: string; // 既往病史
    physicalExamination?: string; // 体格检查
    allergicHistory?: string; // 过敏史
    familyHistory?: string; // 家族病史
    personalHistory?: string; // 个人历史

    patientOrderId?: string;
    sourceFrom?: string;
    departmentId?: string; // 科室 ID
    departmentName?: string; // 部门 名称
    doctorId?: string; // 理疗师 ID
    doctorName?: string; // 理疗师 姓名

    @JsonProperty({ type: ChargeInvoiceDetailData })
    chargeSheet?: ChargeInvoiceDetailData;

    signIn?: TherapySignInStatus;

    @JsonProperty({ fromJson: fromJsonToDate })
    reserveDate?: Date; // 预约日期 (-年 -月 -日)

    @JsonProperty({ type: Array, clazz: AppointmentDetailOrderNosReq })
    orderNos?: AppointmentDetailOrderNosReq[]; // 签到号数 时段
    reserveStart?: string;
    reserveEnd?: string;
    consultingRoomName?: string;
}

/**
 * 理疗预约（旧订单）
 */
export class AppointmentOldOrderNos {
    orderNo?: number; // 签到号数（旧）
    reserveStart?: string; // 开始预约时间（旧）
    reserveEnd?: string; // 结束预约时间（旧）
}
/**
 * 理疗预约（旧预约信息）
 */
export class AppointmentOldSimpleInfo {
    @JsonProperty({ type: AppointmentOldOrderNos })
    orderNos?: AppointmentOldOrderNos;
    reserveDate?: string; // 预约日期 (-年 -月 -日)
    reserveStart?: string; // 预约开始时间 (-年 -月 -日)
    reserveEnd?: string; // 预约结束时间 (-年 -月 -日)
    reason?: number; // 1
}

/**
 * 拉取理疗师（全部理疗师）
 */
export class TherapyDoctorsInfo {
    doctorId?: string;
    doctorName?: string;
    namePy?: string;
    namePyFirst?: string;
}

export class GetTherapyDoctorsListRsp {
    @JsonProperty({ type: Array, clazz: TherapyDoctorsInfo })
    doctors?: TherapyDoctorsInfo[];
}

/**
 * 理疗师排班随时间变化 (服务时长/到店时间)
 */

export class TherapyTimeRestOrderNos {
    number?: number; // 第几号
}

export class TherapyShiftCells {
    isAvailable?: number; // 当前单元格是否可用 1
    reserveStart?: string; // 预约单元格开始时间
    reserveEnd?: string; // 预约单元格结束时间
    restOrderNos?: TherapyTimeRestOrderNos[]; // 剩余可用号数列表
    timeOfDay?: string; // 时段 上午/下午
    start?: string; // 预约单元格开始时间
    end?: string; // 预约单元格开始结束
    serviceMaxMinutes?: number; // 最大服务时长
}

export class TherapyDoctorShiftsWithTime {
    canReserve?: number; // 是否可预约 1
    dayOfWeek?: string; // 周几
    doctorId?: string; // 理疗师 ID
    doctorName?: string; // 理疗师 姓名
    serviceMinMinutes?: number; // 最小服务分钟数
    serviceMaxMinutes?: number; // 最大服务分钟数
    shiftCells?: TherapyShiftCells[]; // 移动单元格
    @JsonProperty({ fromJson: fromJsonToDate })
    workingDate?: Date; // 排班日期
    restCountToday?: number; // 剩余号数，无排班时，剩余号数为空
    isScheduled?: number; // 是否排班

    // get displayRestCount(): string {
    //     if (this.canReserve) {
    //         return "余" + (this.restCountToday ? this.restCountToday : 0) + "号";
    //     }
    //     return "";
    // }
}

/**
 * App获取指定时间范围医生排班预约号源
 */
// 号源列表
export class GetTherapyShiftCells {
    timeOfDay?: string; // 时段
    restOrderNos?: string[]; // 剩余可用号数列表
    reserveStart?: string; // 预约单元格开始时间
    reserveEnd?: string; // 预约单元格结束时间
    isAvailable?: number; // 当前单元格是否可用 1
}
export class GetTherapyRangeScheduleItem {
    doctorId?: string; // 理疗师 ID
    // doctorName?: string; // 未合并
    @JsonProperty({ fromJson: fromJsonToDate })
    workingDate?: Date; // 排班日期 YYYY-MM-DD
    canReserve?: number; // 0：不可预约；1：可预约
    dayOfWeek?: string[]; // 周几
    serviceMinMinutes?: number[]; // 最小服务时间 null
    shiftCells?: GetTherapyShiftCells[]; // 号源列表
}

export class GetTherapyRangeScheduleList {
    scheduleList?: GetTherapyRangeScheduleItem[];
}

/**
 * 理疗预约-理疗师统计 (导航栏)
 */
export class TherapyStatDetail extends DataBase {
    doctorId?: string; // 理疗师 ID
    doctorName?: string; // 理疗师姓名
    reservedCount?: number; //
    waitingSignInCount?: number;
    hadSignInCount?: number;
    status?: number;
}
export class GetTherapyStatRsp {
    todayTotalCount?: number;
    tomorrowTotalCount?: number;
    dayAfterTomorrowTotalCount?: number;
    today?: string;
    totalCount?: number;
    reservedCount?: number;
    waitingSignInCount?: number;
    hadSignInCount?: number;
    @JsonProperty({ type: Array, clazz: TherapyStatDetail })
    rows?: Array<TherapyStatDetail>;
}

/**
 * 理疗师预约轮班表 (预约)
 */
export class TherapyShifts {
    canReserve?: number;
    dayOfWeek?: string;
    isScheduled?: number;
    restCountToday?: number; // null
    workingDate?: Date;
}

export class TherapyDoctorShiftsListItem {
    doctorId?: string;
    doctorName?: string;
    @JsonProperty({ type: Array, clazz: TherapyShifts })
    shifts?: TherapyShifts[];
}

export class GetTherapyDoctorShiftsListRsp {
    @JsonProperty({ type: Array, clazz: TherapyDoctorShiftsListItem })
    rows?: TherapyDoctorShiftsListItem[];
    @JsonProperty({ fromJson: fromJsonToDate })
    today?: Date;
}

/**
 * 获取理疗师轮班表Rsp
 */
export class DoctorShiftsListItem {
    doctorId?: string;
    doctorName?: string;
    shifts?: DoctorShifts[];
}

export class GetDoctorShiftsListRsp {
    departmentId?: string;
    departmentName?: string;

    @JsonProperty({ type: Array, clazz: DoctorShiftsListItem })
    rows?: DoctorShiftsListItem[];

    serviceType?: number;

    @JsonProperty({ fromJson: fromJsonToDate })
    today?: Date;
}
