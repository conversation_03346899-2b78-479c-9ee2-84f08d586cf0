/**
 * create by <PERSON><PERSON><PERSON><PERSON><PERSON>
 * desc:
 * create date 2021/01/20
 */

import { Subject } from "rxjs";
import { JsonMapper } from "../../../../common-base-module/json-mapper/json-mapper";
import { ABCApiNetwork } from "../../../../net";
import { RegistrationDetail, RegistrationStatus } from "../../../data/bean";
import { PatientAddress } from "../../../../base-business/data/beans";
import { TimeUtils } from "../../../../common-base-module/utils";
import { GetRegistrationListRsp } from "../../../data/registration-agent";
import {
    GetTherapyDoctorShiftsListRsp,
    GetTherapyDoctorsListRsp,
    GetTherapyRangeScheduleList,
    GetTherapyStatRsp,
    TherapyAppointmentSheet,
    TherapyDetailReq,
    TherapyDoctorShiftsListItem,
    TherapyDoctorShiftsWithTime,
    TherapyDoctorsInfo,
    TherapyRegistration,
    TherapyStatDetail,
} from "./appointment-bean";
import { Range } from "../../../../base-ui/utils/value-holder";
import { ignore } from "../../../../common-base-module/global";

export class TherapyAppointmentAgent {
    static changeObserver = new Subject<number>();

    /**
     * 获取预约理疗师轮班表 (周排班情况:未排班/有号)
     * @param departmentId?
     * @param start?
     */
    static getTherapyDoctorShiftsList(departmentId?: [] | undefined, start?: Date): Promise<TherapyDoctorShiftsListItem[] | undefined> {
        return ABCApiNetwork.get(`nurse/therapy-schedules/doctors-week`, {
            queryParameters: { departmentId: departmentId ?? "", start: TimeUtils.formatDate(start) },
            clazz: GetTherapyDoctorShiftsListRsp,
        }).then((rsp) => rsp.rows);
    }

    /**
     * 获取理疗师详细排班表 (选择理疗时间)
     * @param doctorId
     * @param workingDate
     */
    static getDesignatedTherapyDoctorShiftsList(doctorId?: string, workingDate?: Date): Promise<TherapyDoctorShiftsWithTime> {
        return ABCApiNetwork.get(`nurse/therapy-schedules/${doctorId}/shift-cells`, {
            queryParameters: { doctorId: doctorId, workingDate: TimeUtils.formatDate(workingDate ?? new Date()) },
            clazz: TherapyDoctorShiftsWithTime,
        });
    }

    /**
     * 拉取全部理疗师 (所有理疗师)
     */
    static getAllTherapyDoctorList(): Promise<TherapyDoctorsInfo[] | undefined> {
        return ABCApiNetwork.get(`nurse/therapy-doctors/basic`, {
            clazz: GetTherapyDoctorsListRsp,
        }).then((rsp) => rsp.doctors);
    }

    /**
     * 理疗预约 患者列表（筛选状态）
     * @param params
     */
    static async getTherapyPatientList(params: {
        offset: number;
        limit?: number;
        date?: Date;
        doctorId?: string;
        departmentId?: string;
        keyword?: string;
        displayStatus?: RegistrationStatus;
        patientId?: string;
        end?: Date;
    }): Promise<GetRegistrationListRsp> {
        const { date, displayStatus, end, ...others } = params;
        ignore(end);
        return ABCApiNetwork.get("nurse/therapy-registrations", {
            queryParameters: {
                limit: 20,
                date: TimeUtils.formatDate(date),
                displayStatus: displayStatus !== -1 ? displayStatus : undefined,
                ...others,
            },
            clazz: GetRegistrationListRsp,
            clearUndefined: true,
        });
    }
    /**
     * 理疗预约-筛选时间 (筛选时间 显示当前时间所有预约患者信息列表)
     * @param params
     */
    static getAppointmentDoctorShifts(params: {
        date: Date;
        offset: number;
        limit?: number;
        departmentId?: string;
        displayStatus?: number;
        keyword?: string;
        doctorId?: string;
        doctorName?: string;
    }): Promise<TherapyStatDetail[]> {
        const { date, ...others } = params;
        return ABCApiNetwork.get("nurse/therapy-registrations", {
            queryParameters: { limit: 20, date: TimeUtils.formatDate(date ?? new Date()), ...others },
            clazz: GetTherapyStatRsp,
            clearUndefined: true,
        }).then((rsp) => {
            return rsp.rows ?? [];
        });
    }

    /**
     * 理疗预约-拉取导航栏当前理疗师 (显示当前时间的所有排班理疗师)
     * @param date
     */
    static getTherapyDoctorList(date?: Date): Promise<GetTherapyStatRsp> {
        return ABCApiNetwork.get("nurse/therapy-doctors/registration-stat", {
            queryParameters: { date: TimeUtils.formatDate(date ?? new Date()), limit: 99999, offset: 0 },
            clazz: GetTherapyStatRsp,
        });
    }

    /**
     * 取消理疗预约（退号）
     * @param id
     */
    static cancelPatientAppointment(id: string): Promise<void> {
        return ABCApiNetwork.put(`nurse/therapy-registrations/${id}/cancel`);
    }

    /**
     * 理疗预约签到（签到） TherapySignInStatus
     * @param id
     */
    static therapyAppointmentSignIn(id: string): Promise<void> {
        return ABCApiNetwork.put(`nurse/therapy-registrations/${id}/sign-in`);
    }

    /**
     * 获取理疗预约-详细信息
     * @param id
     */
    static async getTherapyDetail(id: string): Promise<TherapyAppointmentSheet> {
        return ABCApiNetwork.get(`nurse/therapy-registrations/${id}`, { clazz: TherapyAppointmentSheet }).then((rsp) => {
            const {
                addressCityId,
                addressCityName,
                addressDetail,
                addressDistrictId,
                addressDistrictName,
                addressGeo,
                addressProvinceId,
                addressProvinceName,
            } = rsp.patient!;

            //address 或者直接打平在patient中
            if (rsp.patient?.address) {
                return rsp;
            }
            rsp.patient!.address = JsonMapper.deserialize(PatientAddress, {
                addressCityId: addressCityId,
                addressCityName: addressCityName,
                addressDetail: addressDetail,
                addressDistrictId: addressDistrictId,
                addressDistrictName: addressDistrictName,
                addressGeo: addressGeo,
                addressProvinceId: addressProvinceId,
                addressProvinceName: addressProvinceName,
            });
            return rsp;
        });
    }

    /**
     * 完成预约 创建理疗预约单
     * @param params
     */
    static createTherapySheet(params: TherapyDetailReq): Promise<TherapyAppointmentSheet> {
        const { reserveDate, ...others } = params;
        return ABCApiNetwork.post("nurse/therapy-registrations", {
            body: {
                ...others,
                reserveDate: TimeUtils.formatDate(reserveDate), // 预约日期
            },
            clazz: TherapyAppointmentSheet,
        }).then((rsp) => {
            TherapyAppointmentAgent.changeObserver.next();
            return rsp;
        });
    }

    /**
     * App获取指定时间范围医生排班预约号源
     * @param params
     */
    static getTherapyDoctorShiftsListForApp(params: { doctorId: string; timeRange: Range<Date> }): Promise<GetTherapyRangeScheduleList> {
        const { doctorId, timeRange } = params;
        return ABCApiNetwork.get(`nurse/therapy-schedules/${doctorId}/shifts/for-app`, {
            queryParameters: {
                start: TimeUtils.formatDate(timeRange.start),
                end: TimeUtils.formatDate(timeRange.end),
            },
            clazz: GetTherapyRangeScheduleList,
        });
    }

    /**
     * 完成预约
     * @param params
     */
    static getFinishTherapyAppointmentSheet(params: TherapyRegistration): Promise<TherapyRegistration> {
        const { reserveDate, ...others } = params;
        return ABCApiNetwork.post("nurse/therapy-registrations", {
            body: {
                therapyRegistration: {
                    reserveDate: TimeUtils.formatDate(reserveDate ?? new Date()),
                    ...others,
                },
            },
            clazz: RegistrationDetail,
        }).then((rsp) => {
            TherapyAppointmentAgent.changeObserver.next();
            return rsp;
        });
    }
}
