/**
 * create by CZH
 * desc: 挂号预约医生列表 (星期排班情况)
 * create date 2021/1/28
 */
import React from "react";
import { ListView, ScrollView, Text, View, ViewPager } from "@hippy/react";
import { TherapyAppointmentDoctorListPageBloc } from "./therapy-appointment-doctor-list-page-bloc";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../theme";
import { AbcView } from "../../../base-ui/views/abc-view";
import { ABCNavigator } from "../../../base-ui/views/abc-navigator";
import { BlocHelper } from "../../../bloc/bloc-helper";
import { IconFontView, SizedBox, UniqueKey } from "../../../base-ui";
import { LoadingView } from "../../../base-ui/views/loading-view";
import { AnyType } from "../../../common-base-module/common-types";
import { BaseBlocNetworkPage } from "../../../base-ui/base-page";
import UiUtils from "../../../base-ui/utils/ui-utils";
import { TherapyRegistration } from "./data/appointment-bean";
import { BlocBuilder } from "../../../bloc";
import { TimeUtils } from "../../../common-base-module/utils";
import { TherapyDoctorShiftsDetails } from "./views/therapy-views";

interface TherapyAppointmentDoctorListPageProps {
    therapyRegistration?: TherapyRegistration; // 理疗预约
}

export class TherapyAppointmentDoctorListPage extends BaseBlocNetworkPage<
    TherapyAppointmentDoctorListPageProps,
    TherapyAppointmentDoctorListPageBloc
> {
    private _scrollRef?: ScrollView | null;
    private _selectedIndex = 0;
    private _currentMaxOffset = { l: 0 };
    private _viewPager?: ViewPager | null;

    constructor(props: TherapyAppointmentDoctorListPageProps) {
        super(props);
        this.bloc = new TherapyAppointmentDoctorListPageBloc({ therapyRegistration: props.therapyRegistration });
    }

    static showTherapy(therapyRegistration?: TherapyRegistration): Promise<TherapyRegistration> {
        return ABCNavigator.navigateToPage(<TherapyAppointmentDoctorListPage therapyRegistration={therapyRegistration} />);
    }

    getRightAppBarIcons(): JSX.Element[] {
        return [
            <AbcView
                key={"Search"}
                style={{ paddingHorizontal: Sizes.dp8 }}
                onClick={() => {
                    this.bloc.requestSearchTherapyDoctor();
                }}
            >
                <IconFontView name={"Search"} size={Sizes.dp20} color={Colors.black} />
            </AbcView>,
        ];
    }

    getAppBar(): JSX.Element {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }

    componentDidMount(): void {
        BlocHelper.connectLoadingStatus(this.bloc, this);
    }

    renderContent(): JSX.Element | undefined {
        const state = this.bloc.currentState;
        return (
            <View style={{ flex: 1 }}>
                {this._renderTimeListView()}
                <ViewPager
                    ref={(ref) => {
                        this._viewPager = ref;
                    }}
                    initialPage={this._selectedIndex}
                    style={{ flex: 1 }}
                    onPageSelected={this.handlePageSelected.bind(this)}
                >
                    {[...state.doctorShiftsListByTime.values()].map((item, index) => {
                        return (
                            <ListView
                                style={{ backgroundColor: Colors.white, flex: 1 }}
                                key={index}
                                numberOfRows={item.length}
                                dataSource={item}
                                scrollEventThrottle={300}
                                getRowKey={(index) => `${item[index].doctorId}${item[index]}` ?? UniqueKey()}
                                renderRow={(shift) => (
                                    <TherapyDoctorShiftsDetails
                                        therapyDoctor={shift}
                                        onClick={(doctor) => {
                                            this.bloc.requestCheckDoctor(doctor);
                                        }}
                                    />
                                )}
                            />
                        );
                    })}
                </ViewPager>
            </View>
        );
    }

    private handlePageSelected({ position }: AnyType): void {
        this._selectedIndex = position;
        // 滚动当前选中项在可视范围内
        const currentOffsetX = position * Sizes.dp80;
        if (currentOffsetX < this._currentMaxOffset.l) {
            this._scrollRef?.scrollToWithDuration(currentOffsetX, 0, 300);
        } else if (currentOffsetX + Sizes.dp80 > this._currentMaxOffset.l + UiUtils.getScreenWidth()) {
            this._scrollRef?.scrollToWithDuration(currentOffsetX - UiUtils.getScreenWidth() + Sizes.dp80, 0, 300);
        }
        this.forceUpdate();
    }

    /**
     * 星期/日期 （筛选排班医生）
     * @private
     */
    private _renderTimeListView(): JSX.Element {
        const state = this.bloc.currentState;
        return (
            <View style={{ height: Sizes.dp58 }}>
                <ScrollView
                    ref={(ref) => (this._scrollRef = ref)}
                    scrollEventThrottle={30}
                    horizontal={true}
                    style={{ backgroundColor: Colors.white }}
                    showsHorizontalScrollIndicator={false}
                    onScroll={(event) => {
                        const { layoutMeasurement, contentSize, contentOffset } = event;
                        this._currentMaxOffset = {
                            l: contentOffset.x,
                        };
                        if (!state.loading && layoutMeasurement.width + contentOffset.x + 160 > contentSize.width) {
                            this.bloc.requestLoadMoreDoctorShift();
                        }
                    }}
                >
                    {[...state.doctorShiftsListByTime.keys()].map((item, index) => {
                        const selected = index == this._selectedIndex;
                        const textStyle = selected ? TextStyles.t14NM.copyWith({ color: Colors.B1 }) : TextStyles.t14NT1;
                        return (
                            <AbcView
                                key={index}
                                style={[
                                    {
                                        width: Sizes.dp80,
                                        borderWidth: Sizes.dpHalf,
                                        borderColor: Colors.dividerLineColor,
                                        borderRadius: 1,
                                    },
                                    selected ? { borderBottomColor: Colors.B1, borderBottomWidth: Sizes.dp2 } : {},
                                ]}
                                onClick={() => {
                                    this._viewPager?.setPage(index);
                                }}
                            >
                                <View
                                    style={[
                                        selected ? { height: Sizes.dp56 } : { height: Sizes.dp58 },
                                        { alignItems: "center", justifyContent: "center" },
                                    ]}
                                >
                                    <Text style={[textStyle]}>{TimeUtils.formatDate(new Date(item), "MM/dd")}</Text>
                                    <SizedBox height={Sizes.dp8} />
                                    <Text style={[textStyle]}>{TimeUtils.getDayOfWeek(new Date(item))}</Text>
                                </View>
                            </AbcView>
                        );
                    })}
                    {state.timeListLoading && (
                        <View style={[ABCStyles.rowAlignCenterWithPadding, { width: Sizes.dp60, height: Sizes.dp58 }]}>
                            <LoadingView size={Sizes.dp12} />
                        </View>
                    )}
                </ScrollView>
            </View>
        );
    }
}
