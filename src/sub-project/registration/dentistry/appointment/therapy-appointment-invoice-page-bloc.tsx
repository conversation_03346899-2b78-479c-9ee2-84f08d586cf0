/**
 * 成都字节星球科技公司
 * <AUTHOR>
 * @date 2020-01-18
 *
 * @description
 */

import React from "react";
import { Bloc, BlocEvent } from "../../../bloc";
import { actionEvent, EventName } from "../../../bloc/bloc";
import { Range } from "../../../base-ui/utils/value-holder";
import _ from "lodash";
import { BaseLoadingState } from "../../../bloc/bloc-helper";
import { TherapyAppointmentSheet, TherapyStatus } from "./data/appointment-bean";
import { ClinicAgent, EmployeesMeConfig, RegAndTherapyConfig } from "../../../base-business/data/clinic-agent";
import { MedicalRecord, Patient } from "../../../base-business/data/beans";
import { JsonMapper } from "../../../common-base-module/json-mapper/json-mapper";
import { of, Subject } from "rxjs";
import { switchMap } from "rxjs/operators";
import { TherapyAppointmentAgent } from "./data/therapy-appointment-agent";
import { ABCError } from "../../../common-base-module/common-error";
import { CrmAgent } from "../../../patients/data/crm-agent";
import { showBottomSheet, showOptionsBottomSheet } from "../../../base-ui/dialog/bottom_sheet";
import { DialogIndex, showQueryDialog } from "../../../base-ui/dialog/dialog-builder";
import {
    FocusItemKeys,
    PayStatusV2,
    RegistrationDesignatedTime,
    RegistrationDesignatedTimeScheduleInterval,
    RegistrationDetail,
    RegistrationFormItem,
    RegistrationPageSourceType,
    RegistrationProducts,
    RegistrationRevisitStatus,
    RegistrationStatusV2,
} from "../../data/bean";
import { ABCNavigator, TransitionType } from "../../../base-ui/views/abc-navigator";
import { OutpatientAgent } from "../../../outpatient/data/outpatient";
import { Toast } from "../../../base-ui/dialog/toast";
import { errorToStr, TimeUtils } from "../../../common-base-module/utils";
import { LoadingDialog } from "../../../base-ui/dialog/loading-dialog";
import { Department, Doctor, RegistrationDataProvider } from "../../data/registration";
import { AppointmentInvoicePage } from "./appointment-invoice-page";
import { userCenter } from "../../../user-center";
import { DentistryAgent } from "../data/dentistry-agent";
import {
    DentistryClinicProduct,
    DentistryConfig,
    RegistrationDailyReserveStatusRsp,
    RegistrationInfoModifyRsp,
    RegistrationLocalTimeCountRsp,
    RegistrationReserveStatus,
    RegistrationType,
    ReserveStartTime,
} from "../data/bean";
import moment from "moment";
import { RegistrationAgent, RegistrationDetailReq } from "../../data/registration-agent";
import { pxToDp } from "../../../base-ui/utils/ui-utils";
import { Colors, Sizes, TextStyles } from "../../../theme";
import { Text, View } from "@hippy/react";
import { AbcCalendar } from "../../../base-ui/abc-app-library/calendar/calendar-static";
import { showBottomPanel } from "../../../base-ui/abc-app-library/panel";
import { DentistrySearchProject } from "../views/dentistry-search-project";
import { DentistryRemarkInputPage } from "../views/dentistry-remark-input-page";
import { RegistrationSuccessDialog } from "../../views/registration-success-dialog";
import { FixedSourceTime } from "../views/fixed-source-time";
import { URLProtocols } from "../../../url-dispatcher";
import { PatientRegAuditStatus } from "../../../patients/data/crm-bean";

export enum TherapyAppointmentInvoiceType {
    existence = 1, // 预约单
    addition = 2, //  新增预约
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}
// 修改预约患者信息
class _EventModifyAppointmentPatient extends _Event {
    patient: Patient;

    constructor(patient: Patient) {
        super();
        this.patient = patient;
    }
}
// 修改预约理疗师
class _EventModifyAppointmentDoctor extends _Event {}

// 完成预约
class _EventFinishTherapyAppointment extends _Event {}
// 取消预约（退号）
class _EventCancelPatientAppointment extends _Event {}
// 保存预约单修改记录
class _EventSaveAppointment extends _Event {}
// 修改病历卡
class _EventModifyMedicalRecordCard extends _Event {
    medicalRecord: MedicalRecord;

    constructor(medicalRecord: MedicalRecord) {
        super();
        this.medicalRecord = medicalRecord;
    }
}
// 理疗预约签到
class _EventSignInAppointment extends _Event {}

class _EventUpdateAppointmentInfo extends _Event {}
class _EventModifyFlexibleDate extends _Event {}
class _EventSelectTimeRange extends _Event {
    range?: Range<Date>;
    timeOfDay?: string;
    constructor(range?: Range<Date>, timeOfDay?: string) {
        super();
        this.range = range;
        this.timeOfDay = timeOfDay;
    }
}
class _EventModifyFixedSourceDate extends _Event {}
class _EventModifyFixedSourceTime extends _Event {}
class _EventModifyFixedTimeRange extends _Event {}
class _EventSearchProduct extends _Event {
    registrationProducts?: RegistrationProducts[];
    constructor(registrationProducts?: RegistrationProducts[]) {
        super();
        this.registrationProducts = registrationProducts;
    }
}
class _EventModifyRegistrationVisitSourceRemark extends _Event {
    remark: string;
    constructor(text: string) {
        super();
        this.remark = text;
    }
}

class _EventUpdatePatientInfo extends _Event {
    patient: Patient;
    constructor(patient: Patient) {
        super();
        this.patient = patient;
    }
}

export class State extends BaseLoadingState {
    detail?: RegistrationDetail; // 理疗预约单详情

    employeeConfig?: RegAndTherapyConfig; // 员工配置
    pay = { memberId: "" };
    hasChange = false;
    focusKey = "";
    showErrorHint = false; // 显示错误提示

    type: TherapyAppointmentInvoiceType = TherapyAppointmentInvoiceType.addition;
    canEditTimeOfFlexible = false; //灵活模式，编辑状态下，时间是否可选择

    //门诊挂号、理疗预约的预约设置
    registrationConfig?: DentistryConfig;
    departments?: Array<Department>;
    _currentDepartmentDoctors: Array<Doctor> = [];
    set currentDepartmentDoctors(list: Array<Doctor> | undefined) {
        this._currentDepartmentDoctors = list ?? [];
    }

    get currentDepartmentDoctors(): Array<Doctor> | undefined {
        const notCheckDoctor = JsonMapper.deserialize(Doctor, {
            doctorId: undefined,
            doctorName: "不指定理疗师",
        }); // App暂无删除按钮这里使用不指定理疗师代替清除选项 挂号预约同理
        let list: Doctor[] = [];
        list = list.concat(
            [notCheckDoctor],
            this._currentDepartmentDoctors?.filter((f) => f.doctorName !== "不指定理疗师")
        );
        return list;
    }

    localTimeCountList?: RegistrationLocalTimeCountRsp[]; //指定医生在指定科室下挂号数
    dailyReserveStatusList?: RegistrationDailyReserveStatusRsp[]; //医生指定科室日期范围内每日号源状态集合
    registrationTimeRangeList?: RegistrationDesignatedTimeScheduleInterval[]; //固定号源模式、灵活模式下时间段信息
    doctorShiftsInfo?: RegistrationDesignatedTime;
    selectOrderNoTimeList?: RegistrationDesignatedTimeScheduleInterval[]; //固定模式下号源时间段列表

    serviceMinMinutes?: number; //用户排班最小服务时长
    serviceMaxMinutes?: number; //用户排班最大服务时长
    diagnoseCount?: number; //就诊历史个数

    employeesMeConfig?: EmployeesMeConfig;

    //能查看就诊历史
    get canViewDiagnoseHistory(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.registration?.medicalHistory;
    }

    serviceMinDuration?: ReserveStartTime = {
        //就诊时长（1、未选择项目时，根据接口最小时长定；2、选择了项目，则以选中的项目中最小时长的时间为准）
        hour: 0,
        min: 0,
    };
    serviceMaxDuration?: ReserveStartTime = {
        //就诊时长（1、未选择项目时，根据排班最大时长定；2、选择了项目，则以选中的项目中最大时长的时间为准）
        hour: 0,
        min: 0,
    };
    //能够查看患者手机号
    get canSeePatientPhone(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.registration?.isCanSeePatientMobile;
    }

    //能够修改挂号预约
    get canModifyRegistration(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.registration?.isCanSeeModifyRegistration;
    }

    // 灵活模式
    get isFlexibleMode(): boolean {
        return !!this.registrationConfig?.isFlexibleMode;
    }

    //号源模式（固定模式）
    get isFixedMode(): boolean {
        return !!this.registrationConfig?.isFixedMode;
    }

    get isAccuratePart(): boolean {
        return !!this.registrationConfig?.isAccuratePart;
    }

    get isCreate(): boolean {
        return this.type == TherapyAppointmentInvoiceType.addition; // 新增预约
    }
    // 仅待签到状态下课可签到
    get isCanSignIn(): boolean {
        return this.detail?.therapyRegistration?.status == TherapyStatus.waitingSignIn;
    }

    // 已退号状态下不显示底部退号按钮
    get isCanCanceled(): boolean {
        return this.detail?.therapyRegistration?.status == TherapyStatus.canceled;
    }

    get chargeFromRegistration(): boolean {
        return this.detail?.chargeSheet?.type === 1;
    }

    get hasCharged(): boolean {
        const registrationFormItem = this.detail?.therapyRegistration;
        return !(
            (registrationFormItem?.payStatusV2 === PayStatusV2.notPaid || registrationFormItem?.payStatusV2 === PayStatusV2.partedPaid) &&
            (registrationFormItem.statusV2 ?? 0) < RegistrationStatusV2.refunded &&
            this.chargeFromRegistration
        );
    }
    // 病史档案
    get medicalRecord(): MedicalRecord {
        const detail = this.detail;
        return JsonMapper.deserialize(MedicalRecord, {
            chiefComplaint: detail?.chiefComplaint, // 主诉
            presentHistory: detail?.presentHistory, // 现病史
            pastHistory: detail?.pastHistory, // 既往病史
            physicalExamination: detail?.physicalExamination, // 体格检查
        });
    }

    clone(): State {
        return Object.assign(new State(), this);
    }
}

export class ScrollToErrorViewState extends State {
    static fromState(state: State): ScrollToErrorViewState {
        const newState = new ScrollToErrorViewState();
        Object.assign(newState, state);
        return newState;
    }
}

export class TherapyAppointmentInvoicePageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<TherapyAppointmentInvoicePageBloc | undefined>(undefined);
    // private _updatingPatientInformationTrigger: Subject<string> = new Subject<string>(); // 更新患者信息触发器
    private _appointmentDetailsTrigger: Subject<string> = new Subject<string>(); // 预约详情触发器
    private _registrationLocalTimeCountTrigger: Subject<string> = new Subject<string>();
    private _registrationDailyReserveStatusTrigger: Subject<string> = new Subject<string>();
    private _registrationDesignatedTimeTrigger: Subject<string> = new Subject<string>();
    private _employeeServiceMinMinutesTrigger: Subject<string> = new Subject<string>(); //最小服务时长
    private _getPatientBaseDetailTrigger: Subject<string> = new Subject<string>();
    private _getHistoryListTrigger = new Subject<number>(); // 就诊历史
    private _setPatientRegAuditStatusSubject = new Subject<number>();
    private id?: string;
    private _doctorId?: string;
    private _source: RegistrationPageSourceType;

    private _registrationFormItem?: RegistrationFormItem;
    private _fromKanbanEntry?: boolean;
    // 默认预约时间选项
    private defaultAppointmentTimeOptions: {
        id: number;
        title: string;
        timeOfDay: string;
        timeOfDayCount?: number;
        range: Range<string>;
    }[] = [
        { id: 1, title: "今天", timeOfDay: "上午", timeOfDayCount: undefined, range: new Range<string>("00:00", "12:00") },
        { id: 2, title: "今天", timeOfDay: "下午", timeOfDayCount: undefined, range: new Range<string>("12:00", "18:00") },
        { id: 3, title: "今天", timeOfDay: "晚上", timeOfDayCount: undefined, range: new Range<string>("18:00", "24:00") },
    ];

    constructor(options?: {
        id?: string;
        source?: RegistrationPageSourceType;
        registrationFormItem?: RegistrationFormItem;
        fromKanbanEntry?: boolean;
    }) {
        super();

        this.id = options?.id;
        this._registrationFormItem = options?.registrationFormItem;
        this._fromKanbanEntry = options?.fromKanbanEntry;
        this.dispatch(new _EventInit()).then();
        this._source = options?.source ?? RegistrationPageSourceType.normal;
    }

    private _innerState!: State;

    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    static fromContext(context: TherapyAppointmentInvoicePageBloc): TherapyAppointmentInvoicePageBloc {
        return context;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit); // 拉取患者信息
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventModifyAppointmentPatient, this._mapEventModifyAppointmentPatient); // 修改预约患者
        map.set(_EventModifyAppointmentDoctor, this._mapEventModifyAppointmentDoctor); // 修改预约理疗师
        map.set(_EventCancelPatientAppointment, this._mapEventCancelPatientAppointment); // 理疗预约（退号）
        map.set(_EventFinishTherapyAppointment, this._mapEventFinishTherapyAppointment); // 完成理疗预约
        map.set(_EventSignInAppointment, this._mapEventAppointmentSignIn); // 预约签到

        return map;
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    @actionEvent(_EventUpdateAppointmentInfo)
    async *_mapEventUpdateAppointmentInfo(): AsyncGenerator<State> {
        const result = await ABCNavigator.navigateToPage<TherapyAppointmentSheet>(<AppointmentInvoicePage id={this.id} />, {
            transitionType: TransitionType.inFromBottom,
        });
        if (!result) return;
        // this.innerState.detail = result;
        this.update();
    }

    @actionEvent(_EventModifyFlexibleDate)
    async *_mapEventModifyFlexibleDate(): AsyncGenerator<State> {
        this.innerState.detail!.registrationFormItem = this.innerState.detail?.registrationFormItem ?? new RegistrationFormItem();
        const registrationInfo = this.innerState.detail!.registrationFormItem;
        registrationInfo!.reserveDate = registrationInfo?.reserveDate ?? new Date();
        const schedulingInfo = this.innerState.dailyReserveStatusList
            ?.filter((t) => t.status != RegistrationReserveStatus.notScheduling)
            ?.map((item, index) => {
                return {
                    date: moment(item.date!).toDate(),
                    view: () => {
                        return (
                            <View key={index}>
                                <Text
                                    style={[
                                        TextStyles.t10NT2.copyWith({
                                            color:
                                                item.status == RegistrationReserveStatus.canAppointment
                                                    ? Colors.canAppointmentColor
                                                    : item.status == RegistrationReserveStatus.finishedDiagnosis
                                                    ? Colors.Y2
                                                    : Colors.T6,
                                        }),
                                    ]}
                                >
                                    {item?.statusName ?? ""}
                                </Text>
                            </View>
                        );
                    },
                };
            });
        const selectDate = await AbcCalendar.show({
            date: registrationInfo?.reserveDate ?? new Date(),
            minDate: new Date(),
            maxDate: undefined,
            dateExtendView: schedulingInfo,
        });
        if (!selectDate) return;
        registrationInfo!.reserveDate = selectDate;
        registrationInfo!.reserveTime = undefined;
        this._registrationDesignatedTimeTrigger.next();
        this.canEditTimeFlexible();
        this.update();
    }

    @actionEvent(_EventSelectTimeRange)
    async *_mapEventSelectTimeRange(event: _EventSelectTimeRange): AsyncGenerator<State> {
        if (!event.range) return;
        this.innerState.detail!.registrationFormItem = this.innerState.detail?.registrationFormItem ?? new RegistrationFormItem();
        const registrationInfo = this.innerState.detail!.registrationFormItem;
        registrationInfo!.reserveTime = registrationInfo?.reserveTime ?? new Range();
        registrationInfo!.reserveTime = {
            start: TimeUtils.formatDate(event.range.start, "HH:mm"),
            end: TimeUtils.formatDate(event.range.end, "HH:mm"),
        };
        if (!!event?.timeOfDay) {
            registrationInfo.timeOfDay = event.timeOfDay;
        }
        this.update();
    }

    @actionEvent(_EventModifyFixedSourceDate)
    async *_mapEventModifyFixedSourceDate(): AsyncGenerator<State> {
        this.innerState.detail!.registrationFormItem = this.innerState.detail?.registrationFormItem ?? new RegistrationFormItem();
        const registrationInfo = this.innerState.detail!.registrationFormItem;
        const schedulingInfo = this.innerState.dailyReserveStatusList
            ?.filter((t) => t.status != RegistrationReserveStatus.notScheduling)
            ?.map((item, index) => {
                return {
                    date: moment(item.date!).toDate(),
                    view: () => {
                        return (
                            <View key={index}>
                                <Text
                                    style={[
                                        TextStyles.t10NT2.copyWith({
                                            color:
                                                item.status == RegistrationReserveStatus.canAppointment
                                                    ? Colors.canAppointmentColor
                                                    : item.status == RegistrationReserveStatus.finishedDiagnosis
                                                    ? Colors.Y2
                                                    : Colors.T6,
                                        }),
                                    ]}
                                >
                                    {item?.statusName ?? ""}
                                </Text>
                            </View>
                        );
                    },
                };
            });
        registrationInfo!.reserveDate = registrationInfo?.reserveDate ?? new Date();
        const selectDate = await AbcCalendar.show({
            date: registrationInfo?.reserveDate ?? new Date(),
            minDate: new Date(),
            maxDate: undefined,
            dateExtendView: schedulingInfo,
        });
        if (!selectDate) return;
        registrationInfo!.reserveDate = selectDate;
        this.update();
        if (this.innerState.isFixedMode) {
            this._registrationDesignatedTimeTrigger.next();
        }
    }

    @actionEvent(_EventModifyFixedSourceTime)
    async *_mapEventModifyFixedSourceTime(): AsyncGenerator<State> {
        this.innerState.detail!.registrationFormItem = this.innerState.detail?.registrationFormItem ?? new RegistrationFormItem();
        const registrationInfo = this.innerState.detail?.registrationFormItem;
        const timeStrList: { cont: string; type?: number }[] = []; //时间列表
        let initialIndex = 0; //选中索引
        const availableOrderNoTimeList: { orderNo?: number; timeOfDay?: string; start?: string; end?: string; type?: number }[] = [];
        this.innerState.selectOrderNoTimeList?.map((item) => {
            item.list
                ?.filter((k) => k.available == 1)
                ?.forEach((t) => {
                    availableOrderNoTimeList.push({
                        orderNo: t.orderNo,
                        timeOfDay: t.timeOfDay,
                        start: this.innerState.isAccuratePart ? t.start : item.start,
                        end: this.innerState.isAccuratePart ? t.end : item.end,
                        type: t.type,
                    });
                });
        });
        if (!_.isEmpty(availableOrderNoTimeList)) {
            initialIndex = availableOrderNoTimeList.findIndex((k) => k.orderNo == this.innerState.detail?.registrationFormItem?.orderNo);
            availableOrderNoTimeList?.forEach((t) => {
                timeStrList.push({
                    cont: `${registrationInfo?.getDisplayOrderNumber(t?.orderNo)} ${t?.start ?? ""} ~ ${t?.end ?? ""}`,
                    type: t.type,
                });
            });
        } else {
            timeStrList.push({
                cont: `${registrationInfo?.displayOrderNumber} ${registrationInfo?.reserveTime?.start ?? ""} ~ ${
                    registrationInfo?.reserveTime?.end ?? ""
                }`,
            });
        }
        if (!timeStrList || _.isEmpty(timeStrList)) return;
        const resultIndex = await showBottomSheet<number | undefined>(
            <FixedSourceTime
                dataList={[]}
                list={timeStrList}
                initialIndex={initialIndex}
                enableLeaveForMember={
                    this.innerState.registrationConfig?.isFixedMode && this.innerState.registrationConfig?.isEnableLeaveForMember
                }
                enableLeaveForPC={this.innerState.registrationConfig?.isFixedMode && this.innerState.registrationConfig?.isEnableLeaveForPC}
            />
        );
        if (_.isUndefined(resultIndex)) return;
        if (!_.isEmpty(availableOrderNoTimeList)) {
            const result = availableOrderNoTimeList?.[resultIndex];
            this.innerState.detail!.registrationFormItem!.orderNo = result?.orderNo;
            this.innerState.detail!.registrationFormItem!.timeOfDay = result?.timeOfDay;
            this.innerState.detail!.registrationFormItem!.reserveTime = {
                start: result?.start,
                end: result?.end,
            };
        }

        this.update();
    }

    @actionEvent(_EventModifyFixedTimeRange)
    async *_mapEventModifyFixedTimeRange(): AsyncGenerator<State> {
        const list = this.defaultAppointmentTimeOptions;
        const timeIndex = list?.findIndex((item) => {
            return item.timeOfDay == this.innerState.detail?.registrationFormItem?.timeOfDay; // 当日时间段
        });
        const options = list;
        const flexibleTimeList: { cont: string; type?: number }[] = [];
        options?.map((item) => {
            flexibleTimeList.push({
                cont: item.timeOfDay,
            });
        });
        if (_.isEmpty(flexibleTimeList)) return;
        const resultIndex = await showBottomSheet<number | undefined>(
            <FixedSourceTime dataList={[]} list={flexibleTimeList} initialIndex={timeIndex} />
        );

        if (_.isUndefined(resultIndex)) return;

        this.innerState.detail!.registrationFormItem!.timeOfDay = options[resultIndex!].timeOfDay;
        if (this.innerState.isFixedMode) {
            this.innerState.detail!.registrationFormItem!.reserveTime = undefined;
            this.innerState.detail!.registrationFormItem!.orderNo = undefined;
            this._registrationDesignatedTimeTrigger.next();
        }
        this.update();
    }

    @actionEvent(_EventSearchProduct)
    async *_mapEventSearchProduct(event: _EventSearchProduct): AsyncGenerator<State> {
        this.innerState.detail!.registrationFormItem = this.innerState.detail?.registrationFormItem ?? new RegistrationFormItem();
        const registrationFormItem = this.innerState.detail!.registrationFormItem;
        const updateProduct: RegistrationProducts[] = [];
        const result = await showBottomPanel<DentistryClinicProduct[]>(
            <DentistrySearchProject
                doctorId={registrationFormItem.doctorId ?? this._doctorId}
                defaultProducts={event.registrationProducts}
                registrationType={RegistrationType.therapyAppointment}
            />,
            {
                topMaskHeight: Sizes.dp160,
            }
        );
        if (_.isUndefined(result)) return;
        if (!_.isEmpty(result)) {
            result?.map((item) => {
                updateProduct.push(
                    JsonMapper.deserialize(RegistrationProducts, {
                        displayName: item.displayName,
                        id: item.id,
                    })
                );
            });
            //选中项目中serviceMinDuration时长最大的
            const visitServiceDurationItem = _.maxBy(result, (a) => {
                return (a?.serviceMinDuration?.hour ?? 0) * 60 + (a?.serviceMinDuration?.min ?? 0);
            });
            this.innerState.serviceMinDuration = visitServiceDurationItem?.serviceMinDuration;
            //选中项目中serviceMaxDuration时长最大的
            const visitServiceMaxDurationItem = _.maxBy(result, (a) => {
                return (a?.serviceMaxDuration?.hour ?? 0) * 60 + (a?.serviceMaxDuration?.min ?? 0);
            });
            this.innerState.serviceMaxDuration = visitServiceMaxDurationItem?.serviceMaxDuration;
            registrationFormItem!.registrationProducts = updateProduct;
            registrationFormItem!.registrationProductIds = updateProduct?.filter((t) => !!t.id)?.map((item) => item.id);
        } else {
            if (!!registrationFormItem?.registrationProducts) {
                registrationFormItem!.registrationProducts = [];
                registrationFormItem!.registrationProductIds = [];
            }
            this.innerState.serviceMinDuration = this.innerState.serviceMinMinutes
                ? {
                      min: this.innerState.serviceMinMinutes % 60,
                      hour: Math.trunc(this.innerState.serviceMinMinutes / 60),
                  }
                : { min: 30, hour: 0 };
            this.innerState.serviceMaxDuration = this.innerState.serviceMaxMinutes
                ? {
                      min: this.innerState.serviceMaxMinutes % 60,
                      hour: Math.trunc(this.innerState.serviceMaxMinutes / 60),
                  }
                : { min: 0, hour: 4 };
        }
        this.canEditTimeFlexible();
        this.innerState.detail!.registrationFormItem!.reserveTime = undefined;
        this.update();
    }

    @actionEvent(_EventModifyRegistrationVisitSourceRemark)
    private async *_mapEventModifyRegistrationVisitSourceRemark(event: _EventModifyRegistrationVisitSourceRemark): AsyncGenerator<State> {
        this.innerState.hasChange = false;
        if (!!userCenter.isAllowedRegUpgrade) {
            const result = await showBottomPanel<string>(
                <DentistryRemarkInputPage remark={event.remark} registrationType={RegistrationType.therapyAppointment} />,
                {
                    topMaskHeight: Sizes.dp160,
                }
            );
            if (_.isUndefined(result)) return;
            this.innerState.detail!.visitSourceRemark = result;
        } else {
            this.innerState.detail!.visitSourceRemark = event.remark;
        }
        // if (this.innerState.cloneDetail?.visitSourceRemark != event.remark) {
        //     this.innerState.hasChange = true;
        // }
        this.update();
    }

    @actionEvent(_EventUpdatePatientInfo)
    private async *_mapEventUpdatePatientInfo(event: _EventUpdatePatientInfo): AsyncGenerator<State> {
        if (!event?.patient) return;
        this.innerState.detail!.patient = this.innerState.detail?.patient ?? new Patient();
        this.innerState.detail!.patient = event.patient;
        this.update();
    }

    /**
     * 修改预约患者信息
     */
    public _requestModifyAppointmentPatient(patient: Patient): void {
        this.dispatch(new _EventModifyAppointmentPatient(patient));
    }

    /**
     * 修改预约理疗师
     */
    public requestModifyAppointmentDoctor(): void {
        this.dispatch(new _EventModifyAppointmentDoctor());
    }

    /**
     * 完成预约
     */
    public requestFinishTherapyAppointment(): void {
        this.dispatch(new _EventFinishTherapyAppointment());
    }

    /**
     * 理疗预约 （退号）
     */
    public requestCancelPatientAppointment(): void {
        this.dispatch(new _EventCancelPatientAppointment());
    }

    /**
     * 保存预约单修改信息
     */
    public requestSaveAppointment(): void {
        this.dispatch(new _EventSaveAppointment());
    }

    /**
     * 修改预诊信息
     * @param medicalRecord
     */
    public requestModifyMedicalRecord(medicalRecord: MedicalRecord): void {
        this.dispatch(new _EventModifyMedicalRecordCard(medicalRecord));
    }

    /**
     * 预约签到
     */
    public requestSignInAppointment(): void {
        this.dispatch(new _EventSignInAppointment());
    }

    public requestUpdateAppointmentInfo(): void {
        this.dispatch(new _EventUpdateAppointmentInfo());
    }

    //灵活模式--选择日期
    requestModifyFlexibleDate(): void {
        this.dispatch(new _EventModifyFlexibleDate());
    }

    //灵活模式--选择时间段
    requestSelectTimeRange(range?: Range<Date>, timeOfDay?: string): void {
        this.dispatch(new _EventSelectTimeRange(range, timeOfDay));
    }

    //固定模式--日期
    requestModifyFixedSourceDate(): void {
        this.dispatch(new _EventModifyFixedSourceDate());
    }

    //固定模式--号数
    requestModifyFixedSourceTime(): void {
        this.dispatch(new _EventModifyFixedSourceTime());
    }

    //固定模式--时间段
    requestModifyFixedTimeRange(): void {
        this.dispatch(new _EventModifyFixedTimeRange());
    }

    //预约项目搜索
    requestSearchProject(registrationProducts?: RegistrationProducts[]): void {
        this.dispatch(new _EventSearchProduct(registrationProducts));
    }

    requestUpdatePatientInfo(patient: Patient): void {
        this.dispatch(new _EventUpdatePatientInfo(patient));
    }

    /**
     * 修改就诊推荐备注
     */
    public requestModifyRegistrationVisitSourceRemark(text: string): void {
        this.dispatch(new _EventModifyRegistrationVisitSourceRemark(text));
    }

    private async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        this.innerState.employeesMeConfig = await ClinicAgent.getEmployeesMeConfig().catchIgnore();
        //获取预约设置
        if (userCenter.isAllowedRegUpgrade) {
            this.innerState.registrationConfig = await DentistryAgent.queryDentistryRegistrationConfig(
                RegistrationType.therapyAppointment,
                false
            ).catchIgnore();
        }
        // // 预约详情 触发器
        this._appointmentDetailsTrigger
            .pipe(
                switchMap((id) => {
                    this.innerState.startLoading();
                    this.update();
                    return RegistrationAgent.getRegistrationDetail(id)
                        .catch((e) => new ABCError(e))
                        .toObservable();
                })
            )
            .subscribe(async (rsp) => {
                if (rsp instanceof ABCError) {
                    this.innerState.setLoadingError(rsp);
                } else {
                    ///外部传入的提前签到信息
                    // rsp.registrationFormItem!.oldReserveInfo = this._oldReserveInfo;
                    this.innerState.hasChange = false;
                    this.innerState.loading = false;
                    this.innerState.detail = rsp;

                    this._getPatientBaseDetailTrigger.next(rsp.patient?.id);

                    //口腔诊所及升级挂号诊所，且选择预约时
                    if (!!userCenter.isAllowedRegUpgrade) {
                        this._registrationDailyReserveStatusTrigger.next();
                    }
                    //详情编辑状态下，需要查询此时还存在的号源
                    if (this.innerState.type == TherapyAppointmentInvoiceType.existence && this.innerState.hasChange) {
                        this._registrationDesignatedTimeTrigger.next();
                    }
                    if (this.innerState.isFlexibleMode && this.innerState.type == TherapyAppointmentInvoiceType.existence) {
                        // this._employeeServiceMinMinutesTrigger.next();
                        const registrationProducts = this.innerState.detail?.registrationFormItem?.registrationProducts;
                        if (!!registrationProducts?.length) {
                            DentistryAgent.getDentistryClinicProduct({
                                employeeId: this.innerState.detail.registrationFormItem?.doctorId ?? this._doctorId,
                                registrationType: RegistrationType.therapyAppointment,
                                keywords: "",
                            }).then((rsp) => {
                                const selectProduct: DentistryClinicProduct[] = [];
                                registrationProducts?.forEach((item) => {
                                    rsp.forEach((sub) => {
                                        if (item.id == sub.id) {
                                            selectProduct.push(sub);
                                        }
                                    });
                                });

                                //选中项目中serviceMinDuration时长最大的
                                const visitServiceDurationItem = _.maxBy(selectProduct, (a) => {
                                    return (a?.serviceMinDuration?.hour ?? 0) * 60 + (a?.serviceMinDuration?.min ?? 0);
                                });
                                this.innerState.serviceMinDuration = visitServiceDurationItem?.serviceMinDuration;
                                const visitServiceMaxDurationItem = _.maxBy(selectProduct, (a) => {
                                    return (a?.serviceMaxDuration?.hour ?? 0) * 60 + (a?.serviceMaxDuration?.min ?? 0);
                                });
                                this.innerState.serviceMaxDuration = visitServiceMaxDurationItem?.serviceMaxDuration;
                            });
                        }
                    }
                }
                this.update();
            })
            .addToDisposableBag(this);

        this._getPatientBaseDetailTrigger
            .pipe(
                switchMap((patientId) => {
                    if (!patientId) return of(null);
                    return CrmAgent.getPatientById(patientId).catchIgnore().toObservable();
                })
            )
            .subscribe((rsp) => {
                if (rsp) {
                    this.innerState.detail!.patient = rsp;
                    this._getHistoryListTrigger.next();
                    this.update();
                }
            })
            .addToDisposableBag(this);

        //就诊历史
        this._getHistoryListTrigger
            .pipe(
                switchMap((/*data*/) => {
                    const patientId = this.innerState.detail?.patient?.id;
                    if (!patientId) return of(null);

                    return OutpatientAgent.getOutpatientHistoryList(patientId);
                })
            )
            .subscribe(
                (patientSummaryList) => {
                    if (!patientSummaryList) return;
                    this.innerState.diagnoseCount = patientSummaryList.totalCount;
                    this.update();
                },
                (error) => {
                    this.innerState.loading = false;
                    this.innerState.loadError = error;
                    this.update();
                }
            )
            .addToDisposableBag(this);

        this._registrationLocalTimeCountTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.localTimeCountList = [];
                    return DentistryAgent.queryLocalTimeCountByDoctorDepartment({
                        doctorId: this.innerState.detail?.registrationFormItem?.doctorId,
                        departmentId: this.innerState.detail?.registrationFormItem?.departmentId ?? "",
                        start: "06:00",
                        end: "23:00",
                        registrationType: RegistrationType.therapyAppointment,
                        reserveDate: this.innerState.detail?.registrationFormItem?.reserveDate ?? new Date(),
                    })
                        .catchIgnore()
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (rsp) {
                    this.innerState.localTimeCountList = rsp;
                    this.update();
                }
            })
            .addToDisposableBag(this);

        //口腔诊所、升级挂号预约诊所--预约类型（获取排班日期）
        this._registrationDailyReserveStatusTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.dailyReserveStatusList = [];
                    const currentDate = this.innerState.detail?.registrationFormItem?.reserveDate ?? new Date();
                    return DentistryAgent.queryDailyReserveStatusByDoctorDepartment({
                        doctorId: !!this.innerState.detail?.registrationFormItem?.doctorId
                            ? this.innerState.detail?.registrationFormItem?.doctorId
                            : undefined,
                        departmentId: this.innerState.detail?.registrationFormItem?.departmentId ?? "",
                        start: TimeUtils.getThisMonthFirstDay(currentDate),
                        end: TimeUtils.getThisMonthEndDay(currentDate),
                        registrationType: RegistrationType.therapyAppointment,
                        isRevisited: this.innerState.detail!.revisitStatus ?? 0,
                    })
                        .catchIgnore()
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (rsp) {
                    //过滤掉过去时间的排班状态
                    this.innerState.dailyReserveStatusList = rsp?.filter(
                        (t) =>
                            TimeUtils.getStartOfDate(moment(t.date!).toDate()).getTime() >= TimeUtils.getStartOfDate(new Date()).getTime()
                    );
                    this.update();
                }
            })
            .addToDisposableBag(this);

        //固定模式、灵活模式下，获取号源时间段信息
        this._registrationDesignatedTimeTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.registrationTimeRangeList = [];
                    this.innerState.selectOrderNoTimeList = [];
                    this.innerState.detail!.registrationFormItem =
                        this.innerState.detail!.registrationFormItem ?? new RegistrationFormItem();
                    //新增需要清空，详情则需要回填
                    if (this.innerState.type == TherapyAppointmentInvoiceType.addition) {
                        this.innerState.detail!.registrationFormItem!.reserveTime = undefined;
                    }
                    if (this.innerState.type == TherapyAppointmentInvoiceType.addition && this.needCalcNumber()) {
                        this.innerState.detail!.registrationFormItem!.orderNo = undefined;
                    }
                    return RegistrationAgent.getRegistrationDesignatedTime({
                        departmentId: this.innerState.detail?.registrationFormItem?.departmentId ?? "",
                        doctorId: !!this.innerState.detail?.registrationFormItem?.doctorId
                            ? this.innerState.detail?.registrationFormItem?.doctorId
                            : "00000000000000000000000000000000",
                        forNormalRegistration: 0,
                        workingDate: TimeUtils.formatDate(this.innerState.detail?.registrationFormItem?.reserveDate ?? new Date()),
                        registrationType: RegistrationType.therapyAppointment,
                    })
                        .catchIgnore()
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (rsp instanceof RegistrationDesignatedTime) {
                    this.innerState.doctorShiftsInfo = rsp;
                    this.innerState.registrationTimeRangeList = rsp?.getScheduleIntervals(
                        this.innerState.detail?.registrationFormItem?.registrationCategory
                    );
                    const currentCategorySchedule = rsp?.getCategorySchedule(
                        this.innerState.detail?.registrationFormItem?.registrationCategory
                    );
                    this.innerState.serviceMinMinutes = rsp?.serviceMinMinutes ?? currentCategorySchedule?.serviceMinMinutes;
                    this.innerState.serviceMaxMinutes = rsp?.serviceMaxMinutes ?? currentCategorySchedule?.serviceMaxMinutes;
                    //如果此时没有选择项目，则需要根据接口最小时长来进行判断
                    if (_.isEmpty(this.innerState.detail?.registrationFormItem?.registrationProducts)) {
                        this.innerState.serviceMinDuration = this.innerState.serviceMinMinutes
                            ? {
                                  min: this.innerState.serviceMinMinutes % 60,
                                  hour: Math.trunc(this.innerState.serviceMinMinutes / 60),
                              }
                            : { min: 30, hour: 0 };
                        this.innerState.serviceMaxDuration = this.innerState.serviceMaxMinutes
                            ? {
                                  min: this.innerState.serviceMaxMinutes % 60,
                                  hour: Math.trunc(this.innerState.serviceMaxMinutes / 60),
                              }
                            : { min: 0, hour: 4 };
                    }
                    const registrationInfo = this.innerState.detail?.registrationFormItem;
                    this.innerState.selectOrderNoTimeList = this.innerState.registrationTimeRangeList?.filter(
                        (t) => t.timeOfDay == registrationInfo?.timeOfDay
                    );
                    //详情编辑状态或者传入了号数
                    const isEdit =
                        (this.innerState.type == TherapyAppointmentInvoiceType.existence && !!registrationInfo?.reserveTime) ||
                        !this.needCalcNumber();
                    if (isEdit) {
                        this.innerState.selectOrderNoTimeList = this.innerState.selectOrderNoTimeList?.map((t) => {
                            !_.isEmpty(t?.list) &&
                                t?.list?.map((k) => {
                                    if (k?.orderNo == registrationInfo?.orderNo) {
                                        k.available = 1;
                                    }
                                });
                            return t;
                        });
                        // 详情状态下，不能根据available为1来判断，应该根据当前详情的orderNo及timeOfDay判断，只有新增才需要判断available
                        if (this.innerState.type == TherapyAppointmentInvoiceType.existence) {
                            const relativeScheduling = this.innerState.selectOrderNoTimeList?.find((t) => {
                                return t.list?.find(
                                    (k) => k.timeOfDay == registrationInfo?.timeOfDay && k.orderNo == registrationInfo?.orderNo
                                );
                            });
                            if (!!relativeScheduling) {
                                const relativeSchedulingItem = relativeScheduling.list?.find(
                                    (item) => item.orderNo == registrationInfo?.orderNo && item.timeOfDay == registrationInfo?.timeOfDay
                                );
                                this.innerState.detail!.registrationFormItem!.reserveTime = {
                                    start: this.innerState.isAccuratePart ? relativeSchedulingItem?.start : relativeScheduling?.start,
                                    end: this.innerState.isAccuratePart ? relativeSchedulingItem?.end : relativeScheduling?.end,
                                };
                            }
                            return;
                        }
                    }
                    //固定模式---列表进入---排班时间列表显示规则
                    // 1、如果存在空号（即除了会员号、预留号，还有其他号）需要默认跳过会员跟预留号(type=1,2)
                    //2、如果没有空号，但是存在会员、预留号，则还是选中列表第一个
                    const isRegisterType = this.innerState.isFixedMode;
                    const existTimeRangeList = _.cloneDeep(this.innerState.selectOrderNoTimeList)
                        ?.filter((t) => !!t.list?.length)
                        .map((item) => {
                            let isExistOrderNo = false;
                            if (isRegisterType) {
                                isExistOrderNo = !!item.list?.find((k) => !k?.type && k?.available == 1);
                            }
                            item.list = item.list?.filter((k) => (isExistOrderNo ? k?.available == 1 && !k?.type : k?.available == 1));
                            return item;
                        })
                        ?.filter((t) => !!t.list?.length);

                    if (!_.isEmpty(existTimeRangeList)) {
                        const matchTimeRange =
                            existTimeRangeList?.find((item) => {
                                return !!item.list?.find((t) => t.orderNo == registrationInfo?.orderNo);
                            }) ?? existTimeRangeList?.[0];
                        //预约时间--是否按照精确时间预约(是--去list内层的时间段；否-取list外层的时间段)
                        const preferredTime = matchTimeRange?.list?.find((t) =>
                            isEdit
                                ? !this.needCalcNumber() && registrationInfo?.orderNo
                                    ? t.orderNo == registrationInfo?.orderNo
                                    : t.restCount
                                : t.restCount
                        );
                        if (!isEdit) {
                            this.innerState.detail!.registrationFormItem!.orderNo = preferredTime?.orderNo;
                        }
                        //灵活模式，时间要置空
                        if (this.innerState.isFixedMode) {
                            this.innerState.detail!.registrationFormItem!.reserveTime = {
                                start: this.innerState.isAccuratePart ? preferredTime?.start : matchTimeRange?.start,
                                end: this.innerState.isAccuratePart ? preferredTime?.end : matchTimeRange?.end,
                            };
                        }
                    }
                }
                this.update();
            })
            .addToDisposableBag(this);

        this._employeeServiceMinMinutesTrigger
            .pipe(
                switchMap(() => {
                    return DentistryAgent.queryEmployeeServiceMinMinutes({
                        registrationType: RegistrationType.therapyAppointment,
                        workingDate: this.innerState.detail?.registrationFormItem?.reserveDate,
                        departmentId: this.innerState.detail?.registrationFormItem?.departmentId,
                        employeeId: this.innerState.detail?.registrationFormItem?.doctorId,
                    })
                        .catchIgnore()
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                this.innerState.serviceMinMinutes = rsp?.serviceMinMinutes;
                //如果此时没有选择项目，则需要根据接口最小时长来进行判断
                if (_.isEmpty(this.innerState.detail?.registrationFormItem?.registrationProducts)) {
                    this.innerState.serviceMinDuration = this.innerState.serviceMinMinutes
                        ? {
                              min: this.innerState.serviceMinMinutes % 60,
                              hour: Math.trunc(this.innerState.serviceMinMinutes / 60),
                          }
                        : { min: 30, hour: 0 };
                }
                this.update();
            });

        //微信沟通
        this._setPatientRegAuditStatusSubject
            .pipe(
                switchMap(() => {
                    const detail = this.innerState.detail,
                        registrationFormItem = detail?.registrationFormItem;
                    const req = {
                        couponPromotions: [],
                        giftRulePromotions: [],
                        patientCardPromotions: [],
                        patientPointsInfo: undefined,
                        promotions: [],
                        pay: {
                            fee: registrationFormItem?.fee,
                            memberId: detail?.chargeSheet?.memberId,
                            useMemberFlag: detail?.chargeSheet?.useMemberFlag ?? 0,
                        },
                        registrationFormItem: {
                            departmentId: registrationFormItem?.departmentId,
                            departmentName: registrationFormItem?.departmentName,
                            doctorId: registrationFormItem?.doctorId,
                            doctorName: registrationFormItem?.doctorName,
                            isReserved: registrationFormItem?.isReserved ?? 1,
                            orderNo: registrationFormItem?.orderNo,
                            registrationProductIds: registrationFormItem?.registrationProducts
                                ?.filter((t) => !!t?.id)
                                ?.map((item) => item.id),
                            reserveDate: registrationFormItem?.reserveDate,
                            reserveTime: registrationFormItem?.reserveTime,
                        },
                        revisitStatus: detail?.revisitStatus,
                        visitSourceFrom: detail?.visitSourceFrom,
                        visitSourceId: detail?.visitSourceId,
                        visitSourceRemark: detail?.visitSourceRemark,
                    };
                    return RegistrationAgent.putRegistrationPatientManageAudit(detail?.id ?? "", {
                        auditStatus: PatientRegAuditStatus.processed,
                        registrationReq: req,
                    })
                        .catchIgnore()
                        .toObservable();
                })
            )
            .subscribe(() => {
                ABCNavigator.pop({ success: true });
            })
            .addToDisposableBag(this);

        Promise.all([
            RegistrationDataProvider.getDoctorList({ registrationType: RegistrationType.therapyAppointment, isContainType: false }),
            ClinicAgent.getEmployeesMeConfig(),
        ])
            .catchIgnore()
            .toObservable()
            .subscribe((rsp) => {
                this.innerState.departments = rsp?.[0]; // 预约理疗师数据提供程序 (拉取全部理疗师)
                this.innerState.employeeConfig = rsp?.[1]?.clinicInfo?.config; // 员工配置
                //匹配当前理疗师的名字
                this.innerState.departments?.forEach((item) => {
                    const matchEmployee = item?.doctors?.find((t) => t?.doctorId == this.innerState.detail?.registrationFormItem?.doctorId);
                    this.innerState.detail!.registrationFormItem!.doctorName = matchEmployee?.doctorName;
                });
                this.update();
            })
            .addToDisposableBag(this);

        RegistrationAgent.changeObserver
            .subscribe((rsp) => {
                this.id = rsp?.id ?? this.id;
                this._appointmentDetailsTrigger.next(this.id);
            })
            .addToDisposableBag(this);

        this.innerState.serviceMinDuration = this.innerState.serviceMinMinutes
            ? {
                  min: this.innerState.serviceMinMinutes % 60,
                  hour: Math.trunc(this.innerState.serviceMinMinutes / 60),
              }
            : { min: 30, hour: 0 };
        this.innerState.serviceMaxDuration = this.innerState.serviceMaxMinutes
            ? {
                  min: this.innerState.serviceMaxMinutes % 60,
                  hour: Math.trunc(this.innerState.serviceMaxMinutes / 60),
              }
            : { min: 0, hour: 4 };

        if (this.id) {
            this.innerState.type = TherapyAppointmentInvoiceType.existence; // 预约单（已存在）
            this._appointmentDetailsTrigger.next(this.id);
        } else {
            this.innerState.detail = new RegistrationDetail();
            // 默认选中时间

            this.innerState.detail!.registrationFormItem = this._registrationFormItem ?? new RegistrationFormItem();

            //默认选中时间
            let defaultReserve;
            if (!!this.innerState.detail!.registrationFormItem.timeOfDay) {
                defaultReserve = this.defaultAppointmentTimeOptions.find(
                    (item) => item.timeOfDay == this.innerState.detail!.registrationFormItem?.timeOfDay
                );
            } else {
                const hours = new Date().getHours();

                if (hours >= 0 && hours < 12) {
                    defaultReserve = this.defaultAppointmentTimeOptions[0];
                } else if (hours >= 12 && hours < 18) {
                    defaultReserve = this.defaultAppointmentTimeOptions[1];
                } else {
                    defaultReserve = this.defaultAppointmentTimeOptions[2];
                }
            }

            this.innerState.detail!.registrationFormItem!.reserveDate =
                this.innerState.detail!.registrationFormItem!.reserveDate ?? new Date();
            this.innerState.detail!.registrationFormItem.isReserved = 1;
            this.innerState.detail!.registrationFormItem!.reserveTime =
                this.innerState.detail!.registrationFormItem!.reserveTime ?? defaultReserve?.range;
            this.innerState.detail!.registrationFormItem!.timeOfDay =
                this.innerState.detail!.registrationFormItem!.timeOfDay ?? defaultReserve?.timeOfDay;

            this.innerState.detail.revisitStatus = RegistrationRevisitStatus.first;

            //灵活模式下--，需要清空预约时间
            if (this.innerState.isFlexibleMode) {
                this.innerState.detail.registrationFormItem!.reserveTime = undefined;
            }
            this._registrationDesignatedTimeTrigger.next();
            if (this.innerState?.isFixedMode) {
                const result = await RegistrationAgent.getDoctorOrderNo(
                    this.innerState.detail!.registrationFormItem!,
                    userCenter.isAllowedRegUpgrade,
                    RegistrationType.therapyAppointment,
                    !this.kanbanEntry()
                ).catch((error) => {
                    if (this.isDisposed) {
                        showQueryDialog("提示", errorToStr(error));
                    }
                    return undefined;
                });
                if (!!result) {
                    this.innerState.detail!.registrationFormItem = result;
                } else {
                    this.innerState.detail!.registrationFormItem.reserveTime = undefined;
                }
                this._registrationDailyReserveStatusTrigger.next();
            }
            this.innerState.stopLoading();
            this.update();

            yield this.innerState.clone();
        }

        RegistrationDataProvider.getDoctorList({ registrationType: RegistrationType.therapyAppointment, isContainType: false })
            .catchIgnore()
            .toObservable()
            .subscribe((rsp) => {
                this.innerState.departments = rsp;

                this.innerState.detail = this.innerState.detail ?? new RegistrationDetail();
                this.innerState.detail!.registrationFormItem = this.innerState.detail?.registrationFormItem ?? new RegistrationFormItem();
                if (this._doctorId) {
                    this.innerState.departments?.forEach((department) => {
                        department.doctors?.forEach((doctor) => {
                            if (doctor.doctorId == this._doctorId) {
                                this.innerState.detail!.registrationFormItem!.departmentId = department.departmentId;
                                this.innerState.detail!.registrationFormItem!.departmentName = department.departmentName;
                                this.innerState.currentDepartmentDoctors = department.doctors;
                                return;
                            }
                        });
                    });
                } else {
                    this.innerState.detail!.registrationFormItem!.departmentId = _.first(this.innerState.departments)?.departmentId;
                    this.innerState.detail!.registrationFormItem!.departmentName = _.first(this.innerState.departments)?.departmentName;
                    this.innerState.currentDepartmentDoctors = _.first(this.innerState.departments)?.doctors;
                    if (!this.innerState.detail.registrationFormItem?.doctorId) {
                        this.innerState.detail.registrationFormItem!.doctorName = "不指定理疗师";
                    }
                }

                //灵活模式下
                // if (this.innerState.isFlexibleMode) {
                //     this._registrationLocalTimeCountTrigger.next();
                // }
                this.update();
            })
            .addToDisposableBag(this);
    }

    private flexibleServiceDuration(): void {
        const time = this.innerState.detail?.registrationFormItem?.reserveDate ?? new Date();
        //当前选择的初始时间，在这个时间起点，更改时间间隔
        const selStartTime = this.innerState.detail?.registrationFormItem?.reserveTime?.start;
        const isNeedReset = TimeUtils.getStartOfDate(time).getTime() > TimeUtils.getStartOfDate(new Date()).getTime();
        //已诊状态下，切换初复诊，不改变挂号时间
        if (this.innerState.isFlexibleMode && this.innerState.detail?.registrationFormItem?.statusV2 != RegistrationStatusV2.diagnosed) {
            let timeRange;
            const appointmentTime = this.innerState.serviceMinDuration;
            //预约日期大于当天，则重置为早上8点
            if (isNeedReset) {
                timeRange = new Range(
                    "08:00",
                    `${
                        (8 + (appointmentTime?.hour ?? 0) < 10
                            ? "0" + (8 + (appointmentTime?.hour ?? 0))
                            : 8 + (appointmentTime?.hour ?? 0)) +
                        ":" +
                        ((appointmentTime?.min ?? 0) < 10 ? "0" + (appointmentTime?.min ?? 0) : appointmentTime?.min ?? 0)
                    }`
                );
            } else {
                //挂号时间只能为6-23
                let initialHourTime = new Date().getHours(),
                    initialMinuteTime = new Date().getMinutes();
                if (!!selStartTime && selStartTime.indexOf(":") > -1) {
                    const selHour = !!selStartTime.split(":")?.[0] ? Number(selStartTime.split(":")?.[0]) : new Date().getHours(),
                        selMinute = !!selStartTime.split(":")?.[1] ? Number(selStartTime.split(":")?.[1]) : new Date().getMinutes();
                    //新增单需要根据当前时间点定义
                    if (this.innerState.type == TherapyAppointmentInvoiceType.addition) {
                        initialHourTime = selHour > new Date().getHours() ? selHour : new Date().getHours();
                        initialMinuteTime = selHour > new Date().getHours() ? selMinute : new Date().getMinutes();
                    } else {
                        initialHourTime = selHour ?? new Date().getHours();
                        initialMinuteTime = selMinute ?? new Date().getMinutes();
                    }
                }

                const hourTime = initialHourTime < 6 ? 6 : initialHourTime > 23 ? 23 : initialHourTime;
                const minutesTime = initialHourTime < 6 || initialHourTime > 23 ? 0 : initialMinuteTime;
                const minutesList = [0, 15, 30, 45, 60];
                const num = minutesList.find((t) => t >= minutesTime);

                //时间只有0-24,所以还需要考虑大于24后的情况
                if (num == 60) {
                    timeRange = new Range(
                        `${hourTime + 1 < 10 ? "0" + (hourTime + 1) : hourTime + 1 >= 24 ? "00" : hourTime + 1}:00`,
                        `${
                            (appointmentTime?.min ?? 0) == 60
                                ? (hourTime + 1 + (appointmentTime?.hour ?? 0) + 1 < 10
                                      ? "0" + (hourTime + 1 + (appointmentTime?.hour ?? 0) + 1)
                                      : hourTime + 1 + (appointmentTime?.hour ?? 0) + 1 >= 24
                                      ? "00"
                                      : hourTime + 1 + (appointmentTime?.hour ?? 0) + 1) + ":00"
                                : (hourTime + 1 + (appointmentTime?.hour ?? 0) < 10
                                      ? "0" + (hourTime + 1 + (appointmentTime?.hour ?? 0))
                                      : hourTime + 1 + (appointmentTime?.hour ?? 0) >= 24
                                      ? "00"
                                      : hourTime + 1 + (appointmentTime?.hour ?? 0)) +
                                  ":" +
                                  ((appointmentTime?.min ?? 0) < 10 ? "0" + (appointmentTime?.min ?? 0) : appointmentTime?.min ?? 0)
                        }`
                    );
                } else {
                    timeRange = new Range(
                        `${hourTime < 10 ? "0" + hourTime : hourTime >= 24 ? "00" : hourTime}:${num! < 10 ? "0" + num : num}`,
                        `${
                            (appointmentTime?.min ?? 0) + (num ?? 0) >= 60
                                ? (hourTime + (appointmentTime?.hour ?? 0) + 1 < 10
                                      ? "0" + (hourTime + (appointmentTime?.hour ?? 0) + 1)
                                      : hourTime + (appointmentTime?.hour ?? 0) + 1 >= 24
                                      ? "00"
                                      : hourTime + (appointmentTime?.hour ?? 0) + 1) +
                                  ":" +
                                  ((appointmentTime?.min ?? 0) + (num ?? 0) - 60 < 10
                                      ? "0" + ((appointmentTime?.min ?? 0) + (num ?? 0) - 60)
                                      : (appointmentTime?.min ?? 0) + (num ?? 0) - 60)
                                : (hourTime + (appointmentTime?.hour ?? 0) < 10
                                      ? "0" + (hourTime + (appointmentTime?.hour ?? 0))
                                      : hourTime + (appointmentTime?.hour ?? 0) >= 24
                                      ? "00"
                                      : hourTime + (appointmentTime?.hour ?? 0)) +
                                  ":" +
                                  ((appointmentTime?.min ?? 0) + (num ?? 0) < 10
                                      ? "0" + ((appointmentTime?.min ?? 0) + (num ?? 0))
                                      : (appointmentTime?.min ?? 0) + (num ?? 0))
                        }`
                    );
                }
            }
            this.innerState.detail!.registrationFormItem!.reserveTime = timeRange;
        }
    }

    /**
     * 是否从看板进入
     * @private
     */
    private kanbanEntry(): boolean {
        return !!this._fromKanbanEntry;
    }

    /**
     * 是否需要算号
     */
    private needCalcNumber(): boolean {
        return !this._registrationFormItem?.orderNo;
    }

    /**
     * 灵活模式详情单下，时间是否可编辑
     * @private
     */
    private canEditTimeFlexible(): void {
        if (this.innerState.type == TherapyAppointmentInvoiceType.existence && this.innerState.isFlexibleMode) {
            this.innerState.canEditTimeOfFlexible = true;
        }
    }

    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    private _formatPatientAddress(patient: Patient): void {
        _.assign(patient, patient.address);
    }

    /**
     * 新建预约 修改患者信息（修改/编辑患者信息）
     * @param event
     * @private
     */
    private async *_mapEventModifyAppointmentPatient(event: _EventModifyAppointmentPatient): AsyncGenerator<State> {
        this._formatPatientAddress(event.patient);
        if (this.innerState.detail?.patient?.id && this.innerState.detail?.patient?.id == event.patient.id) {
            this.innerState.detail!.patient = event.patient;
            this.update();
            return;
        }
        this.innerState.hasChange = true;
        if (event.patient?.id) {
            CrmAgent.getPatientById(event.patient.id)
                .then((rsp) => {
                    const patient = rsp;
                    this.innerState.detail!.patient = patient;
                    this.innerState.pay.memberId = event.patient.id ?? "";
                    this.update();
                })
                .then(() => {
                    // 获取pastHistoryInfo
                    OutpatientAgent.getPastHistory(event.patient.id!).then((rsp) => {
                        this.innerState.detail!.pastHistory = rsp;
                        this.update();
                    });
                });
        } else {
            this.innerState.detail!.patient = event.patient;
            this.update();
        }
    }

    private resetRegistrationTime(): void {
        if (!this.innerState.detail?.registrationFormItem) return;
        //固定模式--预约类型，timeOfDay不用清空
        if (this.innerState.isFixedMode && !this.innerState.detail.registrationFormItem.isReserved) {
            this.innerState.detail.registrationFormItem!.timeOfDay = undefined;
        }
        this.innerState.detail.registrationFormItem!.reserveEnd = undefined;
        this.innerState.detail.registrationFormItem!.reserveStart = undefined;
        this.innerState.detail.registrationFormItem!.reserveTime = undefined;
        // if (this.innerState.detail.registrationFormItem.isReserved == 0) {
        //     this.dispatch(new _EventCalculateOrderNo());
        // }
    }

    /**
     * 选择预约理疗师 (选择理疗师)
     * @private
     */
    private async *_mapEventModifyAppointmentDoctor(): AsyncGenerator<State> {
        const doctorIndex = this.innerState.currentDepartmentDoctors?.findIndex((item) => {
            return item.doctorId == this.innerState.detail?.registrationFormItem?.doctorId;
        });

        const result = await showOptionsBottomSheet({
            title: "选择理疗师",
            options: this.innerState.currentDepartmentDoctors?.map((item) => item.doctorName ?? ""),
            initialSelectIndexes: !_.isUndefined(doctorIndex) ? new Set<number>([doctorIndex]) : undefined,
            height: pxToDp(375),
            showConfirmBtn: false,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
        });

        if (!result || _.isEmpty(result)) return;
        this.canEditTimeFlexible();
        this.innerState.hasChange = true;

        const currentDoctor = this.innerState.currentDepartmentDoctors?.[result[0]];

        if (!this.innerState.detail?.registrationFormItem) {
            this.innerState.detail!.registrationFormItem = new RegistrationFormItem();
        }
        this.innerState.detail!.registrationFormItem.doctorId = currentDoctor?.doctorId;
        this.innerState.detail!.registrationFormItem.doctorName = currentDoctor?.doctorName;
        this.update();

        if (!this.innerState.isFlexibleMode) {
            this.resetRegistrationTime();
        } else {
            //灵活模式--清空当前选中时间
            this.innerState.detail!.registrationFormItem!.reserveTime = undefined;
        }
        //获取各个时间段信息
        this._registrationDesignatedTimeTrigger.next();
        //口腔诊所、升级挂号预约诊所--预约类型（获取排班信息）
        this._registrationDailyReserveStatusTrigger.next();
    }

    /**
     * 理疗预约（退号）
     * @private
     */
    private async *_mapEventCancelPatientAppointment(): AsyncGenerator<State> {
        const patient = this.innerState.detail?.patient;
        const registration = this.innerState.detail?.registrationFormItem;

        if (registration?.payStatusV2 === PayStatusV2.paid || registration?.payStatusV2 === PayStatusV2.partedPaid) {
        } else if (registration?.payStatusV2 === PayStatusV2.partedPaid) {
            // 2. 部分收费，提示退费，
        } else {
            // 3. 否则提示退号
            const result = await showQueryDialog(
                "",
                registration?.statusV2 == RegistrationStatusV2.diagnosed
                    ? `${patient?.name} 已接受预约，确定要退号？`
                    : "退号后不可恢复，是否继续？"
            );
            if (result == DialogIndex.positive) {
                RegistrationAgent.cancelPatientRegistration(this.innerState.detail!.id!)
                    .then(() => {
                        Toast.show("退号成功", { success: true }).then(() => ABCNavigator.pop());
                    })
                    .catch((error) => {
                        Toast.show(`${errorToStr(new ABCError(error))}`, { warning: true });
                    });
            }
        }
    }

    /**
     * 理疗预约单（签到）
     * @private
     */
    private async *_mapEventAppointmentSignIn(): AsyncGenerator<State> {
        const detail = this.innerState.detail;
        if (!detail || !detail.id) return;
        const { registrationFormItem } = detail;

        if (TimeUtils.formatDate(registrationFormItem?.reserveDate) != TimeUtils.formatDate(new Date())) {
            const result = await showQueryDialog(
                "",
                `患者预约时间（${TimeUtils.formatDate(registrationFormItem?.reserveDate)}）不是今天。签到后会更改预约时间，是否确定`
            );
            if (result != DialogIndex.positive) {
                return;
            }
        }

        const rsp = await DentistryAgent.completeSignInRegistration(
            this.innerState.detail?.registrationFormItem?.registrationSheetId
        ).catchIgnore();

        if (rsp) {
            this.innerState.detail = rsp;
            this.innerState.type = TherapyAppointmentInvoiceType.existence;
            this.innerState.hasChange = false;
            if (this.innerState.hasCharged) {
                Toast.show("签到成功", { success: true }).then(() => {
                    ABCNavigator.pop();
                });
                TherapyAppointmentAgent.changeObserver.next();
            }
        }
    }

    /**
     * 完成预约 (效验:姓名、性别、年龄、电话、职业、身份证、详细地址、来源)
     * @private
     */
    private async *_mapEventFinishTherapyAppointment(): AsyncGenerator<State> {
        const detail = this.innerState.detail; // 理疗预约详情单
        if (!detail) return;
        // 内容完整性校验
        if (!detail.patient || !detail.patient.name) {
            // 无患者
            this.innerState.focusKey = FocusItemKeys.patient;
            this.innerState.showErrorHint = true;
            yield ScrollToErrorViewState.fromState(this.innerState);
            return;
        } else if (this.innerState.registrationConfig?.isMustReserveEmployee && !detail.registrationFormItem?.doctorId) {
            this.innerState.focusKey = FocusItemKeys.doctorId;
            this.innerState.showErrorHint = true;
            yield ScrollToErrorViewState.fromState(this.innerState);
            return;
        } else if (!detail.registrationFormItem?.reserveDate) {
            this.innerState.focusKey = FocusItemKeys.reserveDate;
            this.innerState.showErrorHint = true;
            yield ScrollToErrorViewState.fromState(this.innerState);
            return;
        } else if (!detail.registrationFormItem.reserveTime?.start || !detail.registrationFormItem.reserveTime?.end) {
            this.innerState.focusKey = FocusItemKeys.reserveTime;
            this.innerState.showErrorHint = true;
            yield ScrollToErrorViewState.fromState(this.innerState);
            return;
        }
        const req = JsonMapper.deserialize(RegistrationDetailReq, {
            allergicHistory: detail.allergicHistory,
            chiefComplaint: detail.chiefComplaint,
            familyHistory: detail.familyHistory,
            pastHistory: detail.pastHistory,
            personalHistory: detail.personalHistory,
            physicalExamination: detail.physicalExamination,
            presentHistory: detail.presentHistory,
            epidemiologicalHistory: detail.epidemiologicalHistory,

            patient: detail.patient,

            registrationFormItem: detail.registrationFormItem,

            pay: this.innerState.pay,

            promotions: [],
            couponPromotions: [], // 优惠券促销
            giftRulePromotions: [], // 满减活动
            patientPointsInfo: undefined, //积分抵扣
            patientCardPromotions: [], // 卡项
            visitSourceId: detail.visitSourceId,
            visitSourceFrom: detail.visitSourceId == detail.visitSourceFrom ? "" : detail.visitSourceFrom,
            visitSourceRemark: detail.visitSourceRemark ?? "",
            revisitStatus: detail.revisitStatus,
            registrationType: RegistrationType.therapyAppointment,
        });

        const dialog = new LoadingDialog();
        dialog.show(300);

        //微信沟通进入
        if (this._source == RegistrationPageSourceType.patientChat) {
            this._setPatientRegAuditStatusSubject.next();
            await dialog.hide();
            return;
        } else {
            let rsp;
            //新增
            if (this.innerState.isCreate) {
                if (!req.patient?.id) {
                    //后台使用了source重新定义了patientSource
                    //@ts-ignore
                    req.patient.source = req.patient.patientSource;
                }
                rsp = await DentistryAgent.saveRegistrationManage(req).catch((e) => new ABCError(e));
            } else {
                //修改
                const registrationSheetId = this.innerState.detail?.registrationFormItem?.registrationSheetId ?? this.innerState.detail?.id;
                if (!registrationSheetId) return;
                rsp = await DentistryAgent.modifyRegistrationManage(registrationSheetId, req).catch((e) => new ABCError(e));
            }

            await dialog.hide();
            if (rsp instanceof ABCError) {
                await showQueryDialog(`预约失败`, errorToStr(rsp));
                // }
            } else if (rsp) {
                if (rsp instanceof RegistrationInfoModifyRsp) {
                    dialog.show(300);
                    if (rsp.isSuccess == 1) {
                        await dialog.success("保存成功").then(() => {
                            if (this._source == RegistrationPageSourceType.normal) {
                                ABCNavigator.popUntil(URLProtocols.REGISTRATION_TAB, URLProtocols.REGISTRATION_TAB);
                            } else {
                                ABCNavigator.pop({ success: true });
                            }
                        });
                    } else {
                        await dialog.fail("保存失败");
                    }
                } else {
                    this.innerState.detail = rsp;
                    this.innerState.type = TherapyAppointmentInvoiceType.addition;
                    this.innerState.hasChange = false;
                    const result = await RegistrationSuccessDialog.show({
                        detail: this.innerState.detail,
                        isShowOrderNo: !this.innerState.isFlexibleMode, //灵活模式--无诊号
                        isShowCharge: false,
                        doctorText: "理疗师",
                        isUpdateClinic: userCenter.isAllowedRegUpgrade,
                    });

                    if (result == DialogIndex.positive) {
                        //     this.dispatch(new _EventRegistrationCharge());
                    } else {
                        ABCNavigator.pop({ success: true });
                    }
                }
            }
        }
    }
}
