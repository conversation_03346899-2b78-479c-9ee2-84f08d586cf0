import { BaseBlocNetworkPage } from "../../../base-ui/base-page";
import {
    ScrollToErrorViewState,
    TherapyAppointmentInvoicePageBloc,
    TherapyAppointmentInvoiceType,
} from "./therapy-appointment-invoice-page-bloc";
import BlocBuilder from "../../../bloc/bloc-builder";
import { BlocHelper } from "../../../bloc/bloc-helper";
import { ScrollView, Text, View } from "@hippy/react";
import { FocusItemKeys, RegistrationFormItem, RegistrationPageSourceType, RegistrationStatusV2 } from "../../data/bean";
import { ToolBar, ToolBarButtonStyle1, ToolBarButtonStyle2, UniqueKey } from "../../../base-ui";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../theme";
import React from "react";
import { TherapyDoctorCard } from "./therapy-doctor-card";
import { userCenter } from "../../../user-center";
import AbcPatientCardInfoView from "../../../outpatient/views/new-patient-Info-view";
import { AbcButton } from "../../../base-ui/views/abc-button";
import { HistoryPermissionModuleType } from "../../../base-business/data/beans";

const kFirstChild = "AppointmentInvoicePage.firstChild";
const kLastChild = "AppointmentInvoicePage.lastChild";

interface AppointmentOldSimpleInfo {}

interface AppointmentInvoicePageProps {
    id?: string;
    oldSimpleInfo?: AppointmentOldSimpleInfo;
    source?: RegistrationPageSourceType;
    registrationFormItem?: RegistrationFormItem;
    fromKanbanEntry?: boolean; //是否从看板进入
}

export class AppointmentInvoicePage extends BaseBlocNetworkPage<AppointmentInvoicePageProps, TherapyAppointmentInvoicePageBloc> {
    private _scrollView?: ScrollView | null;

    constructor(props: AppointmentInvoicePageProps) {
        super(props);
        this.bloc = new TherapyAppointmentInvoicePageBloc({
            id: props.id,
            source: props?.source,
            registrationFormItem: props.registrationFormItem,
            fromKanbanEntry: props?.fromKanbanEntry,
        });
    }

    getAppBarTitle(): string {
        return this.bloc.currentState.isCreate ? "新增预约" : "编辑预约";
    }

    getAppBar(): JSX.Element | undefined {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }

    getRightAppBarIcons(): JSX.Element[] {
        const bloc = this.bloc,
            state = bloc.currentState;
        return [
            <View key={"dendistry_registration_finish"}>
                <AbcButton
                    style={{
                        height: Sizes.dp30,
                        width: Sizes.dp52,
                        backgroundColor: Colors.mainColor,
                    }}
                    key={"submit"}
                    pressColor={Colors.mainColorPress}
                    onClick={() => this.bloc.requestFinishTherapyAppointment()}
                >
                    <Text style={[TextStyles.t14MS2, ABCStyles.rowAlignCenter]} numberOfLines={1}>
                        {state.isCreate ? "完成" : "保存"}
                    </Text>
                </AbcButton>
            </View>,
        ];
    }

    componentDidMount(): void {
        BlocHelper.connectLoadingStatus(this.bloc, this);

        this.bloc.state.subscribe((state) => {
            if (state instanceof ScrollToErrorViewState) {
                this._scrollView?.scrollChildToVisible(state.focusKey, kFirstChild, kLastChild);
            }
        });
    }
    renderContent(): JSX.Element | undefined {
        const state = this.bloc.currentState,
            detail = state.detail,
            showErrorHint = state.showErrorHint,
            registrationConfig = state.registrationConfig,
            localTimeCountList = state.localTimeCountList,
            registrationInfo = detail?.registrationFormItem;
        let isEditing = true;
        if (
            registrationInfo?.statusV2 == RegistrationStatusV2.waitingSignIn ||
            registrationInfo?.statusV2 == RegistrationStatusV2.waitingDiagnose
        ) {
            isEditing = true;
        } else if (
            registrationInfo?.statusV2 == RegistrationStatusV2.refunded ||
            registrationInfo?.statusV2 == RegistrationStatusV2.diagnosed
        ) {
            isEditing = false;
        }
        const serviceMinMinutes = (state.serviceMinDuration?.hour ?? 0) * 60 + (state.serviceMinDuration?.min ?? 0);
        const serviceMaxMinutes = (state.serviceMaxDuration?.hour ?? 0) * 60 + (state.serviceMaxDuration?.min ?? 0);
        //灵活模式下，时间是否可编辑
        const canEditTime = state.type == TherapyAppointmentInvoiceType.addition || state.canEditTimeOfFlexible;
        return (
            <View style={{ flex: 1, backgroundColor: Colors.prescriptionBg }}>
                <ScrollView
                    style={{ flex: 1 }}
                    showsVerticalScrollIndicator={false}
                    ref={(ref) => {
                        this._scrollView = ref;
                    }}
                >
                    <View ref={kFirstChild} collapsable={false} />
                    {this._renderPatientView()}
                    <TherapyDoctorCard
                        isEditing={state.isCreate || isEditing}
                        isCreate={state.isCreate}
                        showErrorHint={showErrorHint}
                        registrationFormItem={detail?.registrationFormItem}
                        onChangeDoctor={() => {
                            this.bloc.requestModifyAppointmentDoctor(); // 选择理疗师
                        }}
                        modeType={registrationConfig?.modeType}
                        localTimeCountList={localTimeCountList}
                        flexibleChangeDate={() => this.bloc.requestModifyFlexibleDate()}
                        flexibleSelectTimeRange={(date, timeOfDay) => this.bloc.requestSelectTimeRange(date, timeOfDay)}
                        modifyFixedSourceDate={() => this.bloc.requestModifyFixedSourceDate()}
                        modifyFixedSourceTime={() => this.bloc.requestModifyFixedSourceTime()}
                        modifyFixedTimeRange={() => this.bloc.requestModifyFixedTimeRange()}
                        isOpenReserveProduct={registrationConfig?.isOpenReserveProduct}
                        flexibleSearchProject={(project) => this.bloc.requestSearchProject(project)}
                        remark={detail?.visitSourceRemark}
                        modifyRegistrationVisitSourceRemark={(remark) => this.bloc.requestModifyRegistrationVisitSourceRemark(remark ?? "")}
                        serviceMinMinutes={serviceMinMinutes}
                        serviceMaxMinutes={serviceMaxMinutes}
                        schedulingTimeList={state.doctorShiftsInfo}
                        canEditTimeOfFlexible={canEditTime}
                    />

                    <View ref={kLastChild} collapsable={false} />
                </ScrollView>
                {/*{this._renderBottomGroup()}*/}
            </View>
        );
    }

    private _renderPatientView(): JSX.Element {
        const state = this.bloc.currentState,
            config = state.employeeConfig,
            detail = state.detail,
            showErrorHint = state.showErrorHint,
            patient = detail?.patient;

        const regShowView: JSX.Element[] = [];
        if (!config?.therapyHiddenIdCard) {
            regShowView.push(
                <Text key={"idCard"} style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp22 })]}>
                    {patient?.idCard?.length ? patient?.idCard : "暂无身份证号"}
                </Text>
            );
        }
        if (!config?.therapyHiddenSource) {
            regShowView.push(
                <Text key={"source"} style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp22 })]}>
                    {patient?.patientSource?.sourceDisplay.length ? patient?.patientSource?.sourceDisplay : "暂无来源"}
                </Text>
            );
        }
        if (!config?.therapyHiddenAddress) {
            regShowView.push(
                <Text key={"address"} style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp22 })]}>
                    {patient?.address?.addressDisplay.length ? patient?.address?.addressDisplay : "暂无详细地址"}
                </Text>
            );
        }
        if (!config?.therapyHiddenProfession) {
            regShowView.push(
                <Text key={""} style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp22 })]}>
                    {patient?.profession ? patient?.profession : "暂无职业"}
                </Text>
            );
        }
        if (!config?.therapyHiddenSn) {
            regShowView.push(
                <Text key={"sn"} style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp22 })]}>
                    {patient?.sn ?? "暂无档案号"}
                </Text>
            );
        }
        return (
            <View ref={FocusItemKeys.patient}>
                <AbcPatientCardInfoView
                    patient={detail?.patient}
                    patientSwitchable={state.isCreate}
                    requirePatientSource={userCenter.clinicConfig?.regsRequiredSource ?? false} // 患者来源是否必填
                    requireMobile={userCenter.clinicConfig?.regsRequiredMobile ?? false} // 手机号是否必填
                    showErrorHint={showErrorHint && !patient}
                    onChange={(patient) => {
                        this.bloc._requestModifyAppointmentPatient(patient); // 预约患者信息详情
                    }}
                    diagnoseCount={state.diagnoseCount}
                    type={HistoryPermissionModuleType.registration}
                    isCanSeePatientHistoryInRegister={state.canViewDiagnoseHistory}
                    canSeePatientMobileInRegister={state.canSeePatientPhone}
                />
            </View>
        );
    }

    private _renderBottomGroup(): JSX.Element {
        const state = this.bloc.currentState;

        const buttons: JSX.Element[] = [];

        if (state) {
            if (!state.isCreate && !state.isCanCanceled) {
                buttons.push(
                    <ToolBarButtonStyle2
                        style={{ width: state.isCanSignIn ? Sizes.dp78 : undefined }}
                        key={UniqueKey()}
                        text={"取消预约"}
                        onClick={() => {
                            this.bloc.requestCancelPatientAppointment();
                        }}
                    />
                );
            }
        }

        if (state) {
            if (state.isCreate) {
                buttons.push(
                    <ToolBarButtonStyle1 key={UniqueKey()} text={"完成预约"} onClick={() => this.bloc.requestFinishTherapyAppointment()} />
                );
            } else if (state.isCanSignIn) {
                buttons.push(<ToolBarButtonStyle1 key={UniqueKey()} text={"签到"} onClick={() => this.bloc.requestSignInAppointment()} />);
            }
        }

        if (buttons.length !== 0) {
            return <ToolBar>{buttons}</ToolBar>;
        }
        return <View />;
    }
}
