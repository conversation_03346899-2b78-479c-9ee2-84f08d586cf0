import React from "react";
import { Text, View } from "@hippy/react";
import { RegistrationTabType } from "../data/bean";
import { BasePage } from "../../base-ui";
import { RegistrationListViewBloc } from "../registration-list-view-bloc";
import { AppSearchBar } from "../../base-ui/app-bar";
import { Color, Colors, Sizes, TextStyles } from "../../theme";
import { DentistryListView } from "./dentistry-list-view";
import { AbcView } from "../../base-ui/views/abc-view";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { DentistryAgent } from "./data/dentistry-agent";
import { GetRegistrationListRsp } from "../data/registration-agent";
import { RegistrationInvoiceSummaryDialog } from "../registration-invoice-summary-dialog";
import { AppointmentInvoiceSummaryDialog } from "./appointment/appointment-invoice-summary-dialog";

interface DentistrySearchPageProps {
    type: RegistrationTabType;
    searchContent?: string;
}

export class DentistrySearchPage extends BasePage<DentistrySearchPageProps, RegistrationListViewBloc> {
    private _dentistryListViewRef?: DentistryListView | null;

    constructor(props: DentistrySearchPageProps) {
        super(props);
    }

    pageName(): string | undefined {
        return "搜索患者";
    }

    _jumpRelativePage(rsp?: GetRegistrationListRsp): void {
        const data = rsp?.rows;
        if (data && data?.length == 1) {
            const itemInfo = data?.[0]?.registrationFormItem;
            if (this.props.type == RegistrationTabType.registration) {
                RegistrationInvoiceSummaryDialog.show({
                    id: data[0]?.id,
                    doctorId: itemInfo?.doctorId,
                    oldReserveInfo: data[0]?.registrationFormItem?.oldReserveInfo,
                    statusName: itemInfo?.statusName,
                });
            } else {
                AppointmentInvoiceSummaryDialog.show({
                    id: data[0]?.id,
                    statusName: itemInfo?.statusName,
                });
            }
        } else {
            this._dentistryListViewRef?.searchByKeyword(rsp?.keywords ?? "");
        }
    }

    getAppBar(): JSX.Element {
        return (
            <AppSearchBar
                placeholder={"姓名/手机/诊号"}
                rightPart={this.getRightAppBarIcons()}
                onBackClick={this.onBackClick.bind(this)}
                autoFocus={true}
                onChangeText={(value) => {
                    this._dentistryListViewRef?.searchByKeyword(value);
                }}
                searchContainerStyle={{ backgroundColor: Colors.bg1 }}
                inputStyle={{ backgroundColor: Colors.bg1, height: Sizes.dp36, ...Sizes.marginLTRB(6, 0) }}
                bottomLine={false}
                isNeedAutoFillText={true}
                isShowScanIcon={true}
                qrScanGenerator={DentistryAgent.queryQuickList}
                qrScanRsp={(rsp) => this._jumpRelativePage(rsp)}
            />
        );
    }

    getBackgroundColor(): Color {
        return Colors.white;
    }

    getRightAppBarIcons(): JSX.Element[] {
        return [
            <AbcView key={"dentistry_back"} onClick={() => ABCNavigator.pop()}>
                <Text style={[TextStyles.t14NT6]}>取消</Text>
            </AbcView>,
        ];
    }

    renderContent(): JSX.Element {
        const { type } = this.props;
        return (
            <View style={{ flex: 1 }}>
                <DentistryListView ref={(ref) => (this._dentistryListViewRef = ref)} type={type} searchMode={true} />
            </View>
        );
    }
}
