import React from "react";
import { Text, View } from "@hippy/react";
import { ABCNetworkPageContentStatus, BaseBlocNetworkView } from "../../base-ui/base-page";
import { RegistrationListViewBloc } from "../registration-list-view-bloc";
import { Filters } from "../../base-ui/searchBar/search-bar-bean";
import { PayStatusV2, RegistrationDetail, RegistrationStatusV2, RegistrationTabType } from "../data/bean";
import { AbcEmptyItemView } from "../../base-ui/views/empty-view";
import { AbcListView } from "../../base-ui/list/abc-list-view";
import { DataHolder } from "../../views/list-data-holder";
import {
    DentistryPatientListItemView,
    PatientListItemStatusTextStyle,
    PatientListItemView,
    StatusCountDownView,
} from "../../outpatient/views/patient-list-item-view";
import { TimeUtils } from "../../common-base-module/utils";
import { IconFontView, SizedBox, UniqueKey } from "../../base-ui";
import _ from "lodash";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { AssetImageView } from "../../base-ui/views/asset-image-view";
import { Badge } from "../../base-ui/badge/badge";
import { AbcView } from "../../base-ui/views/abc-view";
import { RegistrationInvoiceSummaryDialog } from "../registration-invoice-summary-dialog";
import { AppointmentInvoiceSummaryDialog } from "./appointment/appointment-invoice-summary-dialog";
import { DentistryConfig } from "./data/bean";
import { RegistrationKanBanFixedOrderModeView } from "../views/registration-kanban/fixed-order-mode";
import { RegistrationKanBanFlexibleTimeModeView } from "../views/registration-kanban/flexible-time-mode";
import { userCenter } from "../../user-center";

interface DentistryListViewProps {
    type?: number;
    searchMode?: boolean;
    viewMode?: boolean;
}

export class DentistryListView extends BaseBlocNetworkView<DentistryListViewProps, RegistrationListViewBloc> {
    initLoad = true;
    constructor(props: DentistryListViewProps) {
        super(props);
        this.bloc = new RegistrationListViewBloc({ type: props.type, searchMode: props.searchMode, viewMode: props.viewMode });
    }

    componentDidMount(): void {
        super.componentDidMount();

        this.bloc.state
            .subscribe((state) => {
                let status = ABCNetworkPageContentStatus.show_data;
                if (state.loading && this.initLoad) {
                    if (_.isEmpty(state.dataList)) {
                        status = ABCNetworkPageContentStatus.loading;
                    }
                } else if (state.loadError) {
                    status = ABCNetworkPageContentStatus.error;
                } else if (
                    ((!state.loading && !this.props.searchMode) || (this.props.searchMode && !!state.keyword?.length)) &&
                    !state.viewMode &&
                    _.isEmpty(state.dataList)
                ) {
                    //第一个条件，判断列表情况；第二个条件，判断搜索列表情况
                    status = ABCNetworkPageContentStatus.empty;
                }

                this.setContentStatus(status, state.loadError);
            })
            .addToDisposableBag(this);
    }

    public changeFilter(filter: Filters): void {
        this.bloc.requestChangeFilter(filter);
    }

    public changeViewMode(isKanban: boolean): void {
        this.bloc.requestChangeViewMode(isKanban);
    }

    public searchByKeyword(keyword: string): void {
        this.bloc.requestSearchPatient(keyword);
    }

    /**
     * 添加新挂号/预约
     */
    public navToAddInvoiceDetail(): void {
        this.bloc.requestAddInvoiceDetail();
    }

    reloadData(): void {
        this.bloc.requestReload();
    }

    emptyContent(): JSX.Element {
        if (this.props.type == RegistrationTabType.registration) {
            return <AbcEmptyItemView tips={"暂无挂号/预约记录"} />;
        } else {
            return <AbcEmptyItemView tips={"暂无理疗预约记录"} />;
        }
    }

    renderKanbanView(): JSX.Element {
        const isFlexibleMode = this.bloc.currentState.registrationConfig?.isFlexibleMode;
        const isFixedMode = this.bloc.currentState.registrationConfig?.isFixedMode;
        const queryParams = this.bloc.currentState.queryParams;
        const viewMode = this.bloc.currentState.viewMode;
        if (isFixedMode) {
            return (
                <RegistrationKanBanFixedOrderModeView
                    showOrderNo={!this.bloc.currentState.registrationConfig?.isSignInGetOrderNo}
                    showCategories={this.bloc.currentState.clinicRegistrationCategories?.enableRegistrationCategories}
                    registrationType={this.props.type}
                    dailyViews={this.bloc.currentState.kanbanDetail?.dailyViews}
                    onCurrentIndex={(index, callback) => {
                        this.bloc.requestKanbanWeekModify(index, callback);
                    }}
                    viewMode={viewMode}
                    canModifyRegistration={
                        !!this.bloc.currentState.employeesMeConfig?.employeeDataPermission?.registration?.isCanSeeModifyRegistration
                    }
                />
            );
        } else if (isFlexibleMode) {
            return (
                <RegistrationKanBanFlexibleTimeModeView
                    initDate={queryParams.date}
                    registrationType={this.props.type}
                    dailyViews={this.bloc.currentState.kanbanDetail?.kanBanDailyViews}
                    onCurrentIndex={(index, callback) => {
                        this.bloc.requestKanbanWeekModify(index, callback);
                    }}
                    viewMode={viewMode}
                    canModifyRegistration={
                        !!this.bloc.currentState.employeesMeConfig?.employeeDataPermission?.registration?.isCanSeeModifyRegistration
                    }
                />
            );
        }
        return <View />;
    }

    renderContent(): JSX.Element {
        const state = this.bloc.currentState;
        // if (ABCUtils.isEmpty(state.dataList)) return <View />;
        const itemCount = state.dataList.length ?? 0;
        const registrationConfig = state.registrationConfig;
        if (!!state.viewMode) {
            return this.renderKanbanView();
        }
        const _dataSource = state.dataList.map((item) =>
            this.props.type == RegistrationTabType.registration
                ? new _RegistrationListItemHolder(item, this.bloc, this.props.searchMode, registrationConfig)
                : new _AppointmentRegistrationListItemHolder(item, this.bloc, this.props.searchMode, registrationConfig, false)
        );
        return (
            <View style={{ flex: 1 }}>
                <AbcListView
                    loading={state.loading}
                    scrollEventThrottle={300}
                    initialListSize={20}
                    preloadItemNumber={7}
                    getRowKey={(index) => `${state.dataList[index].id ?? state.dataList[index].registrationId ?? UniqueKey()} `}
                    renderRow={(item) => item.createView() ?? <View />}
                    numberOfRows={itemCount}
                    dataSource={_dataSource}
                    finished={!state.hasMore}
                    onRefresh={() => {
                        this.initLoad = false;
                        this.bloc.requestReload();
                    }}
                    onEndReached={() => {
                        this.bloc.requestLoadMore();
                    }}
                />
            </View>
        );
    }
}

class _RegistrationListItemHolder extends DataHolder<RegistrationDetail, RegistrationListViewBloc> {
    private _searchMode?: boolean;
    private _registrationConfig?: DentistryConfig;
    private _isShowDepartment?: boolean;

    constructor(
        props: RegistrationDetail,
        bloc: RegistrationListViewBloc,
        searchMode?: boolean,
        registrationConfig?: DentistryConfig,
        isShowDepartment?: boolean
    ) {
        super(props);
        this._searchMode = searchMode;
        this.bloc = bloc;
        this._registrationConfig = registrationConfig;
        this._isShowDepartment = isShowDepartment ?? true;
    }

    handleClick(): void {
        const itemInfo = this.data?.registrationFormItem;
        RegistrationInvoiceSummaryDialog.show({
            id: this.data?.id,
            doctorId: itemInfo?.doctorId,
            oldReserveInfo: this.data?.registrationFormItem?.oldReserveInfo,
            statusName: itemInfo?.statusName,
        });
    }

    createView(): JSX.Element {
        const itemInfo = this.data?.registrationFormItem;
        const statusName = itemInfo?.statusName;
        let statusTextStyle = PatientListItemStatusTextStyle.visited;
        switch (itemInfo?.statusV2) {
            case RegistrationStatusV2.waitingSignIn:
                statusTextStyle = PatientListItemStatusTextStyle.Style4;
                break;
            case RegistrationStatusV2.waitPay:
            case RegistrationStatusV2.waitingDiagnose:
            case RegistrationStatusV2.continueDiagnose:
                statusTextStyle = PatientListItemStatusTextStyle.waitVisit;
                break;
            case RegistrationStatusV2.diagnosed:
            case RegistrationStatusV2.expired:
            case RegistrationStatusV2.refunded:
            case RegistrationStatusV2.canceled:
                statusTextStyle = PatientListItemStatusTextStyle.visited;
                break;
        }
        // 医助能查看医助的医生 显示姓名、号数 ，没有选择医生不做显示
        // 医生查看自己接诊列表 仅显示时间号数
        // 管理员可查看所有医生姓名
        let timeOverride;
        const doctorName = itemInfo?.doctorName?.length ? itemInfo?.doctorName : "--";
        const departmentName: string = itemInfo?.departmentName?.length ? itemInfo?.departmentName : "其他";

        timeOverride = itemInfo?.orderNoStr;

        //TODO 灵活模式显示预约时间 timeOverride
        if (this._registrationConfig?.isFlexibleMode) {
            timeOverride = `${itemInfo?.reserveStart ?? ""}`;
        }

        if (this._registrationConfig?.isSignInGetOrderNo && (itemInfo?.statusV2 ?? 0) < RegistrationStatusV2.waitingSignIn) {
            timeOverride = "";
        }

        if (this._searchMode) {
            timeOverride = TimeUtils.formatDatetimeAsRecent(itemInfo?.reserveDate, { time: false });
        }
        if (userCenter.clinic?.isDentistryClinic) {
            return (
                <DentistryRegistrationPatientListItemView
                    patient={this.data?.patient}
                    extraIconBeforeTime={() => {
                        return !!doctorName ? (
                            <View style={{ flexDirection: "row", alignItems: "center", flexShrink: 1, marginRight: Sizes.dp12 }}>
                                <Text style={[TextStyles.t14NT1, { flexShrink: 1 }]} numberOfLines={1}>
                                    {doctorName ?? ""}
                                </Text>
                            </View>
                        ) : (
                            <View />
                        );
                    }}
                    showStatusPoint={
                        (itemInfo?.statusV2 ?? 0) > RegistrationStatusV2.diagnosing &&
                        this.data?.registrationFormItem?.payStatusV2 == PayStatusV2.paid
                    }
                    contentEndText={itemInfo?.registrationProducts?.map((item) => item.displayName).join("、") ?? ""}
                    time={itemInfo?.diagnosingTime ?? itemInfo?.created}
                    statusDuration={itemInfo?.statusDuration ?? 0}
                    timeOverride={timeOverride}
                    timeStyle={TextStyles.t14NT1}
                    status={itemInfo?.statusV2 == RegistrationStatusV2.diagnosing ? "诊中" : statusName}
                    statusV2={itemInfo?.statusV2}
                    statusTextStyle={statusTextStyle}
                    isReserved={itemInfo?.isReserved}
                    onClick={() => {
                        this.handleClick();
                    }}
                    anonymousName={"匿名患者"}
                    isHasAbnormal={this.data?.isSheBaoAbnormal || this.data?.isNotSheBaoAbnormal}
                    isReferral={this.data?.registrationFormItem?.isReferral}
                    isConvenient={this.data?.registrationFormItem?.isConvenient}
                    onChangeStatus={() => {
                        this.bloc?.requestModifyRegistrationStatus(this.data!);
                    }}
                />
            );
        }

        const isClinicClosed = this.data?.registrationFormItem?.isStopScheduleSelfStatus; // 是否全部停诊
        return (
            <RegistrationPatientListItemView
                patient={this.data?.patient}
                extraIconBeforeTime={() => {
                    return !!doctorName ? (
                        <View style={{ flexDirection: "row", alignItems: "center", flexShrink: 1, marginRight: Sizes.dp14 }}>
                            {this._isShowDepartment && (
                                <Text style={[TextStyles.t14NT1, { flexShrink: 1 }]} numberOfLines={1}>
                                    {!!departmentName?.length ? departmentName : ""}
                                </Text>
                            )}
                            <Text style={[TextStyles.t14NT1, { flexShrink: 1 }]} numberOfLines={1}>
                                {`${this._isShowDepartment && departmentName?.length ? "/" : ""}${doctorName ?? ""}`}
                            </Text>
                        </View>
                    ) : (
                        <View />
                    );
                }}
                time={itemInfo?.created}
                timeOverride={timeOverride}
                timeStyle={TextStyles.t14NT1}
                status={statusName}
                statusTextStyle={statusTextStyle}
                isReserved={itemInfo?.isReserved}
                onClick={() => {
                    this.handleClick();
                }}
                anonymousName={"匿名患者"}
                isHasAbnormal={this.data?.isSheBaoAbnormal || this.data?.isNotSheBaoAbnormal}
                isConvenient={this.data?.registrationFormItem?.isConvenient}
                isReferral={this.data?.registrationFormItem?.isReferral}
                isClinicClosed={isClinicClosed}
            />
        );
    }
}

class _AppointmentRegistrationListItemHolder extends _RegistrationListItemHolder {
    handleClick(): void {
        const itemInfo = this.data?.registrationFormItem;
        AppointmentInvoiceSummaryDialog.show({
            id: this.data?.id,
            statusName: itemInfo?.statusName,
        });
    }
}

export class RegistrationPatientListItemView extends PatientListItemView {
    render(): JSX.Element {
        const { onClick, style = {} } = this.props;
        return (
            <View
                style={[
                    {
                        paddingLeft: Sizes.dp16,
                        minHeight: Sizes.dp56,
                    },
                    style,
                ]}
            >
                <AbcView
                    style={[
                        ABCStyles.rowAlignCenterSpaceBetween,
                        ABCStyles.bottomLine,
                        Sizes.paddingLTRB(0, Sizes.dp18, Sizes.dp16, Sizes.dp18),
                        { position: "relative", top: -0.5 },
                    ]}
                    onClick={() => {
                        onClick?.();
                    }}
                >
                    <View style={[ABCStyles.rowAlignCenter, { flexShrink: 1 }]}>
                        {this.renderAvatarView()}
                        <SizedBox width={Sizes.dp8} />
                        {this.renderPatientNameView()}
                        {this.renderAfterPatientIconsView()}
                    </View>
                    <SizedBox width={Sizes.dp8} />
                    <View style={[ABCStyles.rowAlignCenter, { flexShrink: 1, justifyContent: "flex-end" }]}>
                        {this.renderExtraIconBeforeTimeView()}
                        {this.renderTimeView()}
                        {this.renderExtraIconAfterTimeView()}
                        {this.renderStatusView()}
                    </View>
                </AbcView>
            </View>
        );
    }

    protected renderAvatarView(): JSX.Element {
        const { patient, redPointText, patientHeadImgUrl } = this.props;
        return (
            <Badge value={redPointText} maxValue={99}>
                <View style={[!!patientHeadImgUrl ? {} : Sizes.paddingLTRB(Sizes.dp10, Sizes.dp9, Sizes.dp10, Sizes.dp9)]}>
                    {!!patientHeadImgUrl ? (
                        <AssetImageView src={patientHeadImgUrl} style={{ width: Sizes.dp32, height: Sizes.dp32 }} />
                    ) : (
                        <IconFontView
                            size={Sizes.dp14}
                            name={"patient"}
                            color={patient?.sex == "男" || !patient?.name ? Colors.malePatientColor : Colors.femalePatientColor}
                        />
                    )}
                </View>
            </Badge>
        );
    }

    protected renderStatusView(): JSX.Element {
        const { status, statusTextStyle } = this.props;
        let statusColor = Colors.T3;
        if (statusTextStyle == PatientListItemStatusTextStyle.draft) {
            statusColor = Colors.freshGreen;
        } else if (statusTextStyle == PatientListItemStatusTextStyle.waitVisit) {
            statusColor = Colors.B1;
        } else if (statusTextStyle == PatientListItemStatusTextStyle.Style4) {
            statusColor = Colors.Y2;
        }
        return !_.isEmpty(status) ? (
            <Text style={[TextStyles.t14MT1.copyWith({ color: statusColor }), { lineHeight: Sizes.dp16 }]}>{status ?? ""}</Text>
        ) : (
            <View />
        );
    }

    protected renderPatientNameView(): JSX.Element {
        const { patient, anonymousName } = this.props;
        return (
            <Text style={[TextStyles.t16NT1, { flexShrink: 1, maxWidth: Sizes.dp96 }]} numberOfLines={1}>
                {patient?.name ? patient?.name : anonymousName ?? "匿名患者"}
            </Text>
        );
    }
}

export class DentistryRegistrationPatientListItemView extends DentistryPatientListItemView {
    protected renderPatientNameView(): JSX.Element {
        const { patient, anonymousName } = this.props;
        return (
            <Text
                style={[
                    TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp24 }),
                    { flexShrink: 1, maxWidth: Sizes.dp96, marginRight: Sizes.dp4 },
                ]}
                numberOfLines={1}
            >
                {patient?.name ? patient?.name : anonymousName ?? "匿名患者"}
            </Text>
        );
    }

    renderContentEndView(): JSX.Element {
        const { contentEndText } = this.props;
        if (!contentEndText) return <View />;
        return (
            <Text style={[TextStyles.t14NT6, { marginTop: Sizes.dp2 }]} numberOfLines={1}>
                {contentEndText}
            </Text>
        );
    }

    protected renderStatusView(): JSX.Element {
        const { onChangeStatus, statusDuration, status, time, waringText, showStatusPoint, statusV2 } = this.props;
        return (
            <View>
                <StatusCountDownView
                    serverTime={time ?? new Date()}
                    currentTime={statusDuration}
                    statusText={status}
                    waringText={waringText}
                    showAfterIcon={showStatusPoint}
                    textAfterIcon={
                        statusV2 == RegistrationStatusV2.waitingSignInContacted
                            ? () => (
                                  <View style={[ABCStyles.centerChild, { flex: undefined }]}>
                                      <IconFontView name={"select"} color={Colors.Y2} size={Sizes.dp12} style={{ marginLeft: Sizes.dp2 }} />
                                  </View>
                              )
                            : undefined
                    }
                    onClick={() => {
                        onChangeStatus?.();
                    }}
                />
            </View>
        );
    }
}
