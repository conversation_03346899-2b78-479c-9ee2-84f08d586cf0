/**
 * create by deng<PERSON>e
 * desc:
 * create date 2021/1/6
 */
import React from "react";
import { ScrollView, Text, View } from "@hippy/react";
import {
    FocusItemKeys,
    OldReserveInfo,
    PayStatusV2,
    RegistrationFormItem,
    RegistrationPageSourceType,
    RegistrationStatusV2,
} from "../data/bean";
import { BaseBlocNetworkPage } from "../../base-ui/base-page";
import { RegistrationInvoicePageBloc, ScrollToErrorViewState } from "../registration-invoice-page-bloc";
import { BlocBuilder } from "../../bloc";
import { BlocHelper } from "../../bloc/bloc-helper";
import { IconFontView, SizedBox } from "../../base-ui";
import { ABCStyles, Color, Colors, Sizes, TextStyles } from "../../theme";
import { ABCUtils } from "../../base-ui/utils/utils";
import { ListSettingItem, ListSettingItemStyle } from "../../base-ui/views/list-setting-item";
import { AbcTextInput } from "../../base-ui/views/abc-text-input";
import { ChargeAbnormalTips } from "../../charge/view/charge-abnormal-dialog";
import { TimeUtils } from "../../common-base-module/utils";
import { AbcFlexTextInput } from "../../base-ui/views/abc-flex-text-input";
import { PrecisionLimitFormatter } from "../../base-ui/utils/formatter";
import { NumberKeyboardBuilder } from "../../base-ui/views/keyboards/number-keyboard";
import AbcPatientCardInfoView from "../../outpatient/views/new-patient-Info-view";
import { AbcBasePanel } from "../../base-ui/abc-app-library";
import { DentistryDoctorCard } from "./views/dentistry-doctor-card";
import { Range } from "../../base-ui/utils/value-holder";
import { pxToDp } from "../../base-ui/utils/ui-utils";
import { HistoryPermissionModuleType, Patient } from "../../base-business/data/beans";
import { AbcTimeRangeView } from "../../base-ui/abc-app-library/time/time-range";
import { AbcButton } from "../../base-ui/views/abc-button";
import { AbcView } from "../../base-ui/views/abc-view";
import FontWeight from "../../theme/font-weights";
import abcI18Next from "../../language/config";
import { Toast } from "../../base-ui/dialog/toast";
import { isNil } from "lodash";
import { AssetImageView } from "../../base-ui/views/asset-image-view";

const kFirstChild = "RegistrationInvoicePage.firstChild";
const kLastChild = "RegistrationInvoicePage.lastChild";

interface DentistryInvoicePageProps {
    id?: string;
    doctorId?: string;
    oldReserveInfo?: OldReserveInfo;
    patient?: Patient;
    bloc?: RegistrationInvoicePageBloc;
    source?: RegistrationPageSourceType;
    departmentId?: string;

    //助理医生列表-复诊时使用
    assistantList?: string[];

    //看板添加
    registrationFormItem?: RegistrationFormItem;
    //是否从看板进入
    fromKanbanEntry?: boolean;
}

export class DentistryInvoicePage extends BaseBlocNetworkPage<DentistryInvoicePageProps, RegistrationInvoicePageBloc> {
    private _scrollView?: ScrollView | null;

    constructor(props: DentistryInvoicePageProps) {
        super(props);
        this.bloc = props.bloc ?? new RegistrationInvoicePageBloc(props);
    }

    protected takeBlocOwnership(): boolean {
        return !this.props.bloc;
    }

    getAppBarTitle(): string {
        return this.bloc.currentState.isCreate ? "新增预约" : "编辑预约";
    }

    getAppBar(): JSX.Element | undefined {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }

    getAppBarBgColor(): Color {
        return Colors.panelBg;
    }

    getStatusBarColor(): Color {
        return Colors.panelBg;
    }

    getBottomSafeAreaColor(): Color {
        return Colors.transparent;
    }

    getRightAppBarIcons(): JSX.Element[] {
        const bloc = this.bloc,
            state = bloc.currentState;
        // 按钮是否可以点击
        const disabledClick = !state.isCreate && !state.hasChange;
        return [
            <View key={"dendistry_registration_finish"}>
                <AbcButton
                    style={{
                        height: Sizes.dp30,
                        width: Sizes.dp52,
                        backgroundColor: disabledClick ? Colors.bdColor : Colors.mainColor,
                    }}
                    key={"submit"}
                    pressColor={Colors.mainColorPress}
                    onClick={() => !disabledClick && this.bloc.requestFinishRegistration()}
                >
                    <Text style={[TextStyles.t14MS2, ABCStyles.rowAlignCenter]} numberOfLines={1}>
                        {state.isCreate ? "完成" : "保存"}
                    </Text>
                </AbcButton>
            </View>,
        ];
    }

    componentDidMount(): void {
        BlocHelper.connectLoadingStatus(this.bloc, this);

        this.bloc.state
            .subscribe((state) => {
                if (state instanceof ScrollToErrorViewState) {
                    this._scrollView?.scrollChildToVisible(state.focusKey, kFirstChild, kLastChild);
                }
            })
            .addToDisposableBag(this);
    }
    private get checkDetailEditStatus(): boolean {
        const state = this.bloc.currentState,
            detail = state.detail,
            registrationInfo = detail?.registrationFormItem;
        //判断是否可编辑(类型、科室、医生、初/复诊、时间、备注、就诊来源)
        //待诊、待签、已签均可编辑；已诊只可编辑初/复诊；已退均不可编辑
        let isEditing = true;
        if (
            [RegistrationStatusV2.refunded, RegistrationStatusV2.diagnosed, RegistrationStatusV2.continueDiagnose].includes(
                registrationInfo?.statusV2 ?? 0
            )
        ) {
            isEditing = false;
        }
        return isEditing;
    }

    renderContent(): JSX.Element | undefined {
        const state = this.bloc.currentState,
            // config = state.employeeConfig,
            detail = state.detail,
            registrationInfo = detail?.registrationFormItem,
            showErrorHint = state.showErrorHint,
            discountFee = detail?.chargeSheet?.chargeSheetSummary?.discountFee,
            registrationConfig = state.registrationConfig;

        let statusText: string | number | string[] | undefined = undefined;
        let statusTextStyle = {};
        let feeDisplay = 0;
        // let showNetIncomeFee = false;
        if (registrationInfo?.payStatusV2 == PayStatusV2.paid) {
            statusText = "已收费";
            statusTextStyle = TextStyles.t14NM;
            feeDisplay = detail?.chargeSheet?.chargeSheetSummary?.receivedFee ?? 0;
        } else if (registrationInfo?.payStatusV2 == PayStatusV2.refunded) {
            statusText = "已退费";
            statusTextStyle = TextStyles.t14NT2;
            feeDisplay = 0;
        } else if (registrationInfo?.payStatusV2 == PayStatusV2.partedRefunded) {
            // showNetIncomeFee = true;
            statusText = "部分退费";
            statusTextStyle = TextStyles.t14NT2;
        } else if (registrationInfo?.payStatusV2 == PayStatusV2.partedPaid) {
            // showNetIncomeFee = true;
            statusText = "欠收";
            statusTextStyle = TextStyles.t14NM;
            feeDisplay = detail?.chargeSheet?.chargeSheetSummary?.needPayFee ?? 0;
        } else {
            feeDisplay = state?.pay?.fee ?? 0;
        }

        //判断是否可编辑(类型、科室、医生、初/复诊、时间、备注、就诊来源)
        //待诊、待签、已签均可编辑；已诊、回诊只可编辑初/复诊；已退均不可编辑
        const isEditing = this.checkDetailEditStatus;

        const referralSource = state.detail?.referralSource;

        return (
            <View
                style={{ flex: 1, backgroundColor: Colors.prescriptionBg }}
                onClick={() => {
                    AbcTextInput.focusInput?.blur();
                }}
            >
                {this._renderChargeAbnormalTips()}
                <ScrollView
                    style={{ flex: 1 }}
                    showsVerticalScrollIndicator={false}
                    ref={(ref) => {
                        this._scrollView = ref;
                    }}
                    onScrollBeginDrag={() => {
                        AbcTextInput.focusInput?.blur();
                    }}
                >
                    <View ref={kFirstChild} collapsable={false} />
                    {this._renderPatientView()}
                    <AbcBasePanel panelStyle={Sizes.marginLTRB(Sizes.dp8, Sizes.dp18, Sizes.dp8, 0)}>
                        <DentistryDoctorCard
                            isEditing={isEditing}
                            isEditDiagnoseType={[RegistrationStatusV2.diagnosed, RegistrationStatusV2.continueDiagnose].includes(
                                registrationInfo?.statusV2 ?? 0
                            )}
                            showErrorHint={showErrorHint}
                            showRegistrationCategory={state.enableRegistrationCategories}
                            registrationInfo={detail?.registrationFormItem}
                            revisitStatusName={detail?.__revisitStatusName}
                            disabledEditRevisit={state.disabledEditRevisitStatus}
                            referralSource={referralSource}
                            onChangeType={(index) => {
                                this.bloc.requestModifyRegistrationType(index);
                            }}
                            onChangeDepartment={() => {
                                this.bloc.requestModifyRegistrationDepartment();
                            }}
                            onChangeDoctor={() => {
                                this.bloc.requestModifyRegistrationDoctor();
                            }}
                            onChangeTime={() => {
                                this.bloc.requestModifyRegistrationTime();
                            }}
                            onChangeRevisit={() => {
                                this.bloc.requestModifyRevisitStatus();
                            }}
                            onChangeRegistrationCategory={() => {
                                this.bloc.requestModifyRegistrationCategory();
                            }}
                        />
                    </AbcBasePanel>

                    <AbcBasePanel panelStyle={Sizes.marginLTRB(Sizes.dp8, Sizes.dp18, Sizes.dp8, 0)}>
                        {state.isFlexibleMode ? this._renderFlexibleTimeView() : this._renderFixedSourceTimeView()}
                    </AbcBasePanel>

                    <AbcBasePanel panelStyle={Sizes.marginLTRB(Sizes.dp8, Sizes.dp18, Sizes.dp8, 0)}>
                        {!!registrationConfig?.isFlexibleMode && !!registrationConfig?.isOpenReserveProduct && (
                            <View style={[{ paddingLeft: Sizes.dp16 }]}>
                                <ListSettingItem
                                    style={{ marginRight: Sizes.dp16, justifyContent: "center", height: Sizes.dp55 }}
                                    itemStyle={isEditing ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal}
                                    bottomLine={true}
                                    title={"项目"}
                                    contentHint={"选择项目"}
                                    content={registrationInfo?.registrationProducts?.map((item) => item.displayName).join("、")}
                                    contentStyle={[
                                        ABCStyles.rowAlignCenter,
                                        {
                                            paddingVertical: Sizes.dp16,
                                            justifyContent: "flex-end",
                                        },
                                    ]}
                                    onClick={() => {
                                        isEditing && this.bloc.requestSearchProject(registrationInfo?.registrationProducts);
                                    }}
                                    contentNumberOfLine={1}
                                    titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                                    leftAlignment={{ alignSelf: undefined }}
                                />
                            </View>
                        )}
                        {this._renderVisitSource()}
                    </AbcBasePanel>

                    <AbcBasePanel panelStyle={Sizes.marginLTRB(Sizes.dp8, Sizes.dp18, Sizes.dp8, 0)}>
                        <ListSettingItem
                            ref={FocusItemKeys.fee}
                            style={{ marginHorizontal: Sizes.dp16, backgroundColor: Colors.white }}
                            contentStyle={{ alignItems: "flex-end", justifyContent: "center" }}
                            contentTextStyle={TextStyles.t18MY2}
                            title={"诊费"}
                            contentBuilder={() => {
                                return (
                                    <View
                                        style={[ABCStyles.rowAlignCenter, { width: Sizes.dp160, justifyContent: "flex-end" }]}
                                        onClick={() => {
                                            return;
                                        }}
                                    >
                                        {!!statusText && (
                                            <Text style={[statusTextStyle, { alignSelf: "center", marginRight: Sizes.dp12 }]}>
                                                {statusText}
                                            </Text>
                                        )}
                                        <Text
                                            style={[
                                                TextStyles.t16NT1.copyWith({
                                                    color:
                                                        !state.shouldRegisteredBargain || (!state.isCreate && state.disabledEditCharge)
                                                            ? Colors.T2
                                                            : Colors.T1,
                                                }),
                                                { paddingRight: 4 },
                                            ]}
                                        >
                                            {abcI18Next.t("¥")}
                                        </Text>
                                        <AbcFlexTextInput
                                            returnKeyType={"done"}
                                            editable={state.shouldRegisteredBargain && !state.disabledEditCharge && isEditing}
                                            multiline={false}
                                            syncTextOnBlur={true} //解决更改值后切换医生props不改变问题
                                            numberOfLines={1}
                                            placeholder={"点击输入"}
                                            defaultValue={ABCUtils.formatPrice(feeDisplay ?? 0)}
                                            formatter={PrecisionLimitFormatter(2)}
                                            customKeyboardBuilder={new NumberKeyboardBuilder({})}
                                            style={{
                                                underlineColorAndroid: Colors.white,
                                                backgroundColor: Colors.white,
                                                height: Sizes.listItemHeight,
                                                ...TextStyles.t16NB.copyWith({
                                                    color:
                                                        !state.shouldRegisteredBargain || (!state.isCreate && state.disabledEditCharge)
                                                            ? Colors.T2
                                                            : Colors.black,
                                                }),
                                            }}
                                            onChangeText={(value) => {
                                                !state.disabledEditCharge && isEditing && this.bloc.requestModifyRegistrationFee(value);
                                            }}
                                        />
                                        <SizedBox width={Sizes.dp16} />
                                    </View>
                                );
                            }}
                            bottomLine={registrationInfo?.payStatusV2 == PayStatusV2.notPaid || state.isCreate}
                            titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                            leftAlignment={{ alignSelf: undefined }}
                        />
                        {(registrationInfo?.payStatusV2 == PayStatusV2.notPaid || state.isCreate) && (
                            <ListSettingItem
                                style={{ paddingHorizontal: Sizes.dp16 }}
                                itemStyle={isEditing ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal}
                                bottomLine={false}
                                title={"优惠"}
                                contentHint={"无可用优惠"}
                                content={!isNil(discountFee) ? ABCUtils.formatPriceWithRMB(discountFee, false).toString() : ""}
                                contentStyle={[
                                    ABCStyles.rowAlignCenter,
                                    {
                                        paddingVertical: Sizes.dp16,
                                        justifyContent: "flex-end",
                                    },
                                ]}
                                onClick={() => isEditing && this.bloc.requestDiscountDetail()}
                                titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                                leftAlignment={{ alignSelf: undefined }}
                            />
                        )}
                    </AbcBasePanel>
                    <View ref={kLastChild} collapsable={false} />
                </ScrollView>
            </View>
        );
    }

    //灵活模式---挂号与预约时间相关组件
    private _renderFlexibleTimeView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail,
            showErrorHint = state.showErrorHint,
            registrationInfo = detail?.registrationFormItem;
        const time = registrationInfo?.reserveDate;
        let dateStr: string | undefined;
        //预约1，挂号0
        if (!!time) {
            if (registrationInfo?.isReserved) {
                dateStr =
                    TimeUtils.getStartOfDate(time).getTime() == TimeUtils.getStartOfDate(new Date()).getTime()
                        ? "今天"
                        : TimeUtils.formatDate(time);
            } else {
                dateStr = `${TimeUtils.formatDatetimeAsRecent(time, { time: false })}`;
            }
        }
        //是否需要限制选择时间(1、挂号需要限制；2、预约：小于当天日期需要限制)
        const isLimitSelTime =
            state.isCreate &&
            !(
                registrationInfo?.isReserved == 1 &&
                TimeUtils.getStartOfDate(time ?? new Date()).getTime() > TimeUtils.getStartOfDate(new Date()).getTime()
            ) &&
            !this.bloc.canSupRecord;

        const dateTime = (time ?? new Date())?.format("yyyy/MM/dd");
        const localTimeCountRange = state.localTimeCountList
            ?.filter((t) => !!t.count)
            ?.map((item) => {
                return {
                    date: new Date(`${dateTime} ${item.localTime}`),
                    render: () => {
                        return (
                            <View style={[ABCStyles.rowAlignCenter, { paddingLeft: Sizes.dp29 }]}>
                                <IconFontView name={"patient"} size={Sizes.dp12} color={Colors.T6} />
                                <SizedBox width={Sizes.dp3} />
                                <Text style={[TextStyles.t12NT6.copyWith({ fontWeight: FontWeight.medium })]}>{item?.count ?? ""}</Text>
                            </View>
                        );
                    },
                };
            });

        const hasDoctorHasCategoriesSchedule = state.checkDoctorCurrentTimeHasSchedule;
        //判断是否可编辑(类型、科室、医生、初/复诊、时间、备注、就诊来源)
        //待诊、待签、已签均可编辑；已诊只可编辑初/复诊；已退均不可编辑
        const isEditing = this.checkDetailEditStatus;
        const exclusionList: Date[] = [];
        state.localTimeCountList
            ?.filter((item) => item.isStopDiagnose === 1)
            ?.map((item) => {
                const now = new Date();
                // 解析时间字符串
                const timeParts = item.localTime?.toString()?.split(":");
                const hours = parseInt(timeParts![0], 10);
                const minutes = parseInt(timeParts![1], 10);
                // 设置新的时间
                now.setHours(hours, minutes, 0, 0);
                exclusionList.push(now);
            });

        return (
            <View>
                <ListSettingItem
                    ref={FocusItemKeys.reserveDate + "date"}
                    style={{ marginHorizontal: Sizes.dp16 }}
                    itemStyle={ListSettingItemStyle.normal}
                    bottomLine={true}
                    title={"日期"}
                    contentHint={"选择日期"}
                    contentBuilder={() => {
                        return (
                            <View
                                style={[
                                    ABCStyles.rowAlignCenter,
                                    {
                                        justifyContent: "flex-end",
                                    },
                                ]}
                            >
                                {registrationInfo?.isReserved == 0 && <Text style={[TextStyles.t16NT1]}>{dateStr ?? ""}</Text>}
                                {!!registrationInfo?.isReserved && (
                                    <AbcView
                                        style={[
                                            {
                                                backgroundColor: Colors.whiteSmoke,
                                                width: Sizes.dp126,
                                                justifyContent: "center",
                                                alignItems: "center",
                                                borderRadius: Sizes.dp4,
                                                height: Sizes.dp38,
                                            },
                                            showErrorHint && !dateStr ? { backgroundColor: Colors.Y4 } : {},
                                        ]}
                                        onClick={() => isEditing && this.bloc.requestChangeDate()}
                                    >
                                        <Text style={[TextStyles.t16NT1.copyWith({ color: !!dateStr ? Colors.T1 : Colors.T4 })]}>
                                            {dateStr ?? "选择日期"}
                                        </Text>
                                    </AbcView>
                                )}
                            </View>
                        );
                    }}
                    titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                    leftAlignment={{ alignSelf: undefined }}
                />
                <ListSettingItem
                    ref={FocusItemKeys.reserveDate + "time"}
                    style={{ paddingHorizontal: Sizes.dp16 }}
                    itemStyle={ListSettingItemStyle.normal}
                    bottomLine={false}
                    title={"时间"}
                    contentHint={"选择时间"}
                    contentBuilder={() => {
                        return (
                            <View
                                style={[
                                    ABCStyles.rowAlignCenter,
                                    {
                                        justifyContent: "flex-end",
                                    },
                                ]}
                            >
                                <AbcTimeRangeView
                                    style={{ width: Sizes.dp126, height: Sizes.dp38 }}
                                    emptyContent={"暂无排班"}
                                    date={
                                        hasDoctorHasCategoriesSchedule && !state.showNoSource
                                            ? new Range<Date>(
                                                  new Date(
                                                      !!registrationInfo?.reserveTime?.start
                                                          ? `${dateTime} ${registrationInfo?.reserveTime?.start}`
                                                          : `${dateTime}`
                                                  ),
                                                  new Date(
                                                      !!registrationInfo?.reserveTime?.end
                                                          ? `${dateTime} ${registrationInfo?.reserveTime?.end}`
                                                          : `${dateTime}`
                                                  )
                                              )
                                            : undefined
                                    }
                                    subTitle={localTimeCountRange}
                                    onChange={(date) => this.bloc.requestSelectTimeRange(date)}
                                    limitNow={isLimitSelTime}
                                    enable={isEditing && hasDoctorHasCategoriesSchedule}
                                    special={true}
                                    showErrorBorder={showErrorHint && !hasDoctorHasCategoriesSchedule}
                                    exclusionList={exclusionList}
                                    exclusionSubText={"停诊"}
                                    exclusionValidate={async () => {
                                        await Toast.show("当前时段医生停诊，无法挂号", { warning: true });
                                        return false;
                                    }}
                                />
                            </View>
                        );
                    }}
                    titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                    leftAlignment={{ alignSelf: undefined }}
                />
            </View>
        );
    }

    //固定号源模式--挂号与预约时间相关组件
    private _renderFixedSourceTimeView(): JSX.Element {
        const state = this.bloc.currentState,
            { showErrorHint, isSignInGetOrderNo, checkDoctorCurrentTimeHasSchedule } = state,
            detail = state.detail,
            registrationInfo = detail?.registrationFormItem;
        const time = registrationInfo?.reserveDate;
        let dateStr: string | undefined, timeStr: string | undefined, periodTimeStr: string | undefined, orderNo: string | undefined;

        let timeStrColor = Colors.T1;
        // 若已选时段，医生排班都已停诊，则展示已停诊状态，不可挂号  (若已选时段还有未停诊班次，则只展示未停诊班次)

        if (state.showStopDiagnose()) {
            timeStr = "停诊";
            timeStrColor = Colors.Y1;
            if (registrationInfo?.isReserved) {
                dateStr = time ? TimeUtils.formatDate(time) : undefined;
            } else {
                dateStr = time
                    ? `${TimeUtils.formatDatetimeAsRecent(time, { time: false })} ${registrationInfo?.timeOfDay ?? ""}`
                    : undefined;
            }
        } else {
            //预约
            if (registrationInfo?.isReserved) {
                dateStr = time ? TimeUtils.formatDate(time) : undefined;
                periodTimeStr = registrationInfo.timeOfDay ?? "";
                orderNo = isSignInGetOrderNo ? "" : registrationInfo?.displayOrderNumber ?? "";
                timeStr = !!registrationInfo?.reserveTime
                    ? `${orderNo} ${registrationInfo?.reserveTime?.start} ~ ${registrationInfo?.reserveTime?.end}`
                    : "";
            } else {
                //挂号
                dateStr = time
                    ? `${TimeUtils.formatDatetimeAsRecent(time, { time: false })} ${registrationInfo?.timeOfDay ?? ""}`
                    : undefined;
                timeStr = registrationInfo?.reserveTime
                    ? `${registrationInfo?.displayOrderNumber} ${registrationInfo?.reserveTime?.start ?? ""} ~ ${
                          registrationInfo?.reserveTime?.end ?? ""
                      }`
                    : undefined;
                if (!checkDoctorCurrentTimeHasSchedule) {
                    timeStr = undefined;
                }
            }
            timeStrColor = !!timeStr ? Colors.T1 : Colors.T4;
        }

        //判断是否可编辑(类型、科室、医生、初/复诊、时间、备注、就诊来源)
        //待诊、待签、已签均可编辑；已诊只可编辑初/复诊；已退均不可编辑
        const isEditing = this.checkDetailEditStatus;
        // 显示加号标识：固定号源模式+预约取号+未排班不可挂号+非挂满后不可加号+当前号源类型是加号
        const isShowAdditionFlag =
            state.isFixedMode &&
            state.registrationConfig?.isReservationGetOrderNo &&
            !state.registrationConfig?.isEnableNoneScheduleRegistration &&
            state.registrationConfig?.isCanNotAdd &&
            state.isAdditional;
        return (
            <View>
                <ListSettingItem
                    ref={FocusItemKeys.reserveDate + "date"}
                    style={{ marginHorizontal: Sizes.dp16 }}
                    itemStyle={ListSettingItemStyle.normal}
                    bottomLine={true}
                    title={"日期"}
                    contentBuilder={() => {
                        return (
                            <View style={[ABCStyles.rowAlignCenter, { justifyContent: "flex-end" }]}>
                                <AbcView
                                    style={[
                                        !!periodTimeStr ? { width: Sizes.dp109 } : { width: pxToDp(172) },
                                        {
                                            backgroundColor: Colors.whiteSmoke,
                                            justifyContent: "center",
                                            alignItems: "center",
                                            borderRadius: Sizes.dp4,
                                            height: Sizes.dp38,
                                        },
                                    ]}
                                    onClick={() => {
                                        isEditing && this.bloc.requestModifyFixedSourceDate();
                                    }}
                                >
                                    <Text style={[TextStyles.t16NT1.copyWith({ color: !!dateStr ? Colors.T1 : Colors.T4 })]}>
                                        {dateStr ?? "选择日期"}
                                    </Text>
                                </AbcView>
                                {!!periodTimeStr && (
                                    <AbcView
                                        style={[
                                            Sizes.paddingLTRB(Sizes.dp12, 0),
                                            {
                                                backgroundColor: Colors.whiteSmoke,
                                                marginLeft: Sizes.dp7,
                                                height: Sizes.dp38,
                                                borderRadius: Sizes.dp4,
                                                justifyContent: "center",
                                                alignItems: "center",
                                            },
                                        ]}
                                        onClick={() => isEditing && this.bloc.requestModifyFixedTimeRange()}
                                    >
                                        <Text style={[TextStyles.t16NT1]}>{periodTimeStr}</Text>
                                    </AbcView>
                                )}
                            </View>
                        );
                    }}
                    titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                    leftAlignment={{ alignSelf: undefined }}
                />
                <ListSettingItem
                    ref={FocusItemKeys.reserveDate + "time"}
                    style={{ paddingHorizontal: Sizes.dp16 }}
                    itemStyle={ListSettingItemStyle.normal}
                    bottomLine={false}
                    title={"号数"}
                    contentHint={"选择号数"}
                    onClick={() => !!timeStr && isEditing && !state.showNoSource && this.bloc.requestModifyFixedSourceTime()}
                    contentBuilder={() => {
                        return (
                            <View style={[ABCStyles.rowAlignCenter, { justifyContent: "flex-end" }]}>
                                <View
                                    style={[
                                        {
                                            width: pxToDp(172),
                                            backgroundColor: Colors.whiteSmoke,
                                            justifyContent: "center",
                                            alignItems: "center",
                                            borderRadius: Sizes.dp4,
                                            height: Sizes.dp38,
                                        },
                                        showErrorHint && !timeStr ? { backgroundColor: Colors.Y4 } : {},
                                    ]}
                                >
                                    <View style={ABCStyles.rowAlignCenter}>
                                        <Text style={[TextStyles.t16NT1.copyWith({ color: timeStrColor })]}>
                                            {!!timeStr ? timeStr : "无可用号源"}
                                        </Text>
                                        {isShowAdditionFlag && (
                                            <AssetImageView
                                                name={"plus_tag"}
                                                style={{ width: Sizes.dp16, height: Sizes.dp16, marginLeft: Sizes.dp2 }}
                                            />
                                        )}
                                    </View>
                                </View>
                            </View>
                        );
                    }}
                    titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                    leftAlignment={{ alignSelf: undefined }}
                />
            </View>
        );
    }

    private _renderVisitSource(): JSX.Element {
        const state = this.bloc.currentState,
            clinicFieldConfig = state.clinicFieldConfig,
            showErrorHint = state.showErrorHint,
            detail = state.detail;
        // 是否禁用就诊推荐修改
        // 有挂号单并且不可以修改就诊推荐或者配置的只读都需要禁用就诊推荐修改
        // const isDisableDoctorRemmend = !!this.props?.id && !state.canModifyRecommendation;
        //判断是否可编辑(类型、科室、医生、初/复诊、时间、备注、就诊来源)
        //待诊、待签、已签均可编辑；已诊只可编辑初/复诊；已退均不可编辑
        const isEditing = this.checkDetailEditStatus;
        const _baseConfig = {
            itemStyle: isEditing ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal,
            contentStyle: [ABCStyles.rowAlignCenter, { paddingVertical: Sizes.dp16, justifyContent: "flex-end" }],
            titleStyle: TextStyles.t16NT2.copyWith({ color: Colors.t2 }),
            leftAlignment: { alignSelf: undefined },
        };
        const recommendConfig = clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "registration", type: "create", field: "recommend" });
        const remarkConfig = clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "registration", type: "create", field: "remark" });

        return (
            <View style={{ backgroundColor: Colors.white }}>
                <ListSettingItem
                    {..._baseConfig}
                    showErrorBorder={showErrorHint && !!recommendConfig?.required && !state.detail?.__visitSourceDisplayName}
                    title={"本次推荐"}
                    style={{ marginHorizontal: Sizes.listHorizontalMargin }}
                    contentHint={"未指定"}
                    content={!!state.detail?.__visitSourceDisplayName ? state.detail?.__visitSourceDisplayName : ""}
                    bottomLine={true}
                    onClick={() => {
                        if (isEditing) this.bloc.requestModifyRegistrationVisitSource();
                    }}
                />
                <ListSettingItem
                    {..._baseConfig}
                    showErrorBorder={showErrorHint && !!remarkConfig?.required && !state.detail?.visitSourceRemark}
                    title={"备注"}
                    style={{ marginHorizontal: Sizes.listHorizontalMargin }}
                    content={detail?.visitSourceRemark}
                    bottomLine={false}
                    onClick={() => {
                        if (isEditing) this.bloc.requestModifyRegistrationVisitSourceRemark(detail?.visitSourceRemark ?? "");
                    }}
                    contentNumberOfLine={1}
                />
            </View>
        );
    }

    private _renderChargeAbnormalTips(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail;
        const listConfig = [
            { show: !!detail?.chargeSheet?.isSheBaoAbnormal, isShebaoAbnormal: true },
            { show: !!detail?.chargeSheet?.isNotSheBaoAbnormal, isShebaoAbnormal: false },
        ];
        return (
            <>
                {listConfig
                    .filter((item) => item.show)
                    .map((item, index) => (
                        <ChargeAbnormalTips
                            key={index}
                            isShebaoAbnormal={item.isShebaoAbnormal}
                            onClick={() => {
                                this.bloc.requestHandleChargeAbnormal(item.isShebaoAbnormal);
                            }}
                        />
                    ))}
            </>
        );
    }

    private _renderPatientView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail,
            showErrorHint = state.showErrorHint;
        return (
            <AbcPatientCardInfoView
                showErrorHint={showErrorHint}
                // 不能修改患者的情况： 挂号过来的 || 已诊的 || 已收费的 || 网诊
                patientSwitchable={state.isCreate}
                patient={detail?.patient}
                diagnoseCount={state.diagnoseCount}
                onChange={(patient) => {
                    this.bloc.requestModifyRegistrationPatient(patient);
                }}
                isEditing={false}
                isCanSeePatientHistoryInRegister={state.canViewDiagnoseHistory}
                type={HistoryPermissionModuleType.registration}
                canSeePatientMobileInRegister={state.canSeePatientPhone}
            />
        );
    }
}
