import { ABCApiNetwork } from "../../../net";
import {
    DentistryClinicProduct,
    DentistryConfig,
    DentistryRemarkInfo,
    RegistrationDailyReserveStatusReq,
    RegistrationDailyReserveStatusRsp,
    RegistrationInfoModifyReq,
    RegistrationInfoModifyRsp,
    RegistrationLocalTimeCountReq,
    RegistrationLocalTimeCountRsp,
    RegistrationPreDiagnosisReq,
    RegistrationQuickListReq,
    RegistrationType,
} from "./bean";
import { GetRegistrationListRsp, RegistrationAgent, RegistrationDetailReq } from "../../data/registration-agent";
import { TimeUtils } from "../../../common-base-module/utils";
import { DEFAULT_DOCTOR_ID, RegistrationDetail } from "../../data/bean";
import { JsonProperty } from "../../../common-base-module/json-mapper/json-mapper";

//在doctorId不存在的情况下，默认为32个0
const defaultDoctorId = DEFAULT_DOCTOR_ID;
class QueryDailyReserveStatusByDoctorDepartmentRsp {
    @JsonProperty({ type: Array, clazz: RegistrationDailyReserveStatusRsp })
    rows?: RegistrationDailyReserveStatusRsp[];
}
export class DentistryAgent {
    /**
     *  查看预约设置
     * @param registrationType--- 预约类型
     */
    static _saveRegistrationConfig: Map<RegistrationType, DentistryConfig> = new Map<RegistrationType, DentistryConfig>();
    static async queryDentistryRegistrationConfig(
        registrationType = RegistrationType.outpatientRegistration,
        cache = true
    ): Promise<DentistryConfig | undefined> {
        if (cache && !!this._saveRegistrationConfig.get(registrationType)) {
            return this._saveRegistrationConfig.get(registrationType);
        }
        return await ABCApiNetwork.get(`registrations/config`, {
            queryParameters: { registrationType },
            clazz: DentistryConfig,
        }).then((rsp) => {
            this._saveRegistrationConfig.set(registrationType, rsp);
            return rsp;
        });
    }

    /**
     * 获取诊所可预约项目
     * @param params
     */
    static async getDentistryClinicProduct(params: {
        employeeId?: string; //医生id
        keywords: string;
        registrationType?: number;
    }): Promise<DentistryClinicProduct[]> {
        const { employeeId = defaultDoctorId, ...others } = params;
        const rsp: { rows: DentistryClinicProduct[] } = await ABCApiNetwork.get("registrations/product/clinic", {
            queryParameters: {
                employeeId,
                ...others,
            },
        });
        return rsp?.rows || [];
    }

    /**
     * 查询门店对应备注模板数据
     * @param registrationType
     */
    static async queryDentistryRemarkTemplates(registrationType?: RegistrationType): Promise<DentistryRemarkInfo[]> {
        const rsp: { rows: DentistryRemarkInfo[] } = await ABCApiNetwork.get("registrations/remark-templates", {
            queryParameters: { registrationType },
        });
        return rsp?.rows ?? [];
    }

    /**
     *  挂号列表视图
     * @param params
     */
    static async queryQuickList(params: RegistrationQuickListReq): Promise<GetRegistrationListRsp> {
        const { date, displayStatus, registrationType, end, offset, ...others } = params;
        const _end = end ?? date;
        return ABCApiNetwork.get("registrations/query/quick-list", {
            queryParameters: {
                offset: offset ?? 0,
                limit: 20,
                date: date ? TimeUtils.formatDate(date) : undefined,
                registrationType: registrationType,
                displayStatus: displayStatus && displayStatus != -1 ? displayStatus : "",
                start: date ? TimeUtils.formatDate(date) : undefined,
                end: _end ? TimeUtils.formatDate(_end) : undefined,
                ...others,
            },
            clazz: GetRegistrationListRsp,
            clearUndefined: true,
        });
    }

    /**
     * 查询指定医生在指定科室下每天每15分钟已存在挂号数（灵活预约模式）
     * @param params
     */
    static async queryLocalTimeCountByDoctorDepartment(params: RegistrationLocalTimeCountReq): Promise<RegistrationLocalTimeCountRsp[]> {
        const { doctorId = defaultDoctorId, reserveDate, ...others } = params;
        const rsp: { rows: RegistrationLocalTimeCountRsp[] } = await ABCApiNetwork.get(
            `registrations/query/doctor/${doctorId}/department/local-time-count`,
            {
                queryParameters: {
                    reserveDate: reserveDate ? TimeUtils.formatDate(reserveDate) : undefined,
                    ...others,
                },
            }
        );
        return rsp?.rows ?? [];
    }

    /**
     * 查询医生指定科室日期范围内每日号源状态集合(固定号源模式)
     * @param params
     */
    static async queryDailyReserveStatusByDoctorDepartment(
        params: RegistrationDailyReserveStatusReq
    ): Promise<RegistrationDailyReserveStatusRsp[]> {
        const { doctorId = defaultDoctorId, departmentId, registrationType, start, end, isRevisited } = params;
        const rsp = await ABCApiNetwork.get(`registrations/query/doctor/${doctorId}/department/daily-reserve-status`, {
            queryParameters: {
                departmentId,
                doctorId,
                start: start ? TimeUtils.formatDate(start) : undefined,
                end: end ? TimeUtils.formatDate(end) : undefined,
                registrationType,
                isRevisited,
            },
            clazz: QueryDailyReserveStatusByDoctorDepartmentRsp,
        });
        return rsp?.rows ?? [];
    }

    /**
     * 新增预约挂号入口
     * @param data
     */
    static async saveRegistrationManage(data: RegistrationDetailReq): Promise<RegistrationDetail> {
        const { registrationFormItem, ...others } = data;
        const { reserveDate, doctorId, ...othersReg } = registrationFormItem!;
        return ABCApiNetwork.post("registrations/manage", {
            body: {
                registrationFormItem: {
                    reserveDate: TimeUtils.formatDate(reserveDate),
                    doctorId: doctorId || "00000000000000000000000000000000",
                    ...othersReg,
                },
                ...others,
            },
            clazz: RegistrationDetail,
        }).then((rsp) => {
            RegistrationAgent.changeObserver.next(rsp);
            return rsp;
        });
    }

    /**
     * 修改预约信息
     * @param registrationSheetId
     */
    static async modifyRegistrationManage(
        registrationSheetId: string,
        data: RegistrationInfoModifyReq
    ): Promise<RegistrationInfoModifyRsp> {
        const { registrationFormItem, ...others } = data;
        const { reserveDate, ...othersReg } = registrationFormItem!;
        return ABCApiNetwork.put(`registrations/manage/${registrationSheetId}`, {
            body: {
                registrationFormItem: {
                    reserveDate: TimeUtils.formatDate(reserveDate),
                    ...othersReg,
                },
                ...others,
            },
            clazz: RegistrationInfoModifyRsp,
        }).then((rsp) => {
            if (rsp?.isSuccess == 1) {
                RegistrationAgent.changeObserver.next();
            }
            return rsp;
        });
    }

    /**
     *  PC端修改预诊信息
     * @param registrationSheetId
     * @param data
     */
    static async modifyRegistrationPreDiagnosis(
        registrationSheetId?: string,
        data?: RegistrationPreDiagnosisReq
    ): Promise<RegistrationInfoModifyRsp> {
        return ABCApiNetwork.put(`registrations/manage/${registrationSheetId}/pre-diagnosis`, {
            body: data,
            clazz: RegistrationInfoModifyRsp,
        });
    }

    /**
     *PC端预约签到
     * @param registrationSheetId
     */
    static async completeSignInRegistration(registrationSheetId?: string): Promise<RegistrationDetail> {
        return ABCApiNetwork.put(`registrations/manage/${registrationSheetId}/signin`, {
            clazz: RegistrationDetail,
        }).then((rsp) => {
            RegistrationAgent.changeObserver.next();
            return rsp;
        });
    }

    /**
     * 获取用户排班最小服务时长
     * @param params
     */
    static async queryEmployeeServiceMinMinutes(params: {
        registrationType: number;
        workingDate?: Date;
        departmentId?: string;
        employeeId?: string;
    }): Promise<{ serviceMinMinutes?: number }> {
        const { employeeId = defaultDoctorId, workingDate, ...others } = params;
        return ABCApiNetwork.get("registrations/schedule/employee/service-min-minutes", {
            queryParameters: {
                workingDate: TimeUtils.formatDate(workingDate),
                employeeId,
                ...others,
            },
        });
    }

    /**
     * 根据门店id校验是否存在有效的预约
     */
    static async getExistAvailableTherapyRegistration(registrationType?: number): Promise<boolean> {
        return ABCApiNetwork.get<{ isYes: 1 }>("registrations/query/clinic/exist-available", {
            queryParameters: { registrationType },
        }).then((rsp) => rsp.isYes == 1);
    }

    /**
     * 修改预约状态
     * @param id
     * @param status
     */
    static async putManageRegistrationStatus(id: string, status: number): Promise<RegistrationDetail> {
        return ABCApiNetwork.put(`registrations/manage/${id}/status-v2`, {
            body: {
                targetStatus: status,
            },
            clazz: RegistrationDetail,
        });
    }
}
