/**
 * create by deng<PERSON>e
 * desc:
 * create date 2021/1/6
 */
import React from "react";
import { SizedBox } from "../../base-ui";
import { CommonFilterId } from "../../base-ui/searchBar/search-bar";
import { FilterItem, Filters } from "../../base-ui/searchBar/search-bar-bean";
import { AbcMap } from "../../base-ui/utils/abc-map";
import { Text } from "@hippy/react";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import IconFontView from "../../base-ui/iconfont/iconfont-view";
import { ABCNavigator, TransitionType } from "../../base-ui/views/abc-navigator";
import { DentistryListView } from "./dentistry-list-view";
import { RegistrationStatus, RegistrationTabType } from "../data/bean";
import { DentistrySearchPage } from "./dentistry-search-page";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import { TimeUtils } from "../../common-base-module/utils";
import { SearchBarWithDropDownFilter } from "../../base-ui/searchBar/filter-view";
import { TherapyStatDetail, TherapyStatus } from "./appointment/data/appointment-bean";
import { runFuncBeforeCheckExpired } from "../../views/clinic-edition";
import { AbcButton } from "../../base-ui/views/abc-button";
import { ClinicDoctorInfo } from "../../base-business/data/beans";
import { ClinicAgent } from "../../base-business/data/clinic-agent";
import { ClinicEmployee, userCenter } from "../../user-center/user-center";
import { AnyType } from "../../common-base-module/common-types";
import { RegistrationAgent, RegistrationsDoctorListItem } from "../data/registration-agent";
import { PatientApplyAuditButton } from "../views/patient-apply-audit/patient-apply-audit-list";
import { delayed } from "../../common-base-module/rxjs-ext/rxjs-ext";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import { employeeSharedPreferences } from "../../base-business/preferences/scoped-shared-preferences";
import { FilterModifyState } from "../registration-list-view-bloc";
import { DentistryAgent } from "./data/dentistry-agent";
import { AbcModuleTabPage, ModuleTabItem } from "../../views/base-tab-page";

interface DentistryTabPageProps {
    tabIndex?: number;
    doctorId?: string;
    startTime?: Date;
    endTime?: Date;
}

interface DentistryTabPageStates {}

const employeeSharedPreferencesKanbanMode = "__employeeSharedPreferencesKanbanModeKey";

export class DentistryTabPage extends AbcModuleTabPage<DentistryTabPageProps, DentistryTabPageStates, DentistryListView> {
    static defaultProps = {
        tabIndex: 0,
    };
    tabsFilterInfo: AbcMap<RegistrationTabType, Filters> = new AbcMap<RegistrationTabType, Filters>();
    tabsViewProps = {
        lazy: false,
        scrollEnabled: false,
    };
    canFilterEmployeeIds: string[] = [];
    canFilterEmployees: ClinicDoctorInfo[] = [];
    private __doctorId?: string;
    private __startTime?: Date;
    private __endTime?: Date;
    private __firstInit = true;
    private enableAppointment = 0; // 理疗预约开关 1为开启 0关闭，（患者可预约理疗师或理疗项目）
    private isCanModifyRegistration = true; // 修改、取消挂号预约开关
    private readonly hasKanbanMode: Map<RegistrationTabType, boolean> = new Map<RegistrationTabType, boolean>([
        [RegistrationTabType.registration, false],
        [RegistrationTabType.appointment, false],
    ]); //默认为列表，图标显示为看板图标

    constructor(props: DentistryTabPageProps) {
        super(props);

        //获取看板缓存数据
        const modeStr = employeeSharedPreferences.getString(employeeSharedPreferencesKanbanMode);
        if (!!modeStr) {
            const _modeStr = JSON.parse(modeStr);
            this.hasKanbanMode = new Map<RegistrationTabType, boolean>(_modeStr);
        }

        this.currentTabIndex = props?.tabIndex ?? 0;
        this.tabsFilterInfo.set(RegistrationTabType.registration, _RegistrationTabPageFilters());
        this.tabsFilterInfo.set(RegistrationTabType.appointment, _TherapyTabPageFilters());

        this.__doctorId = props.doctorId;
        this.__startTime = props.startTime;
        this.__endTime = props.endTime;
        if ((this.__startTime?.getTime() ?? 0) > (this.__endTime?.getTime() ?? 0)) {
            this.__endTime = this.__startTime;
        }

        this.getQueryUserCenterDoctorList()
            .then(() => {
                this.modifyDoctorFilterGroup();
            })
            .catch(() => {
                this.modifyDoctorFilterGroup();
            })
            .finally(() => {
                this.__firstInit = false;
            });
    }

    pageName(): string | undefined {
        return "挂号预约列表";
    }

    get needFilterDoctor(): boolean {
        return !!this.props.doctorId;
    }

    get needFilterTime(): boolean {
        return !!this.props.startTime;
    }

    componentDidMount(): void {
        if (this.currentTabIndex == RegistrationTabType.registration) {
            //更新医生选中项目
            RegistrationAgent.changeObserver
                .subscribe(() => {
                    const blocState = this.listViewRef?.bloc.currentState;
                    this.modifyDoctorFilterGroup(
                        blocState?.queryParams?.date,
                        blocState?.doctorListRsp?.rows,
                        blocState?.queryParams.doctorId
                    );
                })
                .addToDisposableBag(this);
        }
        DentistryAgent.queryDentistryRegistrationConfig(0).then((rsp) => {
            if (rsp?.payMode == 1) {
                this.tabsFilterInfo.get(RegistrationTabType.registration)?.filters[1].filters?.push(
                    JsonMapper.deserialize(FilterItem, {
                        title: "待收",
                        id: CommonFilterId.allStatus + RegistrationStatus.waitPay,
                        exclusive: true,
                    })
                );
            }
        });
        // 是否启用理疗预约功能
        DentistryAgent.queryDentistryRegistrationConfig(1)
            .then((rsp) => {
                this.enableAppointment = rsp?.isOpen ?? 0;
                if (rsp?.payMode == 1) {
                    this.tabsFilterInfo.get(RegistrationTabType.appointment)?.filters[1].filters?.push(
                        JsonMapper.deserialize(FilterItem, {
                            title: "待收",
                            id: CommonFilterId.allStatus + RegistrationStatus.waitPay,
                            exclusive: true,
                        })
                    );
                }
                this.setState({});
            })
            .then(() => {
                if (DeviceUtils.isAndroid()) {
                    delayed(70)
                        .subscribe(() => {
                            this.tabPageRef?.setPage(this.props.tabIndex ?? 0, false);
                            this.setState({});
                        })
                        .addToDisposableBag(this);
                }
            })
            .catchIgnore();

        delayed(2000)
            .subscribe(() => {
                [...this.listViewRefs.values()].map((item) => {
                    item.bloc.state
                        .subscribe((state) => {
                            if (state instanceof FilterModifyState) {
                                //更新筛选条件
                                this.modifyDoctorFilterGroup(state.queryParams.date, state.doctorListRsp?.rows, state.queryParams.doctorId);
                            }
                        })
                        .addToDisposableBag(this);
                });
            })
            .addToDisposableBag(this);
        // 获取挂号预约（修改、取消挂号预约）数据权限
        ClinicAgent.getEmployeesMeConfig()
            .then((rsp) => {
                this.isCanModifyRegistration = !!rsp?.employeeDataPermission?.registration?.isCanSeeModifyRegistration;
                this.setState({});
            })
            .catchIgnore();
    }

    /**
     * 获取当前员支持的医生
     */
    async getQueryUserCenterDoctorList(): Promise<void> {
        if (!this.needFilterTime && !this.needFilterDoctor) return;

        if (!this.needFilterDoctor) {
            this.listViewRef?.bloc.requestModifyDoctorParams({
                doctorId: this.__doctorId ?? "",
                startTime: this.__startTime,
                endTime: this.__endTime,
            });
        } else {
            await Promise.all([RegistrationAgent.getClinicAllDoctorRegFeeList(), ClinicAgent.getClinicAllDoctorsRegfee()]).then(
                (allRsp) => {
                    const DoctorsRegFeeList = allRsp[0];
                    const employee: ClinicEmployee = userCenter.employee ?? {};
                    const canUseDoctor = allRsp[1];
                    /**
                     * 拉取所有的医生列表
                     * 医助角色：拉取医助列表
                     * 非医助不需要拉取，只需要对比当前id
                     */
                    DoctorsRegFeeList?.employeeRegistrationFees
                        ?.filter(
                            (it) =>
                                (userCenter.clinic?.isDoctorAssist &&
                                    !!canUseDoctor?.find((itt) => itt.doctorId == it.doctorId && itt.departmentId == it.departmentId)) ||
                                (userCenter.clinic?.isDoctor && it.doctorId == employee.id)
                        )
                        .forEach((doctor) => {
                            this.canFilterEmployeeIds.push(doctor.doctorId ?? "");
                            this.canFilterEmployees.push(doctor);
                        });
                    if (!!this.canFilterEmployeeIds.length && !this.canFilterEmployeeIds.includes(this.props.doctorId ?? "")) {
                        this.__doctorId = this.canFilterEmployeeIds[0];
                    }

                    this.listViewRef?.bloc.requestModifyDoctorParams({
                        doctorId: this.__doctorId ?? "",
                        startTime: this.__startTime,
                        endTime: this.__endTime,
                    });
                }
            );
        }
    }

    /**
     * 显示导航栏全部医师列表 （医生/理疗师）
     * @param data
     * @param _doctorList
     * @param doctorId
     */
    async modifyDoctorFilterGroup(data?: Date, _doctorList?: RegistrationsDoctorListItem[], doctorId?: string): Promise<void> {
        let doctorList: RegistrationsDoctorListItem[] | TherapyStatDetail[] | undefined = [];
        const _lastFilterDoctorInfo = _doctorList?.find((item) => item.id == doctorId);

        if (this.needFilterDoctor) {
            this.canFilterEmployees.map((item) => {
                //@ts-ignore
                if (doctorList?.find?.((doctor: AnyType) => item.doctorId == doctor.id ?? doctor.doctorId)) return;
                doctorList?.push(
                    JsonMapper.deserialize(RegistrationsDoctorListItem, {
                        id: item.doctorId,
                        doctorId: item.doctorId,
                        name: item.doctorName,
                        doctorName: item.doctorName,
                    })
                );
            });
        } else {
            if (this.currentTabIndex == RegistrationTabType.appointment) {
                doctorList = await RegistrationAgent.getRegistrationsDoctorList(data, this.currentTabIndex)
                    .then((rsp) => rsp.rows)
                    .catch(() => []);
            } else if (this.currentTabIndex == RegistrationTabType.registration) {
                doctorList =
                    _doctorList ??
                    (await RegistrationAgent.getRegistrationsDoctorList(data, this.currentTabIndex)
                        .then((rsp) => rsp.rows)
                        .catch(() => []));
            }
        }

        const tabFilter = this.tabsFilterInfo.get(this.currentTabIndex);
        const doctorFilter = tabFilter!.filters?.[2];

        doctorFilter.filters = [];
        if (this.needFilterDoctor) {
            //@ts-ignore
            doctorList = doctorList?.filter((item: any) => {
                return this.canFilterEmployeeIds.find((employeeId) => employeeId == item?.doctorId || employeeId == item?.id);
            });
        } else {
            doctorFilter.filters = [
                JsonMapper.deserialize(FilterItem, {
                    title: this.currentTabIndex == RegistrationTabType.appointment ? "全部理疗师" : "全部医生",
                    id: CommonFilterId.others,
                    isDefault: true,
                    select: !_lastFilterDoctorInfo,
                    exclusive: true,
                    defaultTitle: this.currentTabIndex == RegistrationTabType.appointment ? "理疗师" : "医生",
                }),
            ];
        }
        /**
         * 显示导航栏指定的理疗师的预约患者列表（指定理疗师）
         */
        doctorList?.forEach((item: RegistrationsDoctorListItem | TherapyStatDetail | undefined, index: number) => {
            if (item instanceof RegistrationsDoctorListItem) {
                doctorFilter.filters?.push(
                    JsonMapper.deserialize(FilterItem, {
                        title: item.name,
                        id: CommonFilterId.others + index + 1,
                        exclusive: true,
                        info: item,
                        select: item.id == (doctorId || this.__doctorId),
                        isDefault: item.id == this.__doctorId,
                    })
                );
            } else if (item instanceof TherapyStatDetail) {
                doctorFilter.filters?.push(
                    JsonMapper.deserialize(FilterItem, {
                        title: item.doctorName,
                        id: CommonFilterId.others + index + 1,
                        exclusive: true,
                        info: item,
                        select: item.doctorId == (doctorId || this.__doctorId),
                        isDefault: item.doctorId == this.__doctorId,
                    })
                );
            }
        });
        //处理筛选医生保存问题
        if (!!_lastFilterDoctorInfo) {
            doctorFilter.filters.forEach((item) => {
                if (item.info?.id == _lastFilterDoctorInfo.id) {
                    item.select = true;
                }
            });
        }

        tabFilter!.filters![2] = doctorFilter;

        // 更新日历时间
        if (!!data) {
            const str = data?.format("MM-dd");
            const calendarDate = data; // 预约日报默认选中开始时间
            if (!!str) {
                const isToday = TimeUtils.isToday(data || new Date());
                tabFilter!.filters![0].filters![0].title = isToday ? "今天" : str;
                tabFilter!.filters![0].filters!.map((item) => {
                    if (item.id == 100014) {
                        item.date = calendarDate;
                        item.title = str;
                    }
                }); // 将外部时间传入日历组件
            }
        }

        if (this.__firstInit) {
            const str = this.__startTime?.format("MM-dd");
            const calendarDate = this.__startTime; // 预约日报默认选中开始时间
            if (!!str) {
                const isToday = TimeUtils.isToday(this.__startTime || new Date());
                tabFilter!.filters![0].filters![0].title = isToday ? "今天" : str;
                tabFilter!.filters![0].filters!.map((item) => {
                    if (item.id == 100014) {
                        item.date = calendarDate;
                        item.title = str;
                    }
                }); // 将外部时间传入日历组件
            }
        }
        this.forceUpdate();
    }

    @runFuncBeforeCheckExpired()
    createRegistrationInvoice(): void {
        this.listViewRef?.navToAddInvoiceDetail();
    }

    //切换模式（看板/列表）
    private _switchMode(value: boolean): void {
        this.hasKanbanMode.set(this.currentTabIndex, !value);
        employeeSharedPreferences.setString(employeeSharedPreferencesKanbanMode, JSON.stringify([...this.hasKanbanMode]));
        this.listViewRef?.changeViewMode(this.hasKanbanMode.get(this.currentTabIndex) ?? false);
        this.setState({});
    }

    /**
     * 页面搜索导航栏
     * @param tabIndex
     * @private
     */
    private _renderSearchFilterView(tabIndex: number): JSX.Element {
        return (
            <SearchBarWithDropDownFilter
                placeholder={"搜索患者"}
                filters={this.tabsFilterInfo.get(tabIndex)}
                onChange={() => {
                    ABCNavigator.navigateToPage(<DentistrySearchPage type={this.currentTabIndex} />, {
                        transitionType: TransitionType.inFromBottom,
                    }).then();
                }}
                onFilterChange={(filters) => {
                    this.listViewRef?.changeFilter(filters);
                }}
                hasKanbanMode={this.hasKanbanMode.get(tabIndex)}
                onSwitchMode={(value) => {
                    this._switchMode(value);
                }}
                style={{ backgroundColor: Colors.panelBg }}
            />
        );
    }

    handleChangeTab(index: number): void {
        super.handleChangeTab(index);
        this.modifyDoctorFilterGroup();
    }

    protected renderRightSuffix(): JSX.Element {
        const isDentistryClinic = !!userCenter.clinic?.isDentistryClinic;
        if (!this.isCanModifyRegistration) return <SizedBox width={Sizes.dp63} height={Sizes.dp30} />; // 无修改、取消挂号数据权限不展示按钮
        return (
            <AbcButton
                style={[
                    ABCStyles.rowAlignCenter,
                    Sizes.paddingLTRB(Sizes.dp10, Sizes.dp5, Sizes.dp12, Sizes.dp5),
                    {
                        width: Sizes.dp63,
                        height: Sizes.dp30,
                        borderRadius: Sizes.dp4,
                        marginRight: Sizes.dp16,
                        backgroundColor: Colors.mainColor,
                    },
                ]}
                key={"registrationAddProject"}
                onClick={() => {
                    this.createRegistrationInvoice();
                }}
                pressColor={Colors.mainColorPress}
            >
                <IconFontView style={{ paddingTop: Sizes.dp1 }} name={"add"} color={Colors.S2} size={Sizes.dp9} />
                <SizedBox width={Sizes.dp4} />
                <Text style={[TextStyles.t14MS2, ABCStyles.rowAlignCenter, { lineHeight: Sizes.dp20 }]} numberOfLines={1}>
                    {this.currentTabIndex == RegistrationTabType.registration && !isDentistryClinic ? "挂号" : "预约"}
                </Text>
            </AbcButton>
        );
    }

    protected renderLeftSuffix(): JSX.Element {
        return super.renderLeftSuffix(!DeviceUtils.isOhos());
    }

    protected createModuleTabList(): ModuleTabItem[] {
        const isDentistryClinic = !!userCenter.clinic?.isDentistryClinic;
        const list = [
            {
                id: RegistrationTabType.registration,
                title: !!this.__doctorId ? "我的预约" : isDentistryClinic ? "预约" : "门诊挂号",
            },
        ];
        if (this.enableAppointment == 1) {
            list.push({
                id: RegistrationTabType.appointment,
                title: "理疗预约",
            });
        }
        return list.map(
            (moduleItem) =>
                ({
                    ...moduleItem,
                    contentRender: (item) => (
                        <>
                            {this._renderSearchFilterView(item.id)}
                            <DentistryListView
                                ref={(ref) => {
                                    if (ref) this.listViewRefs.set(item.id, ref);
                                }}
                                type={item.id}
                                viewMode={this.hasKanbanMode.get(item.id)}
                            />
                            {!this.needFilterDoctor && <PatientApplyAuditButton />}
                        </>
                    ),
                } as ModuleTabItem)
        );
    }
}

const TabPageTimeFilters = {
    id: "time",
    useCalendar: true,
    filters: [
        {
            title: "今天",
            id: CommonFilterId.timeToday,
            isDefault: true,
            select: true,
            exclusive: true,
            time: new Date(),
        },
        {
            title: "日历",
            id: CommonFilterId.timeWithCalendar,
            exclusive: true,
        },
    ],
};
const TabPageStatusFilters = {
    id: "status",
    filters: [
        {
            title: "全部状态",
            id: userCenter.isAllowedRegUpgrade ? CommonFilterId.allStatus : CommonFilterId.allStatus + TherapyStatus.all,
            isDefault: true,
            select: true,
            exclusive: true,
            defaultTitle: "状态",
        },
        {
            title: "待签",
            id: userCenter.isAllowedRegUpgrade
                ? CommonFilterId.allStatus + RegistrationStatus.waitSign
                : CommonFilterId.allStatus + TherapyStatus.waitingSignIn,
            exclusive: true,
        },
        {
            title: "已签",
            id: userCenter.isAllowedRegUpgrade
                ? CommonFilterId.allStatus + RegistrationStatus.signed
                : CommonFilterId.allStatus + TherapyStatus.signed,
            exclusive: true,
        },
        {
            title: "已退",
            id: userCenter.isAllowedRegUpgrade
                ? CommonFilterId.allStatus + RegistrationStatus.refunded
                : CommonFilterId.allStatus + TherapyStatus.canceled,
            exclusive: true,
        },
    ],
};

const _RegistrationTabPageFilters = () =>
    Filters.createFilters([
        TabPageTimeFilters,
        TabPageStatusFilters,
        {
            id: "doctor",
            filters: [
                {
                    title: "全部医生",
                    id: CommonFilterId.others,
                    isDefault: true,
                    select: true,
                    exclusive: true,
                    defaultTitle: "医生",
                },
            ],
        },
    ]);

const _TherapyTabPageFilters = () =>
    Filters.createFilters([
        TabPageTimeFilters,
        TabPageStatusFilters,
        {
            id: "doctor",
            filters: [
                {
                    title: "全部理疗师",
                    id: CommonFilterId.others,
                    isDefault: true,
                    select: true,
                    exclusive: true,
                    defaultTitle: "理疗师",
                },
            ],
        },
    ]);
