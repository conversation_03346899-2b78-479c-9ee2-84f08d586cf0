/**
 * create by deng<PERSON>e
 * desc:
 * create date 2021/1/14
 */

import { Bloc, BlocEvent } from "../bloc";
import React from "react";
import { EventName } from "../bloc/bloc";
import { of, Subject } from "rxjs";
import { debounce, switchMap } from "rxjs/operators";
import { DoctorShiftsListItem, RegistrationAgent } from "./data/registration-agent";
import { ABCError } from "../common-base-module/common-error";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import _ from "lodash";
import { BaseLoadingState } from "../bloc/bloc-helper";
import { delayed } from "../common-base-module/rxjs-ext/rxjs-ext";
import { DoctorShiftsWithTime, RegistrationFormItem } from "./data/bean";
import { DoctorShiftTimeDialog } from "./views/doctor-shift-time-dialog";
import { TimeUtils } from "../common-base-module/utils";
import { Range } from "../base-ui/utils/value-holder";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { RegistrationDoctorSearchPage, RegistrationDoctorSearchPageType } from "./doctor-search/registration-doctor-search-page";
import { RegistrationDoctorShiftPage } from "./registration-doctor-shift-page";
import { ClinicDoctorInfo } from "../base-business/data/beans";
import { Department, RegistrationDataProvider } from "./data/registration";
import { showOptionsBottomSheet } from "../base-ui/dialog/bottom_sheet";

export class State extends BaseLoadingState {
    timeListLoading = false;
    doctorShiftsList: DoctorShiftsListItem[] = [];

    registrationFormItem: RegistrationFormItem = new RegistrationFormItem();

    departments: Department[] = [];

    clone(): State {
        return Object.assign(new State(), this);
    }

    get doctorShiftsListByTime(): Map<Date, DoctorShiftsWithTime[]> {
        const doctorShiftsListByTimeList: Map<Date, DoctorShiftsWithTime[]> = new Map();
        this.doctorShiftsList?.forEach((doctorInfo) => {
            doctorInfo.shifts?.forEach((shift) => {
                const newData = JsonMapper.deserialize(
                    DoctorShiftsWithTime,
                    _.assign(
                        doctorInfo,
                        {
                            departmentId: this.registrationFormItem.departmentId,
                            departmentName: this.registrationFormItem.departmentName,
                        },
                        shift
                    )
                );
                if (shift.workingDate && new Date(shift.workingDate).getTime() > TimeUtils.getTodayStart().getTime()) {
                    if (doctorShiftsListByTimeList.has(shift.workingDate)) {
                        doctorShiftsListByTimeList.get(shift.workingDate)?.push(newData);
                        doctorShiftsListByTimeList.get(shift.workingDate)?.sort((item) => {
                            return item.restCountToday ? -1 : 1;
                        });
                        if (this.registrationFormItem.doctorId) {
                            doctorShiftsListByTimeList.get(shift.workingDate)?.sort((item) => {
                                return item.doctorId == this.registrationFormItem.doctorId ? -1 : 1;
                            });
                        }
                    } else {
                        doctorShiftsListByTimeList.set(shift.workingDate, [newData]);
                    }
                }
            });
        });
        return doctorShiftsListByTimeList;
    }
}

export class RegistrationAppointmentDoctorListPageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<RegistrationAppointmentDoctorListPageBloc | undefined>(undefined);

    private _loadDoctorShiftsList: Subject<number> = new Subject<number>();
    private _registrationFormItem: RegistrationFormItem | undefined;
    private firstLoad = true;

    static fromContext(context: RegistrationAppointmentDoctorListPageBloc): RegistrationAppointmentDoctorListPageBloc {
        return context;
    }

    constructor(params: { registrationFormItem?: RegistrationFormItem }) {
        super();
        this._registrationFormItem = params.registrationFormItem;
        this.innerState.registrationFormItem = params.registrationFormItem ?? new RegistrationFormItem();

        this.dispatch(new _EventInit()).then();
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventLoadMoreDoctorShift, this._mapEventLoadMoreDoctorShift);
        map.set(_EventCheckDoctor, this._mapEventCheckDoctor);
        map.set(_EventSearchDoctor, this._mapEventSearchDoctor);
        map.set(_EventShowCurrentDoctorShifts, this._mapEventShowCurrentDoctorShifts);
        map.set(_EventModifyDepartment, this._mapEventModifyDepartment);

        return map;
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    private async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        this.innerState.loading = true;
        this.update();
        this._loadDoctorShiftsList
            .pipe(
                debounce(() => {
                    if (this.innerState.doctorShiftsList?.length) {
                        return delayed(1000);
                    } else {
                        return of(0);
                    }
                }),
                switchMap(() => {
                    const startTime = _.findLast([...this.innerState.doctorShiftsListByTime.keys()]);
                    return RegistrationAgent.getDoctorShiftsList(
                        this._registrationFormItem?.departmentId,
                        startTime ? new Date(new Date(startTime).getTime() + 7 * 24 * 3600 * 1000) : undefined
                    )
                        .catch((e) => new ABCError(e))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (rsp instanceof ABCError) {
                } else {
                    rsp?.map((item) => {
                        const sameIdItem = this.innerState.doctorShiftsList?.find((doctorShifts) => item.doctorId == doctorShifts.doctorId);
                        if (sameIdItem) {
                            sameIdItem.shifts = sameIdItem.shifts?.concat(item.shifts ?? []);
                        } else {
                            this.innerState.doctorShiftsList?.push(item);
                        }
                    });
                    //置顶当前选中医生
                    if (this.innerState.registrationFormItem.doctorId) {
                        this.innerState.doctorShiftsList.sort((item) =>
                            item.doctorId == this.innerState.registrationFormItem.doctorId ? -1 : 1
                        );
                    }
                    if (this.firstLoad) {
                        this.firstLoad = false;
                        this._loadDoctorShiftsList.next();
                    } else {
                        this.innerState.loading = false;
                    }
                }
                this.innerState.timeListLoading = false;
                this.update();
            })
            .addToDisposableBag(this);
        this._loadDoctorShiftsList.next();

        RegistrationDataProvider.getDoctorList().then((rsp) => {
            this.innerState.departments = rsp;
        });
    }

    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    private async *_mapEventLoadMoreDoctorShift(): AsyncGenerator<State> {
        this.innerState.timeListLoading = true;
        this.update();
        this._loadDoctorShiftsList.next();
    }

    private async *_mapEventCheckDoctor(event: _EventCheckDoctor): AsyncGenerator<State> {
        const doctor = event.doctor;
        if (doctor.canReserve) {
            if (doctor.restCountToday) {
                //有号 选择时间
                const result = await DoctorShiftTimeDialog.show(doctor);
                if (result) {
                    this.innerState.registrationFormItem.doctorId = doctor.doctorId;
                    this.innerState.registrationFormItem.doctorName = doctor.doctorName;
                    this.innerState.registrationFormItem.reserveDate = doctor.workingDate;
                    this.innerState.registrationFormItem.reserveTime = new Range<string>(result.start, result.end);
                    this.innerState.registrationFormItem._reserveTime = new Range<string>(result._start, result._end);
                    this.innerState.registrationFormItem.orderNo = result.orderNo;
                    this.innerState.registrationFormItem.fee = doctor.registrationFee;
                    ABCNavigator.pop(this.innerState.registrationFormItem);
                }
            } else if (doctor.scheduleIntervals?.some((schedule) => !!schedule.list?.length)) {
                //无号 - 当前医生的其他号源信息
                this.dispatch(new _EventShowCurrentDoctorShifts(doctor.doctorId!, doctor.departmentId!));
            } else {
                // 未排班
                this.dispatch(new _EventShowCurrentDoctorShifts(doctor.doctorId!, doctor.departmentId!));
            }
        } else {
            //不可预约
        }
    }

    private _searchDoctor = false;

    private async *_mapEventSearchDoctor(/*event: _EventSearchDoctor*/): AsyncGenerator<State> {
        RegistrationDoctorSearchPage.show({
            popAnimation: false,
            type: RegistrationDoctorSearchPageType.registration,
            onClickItem: (info: ClinicDoctorInfo) => {
                if (info) {
                    this._searchDoctor = true;
                    this.dispatch(new _EventShowCurrentDoctorShifts(info.doctorId!, info.departmentId!, info));
                }
            },
        }).then();
    }

    private async *_mapEventShowCurrentDoctorShifts(event: _EventShowCurrentDoctorShifts): AsyncGenerator<State> {
        const result = await RegistrationDoctorShiftPage.show({
            doctorId: event.doctorId,
            departmentId: event.departmentId,
        });
        if (result) {
            const { doctor, result: shifts } = result;
            this.innerState.registrationFormItem.doctorId = doctor.doctorId;
            this.innerState.registrationFormItem.doctorName = doctor.doctorName;
            this.innerState.registrationFormItem.reserveDate = doctor.workingDate;
            this.innerState.registrationFormItem.reserveTime = new Range<string>(shifts.start, shifts.end);
            this.innerState.registrationFormItem.orderNo = shifts.orderNo;
            this.innerState.registrationFormItem.fee = doctor.registrationFee;
            if (event.doctorInfo) {
                this.innerState.registrationFormItem.departmentName = event.doctorInfo.departmentName;
                this.innerState.registrationFormItem.departmentId = event.doctorInfo.departmentId;
            }
            if (this._searchDoctor) {
                ABCNavigator.pop(this.innerState.registrationFormItem, false);
            }
            ABCNavigator.pop(this.innerState.registrationFormItem);
        }
        this._searchDoctor = false;
        this.update();
    }

    private async *_mapEventModifyDepartment(): AsyncGenerator<State> {
        const departmentList = this.innerState.departments;

        const initIndex = departmentList.findIndex((item) => item.departmentId == this.innerState.registrationFormItem.departmentId);

        const result = await showOptionsBottomSheet({
            title: "选择科室",
            options: departmentList.map((item) => item.departmentName ?? "其他"),
            initialSelectIndexes: initIndex > -1 ? new Set<number>([initIndex]) : undefined,
        });
        if (result && result.length) {
            this.innerState.registrationFormItem.departmentId = departmentList[result[0]].departmentId;
            this.innerState.registrationFormItem.departmentName = departmentList[result[0]].departmentName;

            this.innerState.loading = true;
            this.update();
            this.firstLoad = true;
            this.innerState.doctorShiftsList = [];
            this._loadDoctorShiftsList.next();
        }
    }

    public requestLoadMoreDoctorShift(): void {
        this.dispatch(new _EventLoadMoreDoctorShift());
    }

    public requestCheckDoctor(doctor: DoctorShiftsWithTime): void {
        this.dispatch(new _EventCheckDoctor(doctor));
    }

    public requestSearchDoctor(): void {
        this.dispatch(new _EventSearchDoctor());
    }

    public requestModifyDepartment(): void {
        this.dispatch(new _EventModifyDepartment());
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventLoadMoreDoctorShift extends _Event {}

class _EventCheckDoctor extends _Event {
    doctor: DoctorShiftsWithTime;

    constructor(doctor: DoctorShiftsWithTime) {
        super();
        this.doctor = doctor;
    }
}

class _EventSearchDoctor extends _Event {}

class _EventShowCurrentDoctorShifts extends _Event {
    doctorId: string;
    departmentId: string;
    doctorInfo?: ClinicDoctorInfo;

    constructor(doctorId: string, departmentId: string, doctorInfo?: ClinicDoctorInfo) {
        super();
        this.doctorId = doctorId;
        this.departmentId = departmentId;
        this.doctorInfo = doctorInfo;
    }
}

class _EventModifyDepartment extends _Event {}
