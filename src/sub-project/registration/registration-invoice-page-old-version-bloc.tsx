import { Bloc, BlocEvent } from "../bloc";
import React from "react";
import {
    DEFAULT_DOCTOR_ID,
    DefaultRegistrationTimeOptions,
    FocusItemKeys,
    OldReserveInfo,
    PayStatusV2,
    RegistrationDesignatedDoctorInfo,
    RegistrationDesignatedTime,
    RegistrationDesignatedTimeScheduleInterval,
    RegistrationDetail,
    RegistrationDiffForRevisitedType,
    RegistrationDoctorEnableCategories,
    RegistrationFormItem,
    RegistrationInvoiceType,
    RegistrationPageSourceType,
    RegistrationRevisitStatus,
    RegistrationsAppointmentConfig,
    RegistrationsAppointmentServiceType,
    RegistrationsDoctorFeeItemsRsp,
    RegistrationStatusV2,
    VisitSourceBaseInfo,
} from "./data/bean";
import { actionEvent, EventName } from "../bloc/bloc";
import { ClinicAgent, EmployeesMeConfig, RegAndTherapyConfig } from "../base-business/data/clinic-agent";
import { DataPermission, GetClinicBasicSetting, OnlinePropertyConfigProvider } from "../data/online-property-config-provder";
import { GetOutpatientRevisitStatusRsp, RegistrationAgent, RegistrationDetailReq } from "./data/registration-agent";
import { Department, Doctor, RegistrationDataProvider } from "./data/registration";
import { BaseLoadingState } from "../bloc/bloc-helper";
import { MedicalRecord, Patient, PatientSource, PatientsSourceTypesItem } from "../base-business/data/beans";
import {
    ChargeConfig,
    ChargeForm,
    ChargeFormItem,
    ChargeInvoiceDetailData,
    ChargePayData,
    ChargeSettlementExceptionItem,
    ChargeSourceFormType,
    ChargeStatus,
    CouponPromotion,
    PatientCardPromotion,
    PatientPointsInfo,
    PayMethod,
    Promotion,
} from "../charge/data/charge-beans";
import { userCenter } from "../user-center";
import { RegistrationDailyReserveStatusRsp, RegistrationReserveStatus, RegistrationType, ReserveStartTime } from "./dentistry/data/bean";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { DialogIndex, showConfirmDialog, showQueryDialog } from "../base-ui/dialog/dialog-builder";
import { errorSummary, errorToStr, TimeUtils } from "../common-base-module/utils";
import _ from "lodash";
import { AbnormalTransactionList, ChargeAgent } from "../charge/data/charge-agent";
import { ChargeRefundDialog } from "../charge/view/charge-refund-dialog";
import { showBottomPanel } from "../base-ui/abc-app-library/panel";
import { ChargeAbnormalDialog } from "../charge/view/charge-abnormal-dialog";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import abcI18Next from "../language/config";
import { ABCUtils } from "../base-ui/utils/utils";
import { TherapyAppointmentAgent } from "./appointment/data/therapy-appointment-agent";
import { ABCNavigator, TransitionType } from "../base-ui/views/abc-navigator";
import { BehaviorSubject, of, Subject } from "rxjs";
import { switchMap } from "rxjs/operators";
import { ChargeUtils } from "../charge/utils/charge-utils";
import { ABCError } from "../common-base-module/common-error";
import { Pair, Range } from "../base-ui/utils/value-holder";
import { showBottomSheet, showOptionsBottomSheet } from "../base-ui/dialog/bottom_sheet";
import { pxToDp } from "../base-ui/utils/ui-utils";
import { RegistrationAppointmentDoctorListPage } from "./registration-appointment-doctor-list-page";
import { Text, View } from "@hippy/react";
import { Toast } from "../base-ui/dialog/toast";
import { RegisterTimeDialog } from "./registration-time-dialog";
import { AbcMap } from "../base-ui/utils/abc-map";
import { SelectMemberInfo } from "../charge/view/promotion-card-view";
import { ChargeInvoiceRefundDialog } from "../charge/charge-invoice-refund-dialog";
import { URLProtocols } from "../url-dispatcher";
import { CrmAgent } from "../patients/data/crm-agent";
import { RegistrationUtils } from "./utils/registration-utils";
import { ChargePayPage } from "../charge/charge-pay-page";
import { OutpatientAgent } from "../outpatient/data/outpatient";
import { ExaminationsAgent } from "../data/examinations/examinations-agent";
import { LoadingDialog } from "../base-ui/dialog/loading-dialog";
import { DentistryAgent } from "./dentistry/data/dentistry-agent";
import { TherapySignInStatus } from "./appointment/data/appointment-bean";
import { RegistrationSuccessDialog, RegistrationSuccessDialogType } from "./views/registration-success-dialog";
import { PatientAgent } from "../base-business/data/patient-agent";
import { DiscountCardDialog } from "../views/discount-card-views/discount-card-dialog";
import { DentistryPreDiagnosisDialog } from "./dentistry/views/dentistry-pre-diagnosis-dialog";
import { RegistrationInvoiceDetailPage } from "./registration-invoice-detail-page";
import { AbcCalendar } from "../base-ui/abc-app-library/calendar/calendar-static";
import { FixedSourceTime } from "./dentistry/views/fixed-source-time";
import { PatientSourceTypeSelectDialog } from "../outpatient/patient-edit-page/patient-source-type-select-dialog";
import { DentistryRemarkInputPage } from "./dentistry/views/dentistry-remark-input-page";
import { onlineMessageManager } from "../base-business/msg/online-message-manager";
import { PatientOrderLockDetail, PatientOrderLockType } from "../base-business/data/patient-order/patient-order-bean";

class _Event extends BlocEvent {}
class _EventInit extends _Event {}
class _EventUpdate extends _Event {
    state?: State;
    constructor(state?: State) {
        super();
        this.state = state;
    }
}
class _EventModifyRegistrationPatient extends _Event {
    patient: Patient;
    constructor(patient: Patient) {
        super();
        this.patient = patient;
    }
}
class _EventHandleChargeAbnormal extends _Event {
    isShebaoAbnormal: boolean;
    constructor(isShebaoAbnormal: boolean) {
        super();
        this.isShebaoAbnormal = isShebaoAbnormal;
    }
}

class _EventAbnormalRefund extends _Event {
    abnormalMsg: AbnormalTransactionList;
    constructor(abnormalMsg: AbnormalTransactionList) {
        super();
        this.abnormalMsg = abnormalMsg;
    }
}
class _EventModifyRegistrationType extends _Event {
    type: number;
    constructor(type: number) {
        super();
        this.type = type;
    }
}
class _EventModifyRegistrationDepartment extends _Event {}
class _EventModifyRegistrationDoctor extends _Event {}
class _EventModifyRegistrationTime extends _Event {}
class _EventCalculateOrderNo extends _Event {}
class _EventModifyRegistrationFee extends _Event {
    fee: number; // 修改诊费
    constructor(fee: number) {
        super();
        this.fee = fee;
    }
}
class _EventModifyRevisitStatus extends _Event {}
class _EventUpdatePromotions extends _Event {
    selectPromotions?: number[];
    selectGiftPromotions?: number[];
    selectCouponPromotions? = new AbcMap<CouponPromotion, Pair<boolean, number>>();
    selectMemberPointPromotion?: PatientPointsInfo;
    constructor(
        promotions?: number[],
        giftPromotions?: number[],
        couponPromotions?: AbcMap<CouponPromotion, Pair<boolean, number>>,
        selectMemberPointPromotion?: PatientPointsInfo
    ) {
        super();
        this.selectPromotions = promotions;
        this.selectGiftPromotions = giftPromotions;
        this.selectCouponPromotions = couponPromotions;
        this.selectMemberPointPromotion = selectMemberPointPromotion;
    }
}
class _EventUpdateMemberCard extends _Event {
    member: SelectMemberInfo;
    constructor(member: SelectMemberInfo) {
        super();
        this.member = member;
    }
}
class _EventPatientCardPromotion extends _Event {
    patientCardPromotions: PatientCardPromotion[];
    constructor(patientCardPromotions: PatientCardPromotion[]) {
        super();
        this.patientCardPromotions = patientCardPromotions;
    }
}
class _EventModifyMedicalRecordCard extends _Event {
    medicalRecord: MedicalRecord;
    constructor(medicalRecord: MedicalRecord) {
        super();
        this.medicalRecord = medicalRecord;
    }
}
class _EventRefundedRegistrationFee extends _Event {}
class _EventRefundedRegistration extends _Event {}
class _EventRegistrationCharge extends _Event {}
class _EventBackDirectRegistration extends _Event {}
class _EventRegistrationSignIn extends _Event {}
class _EventFinishRegistration extends _Event {}
class _EventSaveRegistration extends _Event {
    successCallback?(): void;
    constructor(successCallback?: () => void) {
        super();
        this.successCallback = successCallback;
    }
}
class _EventUpdatePatient extends _Event {
    patient: Patient;
    constructor(patient: Patient) {
        super();
        this.patient = patient;
    }
}
class _EventDiscountDetail extends _Event {}
class _EventUpdateRegistrationInfo extends _Event {}
class _EventCompletePreDiagnosis extends _Event {}
class _EventModifyOrderNo extends _Event {}
class _EventModifyRegistrationVisitSource extends _Event {}

class _EventModifyRegistrationVisitSourceRemark extends _Event {
    remark: string;
    constructor(text: string) {
        super();
        this.remark = text;
    }
}
class _EventModifyFixedTimeRange extends _Event {}
class _EventCancelPayment extends _Event {}
class Utils {
    static filterRegistrationPromotions(detail: ChargeInvoiceDetailData): Promotion[] | undefined {
        let registrationPromotionsId: string | undefined = undefined;
        for (const chargeForm of detail.chargeForms ?? []) {
            if (chargeForm.sourceFormType == ChargeSourceFormType.registration) {
                registrationPromotionsId = chargeForm.chargeFormItems?.[0].id;
            }
        }
        const newPromotions: Promotion[] = [];
        detail.promotions?.forEach((item) => {
            const newItem = _.cloneDeep(item);
            newItem.discountPrice = item.discountPrice ?? 0;
            newItem.productItems = newItem.productItems?.filter((productItem) => productItem.id == registrationPromotionsId);
            newPromotions.push(newItem);
        });
        return newPromotions;
    }
}
export class State extends BaseLoadingState {
    type: RegistrationInvoiceType = RegistrationInvoiceType.create;
    pay = { fee: 0, memberId: "", receivable: 0 }; //本地应该收和实收结构
    detail?: RegistrationDetail;
    cloneDetail?: RegistrationDetail;
    employeeConfig?: RegAndTherapyConfig;
    visitSourceList?: PatientsSourceTypesItem[];
    //算费配置
    chargeConfig?: ChargeConfig;
    isOpenContinueDiagnoseWithoutReg?: boolean;
    isEnableEditRevisitStatus?: boolean;
    employeesMeConfig?: EmployeesMeConfig;
    departments?: Array<Department>;
    registrationAppointmentConfig?: RegistrationsAppointmentConfig;
    visitDuration?: ReserveStartTime = {
        //初诊就诊时长（1、未选择项目时，根据初复诊时长来定；2、选择了项目，则以选中的项目中最长的时间为准）
        hour: 0,
        min: 0,
    };
    reVisitDuration?: ReserveStartTime = {
        //复诊就诊时长（1、未选择项目时，根据初复诊时长来定；2、选择了项目，则以选中的项目中最长的时间为准）
        hour: 0,
        min: 0,
    };
    focusKey = "";
    showErrorHint = false;
    hasChange = false;
    _currentDepartmentDoctors: Array<Doctor> = [];
    currentSelectedDoctors?: Doctor;
    abnormalList?: AbnormalTransactionList[]; //非社保异常列表
    doctorFee?: RegistrationsDoctorFeeItemsRsp; // 医生诊费
    diagnoseCount?: number; //就诊历史个数
    discountTotalPrice?: number; //优惠明细价格
    registrationTimeRangeList?: RegistrationDesignatedTime; //号源时间段信息
    selectOrderNoTimeList?: RegistrationDesignatedTimeScheduleInterval[]; //当前对应的号源时间段列表
    isCalculateOrderNo = true; //是否是通过算号算出来的号数（用于请求的时候判断是否需要清空orderNo）
    isDesignTime = false; //是否是指定时间（挂号类型，选择号源的时候，是否需要调用排班接口）
    dailyReserveStatusList?: RegistrationDailyReserveStatusRsp[]; //医生指定科室日期范围内每日号源状态集合

    dataPermission?: DataPermission; // 数据权限

    clinicBasicSetting?: GetClinicBasicSetting;

    settlementExceptionList?: ChargeSettlementExceptionItem[]; // 结算异常列表
    // 医保退费异常
    get shebaoRefundException(): boolean {
        const abnormalList = this.settlementExceptionList?.filter((t) => t.payMode == PayMethod.payHealthCard);
        //   优先展示退费异常
        return !!abnormalList?.some((t) => t.shebaoRefundAbnormal);
    }

    get isAccuratePart(): boolean {
        return this.registrationAppointmentConfig?.serviceType == RegistrationsAppointmentServiceType.accurate;
    }
    get enableLeaveForMember(): boolean {
        return !!this.registrationAppointmentConfig?.enableLeaveForMember;
    }
    get enableLeaveForPC(): boolean {
        return !!this.registrationAppointmentConfig?.enableLeaveForPC;
    }

    get currentDepartmentDoctors(): Array<Doctor> | undefined {
        const bzdys = this._currentDepartmentDoctors?.find((f) => f.doctorName === "不指定医生");
        const notCheckDoctor = JsonMapper.deserialize(Doctor, {
            isScheduled: 1,
            ...bzdys,
            fee: this.pay.fee,
        });
        let list: Doctor[] = [];
        list = list.concat(
            !!bzdys ? [notCheckDoctor] : [],
            this._currentDepartmentDoctors?.filter((f) => f.doctorName !== "不指定医生")
        );
        return list;
    }

    set currentDepartmentDoctors(list: Array<Doctor> | undefined) {
        const { doctorId } = this.detail?.registrationFormItem ?? {};
        this._currentDepartmentDoctors = list ?? [];
        const isEnableNoneScheduleRegistration = 1;
        if (!isEnableNoneScheduleRegistration) {
            this._currentDepartmentDoctors = (list ?? []).filter((item) => item.isScheduled);
            if (!this._currentDepartmentDoctors.find((item) => item.doctorId == doctorId)) {
                this.detail!.registrationFormItem!.doctorId = undefined;
                this.detail!.registrationFormItem!.doctorName = undefined;
            }
        }
    }
    //能够查看患者手机号
    get canSeePatientPhone(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.registration?.isCanSeePatientMobile;
    }

    //能够修改支付方式
    get canSeeModifyPayMode(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.registration?.isCanSeeModifyPayMode;
    }

    //能够修改、取消挂号预约
    get canModifyRegistration(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.registration?.isCanSeeModifyRegistration;
    }

    //挂号预约能查看就诊历史
    get canViewDiagnoseHistory(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.registration?.medicalHistory;
    }
    get isCreate(): boolean {
        return this.type == RegistrationInvoiceType.create;
    }
    get chargeFromRegistration(): boolean {
        return this.detail?.chargeSheet?.type === 1;
    }
    get hasCharged(): boolean {
        const registrationFormItem = this.detail?.registrationFormItem;
        return !(
            (registrationFormItem?.payStatusV2 === PayStatusV2.notPaid || registrationFormItem?.payStatusV2 === PayStatusV2.partedPaid) &&
            (registrationFormItem.statusV2 ?? 0) < RegistrationStatusV2.refunded &&
            this.chargeFromRegistration
        );
    }
    //是否部分收费
    get hasPartedPaid(): boolean {
        const registrationFormItem = this.detail?.registrationFormItem;
        return (
            registrationFormItem?.payStatusV2 === PayStatusV2.partedPaid &&
            (registrationFormItem.statusV2 ?? 0) < RegistrationStatusV2.refunded &&
            this.chargeFromRegistration
        );
    }
    get isHasEditCharge(): boolean {
        const registrationFormItem = this.detail?.registrationFormItem;
        return !(
            (registrationFormItem?.payStatusV2 === PayStatusV2.notPaid || registrationFormItem?.payStatusV2 === PayStatusV2.partedPaid) &&
            (registrationFormItem.statusV2 ?? 0) < RegistrationStatusV2.diagnosed &&
            this.chargeFromRegistration
        );
    }
    /**
     * @description 禁用修改初复诊
     */
    get disabledEditRevisitStatus(): boolean {
        // 连锁禁用了编辑
        if (!this.isEnableEditRevisitStatus) {
            return true;
        }
        return (this.detail?.registrationFormItem?.statusV2 ?? 0) >= RegistrationStatusV2.diagnosed;
    }
    // 允许修改就诊推荐的状态 true 可以 false 不可以
    // 【待签到，待付款未就诊，待就诊，未知】四种状态下允许修改就诊推荐 【完成诊断后的状态都不能修改】
    get canModifyRecommendation(): boolean {
        const registrationFormItem = this.detail?.registrationFormItem;
        return [
            RegistrationStatusV2.waitingSignIn,
            RegistrationStatusV2.waitingDiagnose,
            RegistrationStatusV2.waitingPay,
            RegistrationStatusV2.unknown,
        ].includes(registrationFormItem?.statusV2 ?? 999);
    }

    // 是否显示回诊按钮
    get canBackDirectRegistration(): boolean {
        // 状态为已诊+就诊时间是今天+开启回诊配置
        const { statusV2, reserveDate } = this.detail?.registrationFormItem ?? {};
        return (
            statusV2 == RegistrationStatusV2.diagnosed &&
            !!reserveDate &&
            TimeUtils.isToday(reserveDate!) &&
            !!this.isOpenContinueDiagnoseWithoutReg
        );
    }
    get medicalRecord(): MedicalRecord {
        const detail = this.detail;
        return JsonMapper.deserialize(MedicalRecord, {
            chiefComplaint: detail?.chiefComplaint,
            presentHistory: detail?.presentHistory,
            pastHistory: detail?.pastHistory,
            physicalExamination: detail?.physicalExamination,
            epidemiologicalHistory: detail?.epidemiologicalHistory,
        });
    }
    get disabledMR(): boolean {
        // 已退已诊不能修改患者历史
        return (this.detail?.registrationFormItem?.statusV2 ?? 0) >= RegistrationStatusV2.diagnosed;
    }
    //能否修改收费相关信息（禁用：退号 || 已收费 || 已退费）
    get disabledEditCharge(): boolean {
        const registrationFormItem = this.detail?.registrationFormItem;
        return (
            (registrationFormItem?.payStatusV2 ?? 0) > PayStatusV2.notPaid ||
            (registrationFormItem?.statusV2 ?? 0) >= RegistrationStatusV2.refunded ||
            (this.detail?.chargeSheet?.status ?? 0) > ChargeStatus.unCharged
        );
    }
    get shouldRegisteredBargain(): boolean {
        return !!this.chargeConfig?.reservationRegisteredBargainSwitch || !!userCenter.clinic?.isManager;
    }
    //口腔诊所预诊
    get dentistryMedicalRecord(): MedicalRecord {
        const detail = this.detail;
        return JsonMapper.deserialize(MedicalRecord, {
            chiefComplaint: detail?.chiefComplaint,
            presentHistory: detail?.presentHistory,
            pastHistory: detail?.pastHistory,
            epidemiologicalHistory: detail?.epidemiologicalHistory,
            dentistryExaminations: detail?.dentistryExaminations,
            physicalExamination: detail?.physicalExamination,
            preDiagnosisAttachments: detail?.preDiagnosisAttachments,
        });
    }

    outpatientRevisitStatusRsp?: GetOutpatientRevisitStatusRsp;
    doctorRegistrationCategories?: RegistrationDoctorEnableCategories;

    /**
     * 是短期内再次就诊挂号费不同模式
     */
    get isShortRevisitsDifferent(): boolean {
        return !!this.doctorRegistrationCategories?.registrationFees?.some(
            (fee) => fee?.isDiffForRevisited == RegistrationDiffForRevisitedType.shortRevisitsDifferent
        );
    }

    /**
     * 是否X天内首诊(反之为再诊)
     */
    isFirstRevisitWithinDays(options: { departmentId: string; doctorId: string }): boolean {
        const { departmentId, doctorId } = options;
        let effectiveDays = 0; // X天内在同一医生首次就诊/再次就诊的患者天数
        this.doctorRegistrationCategories?.registrationFees?.find((fee) => {
            if (fee.departmentId == departmentId && fee.doctorId == doctorId) {
                effectiveDays = fee.revisitedFeeCustomUseRule?.effectiveDays ?? 0;
            }
        });
        return !!this.outpatientRevisitStatusRsp?.isFirstRevisitWithinDays({
            reserveDate: this.detail?.registrationFormItem?.reserveDate,
            effectiveDays: effectiveDays,
        });
    }

    clone(): State {
        return Object.assign(new State(), this);
    }
}
export class ScrollToErrorViewState extends State {
    static fromState(state: State): ScrollToErrorViewState {
        const newState = new ScrollToErrorViewState();
        Object.assign(newState, state);
        return newState;
    }
}
export class RegistrationInvoicePageOldVersionBloc extends Bloc<_Event, State> {
    static Context = React.createContext<RegistrationInvoicePageOldVersionBloc | undefined>(undefined);
    static fromContext(context: RegistrationInvoicePageOldVersionBloc): RegistrationInvoicePageOldVersionBloc {
        return context;
    }
    private _doctorId?: string;
    private id?: string;
    private _oldReserveInfo?: OldReserveInfo;
    private _innerState!: State;
    private _source = RegistrationPageSourceType.normal;
    private _calculatePriceTrigger: Subject<boolean> = new Subject<boolean>();
    private _patientRevisitStatusTrigger: Subject<string> = new Subject<string>();
    private _registrationInvoiceDetailTrigger = new BehaviorSubject<{ id: string; isInit?: boolean }>({ id: "", isInit: false });
    private _getPatientBaseDetailTrigger: Subject<string> = new Subject<string>();
    private _getHistoryListTrigger = new Subject<number>(); // 就诊历史
    private _registrationDesignatedTimeTrigger: Subject<string> = new Subject<string>();
    private _registrationDailyReserveStatusTrigger: Subject<string> = new Subject<string>();
    constructor(options?: { id?: string; doctorId?: string; oldReserveInfo?: OldReserveInfo }) {
        super();
        this.id = options?.id;
        this._doctorId = options?.doctorId;
        this._oldReserveInfo = options?.oldReserveInfo;
        if (!!this.id) this.innerState.type = RegistrationInvoiceType.detail;
        this.dispatch(new _EventInit()).then();
    }
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();
        return this._innerState;
    }

    get isDetailPage(): boolean {
        return !!this.id;
    }

    initialState(): State {
        return this.innerState;
    }
    createEventHandlers(): Map<EventName, Function> {
        return new Map<EventName, Function>();
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    private async _initPageConfig(): Promise<void> {
        await Promise.all([
            ClinicAgent.getEmployeesMeConfig().catchIgnore(),
            OnlinePropertyConfigProvider.instance.getChargeConfig().catchIgnore(),
            OnlinePropertyConfigProvider.instance.getClinicBasicSetting().catchIgnore(),
            OnlinePropertyConfigProvider.instance.getChainBasicPropertyConfig().catchIgnore(),
            RegistrationAgent.getRegistrationReturnVisitFee({ doctorId: "", departmentId: "" }).catchIgnore(),
            RegistrationDataProvider.getDoctorList().catchIgnore(),
            this.isDetailPage ? RegistrationAgent.getClinicRegistrationAppointmentConfig().catchIgnore() : undefined,
            OnlinePropertyConfigProvider.instance.getClinicDataPermission().catchIgnore(),
        ]).then((rsp) => {
            const [
                employeesMeConfig,
                chargeConfig,
                clinicBasicSetting,
                chainBasePropertyConfig,
                doctorFeeConfig,
                departments,
                registrationAppointmentConfig,
                dataPermission,
            ] = rsp;
            //当前员工门店设置
            this.innerState.employeesMeConfig = employeesMeConfig;
            this.innerState.employeeConfig = this.innerState.employeesMeConfig?.clinicInfo?.config;
            //诊所收费设置
            this.innerState.chargeConfig = chargeConfig;
            //是否开启回诊
            this.innerState.isOpenContinueDiagnoseWithoutReg =
                clinicBasicSetting?.outpatient?.settings?.isOpenContinueDiagnoseWithoutReg == 1;
            //初复诊是否可编辑
            this.innerState.isEnableEditRevisitStatus = !!chainBasePropertyConfig?.chainBasic?.isEnableEditRevisitStatus;
            //医生诊费
            this.innerState.pay.fee = doctorFeeConfig?.regUnitPrice ?? 0;
            // 医生科室列表
            this.innerState.departments = departments;
            this.innerState.registrationAppointmentConfig = registrationAppointmentConfig;
            this.innerState.dataPermission = dataPermission;
            this.innerState.clinicBasicSetting = clinicBasicSetting;
        });
        /**
         * 显示来源开启时才去加载配置
         */
        if (!this.innerState.employeeConfig?.regsHiddenReCommend) {
            // 加载来源分类
            ClinicAgent.getPatientRelativeAdvise()
                .toObservable()
                .subscribe((rsp) => {
                    this.innerState.visitSourceList = rsp.rows;
                    this.update();
                })
                .addToDisposableBag(this);
        }
    }
    private _initPageTrigger(): void {
        //算费
        this._calculatePriceTrigger
            .pipe(
                switchMap((isClearExpectedUnitPrice = true) => {
                    //算费和patientId有关，所以要先填充patientId,如果是会员，在没有patientId的情况下，会导致折扣不生效
                    if (!this.innerState.detail?.chargeSheet) return of(undefined);
                    this.innerState.detail.chargeSheet.patient =
                        this.innerState.detail.chargeSheet.patient ?? this.innerState.detail?.patient ?? new Patient();
                    this.innerState.detail.chargeSheet.patientId = this.innerState.detail.chargeSheet.patient?.id ?? "";
                    this.innerState.detail?.chargeSheet?.fillKeyIds();
                    if (isClearExpectedUnitPrice) {
                        this.innerState.detail.chargeSheet.chargeForms![0].chargeFormItems![0]!.expectedUnitPrice = undefined;
                    }
                    return ChargeUtils.calculatingPrice(this.innerState.detail!.chargeSheet!)
                        .catch((e) => new ABCError(e))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (!rsp) return;
                if (rsp instanceof ABCError) {
                } else {
                    ChargeUtils.syncChargeInvoiceDetail(this.innerState.detail?.chargeSheet, rsp);
                    const { chargeSheet } = this.innerState.detail!,
                        chargeForm = chargeSheet?.chargeForms?.[0];
                    this.innerState.pay = {
                        fee: chargeForm?.totalPrice ?? 0,
                        memberId: chargeSheet?.memberId ?? "",
                        receivable: chargeForm?.totalRealPayPrice ?? 0,
                    };
                }
                this.update();
            })
            .addToDisposableBag(this);

        this._patientRevisitStatusTrigger
            .pipe(
                switchMap(() => {
                    const detail = this.innerState.detail,
                        doctorId = detail?.registrationFormItem?.doctorId,
                        patientId = detail?.patient?.id;
                    if (!doctorId || !patientId) {
                        return of(RegistrationRevisitStatus.first);
                    }
                    return RegistrationAgent.getOutpatientRevisitStatus({ doctorId, patientId }).catchIgnore().toObservable();
                })
            )
            .subscribe((rsp) => {
                if (rsp instanceof GetOutpatientRevisitStatusRsp && !!rsp) {
                    this.innerState.detail!.revisitStatus = rsp?.revisitStatus ?? RegistrationRevisitStatus.first;
                    this.innerState.outpatientRevisitStatusRsp = rsp;
                } else {
                    this.innerState.detail!.revisitStatus = rsp;
                }
                this.registerFeeCalculation();
            })
            .addToDisposableBag(this);

        this._registrationInvoiceDetailTrigger
            .pipe(
                switchMap((params) => {
                    this.innerState.startLoading();
                    this.update();
                    if (!params?.id) return of(undefined);
                    return RegistrationAgent.getRegistrationDetail(params.id)
                        .then((rsp) => {
                            return { rsp: rsp, isInit: params?.isInit };
                        })
                        .catch((e) => new ABCError(e))
                        .toObservable();
                })
            )
            .subscribe(async (result) => {
                this.innerState.stopLoading();
                if (result instanceof ABCError) {
                    this.innerState.setLoadingError(result);
                } else {
                    if (!result) return;
                    const { rsp, isInit } = result;
                    ///外部传入的提前签到信息
                    rsp.registrationFormItem!.oldReserveInfo = this._oldReserveInfo;
                    this.innerState.hasChange = false;
                    this.innerState.loading = false;
                    this.innerState.detail = rsp;

                    this._getPatientBaseDetailTrigger.next(rsp.patient?.id);
                    if (isInit) {
                        this._registrationDesignatedTimeTrigger.next();
                        this._registrationDailyReserveStatusTrigger.next();
                    }

                    const { chargeSheet } = rsp;
                    this.innerState.pay = {
                        fee: rsp.registrationFormItem?.fee ?? 0,
                        memberId: chargeSheet?.memberId ?? "",
                        receivable: chargeSheet?.chargeSheetSummary?.needPayFee ?? 0,
                    };
                    if (!!this.innerState.detail.visitSourceId) {
                        await this._initVisitSource();
                        this.innerState.cloneDetail = _.cloneDeep(this.innerState.detail);
                    }
                    this._calculatePriceTrigger.next();
                    // 如果医保结算异常，需要再拉取接口判断异常类型
                    await this.getShebaoSettlementExceptionList();
                }
                this.update();
            })
            .addToDisposableBag(this);

        this._getPatientBaseDetailTrigger
            .pipe(
                switchMap((patientId) => {
                    if (!patientId) return of(null);
                    return CrmAgent.getPatientById(patientId).catchIgnore().toObservable();
                })
            )
            .subscribe((rsp) => {
                if (rsp) {
                    this.innerState.detail!.patient = rsp;
                    this._getHistoryListTrigger.next();
                    this.update();
                }
            })
            .addToDisposableBag(this);

        //就诊历史
        this._getHistoryListTrigger
            .pipe(
                switchMap((/*data*/) => {
                    const patientId = this.innerState.detail?.patient?.id;
                    if (!patientId) return of(null);

                    return OutpatientAgent.getOutpatientHistoryList(patientId);
                })
            )
            .subscribe(
                (patientSummaryList) => {
                    if (!patientSummaryList) return;
                    this.innerState.diagnoseCount = patientSummaryList.totalCount;
                    this.update();
                },
                (error) => {
                    this.innerState.loading = false;
                    this.innerState.loadError = error;
                    this.update();
                }
            )
            .addToDisposableBag(this);

        //，获取号源时间段信息
        this._registrationDesignatedTimeTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.registrationTimeRangeList = undefined;
                    this.innerState.selectOrderNoTimeList = [];
                    this.innerState.detail!.registrationFormItem =
                        this.innerState.detail!.registrationFormItem ?? new RegistrationFormItem();
                    if (
                        this.innerState.type == RegistrationInvoiceType.create &&
                        this.innerState.detail?.registrationFormItem?.isReserved
                    ) {
                        this.innerState.detail!.registrationFormItem!.reserveTime = undefined;
                        this.innerState.detail!.registrationFormItem!.orderNo = undefined;
                    }
                    return RegistrationAgent.getRegistrationDesignatedTime({
                        departmentId: this.innerState.detail?.registrationFormItem?.departmentId ?? "",
                        doctorId: !!this.innerState.detail?.registrationFormItem?.doctorId
                            ? this.innerState.detail?.registrationFormItem?.doctorId
                            : DEFAULT_DOCTOR_ID,
                        forNormalRegistration: this.innerState.detail?.registrationFormItem?.isReserved == 0 ? 1 : 0, // 默认预约为0  挂号为1
                        workingDate: TimeUtils.formatDate(this.innerState.detail?.registrationFormItem?.reserveDate ?? new Date()),
                        registrationType: RegistrationType.outpatientRegistration,
                    }).catchIgnore();
                })
            )
            .subscribe((rsp) => {
                if (rsp instanceof RegistrationDesignatedTime) {
                    this.innerState.registrationTimeRangeList = rsp;
                    this._matchReserveTime();
                }
                this.update();
            })
            .addToDisposableBag(this);

        this._registrationDailyReserveStatusTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.dailyReserveStatusList = [];
                    const { registrationFormItem, revisitStatus } = this.innerState.detail ?? {};
                    const currentDate = registrationFormItem?.reserveDate ?? new Date();
                    return DentistryAgent.queryDailyReserveStatusByDoctorDepartment({
                        doctorId: !!registrationFormItem?.doctorId ? registrationFormItem?.doctorId : undefined,
                        departmentId: registrationFormItem?.departmentId ?? "",
                        start: TimeUtils.getThisMonthFirstDay(currentDate),
                        end: TimeUtils.getThisMonthEndDay(currentDate),
                        registrationType: RegistrationType.outpatientRegistration,
                        isRevisited: revisitStatus ?? 0,
                    })
                        .catchIgnore()
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (rsp) {
                    //过滤掉过去时间的排班状态
                    this.innerState.dailyReserveStatusList = rsp?.filter((t) =>
                        TimeUtils.showExpiryDateTip(t.date, TimeUtils.getStartOfDate())
                    );
                    this.update();
                }
            })
            .addToDisposableBag(this);
        /**
         * 锁单socket信息（挂号目前显示收费中（支持取消）、退费中、医保退费异常）
         */
        onlineMessageManager.patientOrderSheetLockMsgObserver.subscribe((data) => {
            if (data.key != this.innerState.detail?.patientOrderId) return;
            if (data.businessKey != PatientOrderLockType.chargeSheet) return;
            if (!data.businessKey) return;
            this.innerState.detail = this.innerState.detail ?? new RegistrationDetail();
            this.innerState.detail.chargeSheet = this.innerState.detail.chargeSheet ?? new ChargeInvoiceDetailData();
            this.innerState.detail.chargeSheet.copyPatientOrderLocks = this.innerState.detail.chargeSheet.copyPatientOrderLocks ?? [];
            const patientOrderLock = this.innerState.detail.chargeSheet.copyPatientOrderLocks?.find(
                (t) => t.businessKey == PatientOrderLockType.chargeSheet
            );
            if (!!patientOrderLock) {
                Object.assign(patientOrderLock, {
                    ...data,
                });
            } else {
                this.innerState.detail.chargeSheet.copyPatientOrderLocks!.push(
                    JsonMapper.deserialize(PatientOrderLockDetail, {
                        ...data,
                    })
                );
            }
            // 如果是收费支付锁单，还需要判断当前是哪方发起的
            if (data.value?.chargeInProgress) {
                this.innerState.detail.chargeSheet.lockStatus = data.value?.businessDetail?.addedLockStatus;
                this.innerState.detail.chargeSheet.lockPayTransactionInfo = this.innerState.detail.chargeSheet.lockPayTransactionInfo ?? {};
                this.innerState.detail.chargeSheet.lockPayTransactionInfo.id = data.value?.businessDetail?.chargePayTransactionId;
            }
            this.update();
        });
    }
    private _initPageData(): void {
        //初复诊占用时长
        this.innerState.visitDuration = userCenter.dentistryConfig?.serviceDuration?.visitServiceDuration;
        this.innerState.reVisitDuration = userCenter.dentistryConfig?.serviceDuration?.revisitedServiceDuration;
        if (this.isDetailPage) {
            this._registrationInvoiceDetailTrigger.next({ id: this.id!, isInit: true });
        } else {
            this.innerState.stopLoading();
            this.innerState.detail = new RegistrationDetail();
            this.innerState.detail.chargeSheet = new ChargeInvoiceDetailData();
            this.innerState.detail!.chargeSheet!.chargeForms = [
                JsonMapper.deserialize(ChargeForm, {
                    sourceFormType: 1,
                    chargeFormItems: [
                        JsonMapper.deserialize(ChargeFormItem, {
                            unit: "次",
                            name: this.innerState.clinicBasicSetting?.registrationFeeStr,
                            unitCount: 1,
                            doseCount: 1,
                            unitPrice: +(this.innerState.pay?.fee ?? 0),
                            sourceUnitPrice: +(this.innerState.pay?.fee ?? 0),
                        }),
                    ],
                }),
            ];
            this.innerState.detail!.registrationFormItem = new RegistrationFormItem();
            this.innerState.detail!.registrationFormItem.isReserved = this.innerState.detail!.registrationFormItem.isReserved ?? 0;
            this.innerState.detail!.registrationFormItem!.reserveDate =
                this.innerState.detail!.registrationFormItem!.reserveDate ?? new Date();

            this.innerState.detail.revisitStatus = RegistrationRevisitStatus.first;
            this.getReserveTime();
            if (!this.innerState.detail?.registrationFormItem?.isReserved) {
                if (this.innerState.detail?.registrationFormItem) this.innerState.detail.registrationFormItem.reserveTime = undefined;
                this.dispatch(new _EventCalculateOrderNo());
            }
            this._calculatePriceTrigger.next();
        }
        this._initSelectedDepartmentDoctor();
        this.update();
    }
    @actionEvent(_EventInit)
    private async *_mapEventInit(): AsyncGenerator<State> {
        //初始化相关依赖配置
        await this._initPageConfig();
        this._initPageTrigger();
        this._initPageData();
    }
    @actionEvent(_EventUpdate)
    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }
    private _formatPatientAddress(patient: Patient): void {
        _.assign(patient, patient.address);
    }
    @actionEvent(_EventModifyRegistrationPatient)
    private async *_mapEventModifyRegistrationPatient(event: _EventModifyRegistrationPatient): AsyncGenerator<State> {
        this._formatPatientAddress(event.patient);

        if (this.innerState.detail?.patient?.id && this.innerState.detail?.patient?.id == event.patient.id) {
            this.innerState.detail!.patient = event.patient;
            this.update();
            return;
        }
        if (event.patient?.id) {
            CrmAgent.getPatientById(event.patient.id)
                .toObservable()
                .subscribe(async (rsp) => {
                    const patient = rsp;
                    this.innerState.detail!.patient = patient;
                    this.innerState.detail!.chargeSheet!.patient = patient;
                    this.innerState.detail!.chargeSheet!.patientId = patient.id; // 必须有patient.id 才能正常显示优惠券信息
                    if (!!patient?.isMember) {
                        const member = await PatientAgent.getMemberInfoById(event.patient.id!).catchIgnore();
                        if (!!member) {
                            this.innerState.detail!.chargeSheet!.memberInfo = member;
                            this.innerState.detail!.chargeSheet!.memberId = member?.patientId ?? "";
                            this.innerState.pay.memberId = member?.patientId ?? "";
                        }
                    }
                    this._getHistoryListTrigger.next();
                    this.update();

                    Promise.all([
                        OutpatientAgent.getPastHistory(event.patient.id!), //获取pastHistoryInfo
                    ])
                        .toObservable()
                        .subscribe((rspAll) => {
                            this.innerState.detail!.pastHistory = rspAll[0];
                        })
                        .addToDisposableBag(this);
                    this.initialRevisitStatus();
                })
                .addToDisposableBag(this);
        } else {
            // 未搜索到patient.id时不显示
            this.innerState.detail!.patient = event.patient;
            this.innerState.detail!.chargeSheet!.patientId = undefined;
            this.innerState.detail!.chargeSheet!.memberInfo = undefined;
            this.innerState.detail!.chargeSheet!.memberId = undefined;
            this.innerState.pay.memberId = "";
            this.innerState.detail!.chargeSheet!.patient = event.patient;
            this.update();
            this.initialRevisitStatus();
        }
    }
    @actionEvent(_EventHandleChargeAbnormal)
    async *_mapEventHandleChargeAbnormal(event: _EventHandleChargeAbnormal): AsyncGenerator<State> {
        this.innerState.abnormalList = [];
        //如果是社保异常，只做弹窗提示，操作需要到PC端
        if (event.isShebaoAbnormal) {
            ChargeRefundDialog.showConfirmPopup({
                logo: "image_dlg_fail",
                title: "处理异常",
                content: "请前往电脑客户端，进入『收费』\n" + "找到当前收费单进行处理",
            });
        } else {
            //如果是非社保异常，需要根据收费单id查询异常列表
            this.innerState.abnormalList = await ChargeAgent.getListAbnormaltransaction(
                this.innerState.detail?.chargeSheet?.id ?? ""
            ).catchIgnore();
            if (_.isEmpty(this.innerState.abnormalList)) return;
            showBottomPanel(<ChargeAbnormalDialog bloc={this} />, { topMaskHeight: Sizes.dp160 });
        }
        this.update();
    }
    @actionEvent(_EventAbnormalRefund)
    async *_mapEventAbnormalRefund(event: _EventAbnormalRefund): AsyncGenerator<State> {
        if (!event.abnormalMsg?.chargeSheetId && !event.abnormalMsg?.id) return;
        const refundResult = await ChargeAgent.dealAbnormalRefund(event.abnormalMsg.chargeSheetId!, event.abnormalMsg.id!).catchIgnore();
        if (refundResult?.status == 20) {
            await ChargeRefundDialog.showConfirmPopup({
                logo: "image_dlg_success",
                title: refundResult?.statusName ?? "退费成功",
                content: `已收费用 (${abcI18Next.t("¥")}${ABCUtils.formatPrice(event.abnormalMsg?.amount ?? 0)})已原路退回至患者${
                    event.abnormalMsg?.payModeDisplayName
                }账户`,
            });
            //退款成功后，再次查询异常列表，如果还有，继续退费
            this.innerState.abnormalList = await ChargeAgent.getListAbnormaltransaction(refundResult?.chargeSheetId ?? "").catchIgnore();
            // this._registrationInvoiceDetailTrigger.next({ id: this.id! }); // 刷新详情
            TherapyAppointmentAgent.changeObserver.next(); //刷新挂号列表
            if (_.isEmpty(this.innerState.abnormalList)) {
                ABCNavigator.pop();
            }
        } else {
            await ChargeRefundDialog.showQueryPopup({
                logo: "image_dlg_fail",
                title: "退费失败",
                content: refundResult?.statusName ?? "该收费单已有一笔收费正在进行中，请确定是否终止",
            });
            // this._registrationInvoiceDetailTrigger.next({ id: this.id! }); // 刷新详情
            TherapyAppointmentAgent.changeObserver.next(); //刷新挂号列表
            ABCNavigator.pop();
        }
        this.update();
    }
    @actionEvent(_EventModifyRegistrationType)
    private async *_mapEventModifyRegistrationType(event: _EventModifyRegistrationType): AsyncGenerator<State> {
        this.innerState.hasChange = true;
        const oldDetail = this.innerState.detail?.registrationFormItem ?? new RegistrationFormItem();
        let newRegistrationFormItem = new RegistrationFormItem();

        this.innerState.detail!.registrationFormItem = new RegistrationFormItem();

        if (this.isDetailPage) {
            newRegistrationFormItem = oldDetail;
        }
        const assignKey: (keyof Pick<RegistrationFormItem, "doctorName" | "doctorId" | "departmentName" | "departmentId" | "timeOfDay">)[] =
            ["doctorName", "doctorId", "departmentName", "departmentId", "timeOfDay"];
        assignKey.forEach((key) => {
            newRegistrationFormItem[key] = oldDetail[key];
        });
        newRegistrationFormItem.isReserved = event.type;
        //当前如果是预约类型，日期默认为传入的日期
        if (!!newRegistrationFormItem.isReserved) {
            newRegistrationFormItem.reserveDate = newRegistrationFormItem?.reserveDate ?? new Date();
        } else {
            //当前如果是挂号类型，日期选择器默认选中今天
            newRegistrationFormItem.reserveDate = new Date();
        }
        this.innerState.detail!.registrationFormItem = newRegistrationFormItem;

        Object.assign(this.innerState.detail?.registrationFormItem, {
            reserveEnd: undefined,
            reserveStart: undefined,
            reserveTime: undefined,
            orderNo: undefined,
        });
        // 挂号类型需要调用算号
        if (event.type == 0) {
            this.dispatch(new _EventCalculateOrderNo());
        }
        if (!this.isDetailPage) {
            if (event.type)
                Object.assign(this.innerState.detail?.registrationFormItem, {
                    reserveDate: undefined,
                });
        } else {
            this._registrationDesignatedTimeTrigger.next();
        }
        this._calculatePriceTrigger.next();
        this.update();
    }
    @actionEvent(_EventCalculateOrderNo)
    private async *_mapEventCalculateOrderNo(): AsyncGenerator<State> {
        const registrationFormItem = this.innerState.detail?.registrationFormItem;
        //默认选中时间
        if (registrationFormItem) {
            if (!registrationFormItem.reserveTime) {
                //没有预约时间
                const hours = new Date().getHours();
                let defaultReserve: { id: number; title: string; timeOfDay: string; imeOfDayCount?: number; range: Range<string> };
                if (hours >= 0 && hours < 12) {
                    defaultReserve = DefaultRegistrationTimeOptions[0];
                } else if (hours >= 12 && hours < 18) {
                    defaultReserve = DefaultRegistrationTimeOptions[1];
                } else {
                    defaultReserve = DefaultRegistrationTimeOptions[2];
                }
                // timeOfDay不变
                defaultReserve =
                    DefaultRegistrationTimeOptions.find((item) => item.timeOfDay == registrationFormItem.timeOfDay) ?? defaultReserve;
                this.innerState.detail!.registrationFormItem!.timeOfDay = defaultReserve.timeOfDay;
                this.innerState.detail!.registrationFormItem!.reserveDate = new Date();
                this.innerState.detail!.registrationFormItem!.reserveTime = defaultReserve.range;
                this.innerState.detail!.registrationFormItem = await RegistrationAgent.getDoctorOrderNo(
                    this.innerState.detail!.registrationFormItem!,
                    false,
                    RegistrationType.outpatientRegistration
                ).catch((error) => {
                    if (!this.isDisposed) {
                        showQueryDialog("提示", errorToStr(error));
                    }
                    return undefined;
                });
                if (!registrationFormItem.isReserved) this.innerState.isCalculateOrderNo = true;
                DefaultRegistrationTimeOptions.forEach((item) => {
                    item.timeOfDayCount = this.innerState.detail!.registrationFormItem?.timeOfDayTotalCountMap?.get(item.timeOfDay);
                });
            }
        }

        this.update();
    }
    @actionEvent(_EventModifyRegistrationDepartment)
    private async *_mapEventModifyRegistrationDepartment(): AsyncGenerator<State> {
        const { departments, detail } = this.innerState;
        const departmentIndex = departments?.findIndex((item) => {
            return item.departmentId == detail?.registrationFormItem?.departmentId;
        });

        const result = await showOptionsBottomSheet({
            title: "选择科室",
            options: departments?.map((item) => item.departmentName ?? "其他"),
            initialSelectIndexes: !_.isUndefined(departmentIndex) ? new Set<number>([departmentIndex]) : undefined,
            height: pxToDp(375),
            showConfirmBtn: false,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
        });

        if (!result || _.isEmpty(result)) return;
        this.innerState.hasChange = true;
        const currentDepartment = this.innerState.departments?.[result[0]];
        this.innerState.currentDepartmentDoctors = currentDepartment?.doctors;
        //修改科室、医生和预约时间
        const registrationFormItem = (detail!.registrationFormItem = detail?.registrationFormItem ?? new RegistrationFormItem());
        registrationFormItem.departmentId = currentDepartment?.departmentId;
        registrationFormItem.departmentName = currentDepartment?.departmentName;
        registrationFormItem.doctorName = undefined;
        registrationFormItem.doctorId = undefined;
        this.innerState.currentSelectedDoctors = currentDepartment?.doctors?.find((item) => item.doctorId === DEFAULT_DOCTOR_ID);
        //初始化初复诊转态
        this.initialRevisitStatus();
        this._calculatePriceTrigger.next();
        if (registrationFormItem.isReserved) {
            registrationFormItem.reserveDate = undefined;
            this._registrationDailyReserveStatusTrigger.next();
        } else {
            if (this.isDetailPage) {
                // 排班接口调用必须在算号之前，否则会有问题
                this.innerState.registrationTimeRangeList = await RegistrationAgent.getRegistrationDesignatedTime({
                    departmentId: this.innerState.detail?.registrationFormItem?.departmentId ?? "",
                    doctorId: !!this.innerState.detail?.registrationFormItem?.doctorId
                        ? this.innerState.detail?.registrationFormItem?.doctorId
                        : DEFAULT_DOCTOR_ID,
                    forNormalRegistration: this.innerState.detail?.registrationFormItem?.isReserved == 0 ? 1 : 0, // 默认预约为0  挂号为1
                    workingDate: TimeUtils.formatDate(this.innerState.detail?.registrationFormItem?.reserveDate ?? new Date()),
                    registrationType: RegistrationType.outpatientRegistration,
                }).catchIgnore();
                this._matchReserveTime();
            }
            if (detail?.registrationFormItem?.reserveTime) detail.registrationFormItem!.reserveTime = undefined;
            this.dispatch(new _EventCalculateOrderNo());
        }
    }
    @actionEvent(_EventModifyRegistrationDoctor)
    private async *_mapEventModifyRegistrationDoctor(): AsyncGenerator<State> {
        const { currentDepartmentDoctors, detail } = this.innerState;
        const doctorIndex = currentDepartmentDoctors?.findIndex((item) => {
            return item.doctorId == detail?.registrationFormItem?.doctorId;
        });

        const result = await showOptionsBottomSheet({
            title: "选择医生",
            options: currentDepartmentDoctors?.map((item) => item.doctorName ?? ""),
            initialSelectIndexes: !_.isUndefined(doctorIndex) ? new Set<number>([doctorIndex]) : undefined,
            height: pxToDp(375),
            showConfirmBtn: false,
            showResetBtn: true,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
        });
        if (!result) return;
        this.innerState.hasChange = true;
        const currentDoctor = this.innerState.currentDepartmentDoctors?.[result[0]];
        this.innerState.currentSelectedDoctors = currentDoctor;
        //医生和预约时间
        const registrationFormItem = (detail!.registrationFormItem = detail?.registrationFormItem ?? new RegistrationFormItem());
        registrationFormItem.doctorId = currentDoctor?.doctorId;
        registrationFormItem.doctorName = !!currentDoctor?.doctorId ? currentDoctor?.doctorName : undefined;
        //初始化初复诊转态
        this.initialRevisitStatus();
        if (!detail?.registrationFormItem?.isReserved) {
            if (this.isDetailPage) {
                // 排班接口调用必须在算号之前，否则会有问题
                this.innerState.registrationTimeRangeList = await RegistrationAgent.getRegistrationDesignatedTime({
                    departmentId: this.innerState.detail?.registrationFormItem?.departmentId ?? "",
                    doctorId: !!this.innerState.detail?.registrationFormItem?.doctorId
                        ? this.innerState.detail?.registrationFormItem?.doctorId
                        : DEFAULT_DOCTOR_ID,
                    forNormalRegistration: this.innerState.detail?.registrationFormItem?.isReserved == 0 ? 1 : 0, // 默认预约为0  挂号为1
                    workingDate: TimeUtils.formatDate(this.innerState.detail?.registrationFormItem?.reserveDate ?? new Date()),
                    registrationType: RegistrationType.outpatientRegistration,
                }).catchIgnore();
                this._matchReserveTime();
            }
            if (detail?.registrationFormItem?.reserveTime) detail.registrationFormItem!.reserveTime = undefined;
            this.dispatch(new _EventCalculateOrderNo());
        } else {
            this._registrationDailyReserveStatusTrigger.next();
        }
        this.update();
    }
    @actionEvent(_EventModifyRegistrationFee)
    private async *_mapEventModifyRegistrationFee(event: _EventModifyRegistrationFee): AsyncGenerator<State> {
        const fee = Number(event.fee);
        this.innerState.hasChange = true;
        this.innerState.pay!.fee = fee;
        this.innerState.detail!.chargeSheet!.chargeForms![0].chargeFormItems![0].sourceUnitPrice = fee;
        this.innerState.detail!.chargeSheet!.chargeForms![0].chargeFormItems![0].unitPrice = fee;
        this.innerState.detail!.chargeSheet!.chargeForms![0].chargeFormItems![0].expectedUnitPrice = fee;
        this._calculatePriceTrigger.next(false);
    }
    //修改挂号时间
    @actionEvent(_EventModifyRegistrationTime)
    private async *_mapEventModifyRegistrationTime(): AsyncGenerator<State> {
        let registrationFormItem = this.innerState.detail?.registrationFormItem;
        // 预约类型
        if (registrationFormItem?.isReserved) {
            if (this.isDetailPage) {
                const registrationInfo = (this.innerState.detail!.registrationFormItem =
                    this.innerState.detail?.registrationFormItem ?? new RegistrationFormItem());
                const schedulingInfo = this.innerState.dailyReserveStatusList
                    ?.filter((t) => t.status != RegistrationReserveStatus.notScheduling)
                    ?.map((item, index) => {
                        return {
                            date: item.date!,
                            view: () => {
                                return (
                                    <View key={index}>
                                        <Text
                                            style={[
                                                TextStyles.t10NT2.copyWith({
                                                    color:
                                                        item.status == RegistrationReserveStatus.canAppointment
                                                            ? Colors.canAppointmentColor
                                                            : item.status == RegistrationReserveStatus.finishedDiagnosis
                                                            ? Colors.Y2
                                                            : Colors.T6,
                                                }),
                                            ]}
                                        >
                                            {item?.statusName ?? ""}
                                        </Text>
                                    </View>
                                );
                            },
                        };
                    });
                registrationInfo!.reserveDate = registrationInfo?.reserveDate ?? new Date();
                const selectDate = await AbcCalendar.show({
                    date: registrationInfo?.reserveDate ?? new Date(),
                    minDate: new Date(),
                    maxDate: !registrationInfo?.isReserved ? registrationInfo?.reserveDate ?? new Date() : undefined,
                    dateExtendView: schedulingInfo,
                });
                if (!selectDate) return;
                this.innerState.hasChange = true;
                registrationInfo!.reserveDate = selectDate;
                this._registrationDesignatedTimeTrigger.next();
            } else {
                const result = await RegistrationAppointmentDoctorListPage.show(registrationFormItem);
                if (!result || _.isEmpty(result)) return;
                this.innerState.hasChange = true;
                this.innerState.detail!.registrationFormItem = result;
                this._patientRevisitStatusTrigger.next();
                this.dispatch(new _EventModifyRegistrationFee(result?.fee ?? 0));
            }
        } else {
            // 如果有排班可选指定时间
            const list = DefaultRegistrationTimeOptions?.filter((t) => (this.isDetailPage ? t.title != "指定时间" : true));
            const timeIndex = list?.findIndex((item) => {
                return item.timeOfDay == registrationFormItem?.timeOfDay; // 当日时间段
            });
            // 挂号类型
            const options = list;

            const result = await showOptionsBottomSheet({
                title: "选择挂号时间",
                options: options?.map((item) => item.title + item.timeOfDay),
                optionsWidgets: options?.map((item, index) => (
                    <View
                        key={index}
                        style={{
                            ...ABCStyles.rowAlignCenter,
                            height: Sizes.listItemHeight,
                            paddingHorizontal: Sizes.listHorizontalMargin,
                        }}
                    >
                        <Text
                            style={[
                                TextStyles.t16NB,
                                {
                                    flex: 1,
                                },
                            ]}
                        >
                            {`${item.title} ${item.timeOfDay}`}
                        </Text>
                        {!_.isUndefined(item.timeOfDayCount) && <Text style={[TextStyles.t16NT2]}>{`(共${item.timeOfDayCount}号)`}</Text>}
                    </View>
                )),
                initialSelectIndexes: !_.isUndefined(timeIndex) ? new Set<number>([timeIndex]) : undefined, // 初始选择索引
            });

            if (!result || _.isEmpty(result)) return;
            if (this.isDetailPage) {
                // 排班接口调用必须在算号之前，否则会有问题
                this.innerState.registrationTimeRangeList = await RegistrationAgent.getRegistrationDesignatedTime({
                    departmentId: this.innerState.detail?.registrationFormItem?.departmentId ?? "",
                    doctorId: !!this.innerState.detail?.registrationFormItem?.doctorId
                        ? this.innerState.detail?.registrationFormItem?.doctorId
                        : DEFAULT_DOCTOR_ID,
                    forNormalRegistration: this.innerState.detail?.registrationFormItem?.isReserved == 0 ? 1 : 0, // 默认预约为0  挂号为1
                    workingDate: TimeUtils.formatDate(this.innerState.detail?.registrationFormItem?.reserveDate ?? new Date()),
                    registrationType: RegistrationType.outpatientRegistration,
                }).catchIgnore();
                this._matchReserveTime();
            }
            // 选择指定挂号时间
            if (result[0] == 3) {
                // 此处调用算号接口，是为了获取timeOfDayTotalCountMap的值
                registrationFormItem = await RegistrationAgent.getDoctorOrderNo(
                    registrationFormItem!,
                    false,
                    RegistrationType.outpatientRegistration
                ).catchIgnore();
                const { departmentId, doctorId, timeOfDayTotalCountMap } = registrationFormItem ?? {};
                // 选择指定时间需先选择医生
                if (doctorId == undefined) return Toast.show("请先选择医生");
                // 所选医生（姓名 诊费
                const selectedDoctors = this.innerState.currentDepartmentDoctors?.find((item) => {
                    return item.doctorId == doctorId;
                });
                // 所选医生未排班时提示
                if (selectedDoctors?.doctorName !== "不指定医生" && !timeOfDayTotalCountMap) {
                    return Toast.show("该医生今天暂未排班");
                }

                const chooseSpecificTime = await RegisterTimeDialog.show(
                    selectedDoctors as RegistrationDesignatedDoctorInfo,
                    departmentId as string
                );

                if (!chooseSpecificTime) return;
                // 指定时间段
                registrationFormItem!.reserveTime = {
                    start: chooseSpecificTime.start,
                    end: chooseSpecificTime.end,
                };
                // 指定时间段号数
                registrationFormItem!.orderNo = chooseSpecificTime.orderNo;
                registrationFormItem!.doctorId = selectedDoctors?.doctorId;
                this.innerState.isCalculateOrderNo = false;
                this.innerState.isDesignTime = true;
            } else {
                registrationFormItem!.reserveTime = options[result[0]].range;
                registrationFormItem!.reserveDate = new Date();
                registrationFormItem = await RegistrationAgent.getDoctorOrderNo(
                    registrationFormItem!,
                    false,
                    RegistrationType.outpatientRegistration
                ).catchIgnore();
                list.forEach((item) => {
                    item.timeOfDayCount = registrationFormItem?.timeOfDayTotalCountMap?.get(item.timeOfDay);
                });
                this.innerState.isCalculateOrderNo = true;
            }
            this.innerState.detail!.registrationFormItem = registrationFormItem ?? this.innerState.detail!.registrationFormItem;
            this.innerState.hasChange = true;
        }
        this.registerFeeCalculation();
    }
    @actionEvent(_EventModifyRevisitStatus)
    private async *_mapEventModifyRevisitStatus(): AsyncGenerator<State> {
        if (this.innerState.disabledEditRevisitStatus) return;
        const initIndex = this.innerState.detail?.revisitStatus;
        const result = await showOptionsBottomSheet({
            title: "选择初诊/复诊",
            options: ["初诊", "复诊"],
            initialSelectIndexes: initIndex ? new Set<number>([initIndex - 1]) : undefined,
        });

        if (result && result.length) {
            this.innerState.hasChange = true;
            this.innerState.detail!.revisitStatus = result[0] + 1;

            this.registerFeeCalculation();
        }
    }
    @actionEvent(_EventUpdatePromotions)
    private async *_mapEventUpdatePromotions(event: _EventUpdatePromotions): AsyncGenerator<State> {
        const chargeSheet = this.innerState.detail!.chargeSheet!;
        this.innerState.hasChange = true;
        ChargeUtils.updatePromotions(chargeSheet, event);
        this._calculatePriceTrigger.next();
    }
    @actionEvent(_EventUpdateMemberCard)
    async *_mapEventUpdateMemberCard(event: _EventUpdateMemberCard): AsyncGenerator<State> {
        const innerState = this.innerState;
        SelectMemberInfo.updateChargeDetail(innerState.detail!.chargeSheet!, event.member);
        this._calculatePriceTrigger.next();
        this.innerState.hasChange = true;
        yield this.innerState.clone();
    }
    @actionEvent(_EventPatientCardPromotion)
    private async *_mapEventPatientCardPromotion(event: _EventPatientCardPromotion): AsyncGenerator<State> {
        const chargeSheet = this.innerState.detail!.chargeSheet!;
        this.innerState.hasChange = true;
        chargeSheet.patientCardPromotions = event.patientCardPromotions;
        this._calculatePriceTrigger.next();
    }
    @actionEvent(_EventModifyMedicalRecordCard)
    private async *_mapEventModifyMedicalRecordCard(event: _EventModifyMedicalRecordCard): AsyncGenerator<State> {
        this.innerState.hasChange = true;
        const { chiefComplaint, presentHistory, pastHistory, physicalExamination, epidemiologicalHistory } = event.medicalRecord;
        Object.assign(this.innerState.detail, { chiefComplaint, presentHistory, pastHistory, physicalExamination, epidemiologicalHistory });
        this.update();
    }
    @actionEvent(_EventRefundedRegistrationFee)
    private async *_mapEventRefundedRegistrationFee(): AsyncGenerator<State> {
        await ChargeInvoiceRefundDialog.show(
            this.innerState.detail!.chargeSheet!,
            () => {
                ABCNavigator.popUntil(URLProtocols.REGISTRATION_TAB, URLProtocols.REGISTRATION_TAB);
            },
            true
        );
    }
    @actionEvent(_EventRefundedRegistration)
    private async *_mapEventRefundedRegistration(): AsyncGenerator<State> {
        const patient = this.innerState.detail?.patient;
        const registration = this.innerState.detail?.registrationFormItem;

        if (registration?.payStatusV2 === PayStatusV2.paid || registration?.payStatusV2 === PayStatusV2.partedPaid) {
            // 1. 完全收费退费，弹出退费
            await ChargeInvoiceRefundDialog.show(
                this.innerState.detail!.chargeSheet!,
                () => {
                    ABCNavigator.popUntil(URLProtocols.REGISTRATION_TAB, URLProtocols.REGISTRATION_TAB);
                    this._registrationInvoiceDetailTrigger.next({ id: this.id! });

                    //退费成功更新一下列表
                    RegistrationAgent.changeObserver.next();
                },
                true
            );
        } else if (registration?.payStatusV2 === PayStatusV2.partedPaid) {
            // 2. 部分收费，提示退费，
        } else {
            // 3. 否则提示退号
            const result = await showQueryDialog(
                "",
                registration?.statusV2 == RegistrationStatusV2.diagnosed
                    ? `${patient?.name} 已接受就诊，确定要退号？`
                    : "退号后不可恢复，是否继续？"
            );
            if (result == DialogIndex.positive) {
                RegistrationAgent.cancelPatientRegistration(this.innerState.detail!.id!)
                    .then(() => {
                        Toast.show("退号成功", { success: true }).then(() => ABCNavigator.pop());
                    })
                    .catch((error) => {
                        Toast.show(`${errorToStr(new ABCError(error))}`, { warning: true });
                    });
            }
        }
    }
    @actionEvent(_EventRegistrationCharge)
    private async *_mapEventRegistrationCharge(): AsyncGenerator<State> {
        // 有更改，先提交保存
        if (this.innerState.hasChange) {
            this.dispatch(
                new _EventSaveRegistration(() => {
                    this._registrationInvoiceDetailTrigger.next({ id: this.id! });
                })
            );
        }

        const detailData = this.innerState.detail?.chargeSheet;
        const payData = new ChargePayData();
        const hasPartedPaid = this.innerState.hasPartedPaid; // 是否部分收费

        ABCNavigator.navigateToPage(
            <ChargePayPage
                chargePayData={payData}
                chargeDetailData={detailData}
                hasPartedPaid={hasPartedPaid}
                successChangeCallback={() => {
                    if (this._source == RegistrationPageSourceType.normal) {
                        ABCNavigator.popUntil(URLProtocols.REGISTRATION_TAB, URLProtocols.REGISTRATION_TAB);
                    } else {
                        ABCNavigator.pop({ success: true });
                    }
                }}
                requestDetailCallback={() => {
                    this._registrationInvoiceDetailTrigger.next({ id: this.id! });
                }}
                isHideOweMethod={true}
            />
        ).then();
        this.update();
    }
    @actionEvent(_EventBackDirectRegistration)
    private async *_mapEventBackDirectRegistration(): AsyncGenerator<State> {
        if (!this.innerState.detail) return;

        const { patientOrderId, id } = this.innerState.detail;
        const examinationsResult = await ExaminationsAgent.getOutpatientExaminationsListByPatientOrderId(patientOrderId!).catchIgnore();
        if (!examinationsResult?.examItems?.length) {
            const queryResult = await showQueryDialog("是否确认回诊签到", "该患者本次就诊未开具检查检验项目，是否确认回诊？");
            if (queryResult != DialogIndex.positive) return;
        }

        const loadingDialog = new LoadingDialog("正在签到");
        loadingDialog.show(500);
        const result = await DentistryAgent.putManageRegistrationStatus(id!, RegistrationStatusV2.continueDiagnose).catch(
            (e) => new ABCError(e)
        );
        if (result instanceof ABCError) {
            await loadingDialog.fail(errorSummary(result));
            return;
        }
        await loadingDialog.hide();
        RegistrationAgent.changeObserver.next();
        ABCNavigator.pop();
    }
    @actionEvent(_EventRegistrationSignIn)
    private async *_mapEventRegistrationSignIn(): AsyncGenerator<State> {
        const detail = this.innerState.detail;
        if (!detail || !detail.id) return;
        const { chargeSheet, registrationFormItem } = detail;

        if (TimeUtils.formatDate(registrationFormItem?.reserveDate) != TimeUtils.formatDate(new Date())) {
            const result = await showQueryDialog(
                "",
                `患者预约时间（${TimeUtils.formatDate(registrationFormItem?.reserveDate)}）不是今天。签到后会更改预约就诊时间，是否确定`
            );
            if (result != DialogIndex.positive) {
                return;
            }
        }

        const req = JsonMapper.deserialize(RegistrationDetailReq, {
            ...detail,
            pay: this.innerState.pay,
            promotions: chargeSheet?.promotions,
            signIn: TherapySignInStatus.signed,
            visitSourceFrom: detail.visitSourceId == detail.visitSourceFrom ? "" : detail.visitSourceFrom,
        });
        if (!req.registrationFormItem?.reserveTime) return;

        const loading = new LoadingDialog();
        loading.show(300);
        const rsp = await RegistrationAgent.savePatientRegistration({ id: detail.id, detail: req }).catch((error) => new ABCError(error));
        await loading.hide();
        if (rsp instanceof ABCError) {
            return await showQueryDialog(`保存失败`, errorToStr(rsp));
        }
        this.innerState.detail = rsp;
        this.innerState.type = RegistrationInvoiceType.detail;
        this.innerState.hasChange = false;
        if (this.innerState.hasCharged) {
            return Toast.show("签到成功", { success: true }).then(() => {
                ABCNavigator.pop();
            });
        }
        const result = await RegistrationSuccessDialog.show({
            detail: this.innerState.detail,
            type: RegistrationSuccessDialogType.signIn,
            isShowCharge: true,
        });
        if (result == DialogIndex.positive) {
            this.dispatch(new _EventRegistrationCharge());
        } else {
            ABCNavigator.pop();
        }
    }
    @actionEvent(_EventFinishRegistration)
    private async *_mapEventFinishRegistration(): AsyncGenerator<State> {
        const detail = this.innerState.detail;
        if (!detail) return;
        const { chargeSheet } = detail;

        ///内容完整性校验
        if (!detail.patient || !detail.patient.name) {
            //无患者
            this.innerState.focusKey = FocusItemKeys.patient;
            this.innerState.showErrorHint = true;
            yield ScrollToErrorViewState.fromState(this.innerState);
            return;
        } else if (!detail.registrationFormItem?.departmentId) {
            this.innerState.focusKey = FocusItemKeys.department;
            this.innerState.showErrorHint = true;
            yield ScrollToErrorViewState.fromState(this.innerState);
            return;
        } else if (!detail.registrationFormItem.reserveDate || !detail.registrationFormItem.reserveTime) {
            this.innerState.focusKey = FocusItemKeys.reserveDate;
            this.innerState.showErrorHint = true;
            yield ScrollToErrorViewState.fromState(this.innerState);
            return;
        } else if (_.isUndefined(this.innerState.pay.fee)) {
            this.innerState.focusKey = FocusItemKeys.fee;
            this.innerState.showErrorHint = true;
            yield ScrollToErrorViewState.fromState(this.innerState);
            return;
        } else if (
            detail.registrationFormItem.isReserved &&
            (!detail.registrationFormItem.reserveTime?.start || !detail.registrationFormItem.reserveTime?.end)
        ) {
            this.innerState.focusKey = FocusItemKeys.reserveTime;
            this.innerState.showErrorHint = true;
            yield ScrollToErrorViewState.fromState(this.innerState);
            return;
        }
        ///
        const req = JsonMapper.deserialize(RegistrationDetailReq, {
            allergicHistory: detail.allergicHistory,
            chiefComplaint: detail.chiefComplaint,
            familyHistory: detail.familyHistory,
            pastHistory: detail.pastHistory,
            personalHistory: detail.personalHistory,
            physicalExamination: detail.physicalExamination,
            presentHistory: detail.presentHistory,
            epidemiologicalHistory: detail.epidemiologicalHistory,

            patient: detail.patient,

            registrationFormItem: detail.registrationFormItem,

            pay: this.innerState.pay,

            promotions: chargeSheet?.promotions,
            couponPromotions: chargeSheet?.couponPromotions, // 优惠券促销
            giftRulePromotions: chargeSheet?.giftRulePromotions, // 满减活动
            patientPointsInfo: chargeSheet?.patientPointsInfo, //积分抵扣
            patientCardPromotions: chargeSheet?.patientCardPromotions, // 卡项
            visitSourceId: detail.visitSourceId,
            visitSourceFrom: detail.visitSourceId == detail.visitSourceFrom ? "" : detail.visitSourceFrom,
            visitSourceRemark: detail.visitSourceRemark ?? "",
            revisitStatus: detail.revisitStatus,
            registrationType: RegistrationType.outpatientRegistration,
        });
        const actionText = detail.registrationFormItem.isReserved ? "预约" : "挂号";

        const dialog = new LoadingDialog();
        dialog.show(300);

        //患者沟通进入
        if (this._source == RegistrationPageSourceType.patientChat) {
            // this._setPatientRegAuditStatusSubject.next();
            await dialog.hide();
            return;
        } else {
            let rsp;
            //普通挂号
            if (this.innerState.isCreate) {
                rsp = await RegistrationAgent.createRegistrationSheet(req).catch((e) => new ABCError(e));
            } else {
                if (!detail?.id) return;
                rsp = await RegistrationAgent.savePatientRegistration({ id: detail.id, detail: req }).catch((e) => new ABCError(e));
            }

            await dialog.hide();
            if (rsp instanceof ABCError) {
                await showQueryDialog(`${actionText}失败`, errorToStr(rsp));
            } else if (rsp) {
                if (this.isDetailPage) {
                    dialog.show(300);
                    if (!!rsp && this.isDetailPage) {
                        await dialog.success("保存成功").then(() => {
                            if (this._source == RegistrationPageSourceType.normal) {
                                ABCNavigator.popUntil(URLProtocols.REGISTRATION_TAB, URLProtocols.REGISTRATION_TAB);
                            } else {
                                ABCNavigator.pop({ success: true });
                            }
                        });
                    } else {
                        await dialog.fail("保存失败");
                    }
                } else {
                    this.innerState.detail = rsp;
                    this.innerState.type = RegistrationInvoiceType.detail;
                    this.innerState.hasChange = false;
                    this._calculatePriceTrigger.next();
                    const result = await RegistrationSuccessDialog.show({
                        detail: this.innerState.detail,
                        isShowOrderNo: true, //灵活模式--无诊号
                        isShowCharge: true,
                        isUpdateClinic: userCenter.isAllowedRegUpgrade,
                        isShowUndetermined: false,
                    });
                    if (result == DialogIndex.positive) {
                        this.dispatch(new _EventRegistrationCharge());
                    } else {
                        ABCNavigator.pop({ success: true });
                    }
                }
            }
        }
    }
    @actionEvent(_EventUpdatePatient)
    private async *_mapEventUpdatePatient(event: _EventUpdatePatient): AsyncGenerator<State> {
        if (!event?.patient) return;
        this.innerState.detail!.patient = this.innerState.detail?.patient ?? new Patient();
        this.innerState.detail!.patient = event.patient;
        this.update();
    }
    /**
     * 优惠明细弹窗
     */
    @actionEvent(_EventDiscountDetail)
    async *_mapEventDiscountDetail(): AsyncGenerator<State> {
        const detail = this.innerState.detail;
        if (!detail?.chargeSheet) return;
        const result = await showBottomPanel<number | undefined>(
            <DiscountCardDialog
                detailData={detail?.chargeSheet}
                canModify={this.innerState.isCreate || !this.innerState.disabledEditCharge}
                promotions={Utils.filterRegistrationPromotions(detail.chargeSheet)}
                onPromotionSelectChanged={(selectPromotions, selectGiftPromotions, selectCouponPromotions, selectMemberPointPromotion) => {
                    this.requestUpdatePromotions(
                        selectPromotions,
                        selectGiftPromotions,
                        selectCouponPromotions,
                        selectMemberPointPromotion
                    );
                }}
                onPromotionMemberCardChanged={(member) => this.requestUpdateMemberCard(member)}
                onPatientCardPromotionChanged={(patientCardPromotions) => this.requestPatientCardPromotionChanged(patientCardPromotions)}
            />,
            {
                topMaskHeight: pxToDp(160),
            }
        );
        this.innerState.hasChange = true;
        if (!!result) {
            this.innerState.discountTotalPrice = result;
        }
        this.update();
    }
    @actionEvent(_EventUpdateRegistrationInfo)
    async *_mapEventUpdateRegistrationInfo(): AsyncGenerator<State> {
        const result = await ABCNavigator.navigateToPage<RegistrationDetail>(
            <RegistrationInvoiceDetailPage id={this.id} doctorId={this._doctorId} />,
            {
                transitionType: TransitionType.inFromBottom,
            }
        );
        if (!result) return;
        this.innerState.detail = result;
        this.update();
    }

    /**
     * 完成预诊
     */
    @actionEvent(_EventCompletePreDiagnosis)
    async *_mapEventCompletePreDiagnosis(): AsyncGenerator<State> {
        const result = await showBottomPanel<MedicalRecord>(
            <DentistryPreDiagnosisDialog medicalRecord={this.innerState.dentistryMedicalRecord} />,
            {
                topMaskHeight: Sizes.dp160,
            }
        );
        if (!result) return;
        this.innerState.hasChange = true;
        this.innerState.detail!.chiefComplaint = result?.chiefComplaint;
        this.innerState.detail!.presentHistory = result?.presentHistory;
        this.innerState.detail!.pastHistory = result?.pastHistory;
        this.innerState.detail!.epidemiologicalHistory = result?.epidemiologicalHistory;
        this.innerState.detail!.dentistryExaminations = result?.dentistryExaminations;
        this.innerState.detail!.physicalExamination = result?.physicalExamination;
        this.innerState.detail!.preDiagnosisAttachments = result?.preDiagnosisAttachments;
        const { detail } = this.innerState;
        const req = {
            allergicHistory: detail?.allergicHistory,
            chiefComplaint: detail?.chiefComplaint,
            dentistryExaminations: detail?.dentistryExaminations,
            epidemiologicalHistory: detail?.epidemiologicalHistory,
            familyHistory: detail?.familyHistory,
            pastHistory: detail?.pastHistory,
            personalHistory: detail?.personalHistory,
            physicalExamination: detail?.physicalExamination,
            presentHistory: detail?.presentHistory,
            preDiagnosisAttachments: detail?.preDiagnosisAttachments ?? [],
        };
        const rsp = await DentistryAgent.modifyRegistrationPreDiagnosis(this.id, req).catchIgnore();
        if (rsp?.isSuccess == 1) {
        }
        this.update();
    }
    @actionEvent(_EventModifyOrderNo)
    async *_mapEventModifyOrderNo(): AsyncGenerator<State> {
        this.innerState.detail!.registrationFormItem = this.innerState.detail?.registrationFormItem ?? new RegistrationFormItem();
        const registrationInfo = this.innerState.detail?.registrationFormItem;
        const timeStrList: { cont: string; type?: number; residueCount?: string }[] = []; //时间列表
        let initialIndex = 0; //选中索引
        const availableOrderNoTimeList: {
            orderNo?: number;
            timeOfDay?: string;
            orderNoTimeOfDay?: string;
            start?: string;
            end?: string;
            type?: number;
            residueCount?: number;
        }[] = [];
        let isHasSchedule = false;
        if (!registrationInfo?.isReserved) {
            // 此时调用算号接口，只是为了更新timeOfDayTotalCountMap的值
            const orderInfo = await RegistrationAgent.getDoctorOrderNo(
                _.cloneDeep(registrationInfo)!, //必须克隆，否则会导致orderNo变化
                false,
                RegistrationType.outpatientRegistration
            ).catchIgnore();
            registrationInfo!.timeOfDayTotalCountMap = orderInfo?.timeOfDayTotalCountMap;
        }
        isHasSchedule = !registrationInfo?.isReserved
            ? !!registrationInfo?.timeOfDay && !!registrationInfo?.timeOfDayTotalCountMap?.get(registrationInfo?.timeOfDay)
            : !!this.innerState.registrationTimeRangeList?.registrationCategoryScheduleIntervals?.length;
        if (isHasSchedule) {
            this.innerState.selectOrderNoTimeList =
                this.innerState.registrationTimeRangeList?.registrationCategoryScheduleIntervals?.flatMap((item) => {
                    return item.scheduleIntervals?.filter((t) => t.timeOfDay == registrationInfo?.timeOfDay) ?? [];
                }) ?? [];
            if (!!this.innerState.selectOrderNoTimeList?.length) {
                _.cloneDeep(this.innerState.selectOrderNoTimeList)?.map((item) => {
                    const availableList = (item.list ?? [])
                        ?.map((sub) => {
                            if (!this.innerState.hasChange && sub.orderNo == registrationInfo?.orderNo) sub.available = 1;
                            return sub;
                        })
                        ?.filter((k) => k.available == 1);
                    const normalNoCount = availableList.filter((it) => !it.type).length;
                    const xianNoCount = availableList.filter((it) => it.type === 1).length;
                    const vipNoCount = availableList.filter((it) => it.type === 2).length;
                    availableList?.forEach((t) => {
                        availableOrderNoTimeList.push({
                            orderNo: t.orderNo,
                            timeOfDay: item.timeOfDay,
                            orderNoTimeOfDay: t.timeOfDay,
                            start: this.innerState.isAccuratePart ? t.start : item.start,
                            end: this.innerState.isAccuratePart ? t.end : item.end,
                            type: t.type,
                            residueCount: t.type ? (t.type == 1 ? xianNoCount : vipNoCount) : normalNoCount,
                        });
                    });
                });

                if (availableOrderNoTimeList) {
                    initialIndex = availableOrderNoTimeList.findIndex((k) => {
                        // 签到取号情况下，没有orderNo,根据时间段和type匹配
                        return k.orderNo == registrationInfo?.orderNo && k.orderNoTimeOfDay == registrationInfo?.timeOfDay;
                    });
                    availableOrderNoTimeList?.forEach((t) => {
                        timeStrList.push({
                            cont: `${registrationInfo?.getDisplayOrderNumber(t?.orderNo)} ${t?.start ?? ""} ~ ${t?.end ?? ""}`,
                            type: t.type,
                            residueCount: "",
                        });
                    });
                } else {
                    timeStrList.push({
                        cont: `${registrationInfo?.displayOrderNumber} ${
                            (registrationInfo?._registrationReserveTime?.start || registrationInfo?.reserveTime?.start) ?? ""
                        } ~ ${(registrationInfo?._registrationReserveTime?.end || registrationInfo?.reserveTime?.end) ?? ""}`,
                    });
                }
            }
        } else {
            timeStrList.push({
                cont: `${registrationInfo?.displayOrderNumber} ${
                    (registrationInfo?._registrationReserveTime?.start || registrationInfo?.reserveTime?.start) ?? ""
                } ~ ${(registrationInfo?._registrationReserveTime?.end || registrationInfo?.reserveTime?.end) ?? ""}`,
            });
        }
        if (!timeStrList.length) return;
        const resultIndex = await showBottomSheet<number | undefined>(
            <FixedSourceTime
                dataList={[]}
                list={timeStrList}
                initialIndex={initialIndex}
                enableLeaveForMember={this.innerState.enableLeaveForMember}
                enableLeaveForPC={this.innerState.enableLeaveForPC}
            />
        );
        if (_.isUndefined(resultIndex) || resultIndex < 0) return;
        if (!_.isEmpty(availableOrderNoTimeList)) {
            const result = availableOrderNoTimeList?.[resultIndex];
            this.innerState.detail!.registrationFormItem!.orderNo = result?.orderNo;
            this.innerState.detail!.registrationFormItem!.timeOfDay = result?.timeOfDay;
            this.innerState.detail!.registrationFormItem!.orderNoTimeOfDay = result?.orderNoTimeOfDay;
            this.innerState.detail!.registrationFormItem!.reserveTime = {
                start: result?.start,
                end: result?.end,
            };
            this.innerState.detail!.registrationFormItem._registrationReserveTime =
                this.innerState.detail!.registrationFormItem!.reserveTime;
        }
        this.innerState.hasChange = true;
        this.update();
    }
    @actionEvent(_EventModifyRegistrationVisitSource)
    private async *_mapEventModifyRegistrationVisitSource(): AsyncGenerator<State> {
        this.innerState.hasChange = false;
        const detail = this.innerState.detail;
        const result = await PatientSourceTypeSelectDialog.show({
            source: JsonMapper.deserialize(PatientSource, {
                id: detail?.visitSourceId,
                name: detail?.visitSourceName, // 来源方式
                sourceFrom: detail?.visitSourceFrom,
                sourceFromName: detail?.visitSourceFromName, // 患者来源-姓名
            }),
        });
        const params = JsonMapper.deserialize(VisitSourceBaseInfo, {
            visitSourceId: result?.id,
            visitSourceName: result?.name,
            visitSourceFrom: result?.sourceFrom,
            visitSourceFromName: result?.sourceFromName,
        });
        const reps = await RegistrationUtils.formatVisitSource(params);
        if (result) {
            this.innerState.detail!.visitSourceId = reps.visitSourceId;
            this.innerState.detail!.visitSourceName = reps.visitSourceName;
            this.innerState.detail!.visitSourceFrom = reps.visitSourceFrom;
            this.innerState.detail!.visitSourceFromName = reps.visitSourceFromName;
        }
        //初始化传入的值与更改后的值比较，相同则无需调接口
        const beforeVisitSource = !!this.innerState.cloneDetail?.__visitSourceDisplayName
            ? this.innerState.cloneDetail?.__visitSourceDisplayName
            : "不指定";
        const nowVisitSource = !!this.innerState.detail?.__visitSourceDisplayName
            ? this.innerState.detail?.__visitSourceDisplayName
            : "不指定";
        if (beforeVisitSource != nowVisitSource) {
            this.innerState.hasChange = true;
        }
        this.update();
    }
    @actionEvent(_EventModifyRegistrationVisitSourceRemark)
    private async *_mapEventModifyRegistrationVisitSourceRemark(event: _EventModifyRegistrationVisitSourceRemark): AsyncGenerator<State> {
        this.innerState.hasChange = false;
        const result = await showBottomPanel<string>(
            <DentistryRemarkInputPage remark={event.remark} registrationType={RegistrationType.outpatientRegistration} />,
            {
                topMaskHeight: Sizes.dp160,
            }
        );
        if (_.isUndefined(result)) return;
        this.innerState.detail!.visitSourceRemark = result;
        if (this.innerState.cloneDetail?.visitSourceRemark != event.remark) {
            this.innerState.hasChange = true;
        }
        this.update();
    }
    @actionEvent(_EventModifyFixedTimeRange)
    async *_mapEventModifyFixedTimeRange(): AsyncGenerator<State> {
        const list = DefaultRegistrationTimeOptions.filter((t) => t.title != "指定时间");
        const timeIndex = list?.findIndex((item) => {
            return item.timeOfDay == this.innerState.detail?.registrationFormItem?.timeOfDay; // 当日时间段
        });
        const options = list;
        const flexibleTimeList: { cont: string; type?: number }[] = [];
        options?.map((item) => {
            flexibleTimeList.push({
                cont: item.timeOfDay,
            });
        });
        const resultIndex = await showBottomSheet<number | undefined>(
            <FixedSourceTime dataList={[]} list={flexibleTimeList} initialIndex={timeIndex} />
        );
        if (_.isUndefined(resultIndex)) return;
        this.innerState.detail!.registrationFormItem!.timeOfDay = options[resultIndex!].timeOfDay;
        this._matchReserveTime();

        this.innerState.hasChange = true;
        this.update();
    }

    @actionEvent(_EventCancelPayment)
    async *_mapEventCancelPayment(): AsyncGenerator<State> {
        // 进行解锁
        const chargePayTransactionId = this.innerState.detail?.chargeSheet?.lockPayTransactionInfo?.id;
        if (!chargePayTransactionId) return;
        const tipsInfo = {
            confirmText: "",
            errorTitle: "",
            errorContent: "",
        };
        if (this.innerState.detail?.chargeSheet?.microclinicsOrSelfServiceMachines) {
            tipsInfo.confirmText = "您可取消本次支付，取消后患者自助支付将会失败，本单可以重新收费。";
            tipsInfo.errorTitle = "收费单内容已更新";
            tipsInfo.errorContent = "收费单内容已更新，请刷新后重试";
        } else {
            tipsInfo.confirmText = "您可取消本次支付，取消后可重新收费，本次支付不入账。";
            tipsInfo.errorTitle = "取消失败";
            tipsInfo.errorContent = "患者已完成支付，如需取消请操作退费";
        }
        const dialogIndex = await showQueryDialog(
            "是否取消本次支付？",
            <View style={ABCStyles.rowAlignCenter}>
                <Text style={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}>{tipsInfo?.confirmText}</Text>
            </View>,
            "取消本次支付",
            "暂不操作"
        );
        if (!dialogIndex) return;
        await ChargeAgent.putChargeUnlock(chargePayTransactionId)
            .then((rsp) => {
                if (rsp.code == 200) {
                    Toast.show("取消成功", { success: true });
                }
            })
            .catch((error) => {
                showConfirmDialog(tipsInfo?.errorTitle, `${error?.msg ?? tipsInfo?.errorContent}`, "知道了");
            })
            .finally(() => {
                this._registrationInvoiceDetailTrigger.next({ id: this.id!, isInit: true });
            });
    }
    async getShebaoSettlementExceptionList(): Promise<void> {
        // 如果医保结算异常，需要再拉取接口判断异常类型
        if (this.innerState.detail?.chargeSheet?.isSheBaoAbnormal && !!this.innerState.detail?.chargeSheet?.id) {
            this.innerState.settlementExceptionList = await ChargeAgent.getChargeSettlementExceptionList(
                this.innerState.detail.chargeSheet.id
            ).catchIgnore();
        }
    }
    async _initVisitSource(): Promise<void> {
        const detail = this.innerState.detail;
        const params = JsonMapper.deserialize(VisitSourceBaseInfo, {
            visitSourceId: detail?.visitSourceId,
            visitSourceName: detail?.visitSourceName,
            visitSourceFrom: detail?.visitSourceFrom,
            visitSourceFromName: detail?.visitSourceFromName,
        });
        const reps = await RegistrationUtils.formatVisitSource(params);
        _.assign(this.innerState.detail, reps);
    }
    private initialRevisitStatus(): void {
        this._patientRevisitStatusTrigger.next();
    }

    /**
     * 初复诊挂号费用
     * @private
     */
    private registerFeeCalculation(): void {
        const registrationFormItem = this.innerState.detail?.registrationFormItem;

        RegistrationAgent.getRegistrationReturnVisitFee({
            doctorId: registrationFormItem?.doctorId ? registrationFormItem?.doctorId : "",
            departmentId: registrationFormItem?.departmentId ? registrationFormItem?.departmentId : "",
        })
            .toObservable()
            .subscribe(
                (rsp) => {
                    this.innerState.doctorFee = rsp;
                    // 短期内再次就诊挂号费不同模式
                    if (this.innerState.isShortRevisitsDifferent) {
                        const regUnitPrice = this.innerState.doctorFee.regUnitPrice ?? 0; // 初诊诊费
                        const revisitedRegUnitPrice = this.innerState.doctorFee.revisitedRegUnitPrice ?? 0; // 复诊诊费
                        const _regUnitPrice = this.innerState.isFirstRevisitWithinDays({
                            departmentId: registrationFormItem?.departmentId ?? "",
                            doctorId: this._doctorId ?? "",
                        })
                            ? regUnitPrice
                            : revisitedRegUnitPrice;
                        this.innerState.pay!.fee = _regUnitPrice;
                        this.innerState.detail!.chargeSheet!.chargeForms![0].chargeFormItems![0].sourceUnitPrice = _regUnitPrice;
                        this.innerState.detail!.chargeSheet!.chargeForms![0].chargeFormItems![0].unitPrice = _regUnitPrice;
                        this._calculatePriceTrigger.next();
                    }
                    // 初诊诊费
                    else if (this.innerState.detail!.revisitStatus == RegistrationRevisitStatus.first) {
                        const regUnitPrice = this.innerState.doctorFee.regUnitPrice ?? 0; // 初诊诊费
                        this.innerState.pay!.fee = regUnitPrice;
                        this.innerState.detail!.chargeSheet!.chargeForms![0].chargeFormItems![0].sourceUnitPrice = regUnitPrice;
                        this.innerState.detail!.chargeSheet!.chargeForms![0].chargeFormItems![0].unitPrice = regUnitPrice;
                        this._calculatePriceTrigger.next();
                    }
                    // 复诊诊费
                    else if (this.innerState.detail!.revisitStatus == RegistrationRevisitStatus.again) {
                        const revisitedRegUnitPrice = this.innerState.doctorFee.revisitedRegUnitPrice ?? 0; // 复诊诊费
                        this.innerState.pay!.fee = revisitedRegUnitPrice;
                        this.innerState.detail!.chargeSheet!.chargeForms![0].chargeFormItems![0].sourceUnitPrice = revisitedRegUnitPrice;
                        this.innerState.detail!.chargeSheet!.chargeForms![0].chargeFormItems![0].unitPrice = revisitedRegUnitPrice;
                        this._calculatePriceTrigger.next();
                    }
                    this.update();
                },
                (error) => {
                    if (error.code === 401) {
                        return;
                    }
                }
            )
            .addToDisposableBag(this);
    }

    //获取当前就诊时间
    private getReserveTime(): void {
        if (!!this.innerState.detail?.registrationFormItem?.timeOfDay) {
            const defaultReserve = DefaultRegistrationTimeOptions.find(
                (item) => item.timeOfDay == this.innerState.detail?.registrationFormItem?.timeOfDay
            );
            this.innerState.detail!.registrationFormItem!.reserveTime = defaultReserve?.range;
        } else {
            const hours = new Date().getHours();
            let defaultReserve;
            if (hours >= 0 && hours < 12) {
                defaultReserve = DefaultRegistrationTimeOptions[0];
            } else if (hours >= 12 && hours < 18) {
                defaultReserve = DefaultRegistrationTimeOptions[1];
            } else {
                defaultReserve = DefaultRegistrationTimeOptions[2];
            }
            this.innerState.detail!.registrationFormItem!.reserveTime = defaultReserve?.range;
        }
    }
    /**
     * 初始选中科室医生
     * @private
     */
    private _initSelectedDepartmentDoctor(): void {
        this.innerState.detail = this.innerState.detail ?? new RegistrationDetail();
        const registrationFormItem = (this.innerState.detail.registrationFormItem = JsonMapper.deserialize(
            RegistrationFormItem,
            this.innerState.detail.registrationFormItem
        ));
        if (this._doctorId) {
            for (const department of this.innerState.departments ?? []) {
                if (!!registrationFormItem.departmentId && department.departmentId != registrationFormItem.departmentId) return;
                const doctor = department.doctors?.find((doctor) => doctor?.doctorId == this._doctorId);
                if (!!doctor) {
                    this.innerState.detail!.registrationFormItem!.departmentId = department.departmentId;
                    this.innerState.detail!.registrationFormItem!.departmentName = department.departmentName;
                    this.innerState.currentDepartmentDoctors = department.doctors;
                    this.innerState.currentSelectedDoctors = doctor;
                    break;
                }
            }
        } else if (!!this.innerState.detail.registrationFormItem?.doctorId) {
            for (const department of this.innerState.departments ?? []) {
                if (!!registrationFormItem.departmentId && department.departmentId != registrationFormItem.departmentId) return;
                const doctor = department.doctors?.find(
                    (doctor) => doctor?.doctorId == this.innerState.detail?.registrationFormItem?.doctorId
                );
                if (!!doctor) {
                    this.innerState.detail!.registrationFormItem!.departmentId = department.departmentId;
                    this.innerState.detail!.registrationFormItem!.departmentName = department.departmentName;
                    this.innerState.detail!.registrationFormItem!.doctorId = doctor.doctorId;
                    this.innerState.detail!.registrationFormItem!.doctorName = doctor.doctorName;
                    this.innerState.currentDepartmentDoctors = department.doctors;
                    this.innerState.currentSelectedDoctors = doctor;
                    break;
                }
            }
        } else {
            this.innerState.detail!.registrationFormItem!.departmentId = _.first(this.innerState.departments)?.departmentId;
            this.innerState.detail!.registrationFormItem!.departmentName = _.first(this.innerState.departments)?.departmentName;
            this.innerState.currentDepartmentDoctors = _.first(this.innerState.departments)?.doctors;
        }
    }

    /**
     * 预约--匹配对应日期时段的号源
     * @private
     */
    private _matchReserveTime(): void {
        this.innerState.detail = this.innerState.detail ?? new RegistrationDetail();
        const registrationFormItem = (this.innerState.detail.registrationFormItem = JsonMapper.deserialize(
            RegistrationFormItem,
            this.innerState.detail.registrationFormItem
        ));
        this.innerState.selectOrderNoTimeList =
            this.innerState.registrationTimeRangeList?.registrationCategoryScheduleIntervals?.flatMap((item) => {
                return item.scheduleIntervals?.filter((t) => t.timeOfDay == registrationFormItem?.timeOfDay) ?? [];
            }) ?? [];
        if (!!this.innerState.selectOrderNoTimeList?.length) {
            const _cloneSelectOrderNoTimeList = _.cloneDeep(this.innerState.selectOrderNoTimeList);
            _cloneSelectOrderNoTimeList?.map((item) => {
                return (item.list ?? [])
                    ?.map((sub) => {
                        if (!this.innerState.hasChange && sub.orderNo == registrationFormItem?.orderNo) sub.available = 1;
                        return sub;
                    })
                    ?.filter((k) => k.available == 1);
            });
            const existTimeInfo = _cloneSelectOrderNoTimeList.find((item) => {
                return item.list?.some(
                    (sub) => sub.orderNo == registrationFormItem?.orderNo && sub.timeOfDay == registrationFormItem?.timeOfDay
                );
            });
            if (!!existTimeInfo) {
                for (const item of _cloneSelectOrderNoTimeList) {
                    const matchItem = item.list?.find((t) => t.orderNo == registrationFormItem?.orderNo);
                    if (matchItem) {
                        registrationFormItem.reserveTime = {
                            start: this.innerState.isAccuratePart ? matchItem?.start : item?.start,
                            end: this.innerState.isAccuratePart ? matchItem?.end : item?.end,
                        };
                        break;
                    }
                }
            } else {
                if (!this.innerState.hasChange) return;
                const firstAvailableTime = this.innerState.selectOrderNoTimeList
                    ?.flatMap((item) => item.list)
                    ?.filter((t) => !!t?.available)?.[0];
                if (!!firstAvailableTime) {
                    registrationFormItem.reserveTime = {
                        start: this.innerState.isAccuratePart
                            ? firstAvailableTime?.start
                            : this.innerState.selectOrderNoTimeList?.[0]?.start,
                        end: this.innerState.isAccuratePart ? firstAvailableTime?.end : this.innerState.selectOrderNoTimeList?.[0]?.end,
                    };
                    registrationFormItem.orderNo = firstAvailableTime?.orderNo;
                } else {
                    registrationFormItem.reserveTime = undefined;
                    registrationFormItem.orderNo = undefined;
                }
            }
        } else {
            if (!this.innerState.hasChange) return;
            registrationFormItem.reserveTime = undefined;
            registrationFormItem.orderNo = undefined;
        }
    }
    /**
     * 修改患者信息
     */
    requestModifyRegistrationPatient(patient: Patient): void {
        this.dispatch(new _EventModifyRegistrationPatient(patient));
    }
    //根据异常类型，做相应的处理
    requestHandleChargeAbnormal(isShebaoAbnormal: boolean): void {
        this.dispatch(new _EventHandleChargeAbnormal(isShebaoAbnormal));
    }
    //异常退费
    requestAbnormalRefund(abnormalMsg: AbnormalTransactionList): void {
        this.dispatch(new _EventAbnormalRefund(abnormalMsg));
    }
    /**
     * 修改挂号类型
     */
    requestModifyRegistrationType(index: number): void {
        this.dispatch(new _EventModifyRegistrationType(index));
    }
    /**
     * 修改挂号科室
     */
    requestModifyRegistrationDepartment(): void {
        this.dispatch(new _EventModifyRegistrationDepartment());
    }
    /**
     * 修改挂号医生
     */
    requestModifyRegistrationDoctor(): void {
        this.dispatch(new _EventModifyRegistrationDoctor());
    }
    /**
     * 修改挂号时间
     */
    requestModifyRegistrationTime(): void {
        this.dispatch(new _EventModifyRegistrationTime());
    }
    /**
     * 修改挂号诊费
     */
    requestModifyRegistrationFee(fee: string): void {
        this.dispatch(new _EventModifyRegistrationFee(Number(fee)));
    }
    /**
     * 切换初诊/复诊
     */
    requestModifyRevisitStatus(): void {
        this.dispatch(new _EventModifyRevisitStatus());
    }
    /**
     * 更新优惠项
     */

    requestUpdatePromotions(
        selectPromotions?: number[],
        selectGiftPromotions?: number[],
        selectCouponPromotions?: AbcMap<CouponPromotion, Pair<boolean, number>>,
        selectMemberPointPromotion?: PatientPointsInfo
    ): void {
        this.dispatch(
            new _EventUpdatePromotions(selectPromotions, selectGiftPromotions, selectCouponPromotions, selectMemberPointPromotion)
        );
    }
    /**
     * 更换会员卡信息
     * @param member
     */
    requestUpdateMemberCard(member: SelectMemberInfo): void {
        this.dispatch(new _EventUpdateMemberCard(member));
    }
    /**
     * 修改使用卡项
     * @param cardPromotions
     */
    requestPatientCardPromotionChanged(cardPromotions: PatientCardPromotion[]): void {
        this.dispatch(new _EventPatientCardPromotion(cardPromotions));
    }

    /**
     * 修改预诊信息
     * @param medicalRecord
     */
    requestModifyMedicalRecord(medicalRecord: MedicalRecord): void {
        this.dispatch(new _EventModifyMedicalRecordCard(medicalRecord));
    }

    //操作禁止提示
    private showOperateToastTip(): void {
        const isDisabledOperate = this.innerState.detail?.chargeSheet?.isDisabledOperate;
        const disabledOperateReason = this.innerState.detail?.chargeSheet?.disabledOperateReason;
        if (!!isDisabledOperate && !!disabledOperateReason) Toast.show(disabledOperateReason, { warning: true });
    }
    /**
     * 退费
     */
    requestRefundedRegistrationFee(): void {
        this.showOperateToastTip();
        const chargeSheet = this.innerState.detail?.chargeSheet;
        if (!chargeSheet?.canEditChargeSheet) return;
        this.dispatch(new _EventRefundedRegistrationFee());
    }
    /**
     * 退号
     */
    requestRefundedRegistration(): void {
        this.dispatch(new _EventRefundedRegistration());
    }
    /**
     * 挂号单收费
     */
    requestRegistrationCharge(): void {
        this.dispatch(new _EventRegistrationCharge());
    }
    /**
     * 回诊
     */
    requestBackDirectRegistration(): void {
        this.dispatch(new _EventBackDirectRegistration());
    }
    /**
     * 签到
     */
    requestRegistrationSignIn(): void {
        this.dispatch(new _EventRegistrationSignIn());
    }

    /**
     * 完成挂号
     */
    requestFinishRegistration(): void {
        this.dispatch(new _EventFinishRegistration());
    }

    /**
     * 保存挂号修改信息
     */
    requestSaveRegistration(): void {
        this.dispatch(new _EventSaveRegistration());
    }
    /**
     * 更新患者信息
     */
    requestUpdatePatient(patient: Patient): void {
        this.dispatch(new _EventUpdatePatient(patient));
    }

    /**
     * 优惠明细弹窗
     */
    requestDiscountDetail(): void {
        this.dispatch(new _EventDiscountDetail());
    }
    /**
     * 更新就诊相关信息
     */
    requestUpdateRegistrationInfo(): void {
        this.dispatch(new _EventUpdateRegistrationInfo());
    }
    /**
     * 完成预诊
     */
    requestCompletePreDiagnosis(): void {
        this.dispatch(new _EventCompletePreDiagnosis());
    }
    requestModifyOrderNo(): void {
        this.dispatch(new _EventModifyOrderNo());
    }
    /**
     * 修改就诊推荐来源
     */
    requestModifyRegistrationVisitSource(): void {
        this.dispatch(new _EventModifyRegistrationVisitSource());
    }

    /**
     * 修改就诊推荐备注
     */
    requestModifyRegistrationVisitSourceRemark(text: string): void {
        this.dispatch(new _EventModifyRegistrationVisitSourceRemark(text));
    }
    /**
     * 预约类型下时间段
     */
    requestModifyFixedTimeRange(): void {
        this.dispatch(new _EventModifyFixedTimeRange());
    }
    //  取消本次支付
    requestCancelPayment(): void {
        this.dispatch(new _EventCancelPayment());
    }
}
