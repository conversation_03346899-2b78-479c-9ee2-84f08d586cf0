/**
 * create by dengjie
 * desc:
 * create date 2021/1/6
 */

import { Bloc, BlocEvent } from "../bloc";
import React from "react";
import { actionEvent, EventName } from "../bloc/bloc";
import { BaseLoadingState } from "../bloc/bloc-helper";
import { of, Subject } from "rxjs";
import {
    GetRegistrationKanbanDetailRsp,
    GetRegistrationListRsp,
    GetRegistrationsDoctorList,
    RegistrationAgent,
    RegistrationsDoctorListItem,
} from "./data/registration-agent";
import { ABCError } from "../common-base-module/common-error";
import {
    DEFAULT_DOCTOR_ID,
    RegistrationDetail,
    RegistrationDoctorEnableCategories,
    RegistrationFormItem,
    RegistrationStatusV2,
    RegistrationTabType,
} from "./data/bean";
import { Filters } from "../base-ui/searchBar/search-bar-bean";
import { debounce, switchMap } from "rxjs/operators";
import { FilterUtils } from "../base-ui/searchBar/filter-utils";
import { CommonFilterId } from "../base-ui/searchBar/search-bar";
import { delayed } from "../common-base-module/rxjs-ext/rxjs-ext";
import { TherapyAppointmentAgent } from "./appointment/data/therapy-appointment-agent";
import { userCenter } from "../user-center";
import { DentistryAgent } from "./dentistry/data/dentistry-agent";
import { DentistryConfig } from "./dentistry/data/bean";
import TimeUtils from "../common-base-module/utils/time-utils";
import moment from "moment";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { AppointmentInvoicePage } from "./dentistry/appointment/appointment-invoice-page";
import { DentistryInvoicePage } from "./dentistry/dentistry-invoice-page";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { RegistrationKanBanViewV3ScrollToTrigger } from "./views/registrationKanBanView";
import { showOptionsBottomSheet } from "../base-ui/dialog/bottom_sheet";
import { Toast } from "../base-ui/dialog/toast";
import { ClinicAgent, EmployeesMeConfig } from "../base-business/data/clinic-agent";

const limit = 20;

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventReload extends _Event {}

class _EventLoadMore extends _Event {}

class _EventChangeTab extends _Event {}

class _EventSearchPatient extends _Event {
    keyword: string;

    constructor(keyword: string) {
        super();
        this.keyword = keyword;
    }
}

class _EventChangeFilter extends _Event {
    filter: Filters;

    constructor(filter: Filters) {
        super();
        this.filter = filter;
    }
}

class _EventModifyDoctorParams extends _Event {
    params: { doctorId: string; startTime?: Date; endTime?: Date };
    constructor(params: { doctorId: string; startTime?: Date; endTime?: Date }) {
        super();
        this.params = params;
    }
}

class _EventChangeViewMode extends _Event {
    isKanban: boolean;
    constructor(isKanban: boolean) {
        super();
        this.isKanban = isKanban;
    }
}

class _EventKanbanWeekModify extends _Event {
    idx: number;
    constructor(idx: number, callback?: () => void) {
        super();
        this.idx = idx;
        this.callback = callback;
    }

    callback?(): void;
}

class _EventAddInvoiceDetail extends _Event {}
class _EventModifyRegistrationStatus extends _Event {
    registrationDetail: RegistrationDetail;
    constructor(detail: RegistrationDetail) {
        super();
        this.registrationDetail = detail;
    }
}

export class State extends BaseLoadingState {
    dataList: Array<RegistrationDetail> = [];
    lastSearchRsp?: GetRegistrationListRsp;

    viewMode = false; // false：列表视图，true：看板视图
    kanbanDetail?: GetRegistrationKanbanDetailRsp;

    keyword?: string;
    id?: string;
    name?: string;

    queryParams: {
        displayStatus?: number;
        date?: Date;
        end?: Date;
        doctorId?: string;
    } = { displayStatus: undefined, date: new Date(), doctorId: undefined };

    doctorListRsp?: GetRegistrationsDoctorList;

    clone(): State {
        return Object.assign(new State(), this);
    }

    get hasMore(): boolean {
        if (!this.lastSearchRsp) return true;
        const { totalCount, offset, rows, limit } = this.lastSearchRsp;
        // return rows?.length == limit;
        //后台返回的totalCount有问题
        //暂时使用上面的判断逻辑
        // return (totalCount ?? 0) > (offset ?? 0) + (rows?.length ?? 0);

        return !this.lastSearchRsp || ((rows?.length ?? 0) == (limit ?? 0) && (offset ?? 0) + (rows?.length ?? 0) < (totalCount ?? 0));
    }

    get doctorList(): RegistrationsDoctorListItem[] {
        return this.doctorListRsp?.rows ?? [];
    }

    //门诊挂号、理疗预约的预约设置
    registrationConfig?: DentistryConfig;
    //当前门店号种使用情况
    clinicRegistrationCategories?: RegistrationDoctorEnableCategories;
    employeesMeConfig?: EmployeesMeConfig;
}

export class FilterModifyState extends State {
    static fromState(state: State): FilterModifyState {
        return Object.assign(new FilterModifyState(), state);
    }
}

export class RegistrationListViewBloc extends Bloc<_Event, State> {
    static Context = React.createContext<RegistrationListViewBloc | undefined>(undefined);

    private _loadRegistrationTrigger: Subject<number> = new Subject<number>();
    private _loadRegistrationKanbanTrigger: Subject<{ idx: number; callback?: () => void }> = new Subject();
    private _loadRegistrationDoctorTrigger: Subject<number> = new Subject<number>();
    private _pageType?: number;
    private _searchMode?: boolean;
    lastFilterDoctorInfo?: RegistrationsDoctorListItem;

    private _willScrollToRegId?: string;

    static fromContext(context: RegistrationListViewBloc): RegistrationListViewBloc {
        return context;
    }

    constructor(options: { type?: number; searchMode?: boolean; viewMode?: boolean }) {
        super();

        this._pageType = options.type;
        this._searchMode = options.searchMode;
        this.innerState.viewMode = options.viewMode ?? false;
        this.dispatch(new _EventInit()).then();
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        return new Map<EventName, Function>();
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    public requestModifyDoctorParams(params: { doctorId: string; startTime?: Date; endTime?: Date }): void {
        this.dispatch(new _EventModifyDoctorParams(params));
    }

    private _resetSearchData(): void {
        this.innerState.lastSearchRsp = undefined;
    }

    @actionEvent(_EventUpdate)
    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    @actionEvent(_EventReload)
    private async *_mapEventReload(): AsyncGenerator<State> {
        if (this.innerState.loading) return;
        this._resetSearchData();
        this.innerState.dataList = [];
        this.innerState.lastSearchRsp = undefined;
        this.innerState.startLoading();
        if (!userCenter.clinic?.isDentistryClinic) {
            yield this.innerState;
        }
        this._loadRegistrationTrigger.next();
    }

    @actionEvent(_EventLoadMore)
    private async *_mapEventLoadMore(): AsyncGenerator<State> {
        if (this.innerState.loading) return;
        this._loadRegistrationTrigger.next();
    }

    @actionEvent(_EventSearchPatient)
    private async *_mapEventSearchPatient(event: _EventSearchPatient): AsyncGenerator<State> {
        this.innerState.dataList = [];
        this.innerState.lastSearchRsp = undefined;
        this.innerState.keyword = event?.keyword?.trim();
        this._loadRegistrationTrigger.next();
    }

    public requestChangeViewMode(isKanban: boolean): void {
        this.dispatch(new _EventChangeViewMode(isKanban));
    }

    @actionEvent(_EventModifyDoctorParams)
    private async *_mapModifyDoctorParams(event: _EventModifyDoctorParams): AsyncGenerator<State> {
        this.innerState.queryParams.doctorId = event.params.doctorId;
        //保存当前筛选医生详情，供下次使用
        this.lastFilterDoctorInfo = this.innerState.doctorList.find((item) => item.id == event.params.doctorId);

        this.innerState.queryParams.date = event.params.startTime;
        this.innerState.queryParams.end = event.params.endTime;
        this._loadRegistrationDoctorTrigger.next();
    }

    public requestKanbanWeekModify(idx: number, callback?: () => void): void {
        this.dispatch(new _EventKanbanWeekModify(idx, callback));
    }

    public requestReload(): void {
        this.dispatch(new _EventReload()).then();
    }

    public requestLoadMore(): void {
        this.dispatch(new _EventLoadMore()).then();
    }

    public requestChangeTabIndex(): void {
        this.dispatch(new _EventChangeTab()).then();
    }

    public requestSearchPatient(keyword: string): void {
        this.dispatch(new _EventSearchPatient(keyword)).then();
    }

    public requestChangeFilter(filter: Filters): void {
        this.dispatch(new _EventChangeFilter(filter)).then();
    }

    public requestAddInvoiceDetail(): void {
        this.dispatch(new _EventAddInvoiceDetail());
    }

    public requestModifyRegistrationStatus(detail: RegistrationDetail): void {
        this.dispatch(new _EventModifyRegistrationStatus(detail));
    }

    @actionEvent(_EventKanbanWeekModify)
    async *_mapEventKanbanWeekModify(event: _EventKanbanWeekModify): AsyncGenerator<State> {
        this._loadRegistrationKanbanTrigger.next({ idx: event.idx, callback: event.callback });
    }

    @actionEvent(_EventChangeViewMode)
    private async *_mapEventChangeViewMode(event: _EventChangeViewMode): AsyncGenerator<State> {
        this.innerState.viewMode = event.isKanban;

        //触发加载看板/列表数据
        this._loadRegistrationTrigger.next();
        yield this.innerState.clone();
        //立即更新视图
    }

    @actionEvent(_EventChangeFilter)
    private async *_mapEventChangeFilter(event: _EventChangeFilter): AsyncGenerator<State> {
        this.innerState.startLoading();
        yield this.innerState;
        this.innerState.dataList = [];
        this.innerState.kanbanDetail = undefined;
        this.innerState.lastSearchRsp = undefined;
        //从预约日报进入,进行条件筛选时，如果不是当天（今天），在没改变日期的情况下，筛选状态、医生，会导致筛选的是当天日期，页面显示的还是之前的日期
        let isKeepLastTime = false;
        //在date不存在的情况下，id与title不匹配
        if (!event.filter.filters?.[0].filters?.[0].date) {
            if (
                event.filter.filters?.[0].filters?.[0].id == CommonFilterId.timeToday &&
                event.filter.filters?.[0].filters?.[0].title != "今天"
            ) {
                isKeepLastTime = true;
            }
        }

        const _oldTime = this.innerState.queryParams.date;

        this.innerState.queryParams!.date =
            event.filter.filters?.[0].filters?.[0].date ??
            (isKeepLastTime ? this.innerState.queryParams.date : FilterUtils.filterId2Time(event.filter.filters?.[0].filters?.[0].id));
        this.innerState.queryParams.end = this.innerState.queryParams.date;

        this.innerState.queryParams!.displayStatus =
            (event.filter.filters[1].filters?.[0].id ?? CommonFilterId.allStatus) - CommonFilterId.allStatus;

        event.filter.filters[2].filters = event.filter.filters[2].filters?.filter((item) => !!item);
        this.innerState.queryParams!.doctorId =
            event.filter.filters[2].filters?.[0]?.info?.id ?? event.filter.filters[2].filters?.[0]?.info?.doctorId ?? "";
        this.lastFilterDoctorInfo = this.innerState.doctorList.find((item) => item.id == this.innerState.queryParams.doctorId);

        if (!moment(this.innerState.queryParams.date).isSame(_oldTime, "day")) {
            //保存当前筛选医生详情，供下次使用
            //修改时间需要清空搜索人员
            // this.innerState.queryParams!.doctorId = "";
            const rsp = await RegistrationAgent.getRegistrationsDoctorList(
                this.innerState.queryParams.date,
                userCenter.isAllowedRegUpgrade ? this._pageType : 0
            ).catchIgnore();
            if (rsp) {
                if (!!this.lastFilterDoctorInfo) {
                    const hasSame = rsp.rows?.find((item) => item.id == this.lastFilterDoctorInfo?.id);
                    if (!hasSame) {
                        rsp.rows?.unshift(this.lastFilterDoctorInfo);
                    }
                }
                this.innerState.doctorListRsp = rsp;
                // this.update(FilterModifyState.fromState(this.innerState));
            }
        }
        //解决筛选状态后，页面未及时更新问题
        this.update(FilterModifyState.fromState(this.innerState));

        this._loadRegistrationTrigger.next();
    }

    @actionEvent(_EventInit)
    private async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        //获取预约设置
        if (userCenter.isAllowedRegUpgrade) {
            this.innerState.registrationConfig = await DentistryAgent.queryDentistryRegistrationConfig(this._pageType, false).catchIgnore();
            this.innerState.clinicRegistrationCategories = await RegistrationAgent.getRegistrationDoctorEnableCategories().catchIgnore();
            this.innerState.employeesMeConfig = await ClinicAgent.getEmployeesMeConfig().catchIgnore(); //当前员工门店设置
        }
        this._loadRegistrationTrigger
            .pipe(
                debounce(() => {
                    if (this.innerState.lastSearchRsp) return of(0);
                    return delayed(500);
                }),
                switchMap(() => {
                    this.innerState.startLoading();
                    this.update();
                    if (this.innerState.viewMode && userCenter.isAllowedRegUpgrade) {
                        const { date, end, doctorId } = this.innerState.queryParams;
                        const isWeekMode = !!doctorId?.length;
                        // 如果当前时间「不指定医生」有已预约患者，展示
                        // 如果门店预约设置为「必须指定医生」，那么预约看板视图不展示「不指定医生」
                        const mustReserveEmployee = this.innerState.registrationConfig?.isMustReserveEmployee;
                        let _doctorList: string[] = mustReserveEmployee ? [] : [DEFAULT_DOCTOR_ID];
                        if (!!doctorId) {
                            _doctorList = [doctorId];
                        } else {
                            _doctorList = _doctorList.concat(this.innerState.doctorList.map((item) => item.id ?? ""));
                        }
                        // 去重
                        const setArr = new Set(_doctorList);
                        _doctorList = new Array(...setArr);
                        let _date = date ?? new Date();
                        let _end = end ?? _date;
                        if (isWeekMode) {
                            _date = new Date(_date.getTime() - TimeUtils.ONE_DAY * 10);
                            _end = new Date(_end.getTime() + TimeUtils.ONE_DAY * 7);
                        }
                        if (this.innerState.registrationConfig?.isFixedMode) {
                            return RegistrationAgent.getRegistrationFixedOrderKanbanDetail({
                                start: _date,
                                end: _end,
                                doctorIds: _doctorList,
                                registrationType: this._pageType ?? 0,
                            });
                        } else if (this.innerState.registrationConfig?.isFlexibleMode) {
                            return RegistrationAgent.getRegistrationFlexibleTimeKanbanDetail({
                                start: _date,
                                end: _end,
                                doctorIds: _doctorList,
                                registrationType: this._pageType ?? 0,
                            });
                        }
                    }
                    if (!this.innerState.hasMore || (!this.innerState.keyword?.length && this._searchMode)) return of(null);

                    let offset = 0;
                    if (this.innerState.lastSearchRsp) {
                        offset = (this.innerState.lastSearchRsp.offset ?? 0) + (this.innerState.lastSearchRsp.rows?.length ?? 0);
                    }

                    //口腔诊所的门诊挂号与开通了挂号升级诊所的门诊挂号与理疗预约
                    //全部状态时，displayStatus应该为undefined，否则预约日报进入的时候，筛选会出错
                    this.innerState.queryParams.displayStatus =
                        this.innerState.queryParams.displayStatus == 0 ? undefined : this.innerState.queryParams.displayStatus;
                    if (userCenter.isAllowedRegUpgrade) {
                        // 预约列表走的接口
                        return DentistryAgent.queryQuickList({
                            offset,
                            limit: limit,
                            registrationType: this._pageType ?? 0, //门诊传0，理疗传1
                            keyword: this.innerState.keyword,
                            ...this.innerState.queryParams,
                        })
                            .catch((e) => new ABCError(e))
                            .toObservable();
                    } else {
                        if (this._pageType == RegistrationTabType.registration) {
                            return RegistrationAgent.getRegistrationList({
                                offset,
                                limit: limit,
                                keyword: this.innerState.keyword,
                                ...this.innerState.queryParams,
                            })
                                .catch((e) => new ABCError(e))
                                .toObservable();
                        } else {
                            return TherapyAppointmentAgent.getTherapyPatientList({
                                offset,
                                limit: limit,
                                keyword: this._innerState.keyword,
                                ...this._innerState.queryParams,
                            })
                                .catch((e) => new ABCError(e))
                                .toObservable();
                        }
                    }
                })
            )
            .subscribe((rsp) => {
                this.innerState.stopLoading();
                if (!rsp) {
                    this.update();
                    return;
                }

                if (rsp instanceof ABCError) {
                    this.innerState.setLoadingError(rsp);
                } else if (rsp instanceof GetRegistrationKanbanDetailRsp) {
                    this.innerState.kanbanDetail = rsp;
                    if (!!this._willScrollToRegId) {
                        RegistrationKanBanViewV3ScrollToTrigger.next(this._willScrollToRegId);
                        this.update(FilterModifyState.fromState(this.innerState));
                    }
                } else {
                    //下拉刷新时，不清除已有的项目
                    if (!this.innerState.lastSearchRsp) {
                        this.innerState.dataList = [];
                    }
                    this.innerState.lastSearchRsp = rsp;
                    this.innerState.dataList = this.innerState.dataList.concat(rsp.rows ?? []);
                }
                this._willScrollToRegId = undefined;
                this.update();
            })
            .addToDisposableBag(this);

        this._loadRegistrationDoctorTrigger
            .pipe(
                switchMap(() => {
                    return RegistrationAgent.getRegistrationsDoctorList(
                        this.innerState.queryParams.date,
                        userCenter.isAllowedRegUpgrade ? this._pageType : 0
                    )
                        .catch((e) => new ABCError(e))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (rsp instanceof ABCError) {
                } else {
                    if (!!this.lastFilterDoctorInfo) {
                        const hasSame = rsp.rows?.find(
                            (item) => item.id == this.lastFilterDoctorInfo?.id || item.doctorId == this.lastFilterDoctorInfo?.doctorId
                        );
                        if (!hasSame) {
                            rsp.rows?.unshift(this.lastFilterDoctorInfo);
                        }
                    }
                    this.innerState.doctorListRsp = rsp;

                    this.update(FilterModifyState.fromState(this.innerState));
                }
            })
            .addToDisposableBag(this);

        this._loadRegistrationKanbanTrigger
            .pipe(
                switchMap((options) => {
                    const { idx, callback } = options;
                    const kanbanDetail = this.innerState.kanbanDetail;
                    const { date, end, doctorId } = this.innerState.queryParams;

                    const list = kanbanDetail?.dailyViews ?? kanbanDetail?.kanBanDailyViews ?? [];
                    let _date = date ?? new Date();
                    let _end = end ?? _date;
                    let isPush = false;
                    if (idx <= 3) {
                        // 滑动到前面快结束了
                        const _firstDetail = list[0];
                        _date = new Date(_firstDetail.reserveDate!.getTime() - TimeUtils.ONE_DAY * 6);
                        _end = new Date(_firstDetail.reserveDate!.getTime() - TimeUtils.ONE_DAY);
                    } else if (idx >= list.length - 7) {
                        // 滑动到后面快结束了
                        const _lastDetail = [...list].reverse()[0];
                        _date = new Date(_lastDetail.reserveDate!.getTime() + TimeUtils.ONE_DAY);
                        _end = new Date(_lastDetail.reserveDate!.getTime() + TimeUtils.ONE_DAY * 6);
                        isPush = true;
                    } else {
                        return of({ callback });
                    }

                    let _doctorList = [DEFAULT_DOCTOR_ID];
                    if (!!doctorId) {
                        _doctorList = [doctorId];
                    } else {
                        _doctorList = _doctorList.concat(this.innerState.doctorList.map((item) => item.id ?? ""));
                    }
                    // 去重
                    const setArr = new Set(_doctorList);
                    _doctorList = new Array(...setArr);
                    if (this.innerState.registrationConfig?.isFixedMode) {
                        return RegistrationAgent.getRegistrationFixedOrderKanbanDetail({
                            start: _date,
                            end: _end,
                            doctorIds: _doctorList,
                            registrationType: this._pageType ?? 0,
                        })
                            .then((rsp) => ({ detail: rsp, isPush, callback }))
                            .catch(() => ({ callback }));
                    } else if (this.innerState.registrationConfig?.isFlexibleMode) {
                        return RegistrationAgent.getRegistrationFlexibleTimeKanbanDetail({
                            start: _date,
                            end: _end,
                            doctorIds: _doctorList,
                            registrationType: this._pageType ?? 0,
                        })
                            .then((rsp) => ({ detail: rsp, isPush, callback }))
                            .catch(() => ({ callback }));
                    }
                    return of({ callback });
                })
            )
            .subscribe((rsp) => {
                const { detail, isPush, callback } = rsp as {
                    detail: GetRegistrationKanbanDetailRsp;
                    isPush: boolean;
                    callback?: (onlyChangeLoading?: boolean) => void;
                };
                if (!detail) {
                    callback?.(true);
                    return;
                }

                const dailyViewsList = this.innerState.kanbanDetail?.dailyViews;
                const kanBanDailyViewsList = this.innerState.kanbanDetail?.kanBanDailyViews;
                if (isPush) {
                    dailyViewsList?.push(...(detail.dailyViews ?? []));
                    kanBanDailyViewsList?.push(...(detail.kanBanDailyViews ?? []));
                } else {
                    dailyViewsList?.unshift(...(detail.dailyViews ?? []));
                    kanBanDailyViewsList?.unshift(...(detail.kanBanDailyViews ?? []));
                }
                // 手动整合列表
                this.innerState.kanbanDetail = {
                    notRefundedCount: (this.innerState.kanbanDetail?.notRefundedCount ?? 0) + (detail?.notRefundedCount ?? 0),
                    reserveTotalCount: (this.innerState.kanbanDetail?.reserveTotalCount ?? 0) + (detail?.reserveTotalCount ?? 0),
                    hadSignInCount: (this.innerState.kanbanDetail?.hadSignInCount ?? 0) + (detail?.hadSignInCount ?? 0),
                    waitingSignInCount: (this.innerState.kanbanDetail?.waitingSignInCount ?? 0) + (detail?.waitingSignInCount ?? 0),
                    waitingDiagnoseCount: (this.innerState.kanbanDetail?.waitingDiagnoseCount ?? 0) + (detail?.waitingDiagnoseCount ?? 0),
                    diagnosedCount: (this.innerState.kanbanDetail?.diagnosedCount ?? 0) + (detail?.diagnosedCount ?? 0),
                    refundedCount: (this.innerState.kanbanDetail?.refundedCount ?? 0) + (detail?.refundedCount ?? 0),
                    dailyViews: dailyViewsList,
                    kanBanDailyViews: kanBanDailyViewsList,
                };
                if (!isPush) {
                    callback?.();
                } else {
                    callback?.(true);
                }
                this.update();
            })
            .addToDisposableBag(this);

        if (!this._searchMode) {
            this.innerState.queryParams.date = new Date();
            this.innerState.doctorListRsp = await RegistrationAgent.getRegistrationsDoctorList(
                this.innerState.queryParams.date,
                userCenter.isAllowedRegUpgrade ? this._pageType : 0
            );
            this._loadRegistrationTrigger.next();

            RegistrationAgent.changeObserver
                .subscribe((rsp) => {
                    if (!!rsp) {
                        //挂号反写筛选条件
                        this._willScrollToRegId = rsp?.id;
                        const { doctorId, date } = this.innerState.queryParams;
                        if (!!doctorId) {
                            if (!!rsp.registrationFormItem?.doctorId) {
                                this.innerState.queryParams.doctorId = rsp.registrationFormItem.doctorId;
                            } else {
                                this.innerState.queryParams.doctorId = DEFAULT_DOCTOR_ID;
                            }
                        }
                        this.innerState.queryParams.date = moment(rsp.registrationFormItem?.reserveDate ?? date).toDate();
                        this.innerState.queryParams.end = undefined;
                    }
                    this.innerState.dataList = [];
                    this._loadRegistrationDoctorTrigger.next();
                    this.dispatch(new _EventReload()).then();
                })
                .addToDisposableBag(this);
            // 刷新签到/退号后理疗列表状态
            TherapyAppointmentAgent.changeObserver
                .subscribe(() => {
                    this.innerState.dataList = [];
                    this.dispatch(new _EventReload()).then();
                })
                .addToDisposableBag(this);
        } else {
            this.innerState.loading = false;
            this.innerState.queryParams!.date = undefined;
            this.innerState.queryParams!.end = undefined;
            this.update();
        }
    }

    @actionEvent(_EventAddInvoiceDetail)
    private async *_mapEventAddInvoiceDetail(/*_EventAddInvoiceDetail*/): AsyncGenerator<State> {
        const queryParams = this.innerState.queryParams;
        let registrationFormItem = undefined;
        const _date = moment(queryParams.date);
        const now = moment();
        if (!!queryParams.date && _date.isSameOrAfter(now, "day")) {
            let timeOfDay = "上午";
            let isReserved = 1;
            if (_date.isSame(now, "day")) {
                const _hour = _date.hour();
                if (_hour >= 0 && _hour < 12) {
                    timeOfDay = "上午";
                } else if (_hour >= 12 && _hour < 18) {
                    timeOfDay = "下午";
                } else if (_hour >= 18 && _hour < 24) {
                    timeOfDay = "晚上";
                }
                isReserved = 0;
            }
            registrationFormItem = JsonMapper.deserialize(RegistrationFormItem, {
                reserveDate: queryParams.date,
                timeOfDay: timeOfDay,
                isReserved: isReserved,
                doctorId: queryParams.doctorId,
            });
        }
        if (this._pageType == RegistrationTabType.registration) {
            //挂号跳转挂号页面
            ABCNavigator.navigateToPage(<DentistryInvoicePage registrationFormItem={registrationFormItem} fromKanbanEntry={true} />);
        } else if (this._pageType == RegistrationTabType.appointment) {
            //理疗跳转理疗页面
            ABCNavigator.navigateToPage(<AppointmentInvoicePage registrationFormItem={registrationFormItem} fromKanbanEntry={true} />);
        }
    }

    @actionEvent(_EventModifyRegistrationStatus)
    private async *_mapModifyRegistrationStatus(event: _EventModifyRegistrationStatus): AsyncGenerator<State> {
        const { registrationDetail } = event;
        const status = registrationDetail.registrationFormItem?.statusV2;

        let statusList = [];
        statusList = [
            { text: "接诊", value: RegistrationStatusV2.diagnosing },
            { text: "完诊", value: RegistrationStatusV2.diagnosed },
        ];
        if (status == RegistrationStatusV2.waitingSignIn || status == RegistrationStatusV2.waitingSignInContacted) {
            statusList = [
                { text: "待签(已确认)", value: RegistrationStatusV2.waitingSignInContacted },
                { text: "待诊", value: RegistrationStatusV2.waitingDiagnose },
            ];
        }

        if (status == RegistrationStatusV2.waitingSignInContacted) {
            statusList.shift();
        }

        if (status == RegistrationStatusV2.diagnosing || status == RegistrationStatusV2.continueDiagnose) {
            statusList.shift();
        }

        const result = await showOptionsBottomSheet({
            title: "就诊状态",
            titlePosition: "center",
            showTopRadius: true,
            options: statusList.map((item) => item.text),
        });
        if (!!result && !!result.length) {
            const status = statusList[result[0]].value;
            if (!registrationDetail.id) return Toast.show("当前单据不支持修改");
            DentistryAgent.putManageRegistrationStatus(registrationDetail.id, status).then((rsp) => {
                const sameIndex = this.innerState.dataList.findIndex((item) => item.id == rsp.id);
                if (sameIndex > -1) {
                    const sameItem = this.innerState.dataList[sameIndex];

                    if (rsp.registrationFormItem?.statusV2 == RegistrationStatusV2.waitingDiagnose) {
                        this.innerState.dataList.splice(sameIndex, 1);
                        this.requestReload();
                        return;
                    }

                    // 相关值去覆盖 - 就诊时间+状态名称
                    sameItem.registrationFormItem!.diagnosingTime = rsp.registrationFormItem?.diagnosingTime ?? new Date();
                    sameItem.registrationFormItem!.statusV2 = rsp.registrationFormItem?.statusV2 ?? sameItem.registrationFormItem!.statusV2;
                    if (rsp.registrationFormItem?.statusV2 == RegistrationStatusV2.diagnosed) {
                        sameItem.registrationFormItem!.statusName = "已诊";
                    }

                    this.update();
                }
            });
        }
    }
}
