/**
 * create by deng<PERSON>e
 * desc:
 * create date 2021/1/15
 */
import React from "react";
import { View, Text } from "@hippy/react";
import { BaseBlocNetworkPage } from "../base-ui/base-page";
import { RegistrationDoctorShiftPageBloc } from "./registration-doctor-shift-page-bloc";
import { <PERSON><PERSON><PERSON><PERSON> } from "../bloc/bloc-helper";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { BaseComponent } from "../base-ui/base-component";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { TimeUtils } from "../common-base-module/utils";
import { DoctorShiftsWithTime } from "./data/bean";
import { ShiftsListItem } from "./data/registration-agent";
import { AbcView } from "../base-ui/views/abc-view";
import { AbcListView } from "../base-ui/list/abc-list-view";

interface RegistrationDoctorShiftPageProps {
    doctorId: string;
    departmentId: string;
}

export class RegistrationDoctorShiftPage extends BaseBlocNetworkPage<RegistrationDoctorShiftPageProps, RegistrationDoctorShiftPageBloc> {
    static async show(
        options: RegistrationDoctorShiftPageProps
    ): Promise<{ doctor: DoctorShiftsWithTime; result: ShiftsListItem } | undefined> {
        return ABCNavigator.navigateToPage(<RegistrationDoctorShiftPage {...options} />);
    }

    constructor(props: RegistrationDoctorShiftPageProps) {
        super(props);
        this.bloc = new RegistrationDoctorShiftPageBloc({ doctorId: props.doctorId, departmentId: props.departmentId });
    }

    componentDidMount(): void {
        super.componentDidMount();
        BlocHelper.connectLoadingStatus(this.bloc, this);
    }

    getAppBarTitle(): string {
        return "医生排班";
    }

    renderContent(): JSX.Element {
        const state = this.bloc.currentState,
            { shiftsList, doctorDetail } = state;
        return (
            <View style={{ backgroundColor: Colors.white, flex: 1 }}>
                <View style={[ABCStyles.rowAlignCenter, Sizes.paddingLTRB(Sizes.dp16), ABCStyles.bottomLine]}>
                    {/*<View*/}
                    {/*    style={[*/}
                    {/*        ABCStyles.borderOne,*/}
                    {/*        { width: Sizes.dp56, height: Sizes.dp56, borderRadius: Sizes.dp6, overflow: "hidden", marginRight: Sizes.dp12 },*/}
                    {/*    ]}*/}
                    {/*>*/}
                    {/*    <AssetImageView src={doctorDetail?.headImgUrl} />*/}
                    {/*</View>*/}
                    <View style={{ flex: 1 }}>
                        <Text style={[TextStyles.t16MT1.copyWith({ lineHeight: Sizes.dp24 }), { flexShrink: 1 }]} numberOfLines={1}>
                            {doctorDetail?.doctorName ?? ""}
                        </Text>
                        <Text style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp20 }), { flexShrink: 1 }]} numberOfLines={1}>
                            {doctorDetail?.departmentName?.length ? doctorDetail.departmentName : "其他"}
                        </Text>
                    </View>
                </View>
                {!shiftsList.length ? (
                    <View style={[ABCStyles.centerChild, { flex: 1 }]}>
                        <Text style={[TextStyles.t14NT2]}>该医生暂无可用号源</Text>
                    </View>
                ) : (
                    <View style={[{ flex: 1 }]}>
                        <AbcListView
                            style={{ flex: 1 }}
                            numberOfRows={shiftsList.length}
                            dataSource={shiftsList}
                            scrollEventThrottle={300}
                            initialListSize={20}
                            getRowKey={(index) => index.toString()}
                            renderRow={(data) => (
                                <_ShiftsListItem
                                    doctor={data}
                                    onClick={() => {
                                        this.bloc.requestCheckDoctor(data);
                                    }}
                                />
                            )}
                            onEndReached={() => {
                                this.bloc.requestLoadMore();
                            }}
                        />
                    </View>
                )}
            </View>
        );
    }
}

interface _ShiftsListItemProps {
    doctor: DoctorShiftsWithTime;

    onClick?(arg1: DoctorShiftsWithTime): void;
}

class _ShiftsListItem extends BaseComponent<_ShiftsListItemProps> {
    renderRestCountDisplayView(): JSX.Element {
        const { doctor } = this.props;
        let restCountDisplay = "";
        let restCountDisplayStyle = TextStyles.t16MT1;
        if (doctor.canReserve) {
            if (doctor.restCountToday) {
                restCountDisplay = doctor.displayRestCount;
            } else if (doctor.scheduleIntervals?.length && doctor.restCountToday == 0) {
                return (
                    <View style={[ABCStyles.rowAlignCenter]}>
                        <Text style={TextStyles.t16NT2.copyWith({ lineHeight: Sizes.dp24 })}>无号</Text>
                    </View>
                );
            } else {
                restCountDisplayStyle = restCountDisplayStyle.copyWith({ color: Colors.T2 });
                restCountDisplay = "未排班";
            }
        } else {
            restCountDisplayStyle = restCountDisplayStyle.copyWith({ color: Colors.T2 });
            restCountDisplay = "不可预约";
        }
        restCountDisplayStyle = restCountDisplayStyle.copyWith({ lineHeight: Sizes.dp24 });
        return <Text style={[restCountDisplayStyle]}>{restCountDisplay}</Text>;
    }

    render(): JSX.Element {
        const { doctor, onClick } = this.props;
        return (
            <AbcView
                style={[
                    ABCStyles.rowAlignCenter,
                    ABCStyles.bottomLine,
                    {
                        height: Sizes.dp48,
                        paddingHorizontal: Sizes.dp16,
                    },
                ]}
                onClick={() => {
                    onClick?.(doctor);
                }}
            >
                <Text style={[{ width: Sizes.dp96 }, TextStyles.t16MT1.copyWith({ lineHeight: Sizes.dp24 })]} numberOfLines={1}>
                    {TimeUtils.formatDate(doctor.workingDate, "MM-dd") ?? ""}
                </Text>
                <Text style={[{ flex: 1 }, TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp20 })]}>{doctor.dayOfWeek ?? ""}</Text>
                {this.renderRestCountDisplayView()}
            </AbcView>
        );
    }
}
