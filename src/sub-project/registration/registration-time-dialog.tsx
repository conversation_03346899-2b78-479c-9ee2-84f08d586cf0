/**
 * create by <PERSON><PERSON><PERSON><PERSON><PERSON>
 * desc:
 * create date 2021/6/21
 */
import React from "react";
import { ScrollView, Style, Text, View } from "@hippy/react";
import _ from "lodash";
import { BaseComponent } from "../base-ui/base-component";
import { SafeAreaBottomView } from "../base-ui/safe_area_view";
import { ABCStyles, Colors, flattenStyles, Sizes, TextStyles } from "../theme";
import { BottomSheetCloseButton, showBottomSheet } from "../base-ui/dialog/bottom_sheet";
import { pxToDp } from "../base-ui/utils/ui-utils";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { DividerLine, SizedBox, Spacer, ToolBarButtonStyle1 } from "../base-ui";
import { errorToStr, TimeUtils } from "../common-base-module/utils";
import {
    BusinessType,
    DoctorShiftsType,
    RegistrationDesignatedDoctorInfo,
    RegistrationDesignatedTimeItem,
    RegistrationDesignatedTimeScheduleInterval,
    RegistrationsAppointmentConfig,
} from "./data/bean";
import { RegistrationAgent, ShiftsListItem } from "./data/registration-agent";
import { GridView } from "../base-ui/views/grid-view";
import { AbcView } from "../base-ui/views/abc-view";
import sizes from "../theme/sizes";
import { StringUtils } from "../base-ui/utils/string-utils";
import { ABCNetworkPageContentStatus, NetworkView } from "../base-ui/base-page";
import { LogUtils } from "../common-base-module/log";
import { showConfirmDialog } from "../base-ui/dialog/dialog-builder";

interface RegisterTimeDialogProps {
    doctorInfo: RegistrationDesignatedDoctorInfo;
    departmentId: string;
}
interface TherapyDoctorShiftTimeDialogState {
    currentShift?: RegistrationDesignatedTimeItem; // 已选排班信息
}

export class RegisterTimeDialog extends NetworkView<RegisterTimeDialogProps, TherapyDoctorShiftTimeDialogState> {
    static async show(doctorInfo: RegistrationDesignatedDoctorInfo, departmentId: string): Promise<ShiftsListItem | undefined> {
        return await showBottomSheet(<RegisterTimeDialog doctorInfo={doctorInfo} departmentId={departmentId} />);
    }

    private _registrationAppointmentConfig?: RegistrationsAppointmentConfig; // 挂号预约配置
    private _registrationDesignatedTimeScheduleInterval?: RegistrationDesignatedTimeScheduleInterval[]; // 挂号列表

    private _forNormalRegistration = 1;
    constructor(props: RegisterTimeDialogProps) {
        super(props);
        this.state = {
            currentShift: undefined,
        };
    }

    // 获取诊所预约配置 (enableLeaveForPC:排班时可设置部分号源仅供会员预约 enableLeaveForMember:会员预留号)
    componentDidMount(): void {
        this.setContentStatus(ABCNetworkPageContentStatus.loading);
        RegistrationAgent.getClinicRegistrationAppointmentConfig()
            .then((rsp) => {
                this._registrationAppointmentConfig = rsp;
                this.setContentStatus(ABCNetworkPageContentStatus.show_data);
            })
            .catch((e) => {
                this.setContentStatus(ABCNetworkPageContentStatus.error, e);
            });
        const { doctorId } = this.props.doctorInfo;
        this.setContentStatus(ABCNetworkPageContentStatus.loading);
        // 获取
        RegistrationAgent.getRegistrationDesignatedTime({
            departmentId: this.props.departmentId,
            doctorId: doctorId,
            forNormalRegistration: this._forNormalRegistration, // 默认预约为0  挂号为1
            workingDate: TimeUtils.formatDate(new Date()),
            registrationType: BusinessType.registration,
        })
            .then((rsp) => {
                this._registrationDesignatedTimeScheduleInterval = rsp.getScheduleIntervals();
                this.setContentStatus(ABCNetworkPageContentStatus.show_data);
                this.setState({});
            })
            .catch((e) => {
                showConfirmDialog("", errorToStr(e)).then(() => ABCNavigator.pop());
                this.setContentStatus(ABCNetworkPageContentStatus.error, e);
            })
            .toObservable();
    }

    /**
     * 时间宫格视图
     * @private
     */
    private _renderShiftTimeItem(info: RegistrationDesignatedTimeItem, select?: boolean): JSX.Element {
        const isCustomPart = this._registrationAppointmentConfig?.isAccuratePart; // 精确时间

        const borderColor = select ? Colors.mainColor : Colors.dividerLineColor;
        let textDefaultColor = Colors.T2,
            showPoint = false;
        const textSelectColor = Colors.mainColor; // 选中字体颜色
        const disable = !info.available;
        switch (info.type) {
            case DoctorShiftsType.normal: {
                // 普通号源
                break;
            }
            case DoctorShiftsType.forPatient: {
                // 预留号源
                showPoint = true;
                textDefaultColor = Colors.mainColor;
                break;
            }
            case DoctorShiftsType.forMember: {
                // 会员预留
                showPoint = true;
                textDefaultColor = Colors.Y2;
                break;
            }
        }

        return (
            <AbcView
                key={info.start}
                style={[
                    sizes.marginLTRB(0, Sizes.dp8, Sizes.dp8, 0),
                    sizes.paddingLTRB(Sizes.dp8, Sizes.dp4),
                    { borderWidth: 1, borderRadius: Sizes.dp3 },
                    disable ? { borderColor: Colors.dividerLineColor, backgroundColor: Colors.window_bg } : { borderColor: borderColor },
                ]}
                onClick={() => {
                    if (disable) return;
                    this.setState({ currentShift: info });
                }}
            >
                {showPoint && <_PointView color={textDefaultColor} style={{ position: "absolute", left: Sizes.dp4, top: Sizes.dp4 }} />}
                <Text
                    style={[
                        TextStyles.t14NT2.copyWith({
                            lineHeight: Sizes.dp20,
                            color: select ? textSelectColor : Colors.T2,
                        }),
                        { textAlign: "center" },
                    ]}
                >
                    {isCustomPart ? StringUtils.numberWithFillZero(info.orderNo ?? 0) : (info.start as string) ?? info.start}
                </Text>
            </AbcView>
        );
    }

    private renderScheduleShiftTimeView(scheduleItem: RegistrationDesignatedTimeScheduleInterval, showScheduleTitle = true): JSX.Element {
        const isCustomPart = this._registrationAppointmentConfig?.isCustomPart;
        return (
            <View
                key={scheduleItem.start}
                style={[
                    {
                        paddingHorizontal: Sizes.dp16,
                        paddingVertical: Sizes.dp16,
                    },
                ]}
            >
                {showScheduleTitle && (
                    // 时间段（上午、下午、晚上）
                    <Text style={[TextStyles.t14MT2.copyWith({ lineHeight: Sizes.dp20 })]}>{scheduleItem.timeOfDay ?? ""}</Text>
                )}
                <Text style={[TextStyles.t14MT2.copyWith({ lineHeight: Sizes.dp20 }), { paddingTop: showScheduleTitle ? Sizes.dp12 : 0 }]}>
                    {`${scheduleItem.start}-${scheduleItem.end}`}
                </Text>
                <GridView
                    itemHeight={Sizes.dp28}
                    mainAxisSpacing={Sizes.dp8}
                    crossAxisCount={4}
                    style={[{ flex: 1, paddingBottom: Sizes.dp16 }]}
                >
                    {(scheduleItem.list ?? []).map((item) =>
                        this._renderShiftTimeItem(
                            item,
                            isCustomPart
                                ? this.state.currentShift?.start == item.start && this.state.currentShift?.orderNo == item.orderNo
                                : this.state.currentShift?.start == item.start
                        )
                    )}
                </GridView>
            </View>
        );
    }

    private _renderShiftTime(): JSX.Element {
        const shiftList: RegistrationDesignatedTimeItem[] = _.cloneDeep(this._registrationDesignatedTimeScheduleInterval) ?? [];
        if (!shiftList.length) return <View />;

        const timeOfDay: Set<string> = new Set<string>();
        return (
            <ScrollView>
                {shiftList.map((item) => {
                    const showTitle = !timeOfDay.has(item.timeOfDay ?? "");
                    timeOfDay.add(item.timeOfDay ?? "");
                    return this.renderScheduleShiftTimeView(item, showTitle);
                })}
            </ScrollView>
        );
    }

    /**
     * 弹窗标题 (医生姓名 06-24(星期四))
     * @private
     */
    // 挂号标签
    private _renderLabel(text: string, color: string): JSX.Element {
        return (
            <View style={[ABCStyles.rowAlignCenter]}>
                <_PointView size={Sizes.dp6} color={color} />
                <SizedBox width={Sizes.dp6} />
                <Text style={TextStyles.t12NT1.copyWith({ color: color, lineHeight: Sizes.dp16 })}>{text}</Text>
            </View>
        );
    }
    private _renderDialogTitle(): JSX.Element {
        return (
            <View style={[ABCStyles.rowAlignCenter, ABCStyles.bottomLine, { height: Sizes.dp48, paddingLeft: Sizes.dp16 }]}>
                <Text style={[TextStyles.t14NT4]} numberOfLines={1}>{`${this.props.doctorInfo?.doctorName ?? ""} ${TimeUtils.formatDate(
                    new Date(),
                    "MM-dd"
                )} (${TimeUtils.getDayOfWeek(new Date())})`}</Text>
                <Spacer />
                {this._renderLabel("会员号", Colors.Y2)}
                <SizedBox width={Sizes.dp16} />
                {this._renderLabel("现场号", Colors.theme2)}
                <BottomSheetCloseButton onTap={() => ABCNavigator.pop()} />
            </View>
        );
    }

    renderContent(): JSX.Element {
        const currentShift = this.state.currentShift,
            hasCheck = !!this.state.currentShift;

        LogUtils.d("this.state.currentShift==" + JSON.stringify(this.state.currentShift));

        return (
            <View style={[{ backgroundColor: Colors.white }]}>
                <View style={[{ height: pxToDp(468) }]}>
                    {this._renderDialogTitle()}
                    {this._renderShiftTime()}
                    <View>
                        <DividerLine />
                        {hasCheck && (
                            /**
                             * 显示底部选择预约时间    杨永兴（下午01 12：15-12：30）
                             * @private
                             */
                            <View
                                style={{
                                    justifyContent: "center",
                                    paddingVertical: Sizes.dp12,
                                    alignItems: "center",
                                }}
                            >
                                <Text style={TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp20 })} numberOfLines={1}>
                                    {`${this.props.doctorInfo?.doctorName}(${currentShift?.timeOfDay}${StringUtils.numberWithFillZero(
                                        currentShift?.orderNo ?? 0
                                    )} ${currentShift?.start ?? currentShift?.start}-${currentShift?.end ?? currentShift?.end})`}
                                </Text>
                            </View>
                        )}
                    </View>
                    <View
                        style={{
                            backgroundColor: Colors.white,
                            flexDirection: "row",
                            alignItems: "center",
                            paddingHorizontal: Sizes.dp12,
                        }}
                    >
                        <ToolBarButtonStyle1
                            text={"确定"}
                            onClick={
                                hasCheck
                                    ? () => {
                                          ABCNavigator.pop(this.state.currentShift);
                                      }
                                    : undefined
                            }
                        />
                    </View>
                </View>
                <SafeAreaBottomView />
            </View>
        );
    }
}

interface _PointViewProps {
    color?: string;
    size?: number;
    style?: Style | Style[];
}
// 圆点标签
class _PointView extends BaseComponent<_PointViewProps> {
    constructor(props: _PointViewProps) {
        super(props);
    }

    render(): JSX.Element {
        const size = this.props.size ?? Sizes.dp6,
            color = this.props.color,
            style = this.props.style;
        return <View style={[{ width: size, height: size, borderRadius: size, backgroundColor: color }, flattenStyles(style)]} />;
    }
}
