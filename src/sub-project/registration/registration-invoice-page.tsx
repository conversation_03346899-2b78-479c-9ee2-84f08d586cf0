/**
 * create by deng<PERSON>e
 * desc:
 * create date 2021/1/6
 */
import React from "react";
import { ScrollView, Text, View } from "@hippy/react";
import { BaseBlocNetworkPage } from "../base-ui/base-page";
import { ScrollToErrorViewState } from "./registration-invoice-page-bloc";
import { ListSettingEditItem, ListSettingItem, ListSettingItemStyle } from "../base-ui/views/list-setting-item";
import { NumberUtils } from "../common-base-module/utils";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { AppointmentDoctorCard } from "./views/appointment-doctor-card";
import { SizedBox, ToolBar, ToolBarButtonStyle1, ToolBarButtonStyle2 } from "../base-ui";
import { FocusItemKeys, OldReserveInfo, PayStatusV2, RegistrationStatusV2 } from "./data/bean";
import { Bloc<PERSON>uilder } from "../bloc";
import { PatientInfoViewWithModifyAndHistory } from "../views/patient-info-view";
import { <PERSON><PERSON>elper } from "../bloc/bloc-helper";
import { ABCUtils } from "../base-ui/utils/utils";
import { PrecisionLimitFormatter } from "../base-ui/utils/formatter";
import { NumberKeyboardBuilder } from "../base-ui/views/keyboards/number-keyboard";
import { AbcFlexTextInput } from "../base-ui/views/abc-flex-text-input";
import { MedicalRecordCard } from "./views/medical-record-card";
import { userCenter } from "../user-center";
import { AbcTextInput } from "../base-ui/views/abc-text-input";
import { MarketCardItem } from "../charge/view/market-card-item";
import { ChargeAbnormalTips } from "../charge/view/charge-abnormal-dialog";
import abcI18Next from "../language/config";
import { HistoryPermissionModuleType } from "../base-business/data/beans";
import { RegistrationUtils } from "./utils/registration-utils";
import { RegistrationInvoicePageOldVersionBloc } from "./registration-invoice-page-old-version-bloc";

const kFirstChild = "RegistrationInvoicePage.firstChild";
const kLastChild = "RegistrationInvoicePage.lastChild";

interface RegistrationInvoicePageProps {
    id?: string;
    doctorId?: string;
    oldReserveInfo?: OldReserveInfo;
}

export class RegistrationInvoicePage extends BaseBlocNetworkPage<RegistrationInvoicePageProps, RegistrationInvoicePageOldVersionBloc> {
    private _scrollView?: ScrollView | null;

    constructor(props: RegistrationInvoicePageProps) {
        super(props);
        this.bloc = new RegistrationInvoicePageOldVersionBloc({
            id: props.id,
            doctorId: props.doctorId,
            oldReserveInfo: props.oldReserveInfo,
        });
    }

    getAppBarTitle(): string {
        return this.bloc.currentState.isCreate ? "新增挂号" : "挂号单";
    }

    getAppBar(): JSX.Element | undefined {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }

    componentDidMount(): void {
        BlocHelper.connectLoadingStatus(this.bloc, this);

        this.bloc.state
            .subscribe((state) => {
                if (state instanceof ScrollToErrorViewState) {
                    this._scrollView?.scrollChildToVisible(state.focusKey, kFirstChild, kLastChild);
                }
            })
            .addToDisposableBag(this);
    }

    renderContent(): JSX.Element | undefined {
        const state = this.bloc.currentState,
            config = state.employeeConfig,
            detail = state.detail,
            registrationInfo = detail?.registrationFormItem,
            showErrorHint = state.showErrorHint;

        let statusText: string | number | string[] | undefined = undefined;
        let statusTextStyle = {};
        let showNetIncomeFee = false;
        if (registrationInfo?.payStatusV2 == PayStatusV2.paid) {
            statusText = "已收费";
            statusTextStyle = TextStyles.t14NM;
        } else if (registrationInfo?.payStatusV2 == PayStatusV2.refunded) {
            statusText = "已退费";
            statusTextStyle = TextStyles.t14NT2;
        } else if (registrationInfo?.payStatusV2 == PayStatusV2.partedRefunded) {
            showNetIncomeFee = true;
            statusText = "部分退费";
            statusTextStyle = TextStyles.t14NT2;
        } else if (registrationInfo?.payStatusV2 == PayStatusV2.partedPaid) {
            showNetIncomeFee = true;
            statusText = "部分收费";
            statusTextStyle = TextStyles.t14NM;
        }
        return (
            <View
                style={{ flex: 1 }}
                onClick={() => {
                    AbcTextInput.focusInput?.blur();
                }}
            >
                {this._renderChargeAbnormalTips()}
                <ScrollView
                    style={{ flex: 1 }}
                    showsVerticalScrollIndicator={false}
                    ref={(ref) => {
                        this._scrollView = ref;
                    }}
                    onScrollBeginDrag={() => {
                        AbcTextInput.focusInput?.blur();
                    }}
                >
                    <View ref={kFirstChild} collapsable={false} />
                    {this._renderPatientView()}
                    <AppointmentDoctorCard
                        isEditing={state.isCreate}
                        showErrorHint={showErrorHint}
                        registrationInfo={detail?.registrationFormItem}
                        referralSource={detail?.referralSource}
                        revisitStatusName={detail?.__revisitStatusName}
                        disabledEditRevisit={state.disabledEditRevisitStatus}
                        onChangeType={(index) => {
                            this.bloc.requestModifyRegistrationType(index);
                        }}
                        onChangeDepartment={() => {
                            this.bloc.requestModifyRegistrationDepartment();
                        }}
                        onChangeDoctor={() => {
                            this.bloc.requestModifyRegistrationDoctor();
                        }}
                        onChangeTime={() => {
                            this.bloc.requestModifyRegistrationTime();
                        }}
                        onChangeRevisit={() => {
                            this.bloc.requestModifyRevisitStatus();
                        }}
                    />
                    {!state.isCreate && (
                        <ListSettingItem
                            itemStyle={state.disabledEditRevisitStatus ? ListSettingItemStyle.normal : ListSettingItemStyle.expandIcon}
                            style={{ paddingHorizontal: Sizes.dp16, backgroundColor: Colors.white }}
                            contentStyle={[
                                ABCStyles.rowAlignCenter,
                                {
                                    paddingVertical: Sizes.dp14,
                                    justifyContent: "flex-end",
                                    paddingRight: state.disabledEditRevisitStatus ? Sizes.dp16 : 0,
                                },
                            ]}
                            title={"初诊/复诊"}
                            content={detail?.__revisitStatusName}
                            bottomLine={true}
                            onClick={() => this.bloc.requestModifyRevisitStatus()}
                        />
                    )}
                    <ListSettingItem
                        ref={FocusItemKeys.fee}
                        style={{ paddingHorizontal: Sizes.dp16, backgroundColor: Colors.white }}
                        contentStyle={{ alignItems: "flex-end" }}
                        contentTextStyle={TextStyles.t18MY2}
                        title={"诊费"}
                        content={`${abcI18Next.t("￥")}${NumberUtils.formatMaxFixed(state.pay?.fee, 2)}`}
                        contentBuilder={() => {
                            return (
                                <View
                                    style={[ABCStyles.rowAlignCenter, { width: Sizes.dp160, justifyContent: "flex-end" }]}
                                    onClick={() => {
                                        return;
                                    }}
                                >
                                    {!!statusText && (
                                        <Text style={[statusTextStyle, { alignSelf: "center", marginRight: Sizes.dp12 }]}>
                                            {statusText}
                                        </Text>
                                    )}
                                    <Text style={[TextStyles.t16NT2, { paddingRight: 4 }]}>{abcI18Next.t("¥")}</Text>
                                    <AbcFlexTextInput
                                        returnKeyType={"done"}
                                        editable={state.shouldRegisteredBargain && (state.isCreate || !state.disabledEditCharge)}
                                        multiline={false}
                                        syncTextOnBlur={true} //解决更改值后切换医生props不改变问题
                                        numberOfLines={1}
                                        placeholder={"点击输入"}
                                        defaultValue={ABCUtils.formatPrice(state.pay?.fee ?? 0)}
                                        formatter={PrecisionLimitFormatter(2)}
                                        customKeyboardBuilder={new NumberKeyboardBuilder({})}
                                        style={{
                                            underlineColorAndroid: Colors.white,
                                            backgroundColor: Colors.white,
                                            height: Sizes.listItemHeight,
                                            ...TextStyles.t16NB.copyWith({
                                                color:
                                                    !state.shouldRegisteredBargain || (!state.isCreate && state.disabledEditCharge)
                                                        ? Colors.T2
                                                        : Colors.black,
                                            }),
                                        }}
                                        onChangeText={(value) => {
                                            this.bloc.requestModifyRegistrationFee(value);
                                        }}
                                    />
                                    <SizedBox width={Sizes.dp16} />
                                </View>
                            );
                        }}
                        bottomLine={showNetIncomeFee}
                    />
                    {this._renderVisitSource()}
                    {showNetIncomeFee && state.chargeFromRegistration && (
                        <ListSettingItem
                            title={"实收"}
                            style={{ paddingHorizontal: Sizes.dp16, backgroundColor: Colors.white }}
                            contentStyle={{ alignItems: "flex-end", paddingRight: Sizes.dp16 }}
                            contentTextStyle={TextStyles.t16NT2}
                            content={`${abcI18Next.t("￥")}${NumberUtils.formatMaxFixed(
                                state.detail?.chargeSheet?.chargeSheetSummary?.netIncomeFee,
                                2
                            )}`}
                        />
                    )}

                    {!!detail?.chargeSheet && (
                        <MarketCardItem
                            detailData={detail.chargeSheet}
                            canModify={state.isCreate || !state.disabledEditCharge}
                            promotions={RegistrationUtils.filterRegistrationPromotions(detail.chargeSheet)}
                            onPromotionSelectChanged={(
                                selectPromotions,
                                selectGiftPromotions,
                                selectCouponPromotions,
                                selectMemberPointPromotion
                            ) => {
                                this.bloc.requestUpdatePromotions(
                                    selectPromotions,
                                    selectGiftPromotions,
                                    selectCouponPromotions,
                                    selectMemberPointPromotion
                                );
                            }}
                            onPromotionMemberCardChanged={(member) => this.bloc.requestUpdateMemberCard(member)}
                            onPatientCardPromotionChanged={(patientCardPromotions) =>
                                this.bloc.requestPatientCardPromotionChanged(patientCardPromotions)
                            }
                        />
                    )}

                    {!config?.regsHiddenMedicalRecord && (
                        <MedicalRecordCard
                            editable={
                                state.isCreate ||
                                (state.chargeFromRegistration &&
                                    !state.disabledMR &&
                                    (!state.hasCharged || state.detail?.registrationFormItem?.payStatusV2 == PayStatusV2.paid))
                            }
                            medicalRecord={state.medicalRecord}
                            onChange={(medicalRecord) => {
                                this.bloc.requestModifyMedicalRecord(medicalRecord);
                            }}
                        />
                    )}
                    <View ref={kLastChild} collapsable={false} />
                </ScrollView>
                {this._renderBottomGroup()}
            </View>
        );
    }

    private _renderChargeAbnormalTips(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail;
        const listConfig = [
            { show: !!detail?.chargeSheet?.isSheBaoAbnormal, isShebaoAbnormal: true },
            { show: !!detail?.chargeSheet?.isNotSheBaoAbnormal, isShebaoAbnormal: false },
        ];
        return (
            <>
                {listConfig
                    .filter((item) => item.show)
                    .map((item, index) => (
                        <ChargeAbnormalTips
                            key={index}
                            isShebaoAbnormal={item.isShebaoAbnormal}
                            onClick={() => {
                                this.bloc.requestHandleChargeAbnormal(item.isShebaoAbnormal);
                            }}
                        />
                    ))}
            </>
        );
    }

    private _renderBottomGroup(): JSX.Element {
        const state = this.bloc.currentState;
        const registration = state.detail?.registrationFormItem;
        const chargeSheet = state.detail?.chargeSheet;
        const isFunded =
            ((registration?.statusV2 ?? 0) < RegistrationStatusV2.refunded && (registration?.payStatusV2 ?? 0) < PayStatusV2.refunded) ||
            (registration?.statusV2 === RegistrationStatusV2.refunded && registration?.payStatusV2 === PayStatusV2.partedRefunded);
        if (!isFunded) return <View />;
        const isRefundFee =
            (registration?.payStatusV2 === PayStatusV2.paid ||
                registration?.payStatusV2 === PayStatusV2.partedPaid ||
                registration?.payStatusV2 === PayStatusV2.partedRefunded) &&
            (chargeSheet?.chargeSheetSummary?.receivedFee ?? 0) > 0;

        const buttons: JSX.Element[] = [];
        if (!state.isCreate && state.chargeFromRegistration) {
            buttons.push(
                isRefundFee ? (
                    <ToolBarButtonStyle2
                        style={{
                            width:
                                !state.hasCharged ||
                                registration?.statusV2 == RegistrationStatusV2.waitingSignIn ||
                                (registration?.statusV2 && registration?.statusV2 < RegistrationStatusV2.diagnosed)
                                    ? Sizes.dp78
                                    : undefined,
                        }}
                        key={"registrationRefund"}
                        text={"退费"}
                        onClick={() => {
                            this.bloc.requestRefundedRegistrationFee();
                        }}
                    />
                ) : (
                    <ToolBarButtonStyle2
                        style={{
                            width:
                                !state.hasCharged ||
                                registration?.statusV2 == RegistrationStatusV2.waitingSignIn ||
                                state.isCreate ||
                                registration?.statusV2 ||
                                (registration?.statusV2 && registration?.statusV2 < RegistrationStatusV2.diagnosed)
                                    ? Sizes.dp78
                                    : undefined,
                        }}
                        key={"registrationRefundOrderNumber"}
                        text={"退号"}
                        onClick={() => {
                            this.bloc.requestRefundedRegistration();
                        }}
                    />
                )
            );
        }
        if (!state.hasCharged) {
            buttons.push(
                <ToolBarButtonStyle1
                    style={{ textAlign: "center" }}
                    key={"registrationCharge"}
                    text={`${abcI18Next.t("￥")}${ABCUtils.formatPrice(state.pay?.receivable ?? 0)}\n收费`}
                    onClick={() => {
                        this.bloc.requestRegistrationCharge();
                    }}
                />
            );
        }
        if (state.canBackDirectRegistration) {
            buttons.push(
                <ToolBarButtonStyle2
                    key={"DirectRegistration"}
                    text={"回诊"}
                    onClick={() => {
                        this.bloc.requestBackDirectRegistration();
                    }}
                />
            );
        }
        if (registration?.statusV2 == RegistrationStatusV2.waitingSignIn) {
            buttons.push(
                <ToolBarButtonStyle1
                    key={"registrationSign"}
                    text={"签到"}
                    onClick={() => {
                        this.bloc.requestRegistrationSignIn();
                    }}
                />
            );
        }

        if (state.isCreate) {
            buttons.push(
                <ToolBarButtonStyle1
                    key={"registrationFinish"}
                    text={`完成${registration?.isReserved ? "预约" : "挂号"}`}
                    onClick={() => this.bloc.requestFinishRegistration()}
                />
            );
        }

        if (registration?.statusV2 && registration?.statusV2 <= RegistrationStatusV2.refunded) {
            buttons.push(
                <ToolBarButtonStyle1
                    key={"registrationSave"}
                    text={"保存"}
                    onClick={
                        state.hasChange
                            ? () => {
                                  this.bloc.requestSaveRegistration();
                              }
                            : undefined
                    }
                />
            );
        }
        if (!buttons.length) return <View />;

        return <ToolBar hideWhenKeyboardShow={true}>{buttons}</ToolBar>;
    }

    private _renderPatientView(): JSX.Element {
        const state = this.bloc.currentState,
            config = state.employeeConfig,
            detail = state.detail,
            showErrorHint = state.showErrorHint,
            patient = detail?.patient;

        const regShowViewConfig = [
            {
                show: !config?.regsHiddenIdCard,
                type: "idCard",
                text: !!patient?.idCard?.length ? patient?.idCard : "暂无身份证号",
            },
            {
                show: !config?.regsHiddenSource,
                type: "patientSource",
                text: !!patient?.patientSource?.sourceDisplay.length ? patient?.patientSource?.sourceDisplay : "暂无来源",
            },
            {
                show: !config?.regsHiddenAddress,
                type: "address",
                text: !!patient?.address?.addressDisplay.length ? patient?.address?.addressDisplay : "暂无详细地址",
            },
            {
                show: !config?.regsHiddenProfession,
                type: "profession",
                text: !!patient?.profession ? patient?.profession : "暂无职业",
            },
            {
                show: !config?.regsHiddenSn,
                type: "sn",
                text: patient?.sn ?? "暂无档案号",
            },
        ];
        const regShowView = regShowViewConfig.filter((config) => config.show);
        return (
            <View ref={FocusItemKeys.patient}>
                <View style={[showErrorHint && !patient ? ABCStyles.errorBorder : {}]}>
                    <PatientInfoViewWithModifyAndHistory
                        patient={detail?.patient}
                        hintSex={"男"}
                        bottomLine={false}
                        patientSwitchable={state.isCreate}
                        showPatientSource={true}
                        showIDInfo={true}
                        showAddressInfo={true}
                        showSN={true}
                        copyEnable={false}
                        requirePatientSource={userCenter.clinicConfig?.regsRequiredSource ?? false}
                        requireMobile={userCenter.clinicConfig?.regsRequiredMobile ?? false}
                        onChanged={(patient) => {
                            this.bloc.requestModifyRegistrationPatient(patient);
                        }}
                        showHistoryBtn={state.canViewDiagnoseHistory}
                        type={HistoryPermissionModuleType.registration}
                        canSeePatientMobileInRegister={state.canSeePatientPhone}
                    />
                    {!!patient && !!regShowView.length && (
                        <View style={[{ backgroundColor: Colors.white }, Sizes.paddingLTRB(Sizes.dp16, Sizes.dp8)]}>
                            {regShowView.map((config) => (
                                <Text key={config.type} style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp22 })]}>
                                    {config.text}
                                </Text>
                            ))}
                        </View>
                    )}
                </View>
                <SizedBox height={Sizes.dp8} />
            </View>
        );
    }

    private _renderVisitSource(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail;
        // 是否禁用就诊推荐修改
        // 有挂号单并且不可以修改就诊推荐或者配置的只读都需要禁用就诊推荐修改
        // 就诊推荐常驻
        const isDisableDoctorRemmend = !!this.props?.id && !state.canModifyRecommendation;

        return (
            <View style={{ backgroundColor: Colors.white }}>
                <ListSettingItem
                    itemStyle={!isDisableDoctorRemmend ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal}
                    title={"就诊推荐"}
                    style={{ paddingHorizontal: Sizes.listHorizontalMargin }}
                    contentStyle={{ paddingVertical: Sizes.dp12, justifyContent: "flex-end" }}
                    content={!!state.detail?.__visitSourceDisplayName ? state.detail?.__visitSourceDisplayName : "不指定"}
                    bottomLine={true}
                    onClick={() => {
                        if (!isDisableDoctorRemmend) this.bloc.requestModifyRegistrationVisitSource();
                    }}
                />
                <ListSettingEditItem
                    title={"就诊备注"}
                    style={{ paddingHorizontal: Sizes.listHorizontalMargin }}
                    contentStyle={{ justifyContent: "flex-end" }}
                    content={detail?.visitSourceRemark ?? ""}
                    bottomLine={true}
                    editable={!isDisableDoctorRemmend}
                    onChanged={(text) => {
                        this.bloc.requestModifyRegistrationVisitSourceRemark(text);
                    }}
                />
            </View>
        );
    }
}
