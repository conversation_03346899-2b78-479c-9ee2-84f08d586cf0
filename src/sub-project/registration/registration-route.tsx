/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/3/24
 */

import React from "react";
import { Route } from "../url-dispatcher/router-decorator";
import URLProtocols from "../url-dispatcher/url-protocols";
import { UrlRoute } from "../url-dispatcher/url-router";
import { UrlUtils } from "../common-base-module/utils";
import { AnyType } from "../common-base-module/common-types";
import { UniqueKey } from "../base-ui";
import moment from "moment";

@Route({ path: URLProtocols.REGISTRATION_TAB })
export class RegistrationTabRoute implements UrlRoute {
    handleUrl(action: string): JSX.Element {
        const params = UrlUtils.getUrlParams(action);
        const tabId = Number(params.get("tab") ?? 0);
        const doctorId = params.get("doctorId");
        const startTime = params.get("startTime"); // 格式为 2022-09-25 的字符串
        const endTime = params.get("endTime"); // 格式为 2022-09-25 的字符串
        const { userCenter } = require("../user-center");
        const { RegistrationTabPage } = require("./registration-tab-page");
        const { DentistryTabPage } = require("./dentistry/dentistry-tab-page");

        const { ClinicChangeDialog } = require("../clinics/clinic-change-dialog");
        const clinicId = params.get("clinicId");
        const chainId = params.get("chainId");

        if (
            clinicId &&
            chainId &&
            clinicId != chainId &&
            (clinicId != userCenter.clinic?.clinicId || chainId != userCenter.clinic.chainId)
        ) {
            return (
                <ClinicChangeDialog
                    action={action}
                    chainId={chainId}
                    clinicId={clinicId}
                    textContentGetter={(clinic: AnyType) => {
                        return `确定切换到${clinic?.chainName ?? ""}${clinic?.name ?? ""}查看预约情况吗`;
                    }}
                />
            );
        } else {
            return userCenter.isAllowedRegUpgrade ? (
                <DentistryTabPage
                    tabIndex={tabId}
                    doctorId={doctorId}
                    startTime={moment(startTime).toDate()}
                    endTime={moment(endTime).toDate()}
                />
            ) : (
                <RegistrationTabPage tabIndex={tabId} startTime={moment(startTime).toDate()} endTime={moment(endTime).toDate()} />
            );
        }
    }
}

@Route({ path: URLProtocols.REGISTRATION_DETAIL })
export class RegistrationInvoicePageRoute implements UrlRoute {
    handleUrl(action: string): JSX.Element {
        const { ClinicChangeDialog } = require("../clinics/clinic-change-dialog");
        const { UnPermissionPage } = require("../un-permission-page");
        const { userCenter } = require("../user-center");
        const { ModuleIds } = require("../user-center/user-center");
        const params = UrlUtils.getUrlParams(action);
        const clinicId = params.get("clinicId");
        const chainId = params.get("chainId");
        const id = params.get("id");

        if (
            clinicId &&
            chainId &&
            clinicId != chainId &&
            (clinicId != userCenter.clinic?.clinicId || chainId != userCenter.clinic.chainId)
        ) {
            return (
                <ClinicChangeDialog
                    action={action}
                    chainId={chainId}
                    clinicId={clinicId}
                    textContentGetter={(clinic: AnyType) => {
                        return `该挂号预约来自${clinic?.chainName ?? ""}${clinic?.name ?? ""}，请跳转后查看`;
                    }}
                />
            );
        } else {
            if (userCenter.clinic?.can([ModuleIds.MODULE_ID_REGISTER])) {
                const { RegistrationInvoicePage } = require("./registration-invoice-page");
                // const { DentistryInvoicePage } = require("./dentistry/dentistry-invoice-page");
                const { RegistrationInvoiceSummaryDialog } = require("./registration-invoice-summary-dialog");
                return !!userCenter.clinic?.isDentistryClinic ? (
                    // <DentistryInvoicePage key={UniqueKey()} id={id} />
                    <RegistrationInvoiceSummaryDialog key={UniqueKey()} id={id} />
                ) : (
                    <RegistrationInvoicePage key={UniqueKey()} id={id} />
                );
            } else {
                return <UnPermissionPage />;
            }
        }
    }
}

@Route({ path: URLProtocols.APPOINTMENT_DETAIL })
export class AppointmentInvoicePageRoute implements UrlRoute {
    handleUrl(action: string): JSX.Element {
        const { ClinicChangeDialog } = require("../clinics/clinic-change-dialog");
        const { UnPermissionPage } = require("../un-permission-page");
        const { userCenter } = require("../user-center");
        const { ModuleIds } = require("../user-center/user-center");
        const params = UrlUtils.getUrlParams(action);
        const clinicId = params.get("clinicId");
        const chainId = params.get("chainId");
        const id = params.get("id");

        if (
            clinicId &&
            chainId &&
            clinicId != chainId &&
            (clinicId != userCenter.clinic?.clinicId || chainId != userCenter.clinic.chainId)
        ) {
            return (
                <ClinicChangeDialog
                    action={action}
                    chainId={chainId}
                    clinicId={clinicId}
                    textContentGetter={(clinic: AnyType) => {
                        return `该理疗预约来自${clinic?.chainName ?? ""}${clinic?.name ?? ""}，请跳转后查看`;
                    }}
                />
            );
        } else {
            if (userCenter.clinic?.can([ModuleIds.MODULE_ID_REGISTER])) {
                const { AppointmentInvoicePage } = require("./appointment/appointment-invoice-page");
                return <AppointmentInvoicePage key={UniqueKey()} id={id} />;
            } else {
                return <UnPermissionPage />;
            }
        }
    }
}
