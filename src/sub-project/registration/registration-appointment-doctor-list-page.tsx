/**
 * create by deng<PERSON>e
 * desc: 挂号预约医生列表
 * create date 2021/1/14
 */
import React from "react";
import { ListView, ScrollView, Text, View, ViewPager } from "@hippy/react";
import { BaseBlocNetworkPage } from "../base-ui/base-page";
import { RegistrationAppointmentDoctorListPageBloc } from "./registration-appointment-doctor-list-page-bloc";
import { TimeUtils } from "../common-base-module/utils";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { AnyType } from "../common-base-module/common-types";
import { IconFontView, SizedBox, UniqueKey } from "../base-ui";
import { RegistrationFormItem } from "./data/bean";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { BlocHelper } from "../bloc/bloc-helper";
import { AbcView } from "../base-ui/views/abc-view";
import { DoctorShiftsDetails } from "./views/views";
import UiUtils from "../base-ui/utils/ui-utils";
import { LoadingView } from "../base-ui/views/loading-view";
import { BlocBuilder } from "../bloc";
import { DeviceUtils } from "../base-ui/utils/device-utils";

interface RegistrationAppointmentDoctorListPageProps {
    registrationFormItem?: RegistrationFormItem;
}

export class RegistrationAppointmentDoctorListPage extends BaseBlocNetworkPage<
    RegistrationAppointmentDoctorListPageProps,
    RegistrationAppointmentDoctorListPageBloc
> {
    private _scrollRef?: ScrollView | null;

    static show(registrationFormItem?: RegistrationFormItem): Promise<RegistrationFormItem> {
        return ABCNavigator.navigateToPage(<RegistrationAppointmentDoctorListPage registrationFormItem={registrationFormItem} />);
    }

    private _selectedIndex = 0;
    private _currentMaxOffset = { l: 0 };
    private _viewPager?: ViewPager | null;

    constructor(props: RegistrationAppointmentDoctorListPageProps) {
        super(props);
        this.bloc = new RegistrationAppointmentDoctorListPageBloc({ registrationFormItem: props.registrationFormItem });
    }

    getAPPBarCustomTitle(): JSX.Element | undefined {
        const state = this.bloc.currentState;
        return (
            <AbcView
                style={[
                    ABCStyles.rowAlignCenter,
                    DeviceUtils.isAndroid() ? { width: Sizes.dp160, justifyContent: "center" } : { maxWidth: Sizes.dp160 },
                ]}
                onClick={() => {
                    this.bloc.requestModifyDepartment();
                }}
            >
                <Text style={[TextStyles.t16MT1, { flexShrink: 1 }]} numberOfLines={1}>
                    {state.registrationFormItem.departmentName ?? ""}
                </Text>
                <IconFontView name={"arrow_down"} color={Colors.black} size={Sizes.dp16} />
            </AbcView>
        );
    }

    getRightAppBarIcons(): JSX.Element[] {
        return [
            <AbcView
                key={"Search"}
                style={{ paddingHorizontal: Sizes.dp8 }}
                onClick={() => {
                    this.bloc.requestSearchDoctor();
                }}
            >
                <IconFontView name={"Search"} size={Sizes.dp20} color={Colors.black} />
            </AbcView>,
        ];
    }

    getAppBar(): JSX.Element {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }

    componentDidMount(): void {
        BlocHelper.connectLoadingStatus(this.bloc, this);
    }

    private handlePageSelected({ position }: AnyType): void {
        this._selectedIndex = position;
        //滚动当前选中项在可视范围内
        const currentOffsetX = position * Sizes.dp80;
        if (currentOffsetX < this._currentMaxOffset.l) {
            this._scrollRef?.scrollToWithDuration(currentOffsetX, 0, 300);
        } else if (currentOffsetX + Sizes.dp80 > this._currentMaxOffset.l + UiUtils.getScreenWidth()) {
            this._scrollRef?.scrollToWithDuration(currentOffsetX - UiUtils.getScreenWidth() + Sizes.dp80, 0, 300);
        }
        this.forceUpdate();
    }

    private _renderTimeListView(): JSX.Element {
        const state = this.bloc.currentState;
        return (
            <View style={{ height: Sizes.dp58 }}>
                <ScrollView
                    ref={(ref) => (this._scrollRef = ref)}
                    scrollEventThrottle={30}
                    horizontal={true}
                    style={{ backgroundColor: Colors.white }}
                    showsHorizontalScrollIndicator={false}
                    onScroll={(event) => {
                        //@ts-ignore
                        const { layoutMeasurement, contentSize, contentOffset } = event;
                        this._currentMaxOffset = {
                            l: contentOffset.x,
                        };
                        if (!state.loading && layoutMeasurement.width + contentOffset.x + 160 > contentSize.width) {
                            this.bloc.requestLoadMoreDoctorShift();
                        }
                    }}
                >
                    {[...state.doctorShiftsListByTime.keys()].map((item, index) => {
                        const selected = index == this._selectedIndex;
                        const textStyle = selected ? TextStyles.t14NM : TextStyles.t14NT1;
                        return (
                            <AbcView
                                key={index}
                                style={[
                                    {
                                        width: Sizes.dp80,
                                        borderWidth: Sizes.dpHalf,
                                        borderColor: Colors.dividerLineColor,
                                        borderRadius: DeviceUtils.isIOS() ? 3 : undefined,
                                    },
                                    selected ? { borderBottomColor: Colors.mainColor, borderBottomWidth: Sizes.dp2 } : {},
                                ]}
                                onClick={() => {
                                    this._viewPager?.setPage(index);
                                }}
                            >
                                <View
                                    style={[
                                        selected ? { height: Sizes.dp56 } : { height: Sizes.dp58 },
                                        { alignItems: "center", justifyContent: "center" },
                                    ]}
                                >
                                    <Text style={[textStyle]}>{TimeUtils.formatDate(new Date(item), "MM/dd")}</Text>
                                    <SizedBox height={Sizes.dp8} />
                                    <Text style={[textStyle]}>{TimeUtils.getDayOfWeekRecent(new Date(item))}</Text>
                                </View>
                            </AbcView>
                        );
                    })}
                    {state.timeListLoading && (
                        <View style={[ABCStyles.rowAlignCenterWithPadding, { width: Sizes.dp60, height: Sizes.dp58 }]}>
                            <LoadingView size={Sizes.dp12} />
                        </View>
                    )}
                </ScrollView>
            </View>
        );
    }

    renderContent(): JSX.Element | undefined {
        const state = this.bloc.currentState;
        return (
            <View style={{ flex: 1 }}>
                {this._renderTimeListView()}
                <ViewPager
                    ref={(ref) => {
                        this._viewPager = ref;
                    }}
                    initialPage={this._selectedIndex}
                    style={{ flex: 1 }}
                    onPageSelected={this.handlePageSelected.bind(this)}
                >
                    {[...state.doctorShiftsListByTime.values()].map((item, index) => {
                        return (
                            <ListView
                                style={{ backgroundColor: Colors.white, flex: 1 }}
                                key={index}
                                numberOfRows={item.length}
                                dataSource={item}
                                scrollEventThrottle={300}
                                getRowKey={(index) => `${item[index].doctorId}${item[index].departmentId}` ?? UniqueKey()}
                                renderRow={(shift) => (
                                    <DoctorShiftsDetails
                                        doctor={shift}
                                        onClick={(doctor) => {
                                            this.bloc.requestCheckDoctor(doctor);
                                        }}
                                    />
                                )}
                            />
                        );
                    })}
                </ViewPager>
            </View>
        );
    }
}
