/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/9/8
 */
import { VisitSourceBaseInfo } from "../data/bean";
import { ClinicAgent } from "../../base-business/data/clinic-agent";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import { ChargeInvoiceDetailData, ChargeSourceFormType, Promotion } from "../../charge/data/charge-beans";
import _ from "lodash";

export class RegistrationUtils {
    static async formatVisitSource(params: VisitSourceBaseInfo): Promise<VisitSourceBaseInfo> {
        const visitSourceList = (await ClinicAgent.getPatientRelativeAdvise().catchIgnore())?.rows;
        const responseData = JsonMapper.deserialize(VisitSourceBaseInfo, params);

        if (!visitSourceList || !visitSourceList.length) return responseData;

        for (const visitSourceItem of visitSourceList) {
            if (visitSourceItem.children?.length) {
                const visitSourceFromItem = visitSourceItem?.children?.find(
                    (item) => item.id == params.visitSourceId || item.id == params.visitSourceFrom
                );
                if (visitSourceFromItem) {
                    responseData.visitSourceId = visitSourceFromItem.id;
                    responseData.visitSourceName = visitSourceItem.name;
                    responseData.relatedType = visitSourceItem.relatedType;
                    responseData.visitSourceFrom = visitSourceFromItem.id;
                    responseData.visitSourceFromName = visitSourceFromItem.name;
                    return responseData;
                }
            }
            if (visitSourceItem.id == params.visitSourceId) {
                responseData.visitSourceId = visitSourceItem.id;
                responseData.visitSourceName = visitSourceItem.name;
                return responseData;
            }
        }
        return responseData;
    }

    static filterRegistrationPromotions(detail: ChargeInvoiceDetailData): Promotion[] | undefined {
        let registrationPromotionsId: string | undefined = undefined;
        for (const chargeForm of detail.chargeForms ?? []) {
            if (chargeForm.sourceFormType == ChargeSourceFormType.registration) {
                registrationPromotionsId = chargeForm.chargeFormItems?.[0].id;
            }
        }
        const newPromotions: Promotion[] = [];
        detail.promotions?.forEach((item) => {
            const newItem = _.cloneDeep(item);
            newItem.discountPrice = item.discountPrice ?? 0;
            newItem.productItems = newItem.productItems?.filter((productItem) => productItem.id == registrationPromotionsId);
            // 营销卡项删除？？？
            // newItem.productItems?.forEach((productItem) => {
            //     newItem.discountPrice = (newItem.discountPrice ?? 0) + (productItem.discountPrice ?? 0);
            // });
            newPromotions.push(newItem);
        });
        return newPromotions;
    }
}
