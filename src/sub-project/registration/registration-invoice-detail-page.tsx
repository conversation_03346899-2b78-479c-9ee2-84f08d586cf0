import { BaseInvoicePage } from "./views/base-invoice-page";
import { RegistrationInvoicePageOldVersionBloc } from "./registration-invoice-page-old-version-bloc";
import { FocusItemKeys, OldReserveInfo, PayStatusV2, RegistrationStatusV2 } from "./data/bean";
import { Text, View } from "@hippy/react";
import React from "react";
import { ChargeAbnormalTips } from "../charge/view/charge-abnormal-dialog";
import AbcPatientCardInfoView from "../outpatient/views/new-patient-Info-view";
import { HistoryPermissionModuleType } from "../base-business/data/beans";
import { DentistryDoctorCard } from "./dentistry/views/dentistry-doctor-card";
import { TimeUtils } from "../common-base-module/utils";
import { ListSettingItem, ListSettingItemStyle } from "../base-ui/views/list-setting-item";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { AbcView } from "../base-ui/views/abc-view";
import { pxToDp } from "../base-ui/utils/ui-utils";
import abcI18Next from "../language/config";
import { AbcFlexTextInput } from "../base-ui/views/abc-flex-text-input";
import { ABCUtils } from "../base-ui/utils/utils";
import { PrecisionLimitFormatter } from "../base-ui/utils/formatter";
import { NumberKeyboardBuilder } from "../base-ui/views/keyboards/number-keyboard";
import { SizedBox } from "../base-ui";
import { AbcButton } from "../base-ui/views/abc-button";
import { isNil } from "lodash";
interface RegistrationInvoiceDetailPageProps {
    id?: string;
    doctorId?: string;
    oldReserveInfo?: OldReserveInfo;
}
export class RegistrationInvoiceDetailPage extends BaseInvoicePage<
    RegistrationInvoiceDetailPageProps,
    RegistrationInvoicePageOldVersionBloc
> {
    getAppBarTitle(): string {
        return this.bloc.currentState.isCreate ? "新增预约" : "编辑预约";
    }
    getRightAppBarIcons(): JSX.Element[] {
        const state = this.bloc.currentState;
        // 按钮是否可以点击
        const disabledClick = !state.isCreate && !state.hasChange;
        return [
            <View key={"dendistry_registration_finish"}>
                <AbcButton
                    style={{
                        height: Sizes.dp30,
                        width: Sizes.dp52,
                        backgroundColor: disabledClick ? Colors.bdColor : Colors.mainColor,
                    }}
                    key={"submit"}
                    pressColor={Colors.mainColorPress}
                    onClick={() => !disabledClick && this.bloc.requestFinishRegistration()}
                >
                    <Text style={[TextStyles.t14MS2, ABCStyles.rowAlignCenter]} numberOfLines={1}>
                        {state.isCreate ? "完成" : "保存"}
                    </Text>
                </AbcButton>
            </View>,
        ];
    }
    constructor(props: RegistrationInvoiceDetailPageProps) {
        super(props);
        this.bloc = new RegistrationInvoicePageOldVersionBloc({
            id: props.id,
            doctorId: props.doctorId,
            oldReserveInfo: props.oldReserveInfo,
        });
    }
    _renderSheBaoAbnormalTips(): JSX.Element {
        const { detail } = this.bloc.currentState;
        if (!detail?.chargeSheet?.isSheBaoAbnormal) return <View />;
        return (
            <ChargeAbnormalTips
                isShebaoAbnormal={detail?.chargeSheet?.isSheBaoAbnormal}
                onClick={() => {
                    this.bloc.requestHandleChargeAbnormal(detail!.chargeSheet!.isSheBaoAbnormal);
                }}
            />
        );
    }
    _renderNotSheBaoAbnormalTips(): JSX.Element {
        const { detail } = this.bloc.currentState;
        if (!detail?.chargeSheet?.isNotSheBaoAbnormal) return <View />;
        return (
            <ChargeAbnormalTips
                isShebaoAbnormal={!detail?.chargeSheet?.isNotSheBaoAbnormal}
                onClick={() => {
                    this.bloc.requestHandleChargeAbnormal(!detail!.chargeSheet!.isNotSheBaoAbnormal);
                }}
            />
        );
    }
    _renderPatientView(): JSX.Element {
        const { showErrorHint, detail, isCreate, diagnoseCount, canViewDiagnoseHistory, canSeePatientPhone } = this.bloc.currentState;
        return (
            <AbcPatientCardInfoView
                showErrorHint={showErrorHint}
                // 不能修改患者的情况： 挂号过来的 || 已诊的 || 已收费的 || 网诊
                patientSwitchable={isCreate}
                patient={detail?.patient}
                diagnoseCount={diagnoseCount}
                onChange={(patient) => {
                    this.bloc.requestModifyRegistrationPatient(patient);
                }}
                isEditing={false}
                isCanSeePatientHistoryInRegister={canViewDiagnoseHistory}
                type={HistoryPermissionModuleType.registration}
                canSeePatientMobileInRegister={canSeePatientPhone}
            />
        );
    }
    _renderDoctorCardView(): JSX.Element {
        const { detail, showErrorHint, disabledEditRevisitStatus } = this.bloc.currentState;
        const registrationInfo = detail?.registrationFormItem,
            referralSource = detail?.referralSource;
        //判断是否可编辑(类型、科室、医生、初/复诊、时间、备注、就诊来源)
        //待诊、待签、已签均可编辑；已诊、回诊只可编辑初/复诊；已退均不可编辑
        let isEditing = true;
        if (
            registrationInfo?.statusV2 == RegistrationStatusV2.waitingSignIn ||
            registrationInfo?.statusV2 == RegistrationStatusV2.waitingDiagnose
        ) {
            isEditing = true;
        } else if (
            registrationInfo?.statusV2 == RegistrationStatusV2.refunded ||
            registrationInfo?.statusV2 == RegistrationStatusV2.diagnosed ||
            registrationInfo?.statusV2 == RegistrationStatusV2.continueDiagnose
        ) {
            isEditing = false;
        }
        return (
            <DentistryDoctorCard
                isEditing={isEditing}
                isEditDiagnoseType={
                    registrationInfo?.statusV2 == RegistrationStatusV2.diagnosed ||
                    registrationInfo?.statusV2 == RegistrationStatusV2.continueDiagnose
                }
                showErrorHint={showErrorHint}
                registrationInfo={detail?.registrationFormItem}
                revisitStatusName={detail?.__revisitStatusName}
                disabledEditRevisit={disabledEditRevisitStatus}
                referralSource={referralSource}
                onChangeType={(index) => {
                    this.bloc.requestModifyRegistrationType(index);
                }}
                onChangeDepartment={() => {
                    this.bloc.requestModifyRegistrationDepartment();
                }}
                onChangeDoctor={() => {
                    this.bloc.requestModifyRegistrationDoctor();
                }}
                onChangeRevisit={() => {
                    this.bloc.requestModifyRevisitStatus();
                }}
            />
        );
    }
    _renderFixedSourceTimeView(): JSX.Element {
        const { showErrorHint, detail } = this.bloc.currentState,
            registrationInfo = detail?.registrationFormItem,
            time = registrationInfo?.reserveDate;
        let dateStr: string | undefined, timeStr: string | undefined, periodTimeStr: string | undefined, orderNo: string | undefined;
        //预约1，挂号0
        if (registrationInfo?.isReserved) {
            dateStr = time ? TimeUtils.formatDate(time) : undefined;
            periodTimeStr = registrationInfo.timeOfDay ?? "";
            orderNo = registrationInfo?.displayOrderNumber ?? "";
            timeStr = !!registrationInfo?.reserveTime
                ? `${orderNo} ${registrationInfo?.reserveTime?.start} ~ ${registrationInfo?.reserveTime?.end}`
                : "";
        } else {
            dateStr = time ? `${TimeUtils.formatDatetimeAsRecent(time, { time: false })} ${registrationInfo?.timeOfDay ?? ""}` : undefined;
            timeStr = time
                ? `${registrationInfo?.displayOrderNumber} ${
                      (registrationInfo?._registrationReserveTime?.start || registrationInfo?.reserveTime?.start) ?? ""
                  } ~ ${(registrationInfo?._registrationReserveTime?.end || registrationInfo?.reserveTime?.end) ?? ""}`
                : undefined;
        }

        //判断是否可编辑(类型、科室、医生、初/复诊、时间、备注、就诊来源)
        //待诊、待签、已签均可编辑；已诊只可编辑初/复诊；已退均不可编辑
        let isEditing = true;
        if (
            registrationInfo?.statusV2 == RegistrationStatusV2.waitingSignIn ||
            registrationInfo?.statusV2 == RegistrationStatusV2.waitingDiagnose
        ) {
            isEditing = true;
        } else if (
            registrationInfo?.statusV2 == RegistrationStatusV2.refunded ||
            registrationInfo?.statusV2 == RegistrationStatusV2.diagnosed ||
            registrationInfo?.statusV2 == RegistrationStatusV2.continueDiagnose
        ) {
            isEditing = false;
        }

        return (
            <View>
                <ListSettingItem
                    ref={FocusItemKeys.reserveDate + "date"}
                    style={{ marginHorizontal: Sizes.dp16 }}
                    itemStyle={ListSettingItemStyle.normal}
                    bottomLine={true}
                    title={"日期"}
                    contentBuilder={() => {
                        return (
                            <View
                                style={[
                                    ABCStyles.rowAlignCenter,
                                    {
                                        justifyContent: "flex-end",
                                    },
                                ]}
                            >
                                <AbcView
                                    style={[
                                        !!periodTimeStr
                                            ? { width: Sizes.dp109 }
                                            : {
                                                  width: pxToDp(172),
                                              },
                                        {
                                            backgroundColor: Colors.whiteSmoke,
                                            justifyContent: "center",
                                            alignItems: "center",
                                            borderRadius: Sizes.dp4,
                                            height: Sizes.dp38,
                                        },
                                    ]}
                                    onClick={() => isEditing && this.bloc.requestModifyRegistrationTime()}
                                >
                                    <Text style={[TextStyles.t16NT1.copyWith({ color: !!dateStr ? Colors.T1 : Colors.T4 })]}>
                                        {dateStr ?? "选择日期"}
                                    </Text>
                                </AbcView>
                                {!!periodTimeStr && (
                                    <AbcView
                                        style={[
                                            Sizes.paddingLTRB(Sizes.dp12, 0),
                                            {
                                                backgroundColor: Colors.whiteSmoke,
                                                marginLeft: Sizes.dp7,
                                                height: Sizes.dp38,
                                                borderRadius: Sizes.dp4,
                                                justifyContent: "center",
                                                alignItems: "center",
                                            },
                                        ]}
                                        onClick={() => isEditing && this.bloc.requestModifyFixedTimeRange()}
                                    >
                                        <Text style={[TextStyles.t16NT1]}>{periodTimeStr}</Text>
                                    </AbcView>
                                )}
                            </View>
                        );
                    }}
                    titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                    leftAlignment={{ alignSelf: undefined }}
                />
                <ListSettingItem
                    ref={FocusItemKeys.reserveDate + "time"}
                    style={{ paddingHorizontal: Sizes.dp16 }}
                    itemStyle={ListSettingItemStyle.normal}
                    bottomLine={false}
                    title={"号数"}
                    contentHint={"选择号数"}
                    onClick={() => !!timeStr && isEditing && this.bloc.requestModifyOrderNo()}
                    contentBuilder={() => {
                        return (
                            <View
                                style={[
                                    ABCStyles.rowAlignCenter,

                                    {
                                        justifyContent: "flex-end",
                                    },
                                ]}
                            >
                                <View
                                    style={[
                                        {
                                            width: pxToDp(172),

                                            backgroundColor: Colors.whiteSmoke,
                                            justifyContent: "center",
                                            alignItems: "center",
                                            borderRadius: Sizes.dp4,
                                            height: Sizes.dp38,
                                        },
                                        showErrorHint && !timeStr ? { backgroundColor: Colors.Y4 } : {},
                                    ]}
                                >
                                    <Text style={[TextStyles.t16NT1.copyWith({ color: !!timeStr ? Colors.T1 : Colors.T4 })]}>
                                        {!!timeStr ? timeStr : "无可用号源"}
                                    </Text>
                                </View>
                            </View>
                        );
                    }}
                    titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                    leftAlignment={{ alignSelf: undefined }}
                />
            </View>
        );
    }
    _renderVisitSource(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail,
            registrationInfo = detail?.registrationFormItem;
        // 是否禁用就诊推荐修改
        // 有挂号单并且不可以修改就诊推荐或者配置的只读都需要禁用就诊推荐修改
        // const isDisableDoctorRemmend = !!this.props?.id && !state.canModifyRecommendation;
        //判断是否可编辑(类型、科室、医生、初/复诊、时间、备注、就诊来源)
        //待诊、待签、已签均可编辑；已诊只可编辑初/复诊；已退均不可编辑
        let isEditing = true;
        if (
            registrationInfo?.statusV2 == RegistrationStatusV2.waitingSignIn ||
            registrationInfo?.statusV2 == RegistrationStatusV2.waitingDiagnose
        ) {
            isEditing = true;
        } else if (
            registrationInfo?.statusV2 == RegistrationStatusV2.refunded ||
            registrationInfo?.statusV2 == RegistrationStatusV2.diagnosed ||
            registrationInfo?.statusV2 == RegistrationStatusV2.continueDiagnose
        ) {
            isEditing = false;
        }
        return (
            <View style={{ backgroundColor: Colors.white }}>
                <ListSettingItem
                    itemStyle={isEditing ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal}
                    title={"本次推荐"}
                    style={{ marginHorizontal: Sizes.listHorizontalMargin }}
                    contentHint={"未指定"}
                    contentStyle={[ABCStyles.rowAlignCenter, { paddingVertical: Sizes.dp16, justifyContent: "flex-end" }]}
                    content={!!state.detail?.__visitSourceDisplayName ? state.detail?.__visitSourceDisplayName : ""}
                    bottomLine={true}
                    onClick={() => {
                        if (isEditing) this.bloc.requestModifyRegistrationVisitSource();
                    }}
                    titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                    leftAlignment={{ alignSelf: undefined }}
                />
                <ListSettingItem
                    itemStyle={isEditing ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal}
                    title={"备注"}
                    style={{ paddingHorizontal: Sizes.listHorizontalMargin }}
                    contentStyle={[ABCStyles.rowAlignCenter, { paddingVertical: Sizes.dp16, justifyContent: "flex-end" }]}
                    content={detail?.visitSourceRemark}
                    bottomLine={false}
                    onClick={() => {
                        if (isEditing) this.bloc.requestModifyRegistrationVisitSourceRemark(detail?.visitSourceRemark ?? "");
                    }}
                    contentNumberOfLine={1}
                    titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                    leftAlignment={{ alignSelf: undefined }}
                />
            </View>
        );
    }

    _renderFeeView(): JSX.Element {
        const { detail, pay, shouldRegisteredBargain, isCreate, disabledEditCharge } = this.bloc.currentState;
        const registrationInfo = detail?.registrationFormItem;
        const isEditing = this._isEditing();
        let statusText: string | number | string[] | undefined = undefined;
        let statusTextStyle = {};
        let feeDisplay = 0;
        if (registrationInfo?.payStatusV2 == PayStatusV2.paid) {
            statusText = "已收费";
            statusTextStyle = TextStyles.t14NM;
            feeDisplay = detail?.chargeSheet?.chargeSheetSummary?.receivedFee ?? 0;
        } else if (registrationInfo?.payStatusV2 == PayStatusV2.refunded) {
            statusText = "已退费";
            statusTextStyle = TextStyles.t14NT2;
            feeDisplay = 0;
        } else if (registrationInfo?.payStatusV2 == PayStatusV2.partedRefunded) {
            statusText = "部分退费";
            statusTextStyle = TextStyles.t14NT2;
        } else if (registrationInfo?.payStatusV2 == PayStatusV2.partedPaid) {
            statusText = "欠收";
            statusTextStyle = TextStyles.t14NM;
            feeDisplay = detail?.chargeSheet?.chargeSheetSummary?.needPayFee ?? 0;
        } else {
            feeDisplay = pay?.fee ?? 0;
        }

        return (
            <ListSettingItem
                ref={FocusItemKeys.fee}
                style={{ marginHorizontal: Sizes.dp16, backgroundColor: Colors.white }}
                contentStyle={{ alignItems: "flex-end", justifyContent: "center" }}
                contentTextStyle={TextStyles.t18MY2}
                title={"诊费"}
                contentBuilder={() => {
                    return (
                        <View
                            style={[ABCStyles.rowAlignCenter, { width: Sizes.dp160, justifyContent: "flex-end" }]}
                            onClick={() => {
                                return;
                            }}
                        >
                            {!!statusText && (
                                <Text style={[statusTextStyle, { alignSelf: "center", marginRight: Sizes.dp12 }]}>{statusText}</Text>
                            )}
                            <Text
                                style={[
                                    TextStyles.t16NT1.copyWith({
                                        color: !shouldRegisteredBargain || (!isCreate && disabledEditCharge) ? Colors.T2 : Colors.T1,
                                    }),
                                    { paddingRight: 4 },
                                ]}
                            >
                                {abcI18Next.t("¥")}
                            </Text>
                            <AbcFlexTextInput
                                returnKeyType={"done"}
                                editable={shouldRegisteredBargain && !disabledEditCharge && isEditing}
                                multiline={false}
                                syncTextOnBlur={true} //解决更改值后切换医生props不改变问题
                                numberOfLines={1}
                                placeholder={"点击输入"}
                                defaultValue={ABCUtils.formatPrice(feeDisplay ?? 0)}
                                formatter={PrecisionLimitFormatter(2)}
                                customKeyboardBuilder={new NumberKeyboardBuilder({})}
                                style={{
                                    underlineColorAndroid: Colors.white,
                                    backgroundColor: Colors.white,
                                    height: Sizes.listItemHeight,
                                    ...TextStyles.t16NB.copyWith({
                                        color: !shouldRegisteredBargain || (!isCreate && disabledEditCharge) ? Colors.T2 : Colors.black,
                                    }),
                                }}
                                onChangeText={(value) => {
                                    !disabledEditCharge && isEditing && this.bloc.requestModifyRegistrationFee(value);
                                }}
                            />
                            <SizedBox width={Sizes.dp16} />
                        </View>
                    );
                }}
                bottomLine={registrationInfo?.payStatusV2 == PayStatusV2.notPaid || isCreate}
                titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                leftAlignment={{ alignSelf: undefined }}
            />
        );
    }

    _renderDiscountView(): JSX.Element {
        const { detail, isCreate } = this.bloc.currentState;
        const registrationInfo = detail?.registrationFormItem;
        const isCanSelectDiscount = registrationInfo?.payStatusV2 == PayStatusV2.notPaid || isCreate;
        const discountFee = detail?.chargeSheet?.chargeSheetSummary?.discountFee;
        if (!isCanSelectDiscount) return <View />;
        const isEditing = this._isEditing();
        return (
            <ListSettingItem
                style={{ paddingHorizontal: Sizes.dp16 }}
                itemStyle={isEditing ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal}
                bottomLine={false}
                title={"优惠"}
                contentHint={"无可用优惠"}
                content={!isNil(discountFee) ? ABCUtils.formatPriceWithRMB(discountFee, false).toString() : ""}
                contentStyle={[
                    ABCStyles.rowAlignCenter,
                    {
                        paddingVertical: Sizes.dp16,
                        justifyContent: "flex-end",
                    },
                ]}
                onClick={() => isEditing && this.bloc.requestDiscountDetail()}
                titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                leftAlignment={{ alignSelf: undefined }}
            />
        );
    }

    private _isEditing(): boolean {
        const { detail } = this.bloc.currentState;
        const registrationInfo = detail?.registrationFormItem;
        //判断是否可编辑(类型、科室、医生、初/复诊、时间、备注、就诊来源)
        //待诊、待签、已签均可编辑；已诊、回诊只可编辑初/复诊；已退均不可编辑
        let isEditing = true;
        if (
            registrationInfo?.statusV2 == RegistrationStatusV2.waitingSignIn ||
            registrationInfo?.statusV2 == RegistrationStatusV2.waitingDiagnose
        ) {
            isEditing = true;
        } else if (
            registrationInfo?.statusV2 == RegistrationStatusV2.refunded ||
            registrationInfo?.statusV2 == RegistrationStatusV2.diagnosed ||
            registrationInfo?.statusV2 == RegistrationStatusV2.continueDiagnose
        ) {
            isEditing = false;
        }
        return isEditing;
    }
}
