/**
 * create by deng<PERSON>e
 * desc:
 * create date 2021/1/18
 */

import { Bloc, BlocEvent } from "../bloc";
import React from "react";
import { EventName } from "../bloc/bloc";
import { RegistrationAgent } from "./data/registration-agent";
import { BaseLoadingState } from "../bloc/bloc-helper";
import { DoctorShiftsWithTime } from "./data/bean";
import { DoctorShiftTimeDialog } from "./views/doctor-shift-time-dialog";
import { ClinicAgent } from "../base-business/data/clinic-agent";
import { ClinicDoctorInfo } from "../base-business/data/beans";
import { Range } from "../base-ui/utils/value-holder";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import _ from "lodash";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { Subject } from "rxjs";
import { switchMap } from "rxjs/operators";
import { ABCError } from "../common-base-module/common-error";

export class State extends BaseLoadingState {
    loading = true;
    doctorDetail?: ClinicDoctorInfo;
    shiftsList: DoctorShiftsWithTime[] = [];

    shiftsLoading = false;
    pageNo = 1;
    shiftsRange: Range<Date> = new Range(new Date());

    clone(): State {
        return Object.assign(new State(), this);
    }
}

export class RegistrationDoctorShiftPageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<RegistrationDoctorShiftPageBloc | undefined>(undefined);
    private _doctorId: string;
    private _departmentId: string;

    static fromContext(context: RegistrationDoctorShiftPageBloc): RegistrationDoctorShiftPageBloc {
        return context;
    }

    constructor(options: { doctorId: string; departmentId: string }) {
        super();
        this._doctorId = options.doctorId;
        this._departmentId = options.departmentId;
        this.dispatch(new _EventInit()).then();
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    private _loadShiftsTrigger: Subject<number> = new Subject<number>();
    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventCheckDoctor, this._mapEventCheckDoctor);
        map.set(_EventLoadMore, this._mapEventLoadMore);

        return map;
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    private _pageNo2Data(): Date {
        return new Date(new Date().getTime() + this.innerState.pageNo * 60 * 60 * 24 * 1000 * 20);
    }

    private async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        Promise.all([ClinicAgent.getClinicDoctorOwnRegfee(this._doctorId), ClinicAgent.getClinicDoctorOwnRegfee(this._doctorId, 0, 1)])
            .then((rsp) => {
                const doctors = rsp[0]?.concat(rsp[1] ?? []);
                this.innerState.doctorDetail = doctors?.find((item) => item.departmentId == this._departmentId);
            })
            .then(() => {
                this._loadShiftsTrigger.next();
            });

        this._loadShiftsTrigger
            .pipe(
                switchMap(() => {
                    const doctor = this.innerState.doctorDetail;
                    this.innerState.shiftsLoading = true;
                    if (this.innerState.shiftsRange.end) {
                        this.innerState.shiftsRange.start = this.innerState.shiftsRange.end;
                        this.innerState.shiftsRange.end = this._pageNo2Data();
                    } else {
                        this.innerState.shiftsRange.end = this._pageNo2Data();
                    }
                    return RegistrationAgent.getDoctorShiftsListForApp({
                        doctorId: doctor!.doctorId!,
                        departmentId: doctor!.departmentId!,
                        timeRange: this.innerState.shiftsRange,
                    })
                        .catch((e) => new ABCError(e))
                        .toObservable();
                })
            )
            .subscribe((doctorInfo) => {
                this.innerState.shiftsLoading = false;
                if (doctorInfo instanceof ABCError) {
                } else {
                    this.innerState.loading = false;
                    doctorInfo.shifts?.forEach((shift) => {
                        const newData = JsonMapper.deserialize(
                            DoctorShiftsWithTime,
                            _.assign(
                                {
                                    doctorId: this.innerState.doctorDetail?.doctorId,
                                    doctorName: this.innerState.doctorDetail?.doctorName,
                                    registrationFee: this.innerState.doctorDetail?.regUnitPrice,
                                },
                                shift
                            )
                        );
                        this.innerState.shiftsList.push(newData);
                    });
                }
                this.update();
            })
            .addToDisposableBag(this);
    }

    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    private async *_mapEventLoadMore(): AsyncGenerator<State> {
        if (this.innerState.pageNo == 10) return;
        this.innerState.pageNo += 1;
        this._loadShiftsTrigger.next();
    }

    private async *_mapEventCheckDoctor(event: _EventCheckDoctor): AsyncGenerator<State> {
        const doctor = event.doctor;
        if (doctor.canReserve) {
            if (doctor.restCountToday) {
                //有号 选择时间
                const result = await DoctorShiftTimeDialog.show(doctor);
                if (result) {
                    ABCNavigator.pop({ doctor: doctor, result: result });
                }
            }
        }
        this.update();
    }

    public requestCheckDoctor(shift: DoctorShiftsWithTime): void {
        this.dispatch(new _EventCheckDoctor(shift));
    }

    public requestLoadMore(): void {
        this.dispatch(new _EventLoadMore());
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventCheckDoctor extends _Event {
    doctor: DoctorShiftsWithTime;

    constructor(doctor: DoctorShiftsWithTime) {
        super();
        this.doctor = doctor;
    }
}

class _EventLoadMore extends _Event {}
