/**
 * create by deng<PERSON>e
 * desc: 预约的医生信息卡片展示
 * create date 2021/1/7
 */
import React from "react";
import { BaseComponent } from "../../base-ui/base-component";
import { FocusItemKeys, PayStatusV2, RegistrationFormItem, RegistrationStatusV2, RegistrationReferralSource } from "../data/bean";
import { View, Text } from "@hippy/react";
import { TimeUtils } from "../../common-base-module/utils";
import { ListSettingItem, ListSettingItemStyle, ListSettingRadiosItem } from "../../base-ui/views/list-setting-item";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { IconFontView } from "../../base-ui";
import { ignore } from "../../common-base-module/global";
import _ from "lodash";
import { UIUtils } from "../../base-ui/utils";
import { OverlayTips } from "../../base-ui/dialog/overlay-tips";
import { AbcView } from "../../base-ui/views/abc-view";
import { Range } from "../../base-ui/utils/value-holder";

interface AppointmentDoctorCardProps {
    isEditing?: boolean;
    showErrorHint?: boolean;
    registrationInfo?: RegistrationFormItem;
    referralSource?: RegistrationReferralSource;
    revisitStatusName?: string;
    disabledEditRevisit?: boolean;

    onChangeType?(index: number): void;

    onChangeDepartment?(): void;

    onChangeDoctor?(): void;

    onChangeTime?(): void;

    onChangeRevisit?(): void;
}

export class AppointmentDoctorCard extends BaseComponent<AppointmentDoctorCardProps> {
    private _iconViewRef?: View | null;

    constructor(props: AppointmentDoctorCardProps) {
        super(props);
    }

    private _formatReserveInfoType(reason?: number): string {
        let str = "";
        switch (reason) {
            case 1:
                str = "提前签到";
                break;
            case 2:
                str = "延后签到";
                break;
            case 3:
                str = "提前就诊";
                break;
            case 4:
                str = "延后就诊";
                break;
        }
        return str;
    }

    async _showTip(): Promise<void> {
        const registrationInfo = this.props.registrationInfo;
        const oldReserveInfo = registrationInfo?.oldReserveInfo;
        if (!oldReserveInfo) return;
        const layout = await UIUtils.measureInWindow(this._iconViewRef);
        OverlayTips.show(
            `预计就诊时间：${oldReserveInfo.reserveDate} ${oldReserveInfo.reserveStart} \n实际就诊时间：${TimeUtils.formatDate(
                registrationInfo?.reserveDate
            )} ${registrationInfo?.reserveTime?.start}`,
            layout,
            {
                preferPosition: "center",
                backgroundColor: Colors.black,
                textStyle: { ...TextStyles.t14NW, paddingHorizontal: Sizes.dp10 },
                arrowSize: Sizes.dp10,
                borderRadius: Sizes.dp4,
            }
        ).then();
    }

    // 显示时间段（处理挂号选择时间段或精确时间段的显示）
    private _showTimePeriod(): string {
        const { registrationInfo } = this.props;
        const { _registrationReserveTime, reserveTime } = registrationInfo || {};
        if (_.isEqual(_registrationReserveTime || reserveTime, new Range<string>("00:00", "12:00"))) {
            return registrationInfo!.timeOfDay ?? "";
        } else if (_.isEqual(_registrationReserveTime || reserveTime, new Range<string>("12:00", "18:00"))) {
            return registrationInfo!.timeOfDay ?? "";
        } else if (_.isEqual(_registrationReserveTime || reserveTime, new Range<string>("18:00", "24:00"))) {
            return registrationInfo!.timeOfDay ?? "";
        } else
            return `${registrationInfo!.timeOfDay ?? ""} ${(_registrationReserveTime?.start || reserveTime?.start) ?? ""} ~ ${
                (_registrationReserveTime?.end || reserveTime?.end) ?? ""
            }`;
    }

    private _renderCanEditCard(): JSX.Element {
        const {
            registrationInfo,
            referralSource,
            showErrorHint,
            onChangeDepartment,
            onChangeDoctor,
            onChangeType,
            onChangeTime,
            revisitStatusName,
            disabledEditRevisit,
            onChangeRevisit,
        } = this.props;
        const time = registrationInfo?.reserveDate;
        const departmentName = registrationInfo?.departmentName || (registrationInfo?.departmentName === "" ? "其他" : undefined);
        const timeDisplay = (() => {
            let str = "";
            if (time) {
                // 预约
                if (registrationInfo?.isReserved) {
                    str =
                        !!registrationInfo?._reserveTime || !!registrationInfo?.reserveTime
                            ? `${TimeUtils.formatDatetimeAsRecent(time, { time: false })} ${
                                  registrationInfo?._reserveTime?.start ?? registrationInfo?.reserveTime?.start ?? ""
                              } ~ ${registrationInfo?._reserveTime?.end ?? registrationInfo?.reserveTime?.end ?? ""} ${
                                  registrationInfo?.displayOrderNumber
                              }`
                            : "";
                } else {
                    //  挂号
                    str = `${TimeUtils.formatDatetimeAsRecent(time, { time: false })} ${this._showTimePeriod()} ${
                        registrationInfo?.displayOrderNumber ?? ""
                    }`;
                }
            }
            return str;
        })();
        const type = ["预约", "挂号"];
        const itemStyle = ListSettingItemStyle.expandIcon;
        const referralDoctor = `${referralSource?.displayDoctorDepartName} ${
            referralSource?.referralTime?.format("yyyy-MM-dd") ?? ""
        } 转出`;

        return (
            <View style={[ABCStyles.bottomLine, { backgroundColor: Colors.white }]}>
                {referralSource && (
                    <View style={[{ paddingLeft: Sizes.dp16 }]}>
                        <ListSettingItem
                            style={[{ paddingRight: Sizes.dp16 }]}
                            itemStyle={ListSettingItemStyle.normal}
                            bottomLine={true}
                            title={"转诊"}
                            contentHint={"选择科室"}
                            content={referralDoctor}
                            contentNumberOfLine={1}
                        />
                    </View>
                )}
                <ListSettingRadiosItem
                    style={{ paddingLeft: Sizes.dp16 }}
                    bottomLine={true}
                    marginBetweenItem={Sizes.dp32}
                    title={"类型"}
                    options={type.reverse()}
                    check={type[registrationInfo?.isReserved ?? 0]}
                    enable={true}
                    onChanged={(option, index) => {
                        ignore(option);
                        onChangeType?.(index);
                    }}
                />
                <View
                    style={[showErrorHint && !registrationInfo?.departmentName ? ABCStyles.errorBorder : {}, { paddingLeft: Sizes.dp16 }]}
                >
                    <ListSettingItem
                        ref={FocusItemKeys.department}
                        style={{ paddingRight: Sizes.dp16 }}
                        itemStyle={itemStyle}
                        bottomLine={true}
                        title={"科室"}
                        contentHint={"选择科室"}
                        content={departmentName}
                        contentStyle={[
                            ABCStyles.rowAlignCenter,
                            {
                                paddingVertical: Sizes.dp14,
                                justifyContent: "flex-end",
                            },
                        ]}
                        onClick={() => onChangeDepartment?.()}
                    />
                </View>
                <View style={[{ paddingLeft: Sizes.dp16 }]}>
                    <ListSettingItem
                        style={{ paddingRight: Sizes.dp16 }}
                        itemStyle={itemStyle}
                        bottomLine={true}
                        title={"医生"}
                        contentHint={"医生"}
                        content={registrationInfo?.doctorName}
                        contentStyle={[
                            ABCStyles.rowAlignCenter,
                            {
                                paddingVertical: Sizes.dp14,
                                justifyContent: "flex-end",
                            },
                        ]}
                        onClick={() => onChangeDoctor?.()}
                    />
                </View>
                <View style={[{ paddingLeft: Sizes.dp16 }]}>
                    <ListSettingItem
                        style={{ paddingRight: Sizes.dp16 }}
                        title={"初诊/复诊"}
                        bottomLine={true}
                        itemStyle={disabledEditRevisit ? ListSettingItemStyle.normal : ListSettingItemStyle.expandIcon}
                        contentStyle={[
                            ABCStyles.rowAlignCenter,
                            {
                                paddingVertical: Sizes.dp14,
                                justifyContent: "flex-end",
                            },
                        ]}
                        content={revisitStatusName}
                        onClick={() => !disabledEditRevisit && onChangeRevisit?.()}
                    />
                </View>
                <View
                    style={[
                        showErrorHint && (!timeDisplay || !(registrationInfo?._reserveTime ?? registrationInfo?.reserveTime))
                            ? ABCStyles.errorBorder
                            : {},
                        { paddingLeft: Sizes.dp16 },
                    ]}
                >
                    <ListSettingItem
                        ref={FocusItemKeys.reserveDate}
                        style={{ paddingRight: Sizes.dp16 }}
                        itemStyle={itemStyle}
                        title={"时间"}
                        contentHint={"选择时间"}
                        content={timeDisplay}
                        contentStyle={[
                            ABCStyles.rowAlignCenter,
                            {
                                paddingVertical: Sizes.dp14,
                                justifyContent: "flex-end",
                            },
                        ]}
                        onClick={() => onChangeTime?.()}
                    />
                </View>
            </View>
        );
    }

    private _renderNotEditCard(): JSX.Element {
        const { registrationInfo } = this.props;
        const time = registrationInfo?.reserveDate;
        let textStyle = TextStyles.t16MT1.copyWith({ lineHeight: Sizes.dp25 });
        if (registrationInfo?.payStatusV2 == PayStatusV2.paid) {
        } else if (registrationInfo?.payStatusV2 == PayStatusV2.refunded) {
            textStyle = textStyle.copyWith({ color: Colors.T2 });
        } else if (registrationInfo?.payStatusV2 == PayStatusV2.partedRefunded) {
            textStyle = textStyle.copyWith({ color: Colors.T2 });
        } else if (registrationInfo?.payStatusV2 == PayStatusV2.partedPaid) {
        }
        const isFunded = (registrationInfo?.statusV2 ?? 0) == RegistrationStatusV2.refunded;
        return (
            <View style={[ABCStyles.bottomLine]}>
                <View style={[ABCStyles.centerChild, Sizes.paddingLTRB(Sizes.dp16), { backgroundColor: Colors.white }]}>
                    <Text style={textStyle.copyWith({ fontSize: Sizes.dp20, lineHeight: Sizes.dp28 })}>
                        {`${registrationInfo?.timeOfDay ?? ""} ${registrationInfo?.displayOrderNumber ?? ""}`}
                    </Text>
                    <Text style={[textStyle.copyWith({ fontWeight: "normal" }), { textAlign: "center" }]}>
                        {registrationInfo?.displayDoctorName ?? ""}
                    </Text>
                    {registrationInfo?.isReserved ? (
                        <Text style={textStyle.copyWith({ fontWeight: "normal" })}>
                            {`${TimeUtils.formatDatetimeAsRecent(time, { time: false })} ${registrationInfo?.reserveTime?.start} ~ ${
                                registrationInfo?.reserveTime?.end
                            }`}
                        </Text>
                    ) : (
                        <Text style={textStyle.copyWith({ fontWeight: "normal" })}>
                            {`${TimeUtils.formatDatetimeAsRecent(time, { time: false })} ${registrationInfo?.reserveTime?.start} ~ ${
                                registrationInfo?.reserveTime?.end
                            }`}
                        </Text>
                    )}
                </View>
                <View style={[{ position: "absolute", right: Sizes.dp12, top: Sizes.dp16 }]}>
                    {registrationInfo?.oldReserveInfo && (
                        <View
                            ref={(ref) => {
                                this._iconViewRef = ref;
                            }}
                        >
                            <AbcView
                                style={{ flexDirection: "row" }}
                                onClick={() => {
                                    this._showTip().then();
                                }}
                            >
                                <Text style={[TextStyles.t16NT2, { alignSelf: "center" }]}>
                                    {this._formatReserveInfoType(registrationInfo.oldReserveInfo.reason)}
                                </Text>
                                <IconFontView
                                    name={"information"}
                                    size={Sizes.dp16}
                                    color={Colors.T2}
                                    style={{ ...Sizes.paddingLTRB(Sizes.dp4) }}
                                />
                            </AbcView>
                        </View>
                    )}
                    {isFunded && (
                        <Text
                            style={[
                                TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp20 }),
                                Sizes.paddingLTRB(Sizes.dp6, 0),
                                { borderWidth: 1, borderRadius: Sizes.dp12, borderColor: Colors.T2, textAlign: "center" },
                            ]}
                        >
                            已退号
                        </Text>
                    )}
                </View>
            </View>
        );
    }

    render(): JSX.Element {
        const { isEditing } = this.props;
        return <View>{isEditing ? this._renderCanEditCard() : this._renderNotEditCard()}</View>;
    }
}
