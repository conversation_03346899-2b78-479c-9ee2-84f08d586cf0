/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2023/3/2
 * @Copyright 成都字节流科技有限公司© 2023
 */
import React from "react";
import {
    KanBanDragView,
    PageViewColValue,
    PageViewSort,
    RegistrationKanBanViewProps,
    RegistrationKanBanViewV3,
} from "../registrationKanBanView";
import { Text, View } from "@hippy/react";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../theme";
import { AbcScrollView2 } from "../../../base-ui/views/abc-scroll-view2";
import { AbcView } from "../../../base-ui/views/abc-view";
import { RegistrationInvoiceSummaryDialog } from "../../registration-invoice-summary-dialog";
import {
    DEFAULT_DOCTOR_ID,
    RegistrationDetail,
    RegistrationFormItem,
    RegistrationKanbanDailyEmployeeList,
    ScheduleDetailDepartment,
    ScheduleDetailDepartmentCell,
} from "../../data/bean";
import { JsonMapper } from "../../../common-base-module/json-mapper/json-mapper";
import { ABCNavigator, TransitionType } from "../../../base-ui/views/abc-navigator";
import { DentistryInvoicePage } from "../../dentistry/dentistry-invoice-page";
import { TimeUtils } from "../../../common-base-module/utils";
import { RegistrationType } from "../../dentistry/data/bean";
import { AppointmentInvoicePage } from "../../dentistry/appointment/appointment-invoice-page";
import { AppointmentInvoiceSummaryDialog } from "../../dentistry/appointment/appointment-invoice-summary-dialog";
import _ from "lodash";
import { Toast } from "../../../base-ui/dialog/toast";

interface RegistrationKanBanFixedOrderModeViewProps extends RegistrationKanBanViewProps {
    showOrderNo?: boolean;
    showCategories?: boolean;
    canModifyRegistration?: boolean; //能够修改挂号预约
}
export class RegistrationKanBanFixedOrderModeView extends RegistrationKanBanViewV3<RegistrationKanBanFixedOrderModeViewProps> {
    constructor(props: RegistrationKanBanFixedOrderModeViewProps) {
        super(props);
        this.scrollOffsetSyncName = "RegistrationKanBanFixedOrderModeView";
    }
    renderScrollRowHeaderView(): JSX.Element {
        return (
            <View style={[{ width: Sizes.dp51 }]}>
                <View style={{ height: Sizes.dp48 }} />
                <AbcScrollView2
                    showsHorizontalScrollIndicator={false}
                    showsVerticalScrollIndicator={false}
                    horizontal={false}
                    scrollOffsetSyncGroup={this.scrollOffsetSyncName}
                    style={[{ width: Sizes.dp51 }]}
                    scrollEventThrottle={1}
                >
                    {[...this.rowHeaderDisplayList.keys()].map((item, index) => {
                        const _heightCount = this.rowHeaderDisplayList.get(item) ?? [4, 0];
                        const height = this.dataGridHeight * Math.max(_heightCount[0], 4) + this.dataDescHeight * _heightCount[1];
                        return (
                            <View
                                key={index}
                                style={[
                                    ABCStyles.topLine,
                                    {
                                        height,
                                        width: Sizes.dp51,
                                    },
                                ]}
                            >
                                <Text
                                    style={[
                                        TextStyles.t11NT2.copyWith({ color: Colors.t2, lineHeight: Sizes.dp16 }),
                                        { marginTop: Sizes.dp18, marginLeft: Sizes.dp16 },
                                    ]}
                                >
                                    {`${item}`}
                                </Text>
                            </View>
                        );
                    })}
                </AbcScrollView2>
            </View>
        );
    }

    renderDescDepartmentView(text?: string): JSX.Element {
        if (!text || text == "null") return <View />;
        return (
            <View style={{ paddingTop: Sizes.dp12, paddingBottom: Sizes.dp4, paddingLeft: Sizes.dp2 }}>
                <Text style={[TextStyles.t14Nt2.copyWith({ lineHeight: Sizes.dp20 })]}>{text}</Text>
            </View>
        );
    }

    renderTabPanelDefaultView(list: PageViewColValue[]): JSX.Element {
        const { showOrderNo, showCategories } = this.props;
        const dataGridWidth = this.DATA_GRID_WIDTH * Math.max(1, 3 / list.length);
        return (
            <View style={{ flexDirection: "row" }}>
                {/*整体横向→*/}
                {list?.map((pv) => {
                    const item = pv.data;
                    //每一列↓
                    return (
                        <View key={`${item.reserveDate?.toDateString()}${item.employeeId}`} style={[ABCStyles.bottomLine]}>
                            {item.scheduleDetails?.map((scheduleDetail) => {
                                const _heightCount = this.rowHeaderDisplayList.get(scheduleDetail.timeOfDay ?? "") ?? [4, 0];
                                const height = this.dataGridHeight * Math.max(_heightCount[0], 4) + this.dataDescHeight * _heightCount[1];
                                return (
                                    <AbcView
                                        key={scheduleDetail.timeOfDay}
                                        style={[
                                            ABCStyles.topLine,
                                            {
                                                height: height,
                                                width: dataGridWidth,
                                            },
                                        ]}
                                        onClick={() => {
                                            const _scheduleDetail = scheduleDetail.departments
                                                ?.reverse()
                                                ?.find((dep) => !!dep.cells?.length);
                                            this.handleAddRegDetail(
                                                item,
                                                _scheduleDetail ?? ({ timeOfDay: scheduleDetail.timeOfDay } as ScheduleDetailDepartment)
                                            );
                                        }}
                                    >
                                        {scheduleDetail.departments?.map((dep, index) => (
                                            <View key={`${dep.timeOfDay}${dep.departmentId}${index}`}>
                                                {this.renderDescDepartmentView(
                                                    `${dep.departmentName}${showCategories ? `${dep.registrationCategoryDisplay}` : ""}`
                                                )}
                                                {dep.cells?.map((cell, index) => (
                                                    <FixedOrderDragView
                                                        key={`${index}${cell.timeOfDay}${cell.orderNo}`}
                                                        showOrderNo={showOrderNo}
                                                        detail={cell}
                                                        dataGridHeight={this.dataGridHeight}
                                                        onChange={(detail) => {
                                                            this.handleReviewRegDetail(item, dep, detail);
                                                        }}
                                                    />
                                                ))}
                                            </View>
                                        ))}
                                    </AbcView>
                                );
                            })}
                        </View>
                    );
                })}
            </View>
        );
    }

    initLeftHeaderList(props?: RegistrationKanBanFixedOrderModeViewProps): void {
        const tableRowHeader: Map<string, number[]> = new Map();
        const tableColHeader: Map<string | Date, number> = new Map();

        const list: PageViewColValue[] = [];
        if (((props ?? this.props).dailyViews?.length ?? 0) > 1) {
            (props ?? this.props).dailyViews?.map((dv) => {
                tableColHeader.set(dv.reserveDate!, 1);
                const employee = dv.dailyEmployeeList![0];
                employee.reserveDate = dv.reserveDate;
                employee?.scheduleDetails?.map((schedule) => {
                    const __tableHeaderYCount = tableRowHeader.get(schedule.timeOfDay ?? "");
                    let __count = 0;
                    schedule.departments?.map((scheduleDepartments) => {
                        __count += scheduleDepartments.cells?.length ?? 0;
                    });
                    tableRowHeader.set(schedule.timeOfDay ?? "", [
                        Math.max(4, __tableHeaderYCount?.[0] ?? 0, __count),
                        Math.max(__tableHeaderYCount?.[1] ?? 0, schedule.departments?.length ?? 0),
                    ]);
                });
                list.push({
                    data: employee,
                    headerKeyStr: employee.reserveDate,
                    notRefundedCount: 0,
                });
                this.tabSelectedIndex = 2;
            });
        } else {
            (props ?? this.props).dailyViews?.map((dv) => {
                dv?.dailyEmployeeList?.map((employee) => {
                    employee.reserveDate = dv.reserveDate;
                    let count = 0;
                    employee.scheduleDetails?.map((schedule) => {
                        const __tableHeaderYCount = tableRowHeader.get(schedule.timeOfDay ?? "");
                        let __count = 0;
                        schedule.departments?.map((scheduleDepartments) => {
                            __count += scheduleDepartments.cells?.length ?? 0;
                            count += scheduleDepartments.notRefundedCount ?? 0;
                        });
                        tableRowHeader.set(schedule.timeOfDay ?? "", [
                            Math.max(4, __tableHeaderYCount?.[0] ?? 0, __count),
                            Math.max(__tableHeaderYCount?.[1] ?? 0, schedule.departments?.length ?? 0),
                        ]);
                    });
                    const _employeeName = employee.employeeId === DEFAULT_DOCTOR_ID ? "不指定" : employee.employeeName;
                    tableColHeader.set(_employeeName ?? "", count);

                    list.push({
                        data: employee,
                        headerKeyStr: _employeeName,
                        notRefundedCount: count,
                    });
                });
            });
        }
        let _allCount = 0;
        [...tableRowHeader.values()].forEach((item) => {
            _allCount += item[0];
        });
        this.rowCount = _allCount;
        this.rowHeaderDisplayList = tableRowHeader;
        this.colHeaderDisplayList = tableColHeader;

        const pageViewDataList: Map<PageViewSort, PageViewColValue[]> = new Map();
        _.chunk(list, 3).map((item, index) => {
            pageViewDataList.set(index, item);
        });
        this.pageViewDataList = pageViewDataList;

        // if (!!this.colHeaderDisplayList.size && 3 / this.colHeaderDisplayList.size >= 1) {
        this.dataGridWidth = this.DATA_GRID_WIDTH * Math.max(1, 3 / this.colHeaderDisplayList.size);
        // }
    }

    handleReviewRegDetail(
        item: RegistrationKanbanDailyEmployeeList,
        scheduleDepDetail: ScheduleDetailDepartment,
        detail: ScheduleDetailDepartmentCell
    ): void {
        const { existedRegistration, start, end, orderNo, orderNoType, timeOfDay } = detail,
            { registrationFormItem } = existedRegistration ?? {};
        const { canModifyRegistration } = this.props;
        const _registrationFormItem = JsonMapper.deserialize(RegistrationFormItem, {
            departmentId: scheduleDepDetail?.departmentId,
            departmentName: scheduleDepDetail?.departmentName,
            orderNoTimeOfDay: timeOfDay,
            doctorId: item.employeeId,
            doctorName: item.employeeName,
            reserveDate: item?.reserveDate,
            reserveEnd: end,
            reserveStart: start,
            timeOfDay: scheduleDepDetail?.timeOfDay ?? timeOfDay,
            isReserved: (item?.reserveDate?.getTime() ?? 0) > TimeUtils.getTodayEnd().getTime() ? 1 : 0,
            registrationCategory: scheduleDepDetail?.registrationCategory,
            orderNo,
            orderNoType,
            ...registrationFormItem,
        });
        if (!!existedRegistration?.id) {
            if (this.isAppointment) {
                AppointmentInvoiceSummaryDialog.show({
                    id: existedRegistration?.id,
                    statusName: registrationFormItem?.statusName,
                });
            } else if (this.isRegistration) {
                RegistrationInvoiceSummaryDialog.show({
                    id: existedRegistration?.id,
                    doctorId: registrationFormItem?.doctorId,
                    oldReserveInfo: registrationFormItem?.oldReserveInfo,
                    statusName: registrationFormItem?.statusName,
                });
            }
        } else {
            ///根据当前类型判断(理疗预约/挂号)进入
            if (this.isRegistration) {
                if (detail.isExpired) {
                    Toast.show("已过期不可挂号", { warning: true }); // 已过期标签不可点击预约
                } else if (detail.isStopScheduleSelfStatus) {
                    Toast.show("已停诊不可挂号", { warning: true }); // 已停诊标签不可点击预约
                } else if (!canModifyRegistration) {
                    Toast.show("暂无挂号权限", { warning: true }); // 已停诊标签不可点击预约
                } else {
                    ABCNavigator.navigateToPage<RegistrationDetail>(
                        <DentistryInvoicePage
                            doctorId={_registrationFormItem.doctorId}
                            departmentId={_registrationFormItem.departmentId}
                            registrationFormItem={_registrationFormItem}
                            fromKanbanEntry={this.props?.viewMode}
                        />,
                        {
                            transitionType: TransitionType.inFromBottom,
                        }
                    );
                }
            } else if (this.isAppointment) {
                ABCNavigator.navigateToPage(
                    <AppointmentInvoicePage registrationFormItem={_registrationFormItem} fromKanbanEntry={this.props?.viewMode} />
                );
            }
        }
    }

    handleAddRegDetail(item: RegistrationKanbanDailyEmployeeList, scheduleDepDetail?: ScheduleDetailDepartment): void {
        if ((item?.reserveDate?.getTime() ?? 0) < TimeUtils.getTodayStart().getTime()) {
            return;
        }
        const registrationFormItem = JsonMapper.deserialize(RegistrationFormItem, {
            departmentId: scheduleDepDetail?.departmentId,
            departmentName: scheduleDepDetail?.departmentName,
            doctorId: item.employeeId,
            doctorName: item.employeeName,
            reserveDate: item?.reserveDate,
            reserveEnd: scheduleDepDetail?.end,
            reserveStart: scheduleDepDetail?.start,
            timeOfDay: scheduleDepDetail?.timeOfDay,
            isReserved: (item?.reserveDate?.getTime() ?? 0) > TimeUtils.getTodayEnd().getTime() ? 1 : 0,
            registrationCategory: scheduleDepDetail?.registrationCategory,
        });
        const { canModifyRegistration } = this.props;
        if (this.props.registrationType == RegistrationType.outpatientRegistration) {
            ABCNavigator.navigateToPage<RegistrationDetail>(
                <DentistryInvoicePage registrationFormItem={registrationFormItem} fromKanbanEntry={this.props?.viewMode} />,
                {
                    transitionType: TransitionType.inFromBottom,
                }
            );
        } else if (this.props.registrationType == RegistrationType.therapyAppointment) {
            if (!canModifyRegistration) {
                Toast.show("暂无预约权限", { warning: true }); // 已停诊标签不可点击预约
                return;
            }
            ABCNavigator.navigateToPage(
                <AppointmentInvoicePage registrationFormItem={registrationFormItem} fromKanbanEntry={this.props?.viewMode} />,
                { transitionType: TransitionType.inFromBottom }
            );
        }
    }
}

class FixedOrderDragView extends KanBanDragView {}
