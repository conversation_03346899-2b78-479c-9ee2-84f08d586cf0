/**
 * create by deng<PERSON>e
 * desc:
 * create date 2021/1/14
 */
import React from "react";
import { ScrollView, Style, Text, View } from "@hippy/react";
import { BaseComponent } from "../../base-ui/base-component";
import { BottomSheetCloseButton, showBottomSheet } from "../../base-ui/dialog/bottom_sheet";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { ABCStyles, Colors, flattenStyles, Sizes, TextStyles } from "../../theme";
import { SizedBox, ToolBarButtonStyle1 } from "../../base-ui";
import { TimeUtils } from "../../common-base-module/utils";
import { pxToDp } from "../../base-ui/utils/ui-utils";
import { GridView } from "../../base-ui/views/grid-view";
import { RegistrationAgent, ScheduleIntervalsItem, ShiftsListItem } from "../data/registration-agent";
import sizes from "../../theme/sizes";
import { SafeAreaBottomView } from "../../base-ui/safe_area_view";
import { DoctorShiftsType, DoctorShiftsWithTime, RegistrationsAppointmentConfig } from "../data/bean";
import { AbcView } from "../../base-ui/views/abc-view";
import _ from "lodash";
import DividerLine from "../../base-ui/divider-line";
import { StringUtils } from "../../base-ui/utils/string-utils";
import { ABCNetworkPageContentStatus, NetworkView } from "../../base-ui/base-page";

interface DoctorShiftTimeDialogProps {
    doctor: DoctorShiftsWithTime;
}

interface DoctorShiftTimeDialogState {
    currentShift?: ShiftsListItem;
}

export class DoctorShiftTimeDialog extends NetworkView<DoctorShiftTimeDialogProps, DoctorShiftTimeDialogState> {
    static async show(doctor: DoctorShiftsWithTime): Promise<ShiftsListItem | undefined> {
        return await showBottomSheet(<DoctorShiftTimeDialog doctor={doctor} />);
    }

    private _registrationAppointmentConfig?: RegistrationsAppointmentConfig;

    constructor(props: DoctorShiftTimeDialogProps) {
        super(props);
        this.state = {
            currentShift: undefined,
        };
    }

    componentDidMount(): void {
        this.setContentStatus(ABCNetworkPageContentStatus.loading);
        RegistrationAgent.getClinicRegistrationAppointmentConfig()
            .then((rsp) => {
                this._registrationAppointmentConfig = rsp;
                this.setContentStatus(ABCNetworkPageContentStatus.show_data);
            })
            .catch((e) => {
                this.setContentStatus(ABCNetworkPageContentStatus.error, e);
            });
    }

    private _renderLabel(text: string, color: string): JSX.Element {
        const size = Sizes.dp6;
        return (
            <View style={[ABCStyles.rowAlignCenter]}>
                <_PointView size={size} color={color} />
                <SizedBox width={size} />
                <Text style={TextStyles.t12NT1.copyWith({ color: color, lineHeight: Sizes.dp16 })}>{text}</Text>
            </View>
        );
    }

    private _renderShiftTimeItem(info: ShiftsListItem, select?: boolean): JSX.Element {
        const isCustomPart = this._registrationAppointmentConfig?.isCustomPart;
        const borderColor = select ? Colors.B1 : Colors.dividerLineColor;
        let textDefaultColor = Colors.T2,
            showPoint = false;
        const textSelectColor = Colors.B1;
        const disable = !info.available;

        switch (info.type) {
            case DoctorShiftsType.normal: {
                break;
            }
            case DoctorShiftsType.forPatient: {
                showPoint = true;
                textDefaultColor = Colors.mainColor;
                break;
            }
            case DoctorShiftsType.forMember: {
                showPoint = true;
                textDefaultColor = Colors.Y2;
                break;
            }
        }
        return (
            <AbcView
                key={info.start}
                style={[
                    sizes.marginLTRB(0, Sizes.dp4, Sizes.dp8, Sizes.dp4),
                    sizes.paddingLTRB(Sizes.dp8, Sizes.dp4),
                    { borderWidth: 1, borderRadius: Sizes.dp3 },
                    disable
                        ? { borderColor: Colors.dividerLineColor, backgroundColor: Colors.window_bg }
                        : {
                              borderColor: borderColor,
                          },
                ]}
                onClick={() => {
                    if (disable) return;
                    this.setState({ currentShift: info });
                }}
            >
                {showPoint && <_PointView color={textDefaultColor} style={{ position: "absolute", left: Sizes.dp4, top: Sizes.dp4 }} />}
                <Text
                    style={[
                        TextStyles.t14NT2.copyWith({
                            lineHeight: Sizes.dp20,
                            color: select ? textSelectColor : Colors.T2,
                        }),
                        { textAlign: "center" },
                    ]}
                >
                    {isCustomPart ? StringUtils.numberWithFillZero(info.orderNo ?? 0) : info._start ?? info.start}
                </Text>
            </AbcView>
        );
    }

    private _renderScheduleIntervalShiftTimeView(scheduleItem: ScheduleIntervalsItem, showScheduleTitle = true): JSX.Element {
        const isCustomPart = this._registrationAppointmentConfig?.isCustomPart;
        return (
            <View key={scheduleItem.start} style={[Sizes.paddingLTRB(Sizes.dp16, 0, Sizes.dp16, Sizes.dp9)]}>
                {showScheduleTitle && (
                    <Text style={[TextStyles.t14MT2.copyWith({ lineHeight: Sizes.dp20 }), { marginTop: Sizes.dp9 }]}>
                        {scheduleItem.timeOfDay}
                    </Text>
                )}
                {!!isCustomPart && (
                    <Text
                        style={[TextStyles.t14MT2.copyWith({ lineHeight: Sizes.dp20 }), { marginTop: showScheduleTitle ? Sizes.dp12 : 0 }]}
                    >
                        {`${scheduleItem.start} - ${scheduleItem.end}`}
                    </Text>
                )}
                <GridView itemHeight={Sizes.dp36} crossAxisCount={4} style={[{ marginTop: Sizes.dp4 }, { flex: 1 }]}>
                    {(scheduleItem.list ?? []).map((item) =>
                        this._renderShiftTimeItem(
                            item,
                            isCustomPart
                                ? this.state.currentShift?.start == item.start && this.state.currentShift.orderNo == item.orderNo
                                : this.state.currentShift?.start == item.start
                        )
                    )}
                </GridView>
            </View>
        );
    }

    private _renderShiftTime(): JSX.Element {
        const { scheduleIntervals } = this.props.doctor;
        let shiftList: ScheduleIntervalsItem[] = _.cloneDeep(scheduleIntervals) ?? [];
        if (!shiftList?.length) return <View />;
        if (this._registrationAppointmentConfig?.isCustomPart) {
            shiftList = shiftList.map((scheduleItem) => {
                scheduleItem.list = scheduleItem.list?.map((scheduleItemItem) => {
                    scheduleItemItem._end = scheduleItem.end;
                    scheduleItemItem._start = scheduleItem.start;
                    scheduleItemItem.timeOfDay = scheduleItem.timeOfDay;
                    return scheduleItemItem;
                });
                return scheduleItem;
            });
        }
        const timeOfDay: Set<string> = new Set<string>();
        return (
            <ScrollView showsVerticalScrollIndicator={false}>
                <SizedBox height={Sizes.dp7} />
                {shiftList.map((item) => {
                    const showTitle = !timeOfDay.has(item.timeOfDay);
                    timeOfDay.add(item.timeOfDay);
                    return this._renderScheduleIntervalShiftTimeView(item, showTitle);
                })}
            </ScrollView>
        );
    }

    private _renderDialogTitle(): JSX.Element {
        const { doctor } = this.props;
        return (
            <View style={[ABCStyles.rowAlignCenter, ABCStyles.bottomLine, { height: Sizes.dp48, paddingLeft: Sizes.dp16 }]}>
                <Text style={[TextStyles.t14NT3, { flex: 1 }]} numberOfLines={1}>{`${doctor.doctorName} ${TimeUtils.formatDate(
                    doctor.workingDate,
                    "MM-dd"
                )} (${doctor.dayOfWeek})`}</Text>
                {this._renderLabel("会员号", Colors.Y2)}
                <SizedBox width={Sizes.dp16} />
                {this._renderLabel("现场号", Colors.mainColor)}
                <BottomSheetCloseButton onTap={() => ABCNavigator.pop()} />
            </View>
        );
    }

    renderContent(): JSX.Element {
        const { doctor } = this.props;
        const currentShift = this.state.currentShift,
            hasCheck = !!this.state.currentShift;
        return (
            <View style={[{ backgroundColor: Colors.white }]}>
                <View style={[{ height: pxToDp(360) }]}>
                    {this._renderDialogTitle()}
                    {this._renderShiftTime()}
                    <View>
                        <DividerLine />
                        {hasCheck && (
                            <View style={[ABCStyles.centerChild, { marginTop: 12 }]}>
                                <Text style={TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp20 })} numberOfLines={1}>
                                    {`${doctor.doctorName}(${currentShift?.timeOfDay}${StringUtils.numberWithFillZero(
                                        currentShift?.orderNo ?? 0
                                    )} ${currentShift?._start ?? currentShift?.start}-${currentShift?._end ?? currentShift?.end})`}
                                </Text>
                            </View>
                        )}
                        <View
                            style={{
                                height: Sizes.toolbarHeight,
                                backgroundColor: Colors.white,
                                flexDirection: "row",
                                alignItems: "center",
                                paddingLeft: 12,
                                paddingRight: 12,
                            }}
                        >
                            <ToolBarButtonStyle1
                                text={"确定"}
                                onClick={
                                    hasCheck
                                        ? () => {
                                              ABCNavigator.pop(this.state.currentShift);
                                          }
                                        : undefined
                                }
                            />
                        </View>
                    </View>
                </View>
                <SafeAreaBottomView />
            </View>
        );
    }
}

interface _PointViewProps {
    color?: string;
    size?: number;
    style?: Style | Style[];
}

class _PointView extends BaseComponent<_PointViewProps> {
    constructor(props: _PointViewProps) {
        super(props);
    }

    render() {
        const size = this.props.size ?? Sizes.dp6,
            color = this.props.color,
            style = this.props.style;
        return <View style={[{ width: size, height: size, borderRadius: size, backgroundColor: color }, flattenStyles(style)]} />;
    }
}
