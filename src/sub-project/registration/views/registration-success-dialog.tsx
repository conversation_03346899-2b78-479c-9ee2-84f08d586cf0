/**
 * create by deng<PERSON><PERSON>
 * desc:
 * create date 2021/1/15
 */
import React from "react";
import { View, Text } from "@hippy/react";
import { BaseComponent } from "../../base-ui/base-component";
import { DialogBuilder, DialogButtonBuilder, DialogIndex } from "../../base-ui/dialog/dialog-builder";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { RegistrationDetail } from "../data/bean";
import { IconFontView, SizedBox } from "../../base-ui";
import { TimeUtils } from "../../common-base-module/utils";
import { ABCUtils } from "../../base-ui/utils/utils";
import { AssetImageView } from "../../base-ui/views/asset-image-view";
export enum RegistrationSuccessDialogType {
    finish = 1,
    signIn,
}

interface RegistrationSuccessDialogProps {
    type?: RegistrationSuccessDialogType;
    detail: RegistrationDetail;
    isShowCategories?: boolean; //是否显示号种
    isShowOrderNo?: boolean; //是否显示诊号
    isShowCharge?: boolean; // 是否显示收费
    doctorText?: string; //医生/理疗师文案显示
    isUpdateClinic?: boolean; //是否是升级诊所
    isShowUndetermined?: boolean; //是否展示待定文本
}

export class RegistrationSuccessDialog extends BaseComponent<RegistrationSuccessDialogProps> {
    static defaultProps = {
        isShowOrderNo: true,
        isShowCharge: true,
        isUpdateClinic: false,
        isShowUndetermined: false,
    };
    static async show(props: RegistrationSuccessDialogProps): Promise<DialogIndex> {
        const { chargeSheet } = props.detail;

        const view = <RegistrationSuccessDialog {...props} />;
        const builder = new DialogBuilder();
        const buttons = new DialogButtonBuilder();

        const price = (chargeSheet?.chargeSheetSummary?.receivableFee ?? 0) + (chargeSheet?.chargeSheetSummary?.refundFee ?? 0);
        if (!!price) {
            buttons.appendNegativeButton("暂不收费");
            if (props.isShowCharge) buttons.appendPositiveButton(`收费${ABCUtils.formatPriceWithRMB(price, false)}`);
        } else {
            buttons.appendNegativeButton("确定");
        }

        builder.button = buttons;
        builder.content = view;
        builder.contentPadding = Sizes.paddingLTRB(Sizes.dp24, Sizes.dp24);
        return await builder.show();
    }

    constructor(props: RegistrationSuccessDialogProps) {
        super(props);
    }

    private _renderDialogBar(): JSX.Element {
        const { detail, type } = this.props,
            { registrationFormItem } = detail;
        return (
            <View style={[ABCStyles.rowAlignCenter]}>
                <IconFontView style={{ paddingRight: Sizes.dp8 }} name={"Chosen"} size={Sizes.dp24} color={Colors.mainColor} />
                <Text style={TextStyles.t18MT1.copyWith({ lineHeight: Sizes.dp28 })}>
                    {`${type == RegistrationSuccessDialogType.signIn ? "签到" : registrationFormItem?.isReserved ? "预约" : "挂号"}成功`}
                </Text>
            </View>
        );
    }

    private _renderUpdateDialogBar(): JSX.Element {
        const { detail, type } = this.props,
            { registrationFormItem } = detail;
        return (
            <View style={{ flex: 1, alignItems: "center" }}>
                <AssetImageView name={"image_dlg_success"} style={{ width: Sizes.dp42, height: Sizes.dp42 }} />
                <SizedBox height={Sizes.dp16} />
                <Text style={TextStyles.t18MB.copyWith({ lineHeight: Sizes.dp28 })}>
                    {`${type == RegistrationSuccessDialogType.signIn ? "签到" : registrationFormItem?.isReserved ? "预约" : "挂号"}成功`}
                </Text>
            </View>
        );
    }

    private _renderContentItem(title: string, content: string, showBottomLine = true): JSX.Element {
        return (
            <View style={[{ flexDirection: "row", marginBottom: showBottomLine ? Sizes.dp6 : undefined }]}>
                <Text style={TextStyles.t16NT2.copyWith({ lineHeight: Sizes.dp24 })}>{title}：</Text>
                <Text style={[TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp24 }), { flex: 1 }]}>{content}</Text>
            </View>
        );
    }

    render(): JSX.Element {
        const { detail, type, isShowOrderNo, doctorText, isUpdateClinic, isShowCategories, isShowUndetermined } = this.props,
            { registrationFormItem, patient } = detail;
        const time = registrationFormItem?.reserveDate;
        const timeDisplay = time
            ? registrationFormItem?.isReserved
                ? `${TimeUtils.formatDatetimeAsRecent(time, { time: false })} ${registrationFormItem?.reserveTime?.start} ~ ${
                      registrationFormItem?.reserveTime?.end
                  }`
                : `${TimeUtils.formatDatetimeAsRecent(time, {
                      time: false,
                  })} ${registrationFormItem?.reserveTime?.start} ~ ${registrationFormItem?.reserveTime?.end}`
            : undefined;
        const orderNoDisplay = isShowUndetermined
            ? "待定（签到后确定号数）"
            : `${registrationFormItem?.timeOfDay}${registrationFormItem?.displayOrderNumber}`;
        const consultingRoomName = registrationFormItem?.consultingRoomName ?? "-";
        return (
            <View>
                {!!isUpdateClinic ? this._renderUpdateDialogBar() : this._renderDialogBar()}
                <View style={{ marginTop: Sizes.dp16 }}>
                    {type != RegistrationSuccessDialogType.signIn && this._renderContentItem("患者", patient?.name ?? "")}
                    {isShowCategories && this._renderContentItem("号种", registrationFormItem?.registrationCategoryDisplay ?? "")}
                    {this._renderContentItem(
                        `${!!doctorText ? doctorText : "医生"}`,
                        `${registrationFormItem?.departmentName?.length ? `${registrationFormItem?.departmentName} - ` : ""}${
                            registrationFormItem?.doctorName?.length
                                ? registrationFormItem?.doctorName
                                : `未指定${!!doctorText ? doctorText : "医生"}`
                        }`
                    )}
                    {this._renderContentItem("时间", timeDisplay ?? "")}
                    {isShowOrderNo && this._renderContentItem("诊号", orderNoDisplay ?? "")}
                    {this._renderContentItem("诊室", consultingRoomName, false)}
                </View>
            </View>
        );
    }
}
