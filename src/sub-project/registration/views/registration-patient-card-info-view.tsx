/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2022/8/4
 * @Copyright 成都字节流科技有限公司© 2022
 */
import React from "react";
import { View } from "@hippy/react";
import AbcPatientCardInfoView from "../../outpatient/views/new-patient-Info-view";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { AbcSvgTag, AbcTag } from "../../base-ui/tag/abc-tag";
import { IconFontView } from "../../base-ui";
import { AbcView } from "../../base-ui/views/abc-view";
import { PatientInfoMethod } from "../../base-business/data/patient-beans";

export class RegistrationPatientCardInfoView extends AbcPatientCardInfoView {
    render(): JSX.Element {
        const { panelBgColor } = this.props;
        return <View style={{ flex: 1, backgroundColor: panelBgColor ?? Colors.theme2_05 }}>{this.renderPatientCard()}</View>;
    }

    renderPatientBaseInfo(): JSX.Element {
        return (
            <View>
                {super.renderPatientBaseInfo(true)}
                {this.renderPatientTagInfoView()}
            </View>
        );
    }

    renderPatientPhoneIconView(): JSX.Element {
        if (!this.props.patient?.mobile || !PatientInfoMethod.canSeePatientMobile({ ...this.props })) return <View />;
        const { textColor } = this.props;
        return (
            <AbcView style={ABCStyles.rowAlignCenter} onClick={this._onClickCallPatient.bind(this)}>
                <View style={{ width: Sizes.dp1, height: Sizes.dp20, backgroundColor: Colors.B2Mask20, marginHorizontal: Sizes.dp12 }} />
                <IconFontView name={"phone_call_glyph"} size={Sizes.dp16} color={textColor ?? Colors.B2} />
            </AbcView>
        );
    }

    renderPatientTagInfoView(): JSX.Element {
        const { textColor } = this.props;
        const views: JSX.Element[] = [];
        this.showTags()?.forEach((it, index) => {
            if (it.viewMode == 0) {
                views.push(
                    <AbcTag
                        key={index}
                        text={it.tagName}
                        style={[
                            Sizes.paddingLTRB(Sizes.dp4, 0),
                            Sizes.marginLTRB(0, Sizes.dp2),
                            { marginRight: Sizes.dp4, borderColor: textColor ?? Colors.mainColor, borderRadius: Sizes.dp24 },
                        ]}
                        textStyle={[TextStyles.t12NM.copyWith({ lineHeight: Sizes.dp18, color: textColor ?? Colors.mainColor })]}
                    />
                );
            } else if (it.viewMode == 1) {
                const options = {
                    color: it.style?.color ?? "",
                    iconUrl: it.style?.iconUrl,
                    shape: it.style?.shape ?? "",
                    text: it.style?.text ?? "",
                    viewMode: it.style?.viewMode,
                    size: 18,
                };
                views.push(
                    <View style={{ marginRight: Sizes.dp4, marginVertical: Sizes.dp2, width: Sizes.dp18, height: Sizes.dp18 }}>
                        <AbcSvgTag key={index} theme={options?.shape} customTagConfig={{ ...options }} />
                    </View>
                );
            }
        });
        return <View style={[ABCStyles.rowAlignCenter, { flexWrap: "wrap" }]}>{views}</View>;
    }
}
