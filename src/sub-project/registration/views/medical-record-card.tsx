/**
 * create by deng<PERSON>e
 * desc: 预诊卡片
 * create date 2021/1/15
 */
import React from "react";
import { View } from "@hippy/react";
import { BaseComponent } from "../../base-ui/base-component";
import { AbcCardHeader } from "../../base-ui/abc-app-library/common/abc-card-header";
import { ListSettingItem, ListSettingItemStyle } from "../../base-ui/views/list-setting-item";
import { MedicalRecord } from "../../base-business/data/beans";
import { Colors, Sizes, TextStyles } from "../../theme";
import _ from "lodash";
import OutpatientPresentHistoryInputPage from "../../outpatient/medical-record-page/outpatient-present-history-input-page";
import ChiefComplaintInputPage from "../../outpatient/medical-record-page/chief-complaint-input-page";
import OutpatientPhysicalExaminationInputPage from "../../outpatient/medical-record-page/outpatient-physical-examination-input-page";
import OutpatientPastHistoryInputPage from "../../outpatient/medical-record-page/outpatient-past-history-input-page";
import { AbcText } from "../../base-ui/views/abc-text";
import { StringUtils } from "../../base-ui/utils/string-utils";
import { showBottomPanel } from "../../base-ui/abc-app-library/panel";
import { EpidemiologicalHistoryInputPage } from "../../outpatient/medical-record-page/epidemiological-history-input-page";
import { MedicalRecordUtils } from "../../outpatient/medical-record-page/utils/medical-record-utils";

interface MedicalRecordCardProps {
    editable?: boolean;
    medicalRecord: MedicalRecord;

    onChange?(arg1: MedicalRecord): void;
}

interface MedicalRecordCardStates {
    showMore: boolean;
}

export class MedicalRecordCard extends BaseComponent<MedicalRecordCardProps, MedicalRecordCardStates> {
    constructor(props: MedicalRecordCardProps) {
        super(props);
        this.state = {
            showMore: false,
        };
    }

    static defaultProps = {
        editable: true,
    };

    protected async _modifyChiefComplaint(): Promise<void> {
        const { onChange, editable } = this.props;
        if (!editable) return;
        const medicalRecord = this.props?.medicalRecord;
        const info = await showBottomPanel<string>(
            <ChiefComplaintInputPage chiefComplaint={StringUtils.stringBr2N(medicalRecord?.chiefComplaint)} />
        );
        if (!_.isNil(info)) {
            medicalRecord.chiefComplaint = StringUtils.stringN2Br(info);
            this.forceUpdate();
            onChange?.(medicalRecord);
        }
    }

    protected async _modifyPresentHistory(): Promise<void> {
        const { onChange, editable } = this.props;
        if (!editable) return;
        const medicalRecord = this.props?.medicalRecord;
        const info = await showBottomPanel<string>(<OutpatientPresentHistoryInputPage text={medicalRecord?.presentHistory} />, {
            topMaskHeight: Sizes.dp160,
        });
        if (!_.isNil(info)) {
            medicalRecord.presentHistory = StringUtils.stringN2Br(info.trim());
            this.forceUpdate();
            onChange?.(medicalRecord);
        }
    }

    protected async _modifyPastHistory(): Promise<void> {
        const { onChange, editable } = this.props;
        if (!editable) return;
        const medicalRecord = this.props?.medicalRecord;
        const info = await showBottomPanel<string>(
            <OutpatientPastHistoryInputPage text={StringUtils.stringBr2N(medicalRecord?.pastHistory)} />,
            {
                topMaskHeight: Sizes.dp160,
            }
        );
        if (!_.isNil(info)) {
            medicalRecord.pastHistory = StringUtils.stringN2Br(info.trim());
            this.forceUpdate();
            onChange?.(medicalRecord);
        }
    }

    protected async _modifyPhysicalExamination(): Promise<void> {
        const { onChange, editable } = this.props;
        if (!editable) return;
        const medicalRecord = this.props?.medicalRecord;
        const info = await showBottomPanel<string>(
            <OutpatientPhysicalExaminationInputPage type={"physical"} text={StringUtils.stringBr2N(medicalRecord?.physicalExamination)} />,
            {
                topMaskHeight: Sizes.dp160,
            }
        );
        if (!_.isNil(info)) {
            medicalRecord.physicalExamination = StringUtils.stringN2Br(info.trim());
            this.forceUpdate();
            onChange?.(medicalRecord);
        }
    }

    private async _moddifyEpidemiologicalHistory(): Promise<void> {
        const { onChange, editable } = this.props;
        if (!editable) return;
        const medicalRecord = this.props?.medicalRecord;
        const info = await showBottomPanel<string>(
            <EpidemiologicalHistoryInputPage content={medicalRecord?.__EpidemiologicalHistoryObj} />,
            {
                topMaskHeight: Sizes.dp160,
            }
        );

        if (!_.isNil(info)) {
            medicalRecord.epidemiologicalHistory = StringUtils.stringN2Br(info.trim());
            this.forceUpdate();
            onChange?.(medicalRecord);
        }
    }

    render(): JSX.Element {
        const { editable } = this.props;
        const { chiefComplaint, presentHistory, pastHistory, physicalExamination, __EpidemiologicalHistoryObj } = this.props.medicalRecord;
        const listType = editable ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal;
        return (
            <View>
                <AbcCardHeader
                    title={"预诊"}
                    titleStyle={[TextStyles.t16MM, { flexShrink: 1 }]}
                    showTopDivider={true}
                    showCardLeftLine={true}
                    rightRender={() => {
                        return (
                            <AbcText
                                style={TextStyles.t12NT2}
                                onClick={() => {
                                    this.setState({ showMore: !this.state.showMore });
                                }}
                            >
                                {this.state.showMore ? "收起" : "展开"}
                            </AbcText>
                        );
                    }}
                />
                {this.state.showMore ? (
                    <View
                        style={[
                            {
                                paddingLeft: Sizes.listHorizontalMargin,
                                backgroundColor: Colors.cardYellowBackgroundColor,
                            },
                        ]}
                    >
                        <ListSettingItem
                            itemStyle={listType}
                            title={"主诉"}
                            content={chiefComplaint}
                            bottomLine={true}
                            style={{ paddingRight: Sizes.listHorizontalMargin }}
                            contentStyle={[{ paddingVertical: Sizes.dp14 }]}
                            onClick={() => {
                                this._modifyChiefComplaint();
                            }}
                        />
                        <ListSettingItem
                            itemStyle={listType}
                            title={"现病史"}
                            content={presentHistory}
                            bottomLine={true}
                            style={{ paddingRight: Sizes.listHorizontalMargin }}
                            contentStyle={[{ paddingVertical: Sizes.dp14 }]}
                            onClick={() => {
                                this._modifyPresentHistory();
                            }}
                        />
                        <ListSettingItem
                            itemStyle={listType}
                            title={"既往史"}
                            content={pastHistory}
                            bottomLine={true}
                            style={{ paddingRight: Sizes.listHorizontalMargin }}
                            contentStyle={[{ paddingVertical: Sizes.dp14 }]}
                            onClick={() => {
                                this._modifyPastHistory();
                            }}
                        />
                        <ListSettingItem
                            itemStyle={listType}
                            title={"流行病史"}
                            content={StringUtils.stringBr2N(MedicalRecordUtils.getEpidemiologicalHistoryStr(__EpidemiologicalHistoryObj))}
                            bottomLine={true}
                            style={{ paddingRight: Sizes.listHorizontalMargin }}
                            contentStyle={[{ paddingVertical: Sizes.dp14 }]}
                            onClick={() => {
                                this._moddifyEpidemiologicalHistory();
                            }}
                        />
                        <ListSettingItem
                            itemStyle={listType}
                            title={"体格检查"}
                            content={physicalExamination}
                            bottomLine={true}
                            style={{ paddingRight: Sizes.listHorizontalMargin }}
                            contentStyle={[{ paddingVertical: Sizes.dp14 }]}
                            onClick={() => {
                                this._modifyPhysicalExamination();
                            }}
                        />
                    </View>
                ) : (
                    <View />
                )}
            </View>
        );
    }
}
