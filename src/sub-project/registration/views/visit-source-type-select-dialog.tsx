import { BottomSheetHelper, showBottomSheet } from "../../base-ui/dialog/bottom_sheet";
import React from "react";
import { Text, View } from "@hippy/react";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { AbcListView } from "../../base-ui/list/abc-list-view";
import { pxToDp } from "../../base-ui/utils/ui-utils";
import { RightArrowView } from "../../base-ui/iconfont/iconfont-view";
import { AbcView } from "../../base-ui/views/abc-view";
import { BaseComponent } from "../../base-ui/base-component";
import { RegistrationVisitSource } from "../data/bean";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";

interface VisitSourceTypeSelectDialogProps {
    source?: RegistrationVisitSource;
    sourceList: RegistrationVisitSource[];
}

interface VisitSourceTypeSelectDialogState {
    currentSource?: RegistrationVisitSource;
    currentSubSource?: RegistrationVisitSource;
    subSourceTypes: RegistrationVisitSource[];
}

export class VisitSourceTypeSelectDialog extends BaseComponent<VisitSourceTypeSelectDialogProps, VisitSourceTypeSelectDialogState> {
    public static show(props: VisitSourceTypeSelectDialogProps): Promise<RegistrationVisitSource | undefined> {
        return showBottomSheet(<VisitSourceTypeSelectDialog {...props} />);
    }

    constructor(props: VisitSourceTypeSelectDialogProps) {
        super(props);
        this.state = {
            subSourceTypes: [],
        };
    }

    componentDidMount(): void {
        this.initCurrentSource();
    }

    private initCurrentSource(): void {
        const source = this.props.source;
        if (!source) return;
        if (source.level == 1) {
            this.setState({ currentSource: this.props.sourceList.find((item) => item.id == source.id) });
        } else if (source.level == 2) {
            const currentSource = this.props.sourceList.find((item) => item.id == source.parentId);
            const subSourceTypes = currentSource?.children ?? [];
            this.setState({
                currentSource: currentSource,
                subSourceTypes: subSourceTypes,
                currentSubSource: subSourceTypes.find((item) => item.id == source.id),
            });
        }
    }

    private requestSelectPrimaryType(data: RegistrationVisitSource): void {
        if (data.id == this.state.currentSource?.id) return;
        if (!data.hasSubType) {
            ABCNavigator.pop(data);
        } else {
            this.setState({ currentSource: data, currentSubSource: undefined, subSourceTypes: data.children ?? [] });
        }
    }

    private requestSelectSubType(data: RegistrationVisitSource): void {
        ABCNavigator.pop(data);
    }

    render(): JSX.Element {
        return (
            <View>
                {BottomSheetHelper.createTitleBar("选择就诊来源")}
                <View
                    style={{
                        flexDirection: "row",
                        height: pxToDp(480),
                        backgroundColor: Colors.white,
                    }}
                >
                    <View style={{ flex: 1 }}>{this._renderPrimaryType()}</View>
                    <View
                        style={{
                            backgroundColor: Colors.dividerLineColor,
                            alignSelf: "stretch",
                            width: Sizes.dpHalf,
                        }}
                    />
                    <View style={{ flex: 1 }}>{this._renderSubType()}</View>
                </View>
            </View>
        );
    }

    /**
     * 患者来源一级类型
     * @private
     */
    private _renderPrimaryType(): JSX.Element {
        const sourceTypes = this.props.sourceList;
        return (
            <AbcListView
                scrollEventThrottle={300}
                numberOfRows={sourceTypes.length}
                dataSource={sourceTypes}
                getRowKey={(index) => index.toString()}
                renderRow={(data) => {
                    return this._renderPrimaryItemRow(data);
                }}
            />
        );
    }

    private _renderPrimaryItemRow(data: RegistrationVisitSource): JSX.Element {
        const { currentSource } = this.state;
        return (
            <AbcView
                style={{
                    backgroundColor: data.id === currentSource?.id ? Colors.D2 : undefined,
                    height: Sizes.listItemHeight,
                    flexDirection: "row",
                    alignItems: "center",
                    paddingHorizontal: Sizes.dp16,
                    ...ABCStyles.bottomLine,
                }}
                onClick={() => this.requestSelectPrimaryType(data)}
            >
                <View style={{ flex: 1 }}>
                    <Text style={TextStyles.t16NT1}>{data.name!}</Text>
                </View>
                {data?.hasSubType && <RightArrowView />}
            </AbcView>
        );
    }

    /**
     *患者来源二级类型列表
     * @private
     */
    private _renderSubType(): JSX.Element {
        const subTypes = this.state.subSourceTypes;
        return (
            <AbcListView
                scrollEventThrottle={300}
                numberOfRows={subTypes.length}
                dataSource={subTypes}
                getRowKey={(index) => index.toString()}
                renderRow={(data) => {
                    return this._renderSubTypeItemRow(data);
                }}
            />
        );
    }

    private _renderSubTypeItemRow(data: RegistrationVisitSource): JSX.Element {
        const { currentSubSource } = this.state;
        return (
            <AbcView
                style={{
                    backgroundColor: currentSubSource?.id === data.id ? Colors.D2 : undefined,
                    height: Sizes.listItemHeight,
                    flexDirection: "row",
                    alignItems: "center",
                    paddingHorizontal: Sizes.dp16,
                    ...ABCStyles.bottomLine,
                }}
                onClick={() => this.requestSelectSubType(data)}
            >
                <Text style={TextStyles.t16NT1}>{data.name!}</Text>
            </AbcView>
        );
    }
}
