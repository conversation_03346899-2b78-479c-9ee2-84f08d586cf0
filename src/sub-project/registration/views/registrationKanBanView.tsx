/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2022/12/2
 * @Copyright 成都字节流科技有限公司© 2022
 */
import React from "react";
import { Text, View } from "@hippy/react";
import { BaseComponent } from "../../base-ui/base-component";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import moment from "moment";
import { AbcScrollView2 } from "../../base-ui/views/abc-scroll-view2";
import { AbcView } from "../../base-ui/views/abc-view";
import { IconFontView, Spacer, Tab, Tabs } from "../../base-ui";
import {
    RegistrationDetail,
    RegistrationKanbanDailyEmployeeList,
    RegistrationKanbanDailyViewDetail,
    RegistrationStatusV2,
    ScheduleDetailDepartmentCell,
} from "../data/bean";
import { AssetImageView } from "../../base-ui/views/asset-image-view";
import { StringUtils } from "../../base-ui/utils/string-utils";
import { ignore } from "../../common-base-module/global";
import { RegistrationType } from "../dentistry/data/bean";
import UiUtils from "../../base-ui/utils/ui-utils";
import { ScrollEvent } from "../../base-ui/ui-events";
import { Subject } from "rxjs";
import { debounceTime } from "rxjs/operators";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import { TimeUtils } from "../../common-base-module/utils";
import { RightArrowView } from "../../base-ui/iconfont/iconfont-view";

export type TimeSegItem = {
    timeFrom: string;
    timeTo: string;
    seg: boolean;
    title: string;
    momentDateObj: {
        timeFrom: {
            h: number;
            m: number;
            s: number;
            ms: number;
        };
        timeTo: {
            h: number;
            m: number;
            s: number;
            ms: number;
        };
    };
};
const timeSeg: (startT: string, endT: string, interval: number) => TimeSegItem[] = (startT = "09:00", endT = "14:00", interval = 15) => {
    const starTime = moment(startT, "HH:mm");
    const endTime = moment(endT, "HH:mm");
    const diff = endTime.diff(starTime, "minutes");
    const num = Math.ceil(diff / interval);
    const segs = [];
    for (let i = 1; i <= num; i++) {
        const timeFrom = starTime.clone().add((i - 1) * interval, "minutes");
        const timeTo = starTime.clone().add(i * interval, "minutes");
        const minutes = timeFrom.minutes();
        const hour = timeFrom.hour();
        segs.push({
            timeFrom: timeFrom.format("HH:mm"),
            timeTo: timeTo.format("HH:mm"),
            seg: minutes === 0,
            title: (hour < 12 ? "上午" : "下午") + hour + "点",
            momentDateObj: {
                timeFrom: {
                    h: hour,
                    m: minutes,
                    s: 0,
                    ms: 0,
                },
                timeTo: {
                    h: timeTo.hour(),
                    m: timeTo.minutes(),
                    s: 0,
                    ms: 0,
                },
            },
        });
    }
    return segs;
};

export interface RegistrationKanBanViewProps {
    viewMode?: boolean;
    showScrollColHeaderView?: boolean;
    registrationType?: number;
    dailyViews?: RegistrationKanbanDailyViewDetail[];
    canModifyRegistration?: boolean; //能够修改挂号预约

    /**
     * 灵活模式-初始滚动位置
     */
    initDate?: Date;

    onCurrentIndex?(index: number, callback?: () => void): void;
}

interface RegistrationKanBanViewStates {
    left: number;
    top: number;

    /**
     * 允许当前窗口滚动
     */
    allowedScroll: boolean;

    activeMoveBlock: {
        left: number;
        top: number;
    };
}

//数据格高度
const dataGridHeight = Sizes.dp44;

export type PageViewSort = number;
export type PageViewColKey = string;
export type PageViewColValue = {
    data: RegistrationKanbanDailyEmployeeList;

    //面板使用数据
    notRefundedCount: number;
    headerKeyStr?: string | Date;
};
export type PageViewContentView = PageViewColValue[];

export const RegistrationKanBanViewV3ScrollToTrigger = new Subject<string>();

export class RegistrationKanBanViewV3<T extends RegistrationKanBanViewProps> extends BaseComponent<T, RegistrationKanBanViewStates> {
    scrollOffsetSyncName = "";

    thresholdLimit = 3;
    static defaultProps = {
        showScrollColHeaderView: true,
    };

    protected _onScrollEndDragTrigger = new Subject<ScrollEvent>();

    protected _onSwitchToTabTrigger = new Subject<number>();

    protected _onScrollToContentTrigger = new Subject<number>();

    readonly DATA_GRID_HEIGHT = Sizes.dp44;
    readonly DATA_GRID_WIDTH = (UiUtils.getScreenWidth() - Sizes.dp51) / 3;
    /**
     *
     */
    //数据格高度
    dataGridHeight = this.DATA_GRID_HEIGHT;
    //数据格宽度
    dataGridWidth = this.DATA_GRID_WIDTH;
    //描述数据格高度（科室）
    dataDescHeight = Sizes.dp36;

    /**
     * 每行显示内容
     * 包含内容和所占行数(描述行数)
     * @protected
     */
    protected rowHeaderDisplayList: Map<string, number[]> = new Map<string, number[]>();

    /**
     * 每列显示内容
     * @protected
     * key - 标题展示
     * value - 统计号数
     */
    protected colHeaderDisplayList: Map<string | Date, number> = new Map<string | Date, number>();

    /**
     * 行数计算
     * @protected
     */
    protected rowCount = 0;

    protected pageViewDataList: Map<PageViewSort, PageViewColValue[]> = new Map();

    protected tabSelectedIndex = 0;
    private hasActiveBlock = false;

    protected abcScrollYViewRef?: AbcScrollView2 | null;
    protected abcScrollYViewRefEvt?: {
        width: number;
        height: number;
    } | null;
    protected scrollWindowLayout?: { width: number; height: number } | null;

    private isScrollAsyncLoading = false;
    private _lastScrollTime?: Date;
    protected scrollOffsetY?: number;
    protected __headerList: TimeSegItem[];
    private _tabsRef?: Tabs | null;

    constructor(props: T) {
        super(props);
        this.__headerList = timeSeg("06:00", "23:00", 15);

        this.initKanbanHeader();
        this.initKanbanScrollInfo();
    }

    get isRegistration(): boolean {
        return this.props.registrationType == RegistrationType.outpatientRegistration;
    }

    get isAppointment(): boolean {
        return this.props.registrationType == RegistrationType.therapyAppointment;
    }

    get shouldScrollAsyncLoad(): boolean {
        return (this.props.dailyViews?.length ?? 0) > 1;
    }

    get shouldChangeTabStatus(): { pre: boolean; next: boolean } {
        return {
            pre: this.tabSelectedIndex > 0,
            next: this.tabSelectedIndex < [...this.pageViewDataList.values()].length - 1,
        };
    }

    componentDidMount(): void {
        super.componentDidMount();

        this._onScrollEndDragTrigger
            .pipe(debounceTime(200))
            .subscribe((evt) => {
                if (new Date().getTime() - (this._lastScrollTime ?? new Date()).getTime() > 200) {
                    this._onScrollEndDrag(evt);
                }
            })
            .addToDisposableBag(this);

        this._onSwitchToTabTrigger
            .pipe(debounceTime(DeviceUtils.isAndroid() ? 200 : 0))
            .subscribe((index) => {
                this._tabsRef?.setPage(index, false);
            })
            .addToDisposableBag(this);

        this._onScrollToContentTrigger
            .subscribe((index) => {
                let y = Math.max(0, index * this.dataGridHeight);
                y = Math.min(this.rowHeaderDisplayList.size * this.dataGridHeight - (this.scrollWindowLayout?.height ?? 0), y);
                this.abcScrollYViewRef?.scrollTo({ x: 0, y: y, animated: false });
            })
            .addToDisposableBag(this);

        RegistrationKanBanViewV3ScrollToTrigger.pipe(debounceTime(200))
            .subscribe((regId: string) => {
                //通过regId找到对应的坐标
                let sortIdx = -1,
                    contentSortIdx = -1;
                for (const [sort, list] of this.pageViewDataList) {
                    const _sortIdx = list.findIndex((item) => {
                        const _sameRegDetail = item.data.registrationList?.find((reg) => reg.id == regId);
                        if (!!_sameRegDetail) {
                            contentSortIdx = [...this.rowHeaderDisplayList.keys()].findIndex(
                                (key) => key == _sameRegDetail.registrationFormItem?.reserveStart
                            );
                            return true;
                        }
                    });
                    if (_sortIdx >= 0) {
                        sortIdx = sort;
                        break;
                    }
                }

                if (sortIdx >= 0) {
                    this._onSwitchToTabTrigger.next(sortIdx);
                }
                if (contentSortIdx >= 0) {
                    this._onScrollToContentTrigger.next(contentSortIdx);
                }
            })
            .addToDisposableBag(this);
    }

    UNSAFE_componentWillReceiveProps(nextProps: Readonly<T>): void {
        this.initKanbanHeader(nextProps);
        this.initKanbanScrollInfo();
        if ((nextProps.dailyViews?.length ?? 0) > 1 && this.props.dailyViews?.length != nextProps.dailyViews?.length) {
            this._onSwitchToTabTrigger.next(this.thresholdLimit);
        }
    }

    /**
     * 左侧行列表头
     */
    renderScrollRowHeaderView(): JSX.Element {
        return <View />;
    }

    private __isNow = moment();

    renderTabScrollColHeaderView(list: PageViewColValue[]): JSX.Element {
        const dataGridWidth = this.DATA_GRID_WIDTH * Math.max(1, 3 / list.length);
        return (
            <View style={[ABCStyles.rowAlignCenter]}>
                {list.map((pageValue, index) => {
                    if (pageValue.headerKeyStr instanceof Date) {
                        const __headerKeyMoment = moment(pageValue.headerKeyStr);
                        const __isFilterDate = __headerKeyMoment.isSame(this.props.initDate, "day");
                        const isAfterTime = __headerKeyMoment.isSameOrAfter(this.__isNow, "day");
                        return (
                            <View
                                key={index}
                                style={[
                                    ABCStyles.rowAlignCenter,
                                    { flexShrink: 1, paddingHorizontal: Sizes.dp4, paddingVertical: Sizes.dp13, width: dataGridWidth },
                                ]}
                            >
                                <Text
                                    style={[
                                        TextStyles.t16MT1.copyWith({ color: Colors.bdColor, lineHeight: Sizes.dp22 }),
                                        isAfterTime ? { color: Colors.T1 } : {},
                                        __isFilterDate ? { color: Colors.mainColor } : {},
                                    ]}
                                    numberOfLines={1}
                                    ellipsizeMode={"clip"}
                                >
                                    {TimeUtils.getDayOfWeekRecent(pageValue.headerKeyStr)}
                                </Text>
                                <Text
                                    style={[
                                        TextStyles.t12MT1.copyWith({ color: Colors.bdColor }),
                                        isAfterTime ? { color: Colors.t2 } : {},
                                        __isFilterDate ? { color: Colors.mainColor } : {},
                                    ]}
                                >
                                    {pageValue.headerKeyStr.format("MM-dd")}
                                </Text>
                            </View>
                        );
                    }
                    return (
                        <View
                            key={index}
                            style={[
                                ABCStyles.rowAlignCenter,
                                {
                                    flexShrink: 1,
                                    paddingHorizontal: Sizes.dp4,
                                    paddingVertical: Sizes.dp13,
                                    width: dataGridWidth,
                                },
                            ]}
                        >
                            <Text
                                style={[{ flexShrink: 1 }, TextStyles.t16MT1.copyWith({ lineHeight: Sizes.dp22 })]}
                                numberOfLines={1}
                                ellipsizeMode={"clip"}
                            >
                                {pageValue.headerKeyStr}
                            </Text>
                            {!!pageValue.notRefundedCount && (
                                <Text style={[TextStyles.t12MT1.copyWith({ color: Colors.t2 }), { marginLeft: Sizes.dp4 }]}>
                                    {`(${pageValue.notRefundedCount})`}
                                </Text>
                            )}
                        </View>
                    );
                })}
            </View>
        );
    }

    renderTabPanelDefaultView(list: PageViewColValue[]): JSX.Element {
        ignore(list);
        return <View />;
    }

    renderTagPagePreChangeView(): JSX.Element {
        const { pre } = this.shouldChangeTabStatus;
        return (
            <AbcView
                style={[
                    { position: "absolute", left: 0, top: Sizes.dp12 },
                    { width: Sizes.dp38, height: Sizes.dp24 },
                    { transform: [{ rotateY: "180deg" }] },
                ]}
                onClick={() => {
                    if (!pre) return;
                    this._tabsRef?.prePage();
                }}
            >
                <View style={[{ alignItems: "center", justifyContent: "center", marginTop: Sizes.dp3, backgroundColor: Colors.white }]}>
                    <RightArrowView color={this.shouldScrollAsyncLoad || pre ? Colors.t2 : Colors.P1} />
                </View>
            </AbcView>
        );
    }

    renderTagPageNextChangeView(): JSX.Element {
        const { next } = this.shouldChangeTabStatus;
        return (
            <AbcView
                style={[
                    { position: "absolute", right: 0, top: Sizes.dp12, zIndex: 1 },
                    { width: Sizes.dp28, height: Sizes.dp24 },
                ]}
                onClick={() => {
                    if (!next) return;
                    this._tabsRef?.nextPage();
                }}
            >
                <View style={[{ alignItems: "center", justifyContent: "center", marginTop: Sizes.dp3, backgroundColor: Colors.white }]}>
                    <RightArrowView color={this.shouldScrollAsyncLoad || next ? Colors.t2 : Colors.P1} />
                </View>
            </AbcView>
        );
    }

    render(): JSX.Element {
        const _pageValueList = [...this.pageViewDataList.values()];
        const hasContent = !!_pageValueList.length;
        return (
            <View style={{ position: "relative", flexDirection: "row", flex: 1, backgroundColor: Colors.white }}>
                {this.renderTagPagePreChangeView()}
                {this.renderTagPageNextChangeView()}
                {this.renderScrollRowHeaderView()}
                <View
                    style={{ flex: 1, backgroundColor: "#FFF" }}
                    onLayout={(evt) => {
                        const { width, height } = evt.layout;
                        this.scrollWindowLayout = {
                            width,
                            height,
                        };
                    }}
                >
                    {hasContent && (
                        <Tabs
                            ref={(ref) => {
                                this._tabsRef = ref;
                            }}
                            onAttachedToWindow={() => {
                                this._onSwitchToTabTrigger.next(this.tabSelectedIndex);
                            }}
                            showTabTitle={false}
                            lazyCount={1}
                            initialPage={this.tabSelectedIndex}
                            onChange={this.handleTabChange.bind(this)}
                        >
                            {_pageValueList.map((pageValueList, index) => {
                                return (
                                    <Tab
                                        key={`${index}${pageValueList[0].data.reserveDate?.toDateString()}`}
                                        title={pageValueList[0].data.reserveDate?.toDateString()}
                                    >
                                        {this.renderTabScrollColHeaderView(pageValueList)}
                                        <AbcScrollView2
                                            bounces={false}
                                            showsHorizontalScrollIndicator={false}
                                            showsVerticalScrollIndicator={false}
                                            scrollOffsetSyncGroup={this.scrollOffsetSyncName}
                                            horizontal={false}
                                            scrollEventThrottle={15}
                                            ref={(ref) => {
                                                this.abcScrollYViewRef = ref;
                                            }}
                                            onLayout={(evt) => {
                                                const { width, height } = evt.layout;
                                                this.abcScrollYViewRefEvt = {
                                                    width,
                                                    height,
                                                };
                                            }}
                                            onAttachedToWindow={() => {
                                                this.scrollOffsetY &&
                                                    this.abcScrollYViewRef?.scrollTo({ x: 0, y: this.scrollOffsetY, animated: false });
                                            }}
                                            onScroll={() => {
                                                this._lastScrollTime = new Date();
                                            }}
                                            onScrollEndDrag={(evt) => {
                                                DeviceUtils.isIOS() && this._onScrollEndDragTrigger.next(evt);
                                            }}
                                            onMomentumScrollEnd={(evt) => this._onScrollEndDrag(evt)}
                                        >
                                            <View style={{ position: "relative" }}>{this.renderTabPanelDefaultView(pageValueList)}</View>
                                        </AbcScrollView2>
                                    </Tab>
                                );
                            })}
                        </Tabs>
                    )}
                </View>
            </View>
        );
    }

    protected initLeftHeaderList(props?: T): void {
        ignore(props);
    }

    private initKanbanHeader(props?: T): void {
        this.initLeftHeaderList(props);
    }

    /**
     * 初始化滚动初始偏移量
     * @protected
     */
    protected initKanbanScrollInfo(): void {
        if (!this.shouldScrollAsyncLoad) {
            this.tabSelectedIndex = 0;
            return;
        }
        if (this.tabSelectedIndex <= this.thresholdLimit) {
            this.tabSelectedIndex = this.thresholdLimit;
        }
    }

    ///拖动相关内容
    private _onScrollEndDrag(evt: ScrollEvent): void {
        const {
            contentOffset: { y },
        } = evt;
        this.scrollOffsetY = y;
    }

    ///切换面板tab相关操作
    private handleTabChange(index: number): void {
        //安卓初始化多次调用问题
        if (DeviceUtils.isAndroid() && this.shouldScrollAsyncLoad) {
            if (index === 0) {
                return;
            }
        }
        this.tabSelectedIndex = index;
        this.forceUpdate();
        if (!this.shouldScrollAsyncLoad) return;
        //拉取数据
        this.isScrollAsyncLoading = true;
        this.props.onCurrentIndex?.(index * 3, (onlyChangeLoading?: boolean) => {
            this.isScrollAsyncLoading = false;
            if (!!onlyChangeLoading) {
                return;
            }
            this.tabSelectedIndex = this.thresholdLimit;
        });
    }
}

export interface _KanBanDragViewProps {
    width?: number;
    height?: number;

    dataGridHeight: number;

    detail: ScheduleDetailDepartmentCell;

    showOrderNo?: boolean;
    /**
     * 修改回调
     */
    onChange?(detail: ScheduleDetailDepartmentCell): void;

    onChangeAllowedScroll?(status: boolean): void;
}
export class KanBanDragView extends BaseComponent<_KanBanDragViewProps> {
    __selfComputedViewLayout: { width?: number; height: number; top?: number } = { height: dataGridHeight };
    private activeDragRef?: null | AbcView;

    get statusStyle(): { color: string; backgroundColor: string } {
        if (!this.registrationDetail) {
            return {
                color: Colors.bdColor,
                backgroundColor: Colors.bg1,
            };
        }
        if (this.registrationDetail.registrationFormItem?.statusV2 == RegistrationStatusV2.waitingSignIn) {
            return {
                color: Colors.Y2,
                backgroundColor: Colors.Y7,
            };
        } else if (this.registrationDetail.registrationFormItem?.statusV2 == RegistrationStatusV2.diagnosed) {
            return {
                color: Colors.T1Mask30,
                backgroundColor: Colors.B10,
            };
        }
        return {
            color: Colors.B1,
            backgroundColor: Colors.B10,
        };
    }

    get registrationDetail(): RegistrationDetail | undefined {
        return this.props.detail?.existedRegistration;
    }

    constructor(props: _KanBanDragViewProps) {
        super(props);

        this.__selfComputedViewLayout = this.computedViewLayout();
    }

    UNSAFE_componentWillUpdate(nextProps: Readonly<_KanBanDragViewProps>): void {
        this.__selfComputedViewLayout = this.computedViewLayout(nextProps);
    }

    computedViewLayout(props?: Readonly<_KanBanDragViewProps>): { width?: number; height: number; top?: number } {
        const layout = {
            height: dataGridHeight,
        };

        const { reserveStart, reserveDate, reserveEnd } =
            props?.detail?.existedRegistration?.registrationFormItem ?? this.props.detail?.existedRegistration?.registrationFormItem ?? {};
        const dateStartMoment = moment(`${reserveDate} ${"06:00"}`);
        const startMoment = moment(`${reserveDate} ${reserveStart}`);
        const endMoment = moment(`${reserveDate} ${reserveEnd}`);

        {
            const diff = startMoment.diff(dateStartMoment, "minutes");
            if (!!diff) {
                Object.assign(layout, { top: (diff / 15) * dataGridHeight });
            }
        }

        {
            const diff = endMoment.diff(startMoment, "minutes");
            if (!!diff) {
                layout.height = (diff / 15) * dataGridHeight;
            }
        }
        return layout;
    }

    renderPatientView(): JSX.Element {
        const { color } = this.statusStyle;
        const { patient } = this.registrationDetail ?? {};
        const { isOccupy, isVip } = this.props.detail;
        return (
            <View style={[ABCStyles.rowAlignCenterSpaceBetween, { flex: 1 }]}>
                <Text style={TextStyles.t12BB.copyWith({ color: color })}>{patient?.name ?? ""}</Text>
                <Spacer />
                {isVip && (
                    <View>
                        <AssetImageView
                            name={"charge_invoice_patient_member"}
                            style={{
                                marginLeft: 6,
                                width: Sizes.dp14,
                                height: Sizes.dp14,
                            }}
                        />
                    </View>
                )}
                {isOccupy && (
                    <View>
                        <AssetImageView
                            name={"reserve_no"}
                            style={{
                                marginLeft: 6,
                                width: Sizes.dp14,
                                height: Sizes.dp14,
                            }}
                        />
                    </View>
                )}
            </View>
        );
    }

    renderRegOrderView(): JSX.Element {
        const { color } = this.statusStyle;
        const { showOrderNo = true } = this.props;
        const { orderNo } = this.props.detail;
        if (!showOrderNo && (this.registrationDetail?.registrationFormItem?.statusV2 ?? 0) <= RegistrationStatusV2.waitingSignIn)
            return <View />;
        return (
            <View style={{ paddingLeft: Sizes.dp2 }}>
                <Text style={TextStyles.t12BB.copyWith({ color: color })}>{StringUtils.numberWithFillZero(orderNo ?? 0)}</Text>
            </View>
        );
    }

    renderReserveTimeView(): JSX.Element {
        const { color } = this.statusStyle;
        const { registrationFormItem } = this.registrationDetail ?? {};
        return (
            <View>
                <Text style={TextStyles.t11NT2.copyWith({ color: color })}>{registrationFormItem?.reserveStart ?? ""}</Text>
            </View>
        );
    }

    renderReserveProductView(): JSX.Element {
        const { color } = this.statusStyle;
        const { registrationFormItem } = this.registrationDetail ?? {};
        return (
            <View>
                <Text style={TextStyles.t11NT2.copyWith({ color: color })}>{registrationFormItem?.visitSourceRemark}</Text>
            </View>
        );
    }
    render(): JSX.Element {
        const { backgroundColor } = this.statusStyle;
        return (
            <View style={{ ...this.__selfComputedViewLayout, paddingHorizontal: Sizes.dp4 }}>
                <AbcView
                    ref={(ref) => {
                        this.activeDragRef = ref;
                    }}
                    style={[
                        Sizes.paddingLTRB(Sizes.dp4, Sizes.dp6),
                        {
                            position: "relative",
                            backgroundColor: backgroundColor,
                            borderRadius: Sizes.dp2,
                        },
                    ]}
                    onClick={() => {
                        // 打开预约详情
                        this.props.onChange?.(this.props.detail);
                    }}
                >
                    {this.props.detail.isIrreducible && (
                        <View style={{ position: "absolute", top: Sizes.dp1, right: Sizes.dp1 }}>
                            <IconFontView name={"fixed"} size={Sizes.dp10} color={Colors.blackMask10} />
                        </View>
                    )}
                    <View style={[ABCStyles.rowAlignCenter]}>
                        {this.renderPatientView()}
                        {this.renderRegOrderView()}
                    </View>
                    <Spacer />
                    <View style={[ABCStyles.rowAlignCenter]}>
                        {this.renderReserveTimeView()}
                        {this.renderReserveProductView()}
                    </View>
                </AbcView>
                <View style={{ height: Sizes.dp2 }} />
            </View>
        );
    }
}
