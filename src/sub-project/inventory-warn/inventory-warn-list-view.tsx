import React from "react";
import { ABCNetworkPageContentStatus, BaseBlocNetworkView } from "../base-ui/base-page";
import { InventoryWarnPageBloc } from "./inventory-warn-page-bloc";
import { View, Text } from "@hippy/react";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { AbcBasePanel } from "../base-ui/abc-app-library";
import { GridView } from "../base-ui/views/grid-view";
import { SizedBox, Spacer, UniqueKey } from "../base-ui";
import { AssetImageView } from "../base-ui/views/asset-image-view";
import { RightArrowView } from "../base-ui/iconfont/iconfont-view";
import { AbcView } from "../base-ui/views/abc-view";
import { AbcListView } from "../base-ui/list/abc-list-view";
import { Filters } from "../base-ui/searchBar/search-bar-bean";
import { environment, ServerEnvType } from "../base-business/config/environment";
import { WxApi } from "../base-business/wxapi/wx-api";
import { userCenter } from "../user-center";
import { InventoryWarnGoodsItem, MallInventoryWarnSkuItem } from "./data/inventory-warn-bean";
import { AbcEmptyItemView } from "../base-ui/views/empty-view";
import URL from "url";
import _ from "lodash";
import abcI18Next from "../language/config";
import { pxToDp } from "../base-ui/utils/ui-utils";
interface InventoryWarnListViewProps {
    tab?: number;
    warnType?: number;
}
export class InventoryWarnListView extends BaseBlocNetworkView<InventoryWarnListViewProps, InventoryWarnPageBloc> {
    private _tabIndex: number;
    constructor(props: InventoryWarnListViewProps) {
        super(props);
        this.bloc = new InventoryWarnPageBloc({ tab: props.tab, warnType: props.warnType });
        this._tabIndex = props.tab ?? 0;
    }
    componentDidMount(): void {
        this.bloc.state.subscribe((state) => {
            let status = ABCNetworkPageContentStatus.show_data;
            if (state.loading) {
                if (state.inventoryWarnGoodsList.length) {
                } else {
                    status = ABCNetworkPageContentStatus.loading;
                }
            } else if (state.loadError) {
                status = ABCNetworkPageContentStatus.error;
            } else if (_.isEmpty(state.inventoryWarnGoodsList)) {
                status = ABCNetworkPageContentStatus.empty;
            }

            this.setContentStatus(status, state.loadError);
        });
    }
    emptyContent(): JSX.Element {
        return <AbcEmptyItemView tips={"暂无预警记录"} />;
    }
    changeFilter(filter: Filters): void {
        this.bloc.requestChangeFilter(filter);
    }
    resetFilter(): void {
        this.bloc.requestResetFilter();
    }
    /**
     * 跳转到小程序商城
     */
    jumpToMiniProgramMall(goodsId: string): void {
        const _userName = environment.serverEnvType == ServerEnvType.normal ? "gh_8780ede6c1fd" : "gh_a9fda1ce6263";
        const path = URL.format({
            pathname: "pages/goods/goods-validate/index",
            query: {
                goodsId,
                clinicId: userCenter.clinic?.clinicId,
                fromSource: "app",
            },
        });

        WxApi.launchMiniProgram({
            userName: _userName,
            miniProgramType: environment.serverEnvType == ServerEnvType.normal ? 0 : 1,
            path: path,
        });
    }
    _renderMallDiscountInfoView(recommendInfo: MallInventoryWarnSkuItem): JSX.Element {
        const { displayGoodsName, packagePriceDisplay, splitPriceDisplay, showSplitPrice } = recommendInfo || {};
        return (
            <AbcBasePanel
                contentStyle={Sizes.paddingLTRB(Sizes.dp12, Sizes.dp8)}
                panelStyle={{ backgroundColor: Colors.lowPriceMallBg, marginTop: Sizes.dp12 }}
            >
                <AbcView style={[ABCStyles.rowAlignCenter]}>
                    <View style={[ABCStyles.rowAlignCenter, { marginRight: Sizes.dp16, width: Sizes.dp134 }]}>
                        <View style={ABCStyles.rowAlignCenter}>
                            <AssetImageView
                                name="mall_logo"
                                style={{
                                    width: Sizes.dp86,
                                    height: Sizes.dp12,
                                }}
                            />
                        </View>
                        <SizedBox width={Sizes.dp8} />
                        <View
                            style={[
                                Sizes.paddingLTRB(Sizes.dp4, Sizes.dp3),
                                { backgroundColor: Colors.lowPriceBgColor, position: "relative", borderRadius: Sizes.dp2 },
                            ]}
                        >
                            <Text
                                style={{
                                    fontSize: Sizes.dp9,
                                    color: Colors.lowPriceTextColor,
                                    fontWeight: "500",
                                    lineHeight: Sizes.dp10,
                                }}
                            >
                                进价更低
                            </Text>
                            <View
                                style={{
                                    position: "absolute",
                                    width: 0,
                                    height: 0,
                                    borderTopWidth: Sizes.dp4,
                                    borderBottomWidth: Sizes.dp4,
                                    borderRightWidth: Sizes.dp4,
                                    borderTopColor: "transparent",
                                    borderBottomColor: "transparent",
                                    borderRightColor: "#FCE6E4",
                                    left: -Sizes.dp3,
                                    top: pxToDp(4.5),
                                }}
                            />
                        </View>
                    </View>
                    <Text style={[TextStyles.t12NT1.copyWith({ lineHeight: Sizes.dp18 }), { flexShrink: 1 }]} numberOfLines={1}>
                        {displayGoodsName}
                    </Text>
                </AbcView>
                <View style={[ABCStyles.rowAlignCenter, { flex: 1, paddingTop: Sizes.dp5 }]}>
                    <View style={[ABCStyles.rowAlignCenter, { width: Sizes.dp134, marginRight: Sizes.dp16 }]}>
                        <Text style={TextStyles.t12NT1.copyWith({ lineHeight: Sizes.dp16 })}>整装价格：</Text>
                        <Text style={TextStyles.t12MT1.copyWith({ color: Colors.lowPriceColor, lineHeight: Sizes.dp16 })}>
                            {`${!!packagePriceDisplay ? abcI18Next.t("¥") + packagePriceDisplay : ""}`}
                        </Text>
                    </View>
                    {!!showSplitPrice && (
                        <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                            <View style={[ABCStyles.rowAlignCenter]}>
                                <Text style={TextStyles.t12NT1.copyWith({ lineHeight: Sizes.dp16 })}>拆零价格：</Text>
                                <Text
                                    style={TextStyles.t12MT1.copyWith({
                                        color: Colors.lowPriceColor,
                                        lineHeight: Sizes.dp16,
                                    })}
                                >
                                    {`${!!splitPriceDisplay ? abcI18Next.t("¥") + splitPriceDisplay : ""}`}
                                </Text>
                            </View>
                        </View>
                    )}
                    <Spacer />
                    <RightArrowView color={Colors.T4} />
                </View>
            </AbcBasePanel>
        );
    }
    _renderRow(data: InventoryWarnGoodsItem): JSX.Element {
        const indicatorList = [
            {
                name: "剩余库存：",
                value: data?.dispStockGoodsCount ?? "-",
                show: true,
                isHighLight: !this._tabIndex,
            },
            {
                name: "周转天数：",
                value: !!data?.turnoverDays ? data?.turnoverDays + "天" : "-",
                show: this._tabIndex != 2,
            },
            {
                name: "售价：",
                value: `${!!data?.packagePrice ? abcI18Next.t("¥") + data?.packagePrice : "-"}`,
                show: this._tabIndex == 2,
            },
            {
                name: "最近进价：",
                value: `${!!data?.lastPackageCostPriceDisplay ? abcI18Next.t("¥") + data?.lastPackageCostPriceDisplay : "-"}`,
                show: true,
            },
            {
                name: "拆零价格：",
                value: `${!!data?.splitPriceDisplay ? abcI18Next.t("¥") + data?.splitPriceDisplay : "-"}`,
                show: !data.isChineseMedicine && this._tabIndex != 2,
            },
            {
                name: "最近效期：",
                value: data?.minExpiryDate ?? "-",
                show: this._tabIndex != 2,
                isHighLight: this._tabIndex == 1,
            },
            {
                name: "毛利：",
                value: `${data?.profitRatio ?? 0}%`,
                show: this._tabIndex == 2,
                isHighLight: this._tabIndex == 2,
            },
        ];
        return (
            <AbcBasePanel
                panelStyle={{
                    backgroundColor: Colors.white,
                    marginBottom: Sizes.dp18,
                }}
                contentStyle={{
                    padding: Sizes.dp16,
                }}
                onClick={() => !!data.skuList?.[0]?.id && this.jumpToMiniProgramMall(data.skuList?.[0]?.id ?? "")}
            >
                <Text style={TextStyles.t16NT1}>{data?.displayName ?? ""}</Text>
                <View style={[ABCStyles.rowAlignCenter, { paddingVertical: Sizes.dp8 }]}>
                    <Text style={TextStyles.t14NT6.copyWith({ lineHeight: Sizes.dp16 })}>{data.goodsTypeName ?? ""}</Text>
                    {!!data.goodsTypeName && <SizedBox width={Sizes.dp12} />}
                    <Text style={TextStyles.t14NT6.copyWith({ lineHeight: Sizes.dp16 })}>{data?.specification ?? ""}</Text>
                    {!!data?.specification && <SizedBox width={Sizes.dp12} />}
                    <Text style={[TextStyles.t14NT6.copyWith({ lineHeight: Sizes.dp16 }), { flexShrink: 1 }]} numberOfLines={1}>
                        {data?.manufacturer ?? ""}
                    </Text>
                </View>
                <GridView crossAxisCount={2} crossAxisSpacing={Sizes.dp22} mainAxisSpacing={Sizes.dp8}>
                    {indicatorList
                        ?.filter((t) => !!t?.show)
                        ?.map((item: any, index: number) => {
                            return (
                                <View key={index} style={ABCStyles.rowAlignCenter}>
                                    <Text style={TextStyles.t14NT6.copyWith({ lineHeight: Sizes.dp16 })}>{item.name ?? ""}</Text>
                                    <SizedBox width={Sizes.dp8} />
                                    <Text
                                        style={TextStyles.t14NT1.copyWith({
                                            lineHeight: Sizes.dp16,
                                            color: item.isHighLight ? Colors.Y2 : Colors.T1,
                                        })}
                                    >
                                        {item.value}
                                    </Text>
                                </View>
                            );
                        })}
                </GridView>
                {!this._tabIndex && data.skuList?.length && this._renderMallDiscountInfoView(data.skuList?.[0])}
            </AbcBasePanel>
        );
    }
    renderContent(): JSX.Element {
        const state = this.bloc.currentState;
        return (
            <View style={{ flex: 1, backgroundColor: Colors.prescriptionBg, ...Sizes.paddingLTRB(Sizes.dp8, Sizes.dp18) }}>
                <AbcListView
                    loading={state.loading}
                    scrollEventThrottle={300}
                    initialListSize={10}
                    numberOfRows={state.inventoryWarnGoodsList.length}
                    dataSource={state.inventoryWarnGoodsList}
                    getRowKey={(index) => state.inventoryWarnGoodsList[index].goodsId ?? UniqueKey()}
                    renderRow={(data) => this._renderRow(data)}
                    onRefresh={() => {
                        this.bloc.requestReload();
                    }}
                    onEndReached={() => {
                        this.bloc.requestLoadMore();
                    }}
                />
            </View>
        );
    }
}
