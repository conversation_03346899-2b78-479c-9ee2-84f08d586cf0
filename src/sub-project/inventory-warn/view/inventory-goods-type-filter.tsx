import { SearchBarWithFilter } from "../../base-ui/searchBar/search-bar";
import { Colors, Sizes } from "../../theme";
import { View } from "@hippy/react";
import { AbcView } from "../../base-ui/views/abc-view";
import { IconFontView } from "../../base-ui";
import React from "react";
import { UIUtils } from "../../base-ui/utils";
import { Filters } from "../../base-ui/searchBar/search-bar-bean";
import { showRightSheet } from "../../base-ui/dialog/bottom_sheet";
import { AbcMultipleTimeFilterPicker } from "../../base-ui/abc-multiple-time-filter-picker/abc-multiple-time-filter-picker";

export class InventoryGoodsTypeFilter extends SearchBarWithFilter {
    protected async _handleFilterClick(): Promise<void> {
        const layout = await UIUtils.measureInWindow(this._iconRef!);
        this._hasFilter = true;
        this.forceUpdate();
        const select: Filters = await showRightSheet(
            <AbcMultipleTimeFilterPicker
                showClear={true}
                filters={this.props.filters}
                height={layout.y + layout.height - (this.viewHeight ?? 0) - UIUtils.safeStatusHeight()}
                width={UIUtils.getScreenWidth()}
                borderRadiusStyle={{ borderBottomLeftRadius: Sizes.dp6, borderBottomRightRadius: Sizes.dp6 }}
            />
        );
        this._hasFilter = false;
        this.forceUpdate();
        if (!select) return;
        this.props.filters?.fillSelectAttrs(select);
        this.props.onFilterChange?.(select);
    }
    render(): JSX.Element {
        return (
            <View style={{ flexGrow: 1 }} collapsable={false} ref={(ref) => (this._iconRef = ref)}>
                <AbcView style={{ alignSelf: "center" }} onClick={this._handleFilterClick.bind(this)}>
                    <IconFontView
                        style={{ ...Sizes.paddingLTRB(0, Sizes.dp6, Sizes.dp12, Sizes.dp6) }}
                        name={"filter"}
                        size={Sizes.dp16}
                        color={this._hasFilter ? Colors.mainColor : Colors.black}
                    />
                </AbcView>
            </View>
        );
    }
}
