import React from "react";
import { View } from "@hippy/react";
import { Color, Colors, Sizes, TextStyles } from "../theme";
import { BasePage, Tab, Tabs, UniqueKey } from "../base-ui";
import { InventoryWarnListView } from "./inventory-warn-list-view";
import { InventoryWarnTabType } from "./data/inventory-warn-bean";
import { FilterItem, Filters } from "../base-ui/searchBar/search-bar-bean";
import { CommonFilterId } from "../base-ui/searchBar/search-bar";
import { InventoryGoodsTypeFilter } from "./view/inventory-goods-type-filter";
import { MessageCardDetail } from "../data/notification";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { InventoryWarnAgent } from "./data/inventory-warn-agent";
import { runFuncStatPoint, StatisticalPointKeyConstants } from "../views/statistical-points";

interface InventoryWarnPageProps {
    warnInfo?: MessageCardDetail;
}
interface InventoryWarnPageStates {
    selected?: boolean;
    selectTypeId?: number;
    inventoryWarningCount?: number;
    expirationWarningCount?: number;
    grossProfitAnomalyCount?: number;
}
export class InventoryWarnTabPage extends BasePage<InventoryWarnPageProps, InventoryWarnPageStates> {
    _listViewRefs: Map<number, InventoryWarnListView> = new Map<number, InventoryWarnListView>();
    currentTabIndex = 0;
    _iconRef: View | null = null;

    tabsTypeFilterInfo: Map<number, Filters> = new Map([
        [0, GoodsTypeFilter()],
        [1, GoodsTypeFilter()],
        [2, GoodsTypeFilter()],
    ]);
    get listViewRef(): InventoryWarnListView | undefined {
        return this._listViewRefs.get(this.currentTabIndex);
    }
    constructor(props: InventoryWarnPageProps) {
        super(props);
        const { expiredWarnCount = 0, profitRatWarnCount = 0, stockWarnShortageCount = 0 } = props?.warnInfo || {};
        this.state = {
            inventoryWarningCount: stockWarnShortageCount,
            expirationWarningCount: expiredWarnCount,
            grossProfitAnomalyCount: profitRatWarnCount,
        };
    }

    getBackgroundColor(): Color {
        return Colors.prescriptionBg;
    }
    getAppBarTitle(): string {
        return "预警详情";
    }
    getAppBarTitleColor(): Color {
        return Colors.T1;
    }
    getAppBarBgColor(): Color {
        return Colors.panelBg;
    }
    getStatusBarColor(): Color {
        return Colors.panelBg;
    }
    getAppBarBottomLine(): boolean {
        return false;
    }

    @runFuncStatPoint({ key: StatisticalPointKeyConstants.Inventory_WarnTabPage })
    componentDidMount(): void {
        InventoryWarnAgent.InventoryWarnListTotalProvider.subscribe((data) => {
            if (data.hasOwnProperty("inventoryWarning")) {
                this.setState({
                    inventoryWarningCount: data?.inventoryWarning,
                });
            }
            if (data.hasOwnProperty("expirationWarning")) {
                this.setState({
                    expirationWarningCount: data?.expirationWarning,
                });
            }
            if (data.hasOwnProperty("grossProfitAnomaly")) {
                this.setState({
                    grossProfitAnomalyCount: data?.grossProfitAnomaly,
                });
            }
        }).addToDisposableBag(this);
    }

    onChangeFilter(filters: Filters): void {
        this.listViewRef?.changeFilter(filters);
    }

    getRightAppBarIcons(): JSX.Element[] {
        return [
            <View key={UniqueKey()} collapsable={false}>
                <InventoryGoodsTypeFilter
                    filters={this.tabsTypeFilterInfo.get(this.currentTabIndex)}
                    onFilterChange={(filters) => this.onChangeFilter(filters)}
                    showClear={true}
                />
            </View>,
        ];
    }
    renderContent(): JSX.Element | undefined {
        return (
            <View style={{ flex: 1 }}>
                <Tabs
                    tabsStyle={{
                        paddingHorizontal: Sizes.dp10,
                        backgroundColor: Colors.panelBg,
                        height: Sizes.dp44,
                    }}
                    tabStyle={{
                        marginRight: Sizes.dp11,
                        ...TextStyles.t16NT2.copyWith({ color: Colors.t2 }),
                    }}
                    currentStyle={{
                        ...TextStyles.t16MT1,
                    }}
                    onChange={(index) => {
                        this.currentTabIndex = index;
                        this.listViewRef?.resetFilter();
                        this.setState({});
                    }}
                >
                    {this.createModuleTabList()?.map((item, index) => {
                        return (
                            <Tab key={index} title={item.title}>
                                <InventoryWarnListView
                                    tab={item.id}
                                    warnType={item.warnType}
                                    ref={(ref) => {
                                        if (ref) this._listViewRefs.set(item.id, ref);
                                    }}
                                />
                            </Tab>
                        );
                    })}
                </Tabs>
            </View>
        );
    }

    createModuleTabList(): Array<{ id: number; title: string; warnType: number }> {
        const { inventoryWarningCount, expirationWarningCount, grossProfitAnomalyCount } = this.state;
        return [
            {
                id: 0,
                title: `库存预警(${inventoryWarningCount})`,
                warnType: InventoryWarnTabType.inventoryWarning,
            },
            {
                id: 1,
                title: `效期预警(${expirationWarningCount})`,
                warnType: InventoryWarnTabType.expirationWarning,
            },
            {
                id: 2,
                title: `毛利异常(${grossProfitAnomalyCount})`,
                warnType: InventoryWarnTabType.grossProfitAnomaly,
            },
        ];
    }
}
const goodsTypeList = [
    {
        name: "西药",
        typeId: 12,
    },
    {
        name: "中药饮片",
        typeId: 14,
    },
    {
        name: "中药颗粒",
        typeId: 15,
    },
    {
        name: "中成药",
        typeId: 16,
    },
];
const TypesFilter = () => {
    const filterGroup: FilterItem[] = [];
    goodsTypeList?.forEach((item, index) => {
        filterGroup.push(
            JsonMapper.deserialize(FilterItem, {
                id: CommonFilterId.all + CommonFilterId.asyncFilter + index,
                title: item.name,
                exclusive: false,
                info: item?.typeId ?? null,
            })
        );
    });
    return filterGroup;
};
const GoodsTypeFilter = () =>
    Filters.createFilters([
        {
            id: "inventoryWarn",
            name: "筛选",
            subTips: "（可多选）",
            filters: [
                {
                    id: CommonFilterId.all + CommonFilterId.asyncFilter + 10,
                    defaultTitle: "全部",
                    title: "全部",
                    exclusive: true,
                    select: true,
                    isDefault: true,
                    info: null,
                },
                ...TypesFilter(),
            ],
        },
    ]);
