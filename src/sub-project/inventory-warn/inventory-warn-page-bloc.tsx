import React from "react";
import { Bloc, BlocEvent } from "../bloc";
import { BaseLoadingState } from "../bloc/bloc-helper";
import { actionEvent } from "../bloc/bloc";
import { switchMap } from "rxjs/operators";
import { of, Subject } from "rxjs";
import { ABCError } from "../common-base-module/common-error";
import { Filters } from "../base-ui/searchBar/search-bar-bean";
import { InventoryWarnAgent } from "./data/inventory-warn-agent";
import { InventoryWarnGoodsItem, InventoryWarnGoodsReq, InventoryWarnTabType, MallInventoryWarnRsp } from "./data/inventory-warn-bean";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { reportPointApi, StatisticalPointKeyConstants } from "../views/statistical-points";
// 库存预警浏览上报方法
const exposureReport = (count?: number) => {
    reportPointApi({
        key: StatisticalPointKeyConstants.Inventory_WarnTabPage_Preview,
        extendData: {
            exposure: {
                goodsCount: count,
            },
        },
    }).then();
};
export class State extends BaseLoadingState {
    lastSearchResponse?: MallInventoryWarnRsp;
    detail?: MallInventoryWarnRsp;
    queryParams: InventoryWarnGoodsReq = {
        typeId: undefined,
        offset: 0,
        limit: 5,
    };
    get inventoryWarnGoodsList(): InventoryWarnGoodsItem[] {
        return this.detail?.rows ?? [];
    }
    get hasMore(): boolean {
        if (!this.lastSearchResponse) return true;
        return (
            this.inventoryWarnGoodsList?.length < (this.detail?.total ?? 0) &&
            (this.lastSearchResponse.rows?.length ?? 0) == this.queryParams.limit
        );
    }
    clone(): State {
        return Object.assign(new State(), this);
    }
}
class _Event extends BlocEvent {}
class _EventInit extends _Event {}
class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}
class _EventLoadMore extends _Event {}

class _EventReload extends _Event {}
class _EventChangeFilter extends _Event {
    filter: Filters;
    constructor(filter: Filters) {
        super();
        this.filter = filter;
    }
}
export class InventoryWarnPageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<InventoryWarnPageBloc | undefined>(undefined);

    static fromContext(context: InventoryWarnPageBloc): InventoryWarnPageBloc {
        return context;
    }
    private _loadDataTrigger: Subject<string> = new Subject<string>();
    private _tabIndex: number;
    private _warnType: number;
    constructor(params: { tab?: number; warnType?: number }) {
        super();
        this._tabIndex = params.tab ?? 0;
        this._warnType = params?.warnType ?? InventoryWarnTabType.inventoryWarning;
        this.dispatch(new _EventInit()).then();
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }
    initialState(): State {
        return this.innerState;
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }
    public requestReload(): void {
        this.dispatch(new _EventReload());
    }

    public requestLoadMore(): void {
        this.dispatch(new _EventLoadMore());
    }
    private _resetParams(): void {
        this.innerState.lastSearchResponse = undefined;
        this.innerState.detail = undefined;
    }
    requestChangeFilter(filters: Filters): void {
        this.dispatch(new _EventChangeFilter(filters));
    }
    requestResetFilter(): void {
        this._resetParams();
        this.innerState.queryParams = {
            typeId: undefined,
            offset: 0,
            limit: 5,
        };
    }
    private _initTrigger(): void {
        this._loadDataTrigger
            .pipe(
                switchMap(() => {
                    if (!this.innerState.hasMore) return of(null);
                    this.innerState.startLoading();
                    this.innerState.queryParams.offset = this.innerState.inventoryWarnGoodsList.length;
                    return InventoryWarnAgent.queryRecommendMallGoodsWarnByWarnType(this._warnType, this.innerState.queryParams)
                        .catch((e) => new ABCError(e))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                this.innerState.stopLoading();
                if (!rsp) return;
                if (rsp instanceof ABCError) {
                    this.innerState.setLoadingError(rsp);
                } else {
                    if (this.innerState.lastSearchResponse) {
                        exposureReport(rsp?.rows?.length);
                    }
                    this.innerState.lastSearchResponse = rsp;
                    this.innerState.detail = JsonMapper.deserialize(MallInventoryWarnRsp, {
                        ...rsp,
                        rows: (this.innerState.detail?.rows ?? [])?.concat(rsp.rows ?? []),
                    });
                    if (this._tabIndex == 0 && rsp.offset == 0) {
                        InventoryWarnAgent.InventoryWarnListTotalProvider.next({
                            inventoryWarning: this.innerState.lastSearchResponse.total ?? 0,
                        });
                    } else if (this._tabIndex == 1 && rsp.offset == 0) {
                        InventoryWarnAgent.InventoryWarnListTotalProvider.next({
                            expirationWarning: this.innerState.lastSearchResponse.total ?? 0,
                        });
                    } else if (this._tabIndex == 2 && rsp.offset == 0) {
                        InventoryWarnAgent.InventoryWarnListTotalProvider.next({
                            grossProfitAnomaly: this.innerState.lastSearchResponse.total ?? 0,
                        });
                    }
                }
                this.update();
            })
            .addToDisposableBag(this);
    }
    @actionEvent(_EventInit)
    private async *_mapEventInit(): AsyncGenerator<State> {
        this._initTrigger();
        this._loadDataTrigger.next();
        this.update();
    }
    @actionEvent(_EventUpdate)
    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    @actionEvent(_EventReload)
    private async *_mapEventReload(/*event: _EventReload*/): AsyncGenerator<State> {
        this._resetParams();
        this._loadDataTrigger.next();
    }

    @actionEvent(_EventLoadMore)
    private async *_mapEventLoadMore(/*event: _EventLoadMore*/): AsyncGenerator<State> {
        if (!this.innerState.hasMore || this.innerState.loading) return;
        this._loadDataTrigger.next();
    }
    @actionEvent(_EventChangeFilter)
    async *_mapEventChangeFilter(event: _EventChangeFilter): AsyncGenerator<State> {
        const typeFilter: number[] = [];
        event.filter?.filters?.[0]?.filters?.forEach((item) => {
            if (item.select) typeFilter.push(item.info as number);
        });
        this.innerState.queryParams.typeId = typeFilter.join(",");
        this._resetParams();
        this._loadDataTrigger.next();
    }
}
