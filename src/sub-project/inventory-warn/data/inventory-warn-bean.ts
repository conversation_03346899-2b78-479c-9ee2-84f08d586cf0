import { JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { GoodsSubType, GoodsType } from "../../base-business/data/beans";

export enum InventoryWarnTabType {
    inventoryWarning = 1, //库存预警
    expirationWarning = 3, //效期预警
    grossProfitAnomaly = 4, //毛利异常
}
export class InventoryWarnGoodsReq {
    typeId?: string; // 商品分类(12:西药 14:中药饮片 15:中药颗粒 16:中成药),多个以逗号隔开
    offset?: number; // 分页偏移量
    limit?: number; // 分页大小
}
export class MallInventoryWarnSkuItem {
    id?: string; // BIS商场商品的主键
    spuGoodsName?: string; //  spu名称
    skuGoodsName?: string; // sku名称
    spuGoodsId?: string; // spu主键
    shortId?: string;
    vendorId?: string;
    vendorName?: string; // 供应商名字
    status?: number; // 状态
    desc?: string; // 描述
    salesUnitPrice?: number; // 销售价格
    originSalesUnitPrice?: number; // 原价
    marketPrice?: number; // 市场价格
    stockCount?: number; // 库存
    expireDate?: Date; // 效期
    productDate?: Date; // 生产日期
    posterImageUrl?: string; // 封面图片
    last30SalesCount?: string; // 30天销量
    categoryId?: string; // 分类id
    categoryName?: string; // 分类名称: 中药、西药、设备、耗材等
    level3CategoryId?: string; // 3级分类Id
    level3CategoryName?: string; // 3级分类名称
    level4CategoryId?: string; // 4级分类Id
    level4CategoryName?: string; // 4级分类名称
    specification?: string; // 包装规格
    brand?: string; // 品牌
    model?: string; // 型号
    examinationName?: string; // 检测项目
    dosageForm?: string; // 剂型
    isLimitPurchase?: number; // 是否限购
    minSalesCount?: number; // 最小销售数量
    maxSalesCount?: number; // 最大销售数量
    department?: string; // 科室信息
    promotion?: string; // 单品折扣优惠信息
    reducedAmount?: number; // 立减金额
    promotionType?: number; // 优惠类型 0:折后价 1:券后价
    postDiscountPrice?: number; // 折后价/券后价
    unit?: string; // 单价
    shebaoCode?: string; // 国家医保码
    medicalFeeGrade?: number; // 医保费用等级
    shebaoCodeCurrentPriceLimited?: string; // 当前限价
    limitUnit?: string; // 限价单位
    manufacturer?: string; // 生成厂家
    externalGoodsId?: string;
    seckillPromotion?: string; // 秒杀活动优惠信息
    recommendView?: string;
    dosageFormUnit?: string; // 剂型单位 10片/盒中的片 1000g/袋 中的g
    packaging?: string; // 包装，10片/盒中的10 1000g/袋 中的1000
    splitPrice?: number; // 拆零价格
    splitPriceUnit?: string; // 拆零价格单位
    priceQueryType?: number; // 是否可询价 --功能升级变成询价类型 0 完整展示价格 1 不展示价格 2 展示部分价格
    packagePriceDisplay?: string; // 整装价格
    splitPriceDisplay?: string; // 拆零价格（前端显示字段)
    isShowSplitPrice?: number; // 是否展示拆零价格（0默认不显示 1显示）
    get displayGoodsName(): string {
        return (!!this.spuGoodsName ? this.spuGoodsName : this.skuGoodsName) ?? "";
    }
    get showSplitPrice(): boolean {
        return !!this.isShowSplitPrice;
    }
}
class InventoryWarnGoodsStock {
    pieceCount?: number;
    packageCount?: number;
}
export class InventoryWarnGoodsItem {
    goodsId?: string; // CIS的商品ID
    name?: string; // CIS药品商品名
    medicineCadn?: string; // CIS药品名
    type?: number; // CIS药品类型
    subType?: number; // CIS药品子类型
    manufacturer?: string; // CIS药品生产厂商
    specification?: string; // CIS药品规格
    @JsonProperty({ type: InventoryWarnGoodsStock })
    currentStock?: InventoryWarnGoodsStock; // CIS药品当前剩余库存
    dispStockGoodsCount?: string; // CIS药品库存
    turnoverDays?: number; // CIS药品周转天数
    packageUnit?: string; // CIS药品包装单位
    lastPackageCostPrice?: number; // CIS药品最近进价
    pieceNum?: number; // CIS药品最小包装数量
    pieceUnit?: string; // CIS药品最小包装单位
    piecePrice?: number; // 门店小单位售价
    splitPrice?: number; // 进价的拆零价格
    minExpiryDate?: string; // CIS药品最近效期
    lastSellDate?: string; // CIS药品最近销售的日期
    profitRatio?: number; // CIS药品毛利率
    typeId?: number; // CIS药品类型id
    goodsTypeName?: string; // CIS药品类型名称
    @JsonProperty({ type: Array, clazz: MallInventoryWarnSkuItem })
    skuList?: MallInventoryWarnSkuItem[]; // 推荐的SKU列表，最优信息排在第一位
    lastPackageCostPriceDisplay?: string; // CIS药品最近进价（前端显示字段)
    splitPriceDisplay?: string; // 进价的拆零价格（前端显示字段)
    packagePrice?: string; // CIS药品包装售价（前端显示字段）
    get isChineseMedicine(): boolean {
        return this.type == GoodsType.medicine && GoodsSubType.medicineChinese == this.subType;
    }
    get displayName(): string {
        if (this.medicineCadn) return this.medicineCadn.trim();
        if (this.name) return this.name.trim();
        return "";
    }
}
export class MallInventoryWarnRsp {
    @JsonProperty({ type: Array, clazz: InventoryWarnGoodsItem })
    rows?: InventoryWarnGoodsItem[];
    total?: number;
    offset?: number;
    limit?: number;
}

export class ReportReq {
    business?: string;
    key?: string;
    extendData?: Object;
    value?: string;
}
