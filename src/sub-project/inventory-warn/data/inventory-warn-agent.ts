import { ABCApiNetwork } from "../../net";
import { InventoryWarnGoodsReq, MallInventoryWarnRsp } from "./inventory-warn-bean";
import { environment } from "../../base-business/config/environment";
import { Subject } from "rxjs";

export class InventoryWarnAgent {
    static InventoryWarnListTotalProvider = new Subject<{
        inventoryWarning?: number;
        expirationWarning?: number;
        grossProfitAnomaly?: number;
    }>();
    /**
     * 查询库存预警推荐商品信息(此处用全路径原因：bis服务分区不同于region分区，目前只能直接调用，并且token的key也是不同)
     * @param warnType  预警类型(1:库存预警 3:效期预警 4:毛利预警)
     * @param params
     */
    static async queryRecommendMallGoodsWarnByWarnType(warnType: number, params: InventoryWarnGoodsReq): Promise<MallInventoryWarnRsp> {
        return await ABCApiNetwork.get(`https://${environment.mallHostname}/api/mall/goods/sku/recommend/goods-warn/${warnType}`, {
            queryParameters: params,
            clazz: MallInventoryWarnRsp,
            clearUndefined: true,
        });
    }
}
