import { UrlRoute } from "../url-dispatcher/url-router";
import { UrlUtils } from "../common-base-module/utils";
import { AnyType } from "../common-base-module/common-types";
import { UniqueKey } from "../base-ui";
import React from "react";
import { Route } from "../url-dispatcher/router-decorator";
import URLProtocols from "../url-dispatcher/url-protocols";

@Route({ path: URLProtocols.INVENTORY_WARN })
export class InventoryWarnRoute implements UrlRoute {
    handleUrl(action: string): JSX.Element {
        const { userCenter } = require("../user-center");
        const { ClinicChangeDialogWithCancel } = require("../clinics/clinic-change-dialog");

        const params = UrlUtils.getUrlParams(action);
        const clinicId = params.get("clinicId");
        const chainId = params.get("chainId");
        if (clinicId && chainId && (clinicId != userCenter.clinic?.clinicId || chainId != userCenter.clinic.chainId)) {
            return (
                <ClinicChangeDialogWithCancel
                    action={action}
                    chainId={chainId}
                    clinicId={clinicId}
                    textContentGetter={(clinic: AnyType) => {
                        return `确认切换到“${clinic?.name ?? ""}”查看库存预警信息吗`;
                    }}
                />
            );
        } else {
            const { InventoryWarnTabPage } = require("./inventory-warn-tab-page");
            const warnInfo = params.get("warnInfo");
            return <InventoryWarnTabPage key={UniqueKey()} warnInfo={!!warnInfo ? JSON.parse(warnInfo) : undefined} />;
        }
    }
}
