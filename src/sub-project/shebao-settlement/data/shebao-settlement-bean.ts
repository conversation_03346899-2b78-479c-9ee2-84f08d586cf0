import { Colors } from "../../theme";
import { fromJsonToDate, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";

export const ClearingStatus = Object.freeze([
    { value: "01", name: "医疗清算" },
    // { valu': "02', name: 'xx' },
    { value: "11", name: "门诊" },
    { value: "12", name: "门诊挂号" },
    { value: "14", name: "门诊慢特病" },
    { value: "1402", name: "门诊特病" },
    { value: "21", name: "住院" },
    { value: "41", name: "药店购药" },
    // { value: '51', name: '暂缓结算' },
    { value: "51", name: "城乡居民清算" }, // 产品提的需求，调整为“城乡居民清算”
    { value: "9901", name: "省内异地职工门诊" },
    { value: "9902", name: "省内异地职工住院" },
    { value: "9903", name: "跨市异地门诊清算（省内）" },
    { value: "9904", name: "省内异地药店购药" },
    { value: "9905", name: "跨市异地门诊清算（跨省）" },
    { value: "9909", name: "域内异地" },
    { value: "9910", name: "省内异地居民门诊" },
    { value: "9914", name: "省内异地居民门诊" },
    { value: "9916", name: "省内异地职工门慢门特" },
    { value: "9917", name: "省内异地居民门慢门特" },
    { value: "9918", name: "跨省异地职工门诊" },
    { value: "9920", name: "跨省异地药店购药" },
    { value: "9921", name: "跨省异地居民门诊" },
    { value: "9926", name: "跨省异地职工门慢门特" },
    { value: "9927", name: "跨省异地居民门慢门特" },
    { value: "9950", name: "职工药店购药清算" },
    { value: "99901", name: "企业清算" },
    { value: "99902", name: "机关清算" },
    { value: "99903", name: "离休清算" },
    { value: "99915", name: "职工门诊清算" },
    { value: "99922", name: "职工门诊慢特病清算" },
    { value: "99927", name: "职工住院清算" },
    { value: "99931", name: "居民门诊清算" },
    { value: "99933", name: "职工生育住院清算" },
    { value: "99939", name: "城乡门诊统筹清算" },
    { value: "9994", name: "省内异地门诊" },
    { value: "99940", name: "省内异地门诊" },
    { value: "99945", name: "城乡门诊慢特病清算" },
    { value: "99948", name: "城乡住院清算" },
    { value: "9995", name: "省内异地住院" },
    { value: "99952", name: "异地医疗费用清算" },
    { value: "9996", name: "省内异地购药" },
    { value: "99960", name: "门诊两病" },
    { value: "99959", name: "医疗费用清算" },
    { value: "99970", name: "职工医疗费用清算" },
    { value: "99971", name: "居民医疗费用清算" },
    { value: "99972", name: "离休医疗费用清算" },
    { value: "99973", name: "居民药店购药" },
    { value: "99974", name: "DRG支付" },
    { value: "99975", name: "区内居民医院费用清算" },
    { value: "99976", name: "区内离休医院费用清算" },
    { value: "99980", name: "区外职工医院费用清算" },
    { value: "99984", name: "职工意外伤害清算" },
    { value: "99985", name: "城乡居民生育清算" },
    { value: "99986", name: "职工特殊购药清算" },
    { value: "99987", name: "城乡特殊购药清算" },
    { value: "99995", name: "职工中医适宜技术清算" },
    { value: "99996", name: "居民中医适宜技术清算" },
]);
export class NationalSettleOfMonthItem {
    id?: string;
    insutype?: string; // 险种类别
    setlym?: string; // 清算年月
    psntimeIsCharge?: number; // 清算人次(只含收费)
    psntimeIsRefund?: number; // 清算人次(只含退费)
    psntime?: number; // 清算人次
    medfeeSumamt?: number; // 医疗费总额
    medSumfee?: number; // 医保拨付金额
    @JsonProperty({ fromJson: fromJsonToDate })
    applyTime?: Date; // 清算申请时间
    medType?: string; // 门诊类别
    fundAppySum?: number; // 基金申报总额
    hifmiPay?: number; // 大病补充支付
    hifobPay?: number; // 大额保险支付
    hifpPay?: number; // 统筹基金支出
    cashPayamt?: number; // 现金支付金额
    acctPay?: number; // 个人账户支付金额
    begndate?: Date; // 开始时间
    clrAppyEvtId?: string; // 机构清算申请事件ID
    clrType?: string; // 清算类别
    clrWay?: string; // 清算方式
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    enddate?: Date; // 结束时间
    setlCenter?: string; // 清算中心
    sourceType?: number; // 1 常规清算 2 手工清算
    settle_Extends?: {
        // 清算扩展Jason
        additionalProp1?: {};
        additionalProp2?: {};
        additionalProp3?: {};
    };
    status?: number; // 清算状态 0 正常 1作废
    settlePdfUrl?: string; // 清算PDF地址
    cvlservPay?: number; //公务员补助
    clrOptins?: string; // 清算分中心
    setlCenterName?: string; // 清算中心名称(清算了的展示)
    clrOptinsName?: string; // 清算中心名称(未清算的展示)
    // 清算状态
    clearStatusInfo(): { text: string; color: string } {
        return this.status == 0 ? { text: "已清算", color: Colors.mainColor } : { text: "待清算", color: Colors.Y2 };
    }
    // 清算类别
    get clrTypeText(): string {
        return ClearingStatus.find((item) => item.value == this.clrType)?.name ?? "";
    }
    // 清算中心名称
    get setlCenterNameText(): string {
        return (this.status == 0 ? this.setlCenterName : this.clrOptinsName) ?? "";
    }
}
