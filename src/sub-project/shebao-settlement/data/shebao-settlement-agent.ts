import { ABCApiNetwork } from "../../net";
import { NationalSettleOfMonthItem } from "./shebao-settlement-bean";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import _ from "lodash";

export class ShebaoSettlementAgent {
    static async getNationalSettleOfMonthList(param: {
        beginDate?: Date; // 对账开始时间
        endDate?: Date; // 对账结束时间
        generalLedgerType?: number;
        settleOrgGroup?: number;
        isExcludeNoFundPay?: number;
    }): Promise<NationalSettleOfMonthItem[]> {
        const { beginDate, endDate, ...other } = param;
        const rsp: { items: NationalSettleOfMonthItem[] } = await ABCApiNetwork.get("shebao-settle/national/charge/settle/settle-months", {
            queryParameters: {
                beginDate: beginDate?.format("yyyy-MM-dd"),
                endDate: endDate?.format("yyyy-MM-dd"),
                ...other,
            },
            clearUndefined: true,
        });
        const items = rsp.items?.map((item) => JsonMapper.deserialize(NationalSettleOfMonthItem, item)) ?? [];
        // 按照清算 applyTime 降序，无applyTime的排在前面
        items.sort((x, y) => {
            if (!x?.applyTime) return -1;
            if (!y?.applyTime) return 1;
            return y.applyTime.getTime() - x.applyTime.getTime();
        });
        // 根据清算类别、清算分中心和险种去重
        return _.uniqWith(items, (x, y) => {
            // 清算类别不同，则不是重复项
            if (x.clrType != y.clrType) {
                return false;
            }
            // 如果有险种且不同，则不是重复项
            if (x.insutype && x.insutype != y.insutype) {
                return false;
            }
            // 如果有清算中心且不同，则不是重复项
            if (x.setlCenter && x.setlCenter != y.setlCenter) {
                return false;
            }
            // 如果没有清算中心，但有清算分中心且不同，则不是重复项
            if (!x.setlCenter && x.clrOptins && x.clrOptins != y.clrOptins) {
                return false;
            }
            // 所有条件都相同，则是重复项
            return true;
        });
    }

    /**
     * 结算清单pdf私有地址兑换临时开发的url
     * @param url
     * @returns
     */
    static async getSheBaoPdfUrl(url: string): Promise<string> {
        return await ABCApiNetwork.get("shebao-settle/national/charge/settle/change-settle-open-pdf", {
            queryParameters: {
                url,
            },
            clearUndefined: true,
        });
    }
}
