import React from "react";
import { UrlRoute } from "../url-dispatcher/url-router";
import { UniqueKey } from "../base-ui";
import { URLProtocols } from "../url-dispatcher";
import { Route } from "../url-dispatcher/router-decorator";

@Route({ path: URLProtocols.SHEBAO_SETTLEMENT })
export class ShebaoSettlementRoute implements UrlRoute {
    handleUrl(/*action: string*/): JSX.Element {
        const { ShebaoSettlementList } = require("./shebao-settlement-list");
        return <ShebaoSettlementList key={UniqueKey()} />;
    }
}
