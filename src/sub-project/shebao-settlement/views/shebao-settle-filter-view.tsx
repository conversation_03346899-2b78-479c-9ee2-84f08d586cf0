import React from "react";
import { View } from "@hippy/react";
import { ABCStyles, Colors, Sizes } from "../../theme";
import { SearchBarWithFilter } from "../../base-ui/searchBar/search-bar";
import { DropDownFilterView } from "../../base-ui/searchBar/filter-view";

export class ShebaoSettleFilterView extends SearchBarWithFilter {
    render(): JSX.Element {
        return (
            <View style={{ backgroundColor: Colors.panelBg }}>
                <View
                    style={[ABCStyles.bottomLine, ABCStyles.rowAlignCenter, { minHeight: Sizes.dp48 }]}
                    collapsable={false}
                    ref={(ref) => (this._iconRef = ref)}
                    onLayout={(layoutInfo) => {
                        //@ts-ignore
                        this.viewHeight = layoutInfo.layout.height;
                        this.setState({});
                    }}
                >
                    <View
                        style={{
                            flex: 1,
                            flexDirection: "row",
                            paddingHorizontal: Sizes.dp16,
                        }}
                    >
                        <View style={[ABCStyles.rowAlignCenterSpaceBetween, { flex: 1 }]}>
                            {this.props.filters?.filters.map((item, index, self) => (
                                <DropDownFilterView
                                    key={index}
                                    iconRef={this._iconRef}
                                    style={[index == self.length - 1 ? { flexShrink: 1 } : {}]}
                                    viewHeight={this.viewHeight}
                                    filterGroup={item}
                                    filters={this.props.filters!}
                                    showClear={false}
                                    onChange={(filters) => {
                                        this.props.onFilterChange?.(filters);
                                    }}
                                />
                            ))}
                        </View>
                    </View>
                </View>
            </View>
        );
    }
}
