import React from "react";
import { switchMap } from "rxjs/operators";
import { Bloc } from "../bloc";
import { actionEvent } from "../bloc/bloc";
import { BaseLoadingState } from "../bloc/bloc-helper";
import { ABCError } from "../common-base-module/common-error";
import { ShebaoSettlementAgent } from "./data/shebao-settlement-agent";
import { NationalSettleOfMonthItem } from "./data/shebao-settlement-bean";
import { Subject } from "rxjs";
import { Filters } from "../base-ui/searchBar/search-bar-bean";
import { DialogIndex, showConfirmDialog, showQueryDialog } from "../base-ui/dialog/dialog-builder";
import { DeviceUtils } from "../base-ui/utils/device-utils";
import { pdf } from "../base-business/pdf/pdf";
import { Version } from "../base-ui/utils/version-utils";
import { AppInfo } from "../base-business/config/app-info";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { upgradeDataManager } from "../upgrade/upgrade";
import { Toast } from "../base-ui/dialog/toast";
import { errorToStr } from "../common-base-module/utils";
import { ShareToOtherApplicationView } from "../base-ui/dialog/share-to-other-application-view";
import { wxLoginHelper } from "../login/wx-login-helper";
import { ShareUtils } from "../common-base-module/utils/share-utils";
import { pxToDp } from "../base-ui/utils/ui-utils";
import { userCenter } from "../user-center";

class BlocEvent {}
export class State extends BaseLoadingState {
    // 清算单列表
    settlementList: NationalSettleOfMonthItem[] = [];
    // 所有数据
    allSettlementList: NationalSettleOfMonthItem[] = [];
    // 分页大小
    pageSize = 10;
    // 当前显示的索引
    currentDisplayIndex = 0;
    // 是否还有更多数据
    hasMore = false;
    paramOptions = (() => {
        const now = new Date();
        // 设置为上个月
        now.setMonth(now.getMonth() - 1);
        const year = now.getFullYear();
        const month = now.getMonth();

        return {
            beginDate: new Date(year, month, 1), // 上个月第一天
            endDate: new Date(year, month + 1, 0), // 上个月最后一天
        };
    })();
    // 当前年月份的清算时间文案
    settlementTimeText = "";
    clone(): State {
        return Object.assign(new State(), this);
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}
class _EventLoadMore extends _Event {}

class _EventReload extends _Event {}

class _EventChangeFilter extends _Event {
    filters: Filters;

    constructor(filters: Filters) {
        super();
        this.filters = filters;
    }
}
class _EventPreviewSettlementPdf extends _Event {
    data: NationalSettleOfMonthItem;

    constructor(data: NationalSettleOfMonthItem) {
        super();
        this.data = data;
    }
}

export class ShebaoSettlementListBloc extends Bloc<_Event, State> {
    static fromContext(context: ShebaoSettlementListBloc): ShebaoSettlementListBloc {
        return context;
    }
    private _loadDataTrigger: Subject<void> = new Subject<void>();
    constructor() {
        super();
        this.dispatch(new _EventInit());
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }
    _resetData(): void {
        this.innerState.settlementList = [];
        this.innerState.allSettlementList = [];
        this.innerState.currentDisplayIndex = 0;
        this.innerState.hasMore = false;
    }

    private _initPageTrigger(): void {
        this._loadDataTrigger
            .pipe(
                switchMap(() => {
                    const _innerState = this.innerState;
                    _innerState.startLoading();
                    this.update();
                    return ShebaoSettlementAgent.getNationalSettleOfMonthList({
                        beginDate: _innerState.paramOptions?.beginDate,
                        endDate: _innerState.paramOptions?.endDate,
                        // 目前app只显示成都地区的清算列表，成都地区这三个参数是这样的，其他地区会不一样
                        generalLedgerType: 0,
                        settleOrgGroup: 1,
                        isExcludeNoFundPay: 0,
                    })
                        .catch((e) => new ABCError(e))
                        .toObservable();
                })
            )
            .subscribe((rsp: NationalSettleOfMonthItem[] | ABCError) => {
                if (rsp instanceof ABCError) {
                    this.innerState.setLoadingError(rsp);
                } else {
                    this.innerState.allSettlementList = rsp;
                    this.innerState.settlementList = rsp.slice(0, this.innerState.pageSize);
                    this.innerState.currentDisplayIndex = this.innerState.settlementList.length;
                    this.innerState.hasMore = this.innerState.currentDisplayIndex < this.innerState.allSettlementList.length;
                    // 需要再最后追加一个【没有很多了】
                    if (!this.innerState.hasMore && this.innerState.settlementList.length > 0) {
                        this.innerState.settlementList.push(JsonMapper.deserialize(NationalSettleOfMonthItem, { id: "noMore" }));
                    }
                }
                this.innerState.stopLoading();
                this.update();
            })
            .addToDisposableBag(this);
    }
    @actionEvent(_EventInit)
    private async *_mapEventInit(): AsyncGenerator<State> {
        this._initPageTrigger();
        this._loadDataTrigger.next();
        // 获取当前年月份的清算时间文案
        this._currentSettlementTimeText();
        yield this.innerState.clone();
    }

    _currentSettlementTimeText(): void {
        const now = new Date();
        // 先设置为上个月
        now.setMonth(now.getMonth() - 1);
        const year = now.getFullYear();
        const month = now.getMonth() + 1;
        this.innerState.settlementTimeText = `${year}年${month.toString().padStart(2, "0")}月`;
    }

    @actionEvent(_EventUpdate)
    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    @actionEvent(_EventReload)
    private async *_mapEventReload(/*event: _EventReload*/): AsyncGenerator<State> {
        this._resetData();
        this._loadDataTrigger.next();
    }

    @actionEvent(_EventLoadMore)
    private async *_mapEventLoadMore(): AsyncGenerator<State> {
        if (this.innerState.loading || !this.innerState.hasMore) return;

        this.innerState.loading = true;
        yield this.innerState;

        try {
            const nextEndIndex = this.innerState.currentDisplayIndex + this.innerState.pageSize;
            const newItems = this.innerState.allSettlementList.slice(this.innerState.currentDisplayIndex, nextEndIndex);

            this.innerState.settlementList = [...this.innerState.settlementList, ...newItems];
            this.innerState.currentDisplayIndex = nextEndIndex;
            this.innerState.hasMore = this.innerState.currentDisplayIndex < this.innerState.allSettlementList.length;
        } catch (e) {
            this.innerState.setLoadingError(e as ABCError);
        }

        this.innerState.loading = false;
        yield this.innerState;
    }

    @actionEvent(_EventChangeFilter)
    private async *_mapEventChangeFilter(event: _EventChangeFilter): AsyncGenerator<State> {
        const timeRange = event.filters.getCurrentItems()?.[0];
        this.innerState.paramOptions.beginDate = timeRange?.info.beginDate;
        this.innerState.paramOptions.endDate = timeRange?.info.endDate;
        this.innerState.settlementTimeText = timeRange?.title ?? "";
        this._resetData();
        this.innerState.startLoading();
        this.update();
        this._loadDataTrigger.next();
    }

    @actionEvent(_EventPreviewSettlementPdf)
    private async *_mapEventPreviewSettlementPdf(event: _EventPreviewSettlementPdf): AsyncGenerator<State> {
        const { settlePdfUrl, status, clrTypeText } = event.data || {};
        if (status == 1) {
            await showConfirmDialog("提示", "尚未清算，无法查看明细。请前往医保电脑ABC客户端清算后重试", "知道了");
            return;
        }
        if (!settlePdfUrl) return;
        // 先兑换url
        const url = await ShebaoSettlementAgent.getSheBaoPdfUrl(settlePdfUrl);
        if (!url) return;
        const pdfName = `${!!clrTypeText ? clrTypeText : "清算单"}.pdf`;
        if (DeviceUtils.isIOS()) {
            const { WebviewPage } = require("../base-ui/webview-page");
            ABCNavigator.navigateToPage(
                <WebviewPage
                    uri={url}
                    title={pdfName}
                    isShowShareIcon={true}
                    desc={`${userCenter.clinic?.name}${this.innerState.settlementTimeText}医保清算单`}
                />
            ).then();
        } else {
            const lessThan285 = new Version(AppInfo.appVersion).compareTo(new Version("2.8.5.0000")) < 0;
            if (lessThan285) {
                const result = await showQueryDialog("无法查看清算单", "APP当前版本过低，请更新后重试", "暂不处理", "前往更新");
                if (result != DialogIndex.positive) return;
                await this._checkUpdate();
                return;
            }
            await this._jumpToOtherApplication(url, pdfName);
        }
    }
    async _jumpToOtherApplication(url: string, title: string): Promise<void> {
        // 是否支持分享到微信
        const hasShareImgToWechat = new Version(AppInfo.appVersion).compareTo(new Version("2.3.0.0100")) < 0;
        // 是否安装微信
        const isWeChatInstalled = wxLoginHelper._weChatInstalled;
        // 是否可以使用微信分享
        const canShareToWeChat = !hasShareImgToWechat && isWeChatInstalled;

        const shareConfig = {
            url: url,
            title: title,
            desc: `${userCenter.clinic?.name}${this.innerState.settlementTimeText}医保清算单`,
        };

        const shareItems = [];

        // 如果可以使用微信分享，添加微信分享选项
        if (canShareToWeChat) {
            shareItems.push(ShareUtils.createDefaultWxShareItem(shareConfig));
        }

        // 添加浏览器打开选项
        shareItems.push(ShareUtils.createDefaultBrowserItem(() => pdf.open(url)));

        await ShareToOtherApplicationView.show({
            items: shareItems,
            defaultConfig: shareConfig,
            topMaskHeight: pxToDp(530),
        });
    }

    private async _checkUpdate(): Promise<void> {
        try {
            const info = await upgradeDataManager.checkAppUpgrade(true);
            if (info == null) {
                await Toast.show("已经是最新版本", { success: true });
            }
        } catch (e) {
            await Toast.show(`检查失败:${errorToStr(e)}`, { warning: true });
        }
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }
    public requestReload(): void {
        this.dispatch(new _EventReload());
    }

    public requestLoadMore(): void {
        this.dispatch(new _EventLoadMore());
    }
    requestChangeFilter(filters: Filters): void {
        this.dispatch(new _EventChangeFilter(filters));
    }
    previewSettlementPdf(data: NationalSettleOfMonthItem): void {
        this.dispatch(new _EventPreviewSettlementPdf(data));
    }
}
