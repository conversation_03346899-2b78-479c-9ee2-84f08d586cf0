import React from "react";
import { View, Text } from "@hippy/react";
import { BaseBlocPage, NetworkView } from "../base-ui/base-page";
import { ShebaoSettlementListBloc } from "./shebao-settlement-list-bloc";
import { AbcListView } from "../base-ui/list/abc-list-view";
import { ABCStyles, Color, Colors, Sizes, TextStyles } from "../theme";
import { SizedBox, UniqueKey } from "../base-ui";
import { NationalSettleOfMonthItem } from "./data/shebao-settlement-bean";
import { AbcEmptyItemView } from "../base-ui/views/empty-view";
import { ShebaoSettleFilterView } from "./views/shebao-settle-filter-view";
import { FilterItem, Filters } from "../base-ui/searchBar/search-bar-bean";
import { CommonFilterId } from "../base-ui/searchBar/search-bar";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import abcI18Next from "../language/config";
import { ABCUtils } from "../base-ui/utils/utils";
import { AbcView } from "../base-ui/views/abc-view";
import { InventoryModuleQLSummaryDisplay } from "../inventory/views/inventory-list-page-views";
import { BlocHelper } from "../bloc/bloc-helper";

interface ShebaoSettlementListProps {}
export class ShebaoSettlementList extends BaseBlocPage<ShebaoSettlementListProps, ShebaoSettlementListBloc> {
    private filters: Filters;

    constructor(props: ShebaoSettlementListProps) {
        super(props);
        this.bloc = new ShebaoSettlementListBloc();
        this.filters = this.initTimeFilter();
    }

    getAppBarTitle(): string {
        return "清算列表";
    }
    getAppBarBgColor(): Color {
        return Colors.panelBg;
    }
    getStatusBarColor(): Color {
        return Colors.panelBg;
    }

    getBackgroundColor(): Color {
        return Colors.panelBg;
    }
    getAppBarBottomLine(): boolean {
        return false;
    }

    private _renderBottomDisplay(): JSX.Element {
        const state = this.bloc.currentState,
            list = state.settlementList;
        // 医保拨付金额
        const medicarePaymentAmount = list.reduce((pre, cur) => pre + (cur?.medSumfee ?? 0), 0);
        // 清算单数量（因为list中最后一个元素是noMore）
        const settlementCount = (list?.length ?? 0) - 1;
        return (
            <InventoryModuleQLSummaryDisplay>
                {`共 ${settlementCount < 0 ? 0 : settlementCount} 条清算单，总计医保拨付金额 ${
                    medicarePaymentAmount < 0 ? "-" : ""
                }${abcI18Next.t("¥")}${ABCUtils.formatPrice(Math.abs(medicarePaymentAmount))}`}
            </InventoryModuleQLSummaryDisplay>
        );
    }
    private _renderFilterView(): JSX.Element {
        return (
            <ShebaoSettleFilterView
                filters={this.filters}
                onFilterChange={(filters) => {
                    this.bloc.requestChangeFilter(filters);
                }}
                showClear={false}
            />
        );
    }

    renderContent(): JSX.Element {
        return (
            <View style={{ flex: 1, backgroundColor: Colors.white }}>
                {this._renderFilterView()}
                <ShebaoSettleListView />
                {this._renderBottomDisplay()}
            </View>
        );
    }

    private initTimeFilter(): Filters {
        const now = new Date();
        // 先设置为上个月
        now.setMonth(now.getMonth() - 1);
        const filters = [];

        for (let i = 0; i < 5; i++) {
            const year = now.getFullYear();
            const month = now.getMonth() + 1;

            const startDate = new Date(year, now.getMonth(), 1);
            const endDate = new Date(year, now.getMonth() + 1, 0);

            filters.push(
                JsonMapper.deserialize(FilterItem, {
                    id: i,
                    title: `${year}年${month.toString().padStart(2, "0")}月`,
                    exclusive: true,
                    select: i === 0,
                    info: {
                        beginDate: startDate,
                        endDate: endDate,
                    },
                })
            );
            now.setMonth(now.getMonth() - 1);
        }

        const filterGroup = {
            id: CommonFilterId.time,
            name: "时间",
            filters: filters,
        };

        return Filters.createFilters([filterGroup]);
    }
}

class ShebaoSettleListView extends NetworkView {
    static contextType = ShebaoSettlementListBloc.Context;

    emptyContent(): JSX.Element {
        return <AbcEmptyItemView tips={"暂无清算记录"} />;
    }

    componentDidMount(): void {
        BlocHelper.connectLoadingStatus(ShebaoSettlementListBloc.fromContext(this.context), this, (state) => !state.settlementList.length);
    }

    _renderRow(data: NationalSettleOfMonthItem): JSX.Element {
        if (data.id == "noMore") {
            return (
                <AbcView style={{ backgroundColor: Colors.white, paddingVertical: Sizes.dp16, marginHorizontal: Sizes.dp16 }}>
                    <Text style={[TextStyles.t14NT6, { textAlign: "center" }]}>{"没有更多了"}</Text>
                </AbcView>
            );
        }
        return (
            <AbcView
                style={[ABCStyles.bottomLine, { backgroundColor: Colors.white, paddingVertical: Sizes.dp16, marginHorizontal: Sizes.dp16 }]}
                onClick={() => ShebaoSettlementListBloc.fromContext(this.context).previewSettlementPdf(data)}
            >
                <View style={ABCStyles.rowAlignCenterSpaceBetween}>
                    <Text style={TextStyles.t14NT1}>{!!data?.clrTypeText ? data?.clrTypeText : "清算类别：-"}</Text>
                    <Text style={TextStyles.t14NM.copyWith({ color: data.clearStatusInfo()?.color })}>{data.clearStatusInfo()?.text}</Text>
                </View>
                <View>
                    <SizedBox height={Sizes.dp5} />
                    <Text style={TextStyles.t12NT2.copyWith({ lineHeight: Sizes.dp16, color: Colors.t2 })}>
                        {`经办机构：${!!data?.setlCenterNameText ? data?.setlCenterNameText : "-"}`}
                    </Text>
                    <SizedBox height={Sizes.dp5} />
                    <Text
                        style={[TextStyles.t12NT2.copyWith({ lineHeight: Sizes.dp16, color: Colors.t2 }), { flexShrink: 1 }]}
                        numberOfLines={1}
                    >
                        {`拨付金额：${(data?.medSumfee ?? 0) < 0 ? "-" : ""}${abcI18Next.t("¥")}${ABCUtils.formatPrice(
                            Math.abs(data?.medSumfee ?? 0)
                        )}`}
                    </Text>
                </View>
            </AbcView>
        );
    }
    renderContent(): JSX.Element {
        const state = ShebaoSettlementListBloc.fromContext(this.context).currentState;
        const { settlementList } = state;
        return (
            <View style={{ flex: 1 }}>
                <AbcListView
                    loading={state.loading}
                    scrollEventThrottle={300}
                    initialListSize={10}
                    numberOfRows={settlementList.length}
                    dataSource={settlementList}
                    getRowKey={(index) => settlementList[index]?.id ?? UniqueKey()}
                    renderRow={(data) => this._renderRow(data)}
                    onRefresh={() => {
                        ShebaoSettlementListBloc.fromContext(this.context).requestReload();
                    }}
                    onEndReached={() => {
                        ShebaoSettlementListBloc.fromContext(this.context).requestLoadMore();
                    }}
                    showScrollIndicator={false}
                />
            </View>
        );
    }
}
