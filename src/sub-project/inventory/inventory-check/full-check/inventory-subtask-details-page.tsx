import React from "react";
import { BaseBlocNetworkPage, BaseNetworkPage, NetworkView } from "../../../base-ui/base-page";
import { <PERSON><PERSON><PERSON><PERSON> } from "../../../bloc/bloc-helper";
import { Text, View } from "@hippy/react";
import colors from "../../../theme/colors";
import { InventorySubtaskDetailsPageBloc, ScrollSameViewState } from "./inventory-subtask-details-page-bloc";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../theme";
import { IconFontView, Spacer, ToolBar, ToolBarButtonStyle2 } from "../../../base-ui";
import { AbcButton } from "../../../base-ui/views/abc-button";
import { ignore } from "../../../common-base-module/global";
import { CheckTaskGoodItem, InventorySubTaskDetailsPageType, PostStockCheckTaskReq, StockCheckTaskInfo } from "../../data/inventory-bean";
import { BlocBuilder } from "../../../bloc";
import { LoadingView } from "../../../base-ui/views/loading-view";
import { AbcListView } from "../../../base-ui/list/abc-list-view";
import { ABCEmptyView } from "../../../base-ui/views/empty-view";
import WillPopListener from "../../../base-ui/views/will-pop-listener";
import { userCenter } from "../../../user-center";
import { ListSettingItem } from "../../../base-ui/views/list-setting-item";
import { cloneDeep } from "lodash";
import { JsonMapper } from "@app/utils";
import { AbcText, AbcView } from "@app/abc-mobile-ui";
import { InventoryDraftTitleView } from "../../views/inventory-draft-title-view";
import { ABCNavigator } from "../../../base-ui/views/abc-navigator";

interface InventorySubtaskDetailsPageProps {
    checkTask: StockCheckTaskInfo; // 盘点任务
    type?: InventorySubTaskDetailsPageType;
    isOnlineDraft?: boolean;

    saveDraft?(options: PostStockCheckTaskReq): Promise<void>;
    pharmacyNo?: number;
    checkScopeDisplay?: string; // 盘点范围名称
}

export class InventorySubtaskDetailsPage extends BaseBlocNetworkPage<InventorySubtaskDetailsPageProps, InventorySubtaskDetailsPageBloc> {
    constructor(props: InventorySubtaskDetailsPageProps) {
        super(props);
        this.bloc = new InventorySubtaskDetailsPageBloc(
            props.checkTask,
            props.type ?? InventorySubTaskDetailsPageType.normal,
            this.props.isOnlineDraft,
            props?.pharmacyNo
        );
    }

    getAPPBarCustomTitle(): JSX.Element {
        const title = this.props.checkTask.taskName;
        return <InventoryDraftTitleView title={title ?? ""} statusName={""} />;
    }

    getAppBarBottomLine(): boolean {
        return false;
    }

    getAppBarBackIcon(): JSX.Element | undefined {
        return (
            <AbcView style={{ paddingHorizontal: Sizes.dp16, paddingVertical: Sizes.dp12 }} onClick={() => ABCNavigator.pop()}>
                <IconFontView name={"s-backto-line"} size={Sizes.dp24} color={Colors.black} />
            </AbcView>
        );
    }

    getRightAppBarIcons(): JSX.Element[] {
        const state = this.bloc.currentState;

        switch (state.type) {
            case InventorySubTaskDetailsPageType.summary:
                return [
                    <AbcButton
                        key={"summary"}
                        text={"完成盘点"}
                        textStyle={TextStyles.t14NW}
                        style={{ height: Sizes.dp26, lineHeight: Sizes.dp14 }}
                        onClick={() => {
                            this.bloc.requestSummary();
                        }}
                    />,
                ];
            case InventorySubTaskDetailsPageType.normal:
                return [
                    <View key={"Submit/Saved"} style={{ flexDirection: "row", marginRight: Sizes.dp7, alignItems: "center" }}>
                        {state.hasChange &&
                            (state.submitLoading ? (
                                <LoadingView size={Sizes.dp12} style={{ marginRight: Sizes.dp6 }} />
                            ) : (
                                <Text
                                    style={[
                                        TextStyles.t10NT2,
                                        {
                                            marginRight: Sizes.dp6,
                                            marginVertical: Sizes.dp6,
                                            lineHeight: Sizes.dp14,
                                        },
                                    ]}
                                    key={"Saved"}
                                >
                                    已保存
                                </Text>
                            ))}

                        {state.canEdit && (
                            <AbcButton
                                text={"提交"}
                                textStyle={TextStyles.t14NW.copyWith({ color: state.hasChange ? Colors.white : Colors.T2 })}
                                style={{ height: Sizes.dp26, lineHeight: Sizes.dp14 }}
                                onClick={
                                    state.hasChange
                                        ? () => {
                                              this.bloc.requestSubmit();
                                          }
                                        : undefined
                                }
                            />
                        )}
                    </View>,
                ];
            case InventorySubTaskDetailsPageType.onlyRead:
            default:
                return [];
        }
    }

    getAppBar(): JSX.Element {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }

    onBackClick(): void {
        if (this.bloc.disable) return super.onBackClick();
        this.bloc.requestBackPage(this.props.saveDraft);
    }

    // 重新加载数据
    reloadData(): void {
        this.bloc.requestReloadData();
    }

    renderContent(): JSX.Element {
        return (
            <View style={{ flex: 1 }}>
                {this.bloc.currentState.hasChange && (
                    <WillPopListener
                        onWillPop={() => {
                            this.onBackClick();
                        }}
                    />
                )}
                <_MissionDetailsView checkScopeDisplay={this.props?.checkScopeDisplay} />
                <TaskAddColumnView />
            </View>
        );
    }
}

interface _MissionDetailsViewProps {
    checkScopeDisplay?: string;
}

// 盘点任务-详情
class _MissionDetailsView extends NetworkView<_MissionDetailsViewProps> {
    static contextType = InventorySubtaskDetailsPageBloc.Context;
    private _listViewRef?: AbcListView | null;

    constructor(props: _MissionDetailsViewProps) {
        super(props);
    }

    emptyContent(): JSX.Element {
        return <ABCEmptyView tips={"请添加盘点药品"} />;
    }

    componentDidMount() {
        BlocHelper.connectLoadingStatus(InventorySubtaskDetailsPageBloc.fromContext(this.context), this, (state) => {
            return !state.detailData?.list?.length;
        });

        InventorySubtaskDetailsPageBloc.fromContext(this.context).state.subscribe((state) => {
            if (state instanceof ScrollSameViewState) {
                // this._listViewRef?.scrollToIndex(0, state.scrollKeyIndex, true);
            }
        });
    }

    renderContent(): JSX.Element {
        const bloc = InventorySubtaskDetailsPageBloc.fromContext(this.context);
        const state = bloc.currentState;
        const detailData = state.detailData,
            list = detailData?.list;
        const data = cloneDeep(list) ?? [];
        data?.unshift(
            JsonMapper.deserialize(CheckTaskGoodItem, {
                id: "summary",
            })
        );
        data?.unshift(
            JsonMapper.deserialize(CheckTaskGoodItem, {
                id: "checkScope",
            })
        );

        console.log("data length", data?.length);

        return (
            <View style={{ flex: 1, backgroundColor: colors.window_bg }}>
                <View
                    onClick={() => {
                        this._listViewRef?.scrollToIndex(0, data.length - 1, true);
                    }}
                >
                    滚动到底部
                </View>
                <AbcListView
                    ref={(ref) => {
                        this._listViewRef = ref;
                    }}
                    initialListSize={1000}
                    style={{ flex: 1 }}
                    dataSource={data ?? []}
                    numberOfRows={data?.length ?? 0}
                    rowShouldSticky={(index) => index == 1}
                    getRowKey={(index) => {
                        if (index === 0 || index === 1) return data[index].id!;
                        return data![index].compareKey() ?? "";
                    }}
                    renderRow={this._renderRow.bind(this)}
                    scrollEventThrottle={300}
                />
            </View>
        );
    }

    _renderHeaderView(): JSX.Element {
        const checkScopeDisplay = this.props.checkScopeDisplay;
        return (
            <>
                <View style={{ backgroundColor: colors.white, paddingLeft: Sizes.dp16 }}>
                    <ListSettingItem title={"盘点范围"} content={checkScopeDisplay} />
                </View>
                <View style={{ height: Sizes.dp8, backgroundColor: Colors.window_bg, flex: 1 }} />
            </>
        );
    }

    _renderSummaryView(): JSX.Element {
        const bloc = InventorySubtaskDetailsPageBloc.fromContext(this.context);
        const state = bloc.currentState;
        const detailData = state.detailData,
            list = detailData?.list;
        const isDrugstoreButler = userCenter.clinic?.isDrugstoreButler;
        // 统计药品的品种数量
        const count = new Set(list?.map((item) => item.goodsId));
        return (
            <View
                style={{
                    backgroundColor: colors.window_bg,
                    ...ABCStyles.rowAlignCenter,
                    ...Sizes.paddingLTRB(Sizes.dp16, Sizes.dp8),
                }}
            >
                <AbcText size={"mini"} theme={"T2"} style={{ flexShrink: 1 }}>
                    {`盘点药品品种：${(count?.size ?? 0).toString()}种${
                        isDrugstoreButler ? "，" + detailData?.displaySummary(state.canViewPrice) : ""
                    }`}
                </AbcText>
            </View>
        );
    }

    _renderRow(data: CheckTaskGoodItem, unknown?: any, index?: number): JSX.Element {
        ignore(unknown, index);
        if (data?.id == "checkScope") return this._renderHeaderView();
        else if (data?.id == "summary") return this._renderSummaryView();
        return (
            <View
                key={index}
                style={{ marginBottom: Sizes.dp4, backgroundColor: colors.white }}
                onClick={() => {
                    if (InventorySubtaskDetailsPageBloc.fromContext(this.context).disable) return;
                    InventorySubtaskDetailsPageBloc.fromContext(this.context).requestChangeMedicineTask(data);
                }}
            >
                <View>
                    <View
                        style={{
                            paddingHorizontal: Sizes.dp16,
                        }}
                    >
                        <View
                            style={{
                                flexDirection: "row",
                                justifyContent: "flex-end",
                                alignItems: "center",
                                paddingTop: Sizes.dp10,
                                paddingBottom: Sizes.dp4,
                                flex: 1,
                            }}
                        >
                            <Text
                                style={[
                                    TextStyles.t16MT1,
                                    {
                                        flexShrink: 1,
                                        textAlign: "center",
                                        lineHeight: Sizes.dp24,
                                    },
                                ]}
                                numberOfLines={1}
                            >
                                {data.goods?.displayName ?? "--"}
                            </Text>
                            <Text
                                style={[
                                    TextStyles.t14NT2,
                                    {
                                        textAlign: "center",
                                        marginLeft: Sizes.dp8,
                                        lineHeight: Sizes.dp24,
                                    },
                                ]}
                            >
                                {data.goods?.packageSpec ?? "--"}
                            </Text>
                            <Spacer />
                            {!InventorySubtaskDetailsPageBloc.fromContext(this.context).disable && (
                                <IconFontView
                                    name={"trash"}
                                    color={Colors.T2}
                                    size={18}
                                    style={{
                                        paddingVertical: Sizes.dp10,
                                        paddingLeft: Sizes.dp8,
                                    }}
                                    onClick={() => InventorySubtaskDetailsPageBloc.fromContext(this.context).requestDeleteGoods(data)}
                                />
                            )}
                        </View>

                        <View
                            style={[
                                ABCStyles.bottomLine,
                                {
                                    flexDirection: "row",
                                    paddingBottom: Sizes.dp8,
                                },
                            ]}
                        >
                            <Text
                                style={[TextStyles.t14NT2, { flexShrink: 1, textAlign: "center", lineHeight: Sizes.dp20 }]}
                                numberOfLines={1}
                            >
                                {`批次：${data.batchId ?? "不指定批次"}`}
                            </Text>
                            <Text
                                style={[
                                    TextStyles.t14NT2,
                                    {
                                        flexShrink: 1,
                                        textAlign: "center",
                                        marginLeft: Sizes.dp16,
                                        minWidth: Sizes.dp132,
                                        lineHeight: Sizes.dp20,
                                    },
                                ]}
                                numberOfLines={1}
                            >
                                {`批号：${data.batchNo ?? "--"}`}
                            </Text>
                        </View>
                    </View>
                    <View
                        style={{
                            flex: 1,
                            flexDirection: "row",
                            paddingHorizontal: Sizes.dp16,
                            paddingVertical: Sizes.dp8,
                            justifyContent: "space-between",
                        }}
                    >
                        <View style={{ flex: 1, flexDirection: "row", alignSelf: "center", justifyContent: "space-between" }}>
                            <Text style={[TextStyles.t14NT1, { lineHeight: Sizes.dp20 }]} numberOfLines={1}>
                                {`账面: ${data.beforeStockDisplay ?? "--"}`}
                            </Text>

                            <Text
                                style={[TextStyles.t14NT1, { flexShrink: 1, marginLeft: Sizes.dp16, lineHeight: Sizes.dp20 }]}
                                numberOfLines={1}
                            >
                                {`实际: ${data.currentStockDisplay.length > 0 ? data.currentStockDisplay : "--"}`}
                            </Text>
                            <Spacer />
                            <Text
                                style={[
                                    data.changeCount > 0 ? TextStyles.t14NG2 : TextStyles.t14NR2,
                                    { alignSelf: "center" },
                                    { paddingLeft: Sizes.dp16 },
                                    { lineHeight: Sizes.dp20 },
                                ]}
                            >
                                {data.stockChangeDisplay}
                            </Text>
                        </View>
                    </View>
                </View>
            </View>
        );
    }
}

interface TaskAddColumnViewProps {}

// 任务添加栏 (输入添加、扫码添加)
class TaskAddColumnView extends BaseNetworkPage<TaskAddColumnViewProps, TaskAddColumnViewProps> {
    static contextType = InventorySubtaskDetailsPageBloc.Context;

    constructor(props: TaskAddColumnViewProps) {
        super(props);
    }

    render(): JSX.Element {
        if (InventorySubtaskDetailsPageBloc.fromContext(this.context).disable) return <View />;
        return (
            <ToolBar>
                <ToolBarButtonStyle2
                    text={"输入添加11"}
                    onClick={() => {
                        InventorySubtaskDetailsPageBloc.fromContext(this.context).requestAddGoods(false);
                    }}
                />
                <ToolBarButtonStyle2
                    text={"扫码添加"}
                    onClick={() => {
                        InventorySubtaskDetailsPageBloc.fromContext(this.context).requestAddGoods(true);
                    }}
                />
            </ToolBar>
        );
    }
}
