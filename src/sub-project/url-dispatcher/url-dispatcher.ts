/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/8/6
 *
 * @description
 */
import _ from "lodash";
import { eventCenter, NavigatorReadyEvent } from "../base-business/event-center/event-center";
import { Subscription } from "rxjs";
import { URLRouter } from "./index";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { ABCPushMessage, pushManager } from "../common-base-module/push/push-manager";
import { DisposableTracker } from "../common-base-module/cleanup/disposable";
import { thirdCall } from "./third-call";
import { LogUtils } from "../common-base-module/log";

interface UrlDispatcherListener {
    (url: string): boolean;
}

class UrlDispatcher extends DisposableTracker {
    homePageShowListener?: Subscription;
    private _launchUrl?: string;

    private _listeners: UrlDispatcherListener[] = [];

    public init(): void {
        return;
    }

    constructor() {
        super();
        const self = this;

        function _handleMessage(message: ABCPushMessage) {
            if (message.url) {
                self.dispatchUrl(message.url);
                return;
            }
            if (message.payload) {
                self.dispatchUrl(message.payload);
                return;
            }
        }

        pushManager.launchNotification
            .subscribe((message) => {
                message && _handleMessage(message);
            })
            .addToDisposableBag(this);

        pushManager.resumeNotification
            .subscribe((message) => {
                _handleMessage(message);
            })
            .addToDisposableBag(this);

        pushManager.resumeWebview
            .subscribe((message) => {
                _handleMessage(message);
            })
            .addToDisposableBag(this);

        this.homePageShowListener = eventCenter.subscribe((event) => {
            if (event instanceof NavigatorReadyEvent) {
                this.homePageShowListener?.unsubscribe();
                this.homePageShowListener = undefined;

                if (!_.isEmpty(this._launchUrl)) {
                    this.dispatchUrl(this._launchUrl!);

                    this._launchUrl = undefined;
                }
            }
        });

        thirdCall.thirdCallObserver
            .subscribe((action) => {
                LogUtils.d("UrlDispatcher third call action = " + action);
                if (!_.isEmpty(action)) {
                    this.dispatchUrl(action);
                }
            })
            .addToDisposableBag(this);
    }

    addListener(listener: UrlDispatcherListener) {
        this._listeners.push(listener);
    }

    removeListener(listener: UrlDispatcherListener) {
        _.remove(this._listeners, (item) => item == listener);
    }

    dispatchUrl(url: string) {
        //如果首页还没有打开，先保存url,等HomePageShowEvent事件后处理
        if (this.homePageShowListener) {
            this._launchUrl = url;
            return;
        }

        for (const listener of this._listeners) {
            if (listener(url)) return;
        }

        this.launchABCPage(url);
    }

    private launchABCPage(url: string) {
        const page = URLRouter.actionToPage(url);
        if (page) ABCNavigator.navigateToPage(page);
    }
}

const urlDispatcher = new UrlDispatcher();
export { urlDispatcher };
