/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/3/24
 */

import "reflect-metadata";
import UrlRouter, { UrlRoute } from "./url-router";

interface RouterPathMeatData {
    path: string;
}

export function Route(metadata: RouterPathMeatData): ClassDecorator {
    return (target) => {
        //@ts-ignore
        const ModuleRouter: UrlRoute = new target();
        UrlRouter.registerRoute(metadata.path, ModuleRouter.handleUrl);
        return Reflect.defineMetadata(metadata.path, metadata.path, target);
    };
}
