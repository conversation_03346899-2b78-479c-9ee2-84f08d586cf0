/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/8/11
 *
 * @description 处理第三方调用（如Android通知栏调用）
 */
import { Subject } from "rxjs";
import { callNativeWithPromise, HippyEventEmitter } from "@hippy/react";

class ThirdCall {
    thirdCallObserver = new Subject<string>();
    private hippyEventEmitterHandler: any;

    constructor() {
        const hippyEventEmitter = new HippyEventEmitter();
        this.hippyEventEmitterHandler = hippyEventEmitter.addListener("ThirdCall", (evt: { action: string }) => {
            this.thirdCallObserver.next(evt.action);
        });

        callNativeWithPromise("ThirdCall", "register", {});
    }
}

const thirdCall = new ThirdCall();

export { thirdCall };
