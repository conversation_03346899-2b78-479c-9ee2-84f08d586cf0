import React from "react";
import { View } from "@hippy/react";
import { UrlUtils } from "../common-base-module/utils";

export default class UrlRouter {
    private static ROUTER = new Map<string, (action: string) => any>();

    static registerRoute(path: string, handler: (action: string) => any): void {
        if (!this.ROUTER.has(path)) {
            this.ROUTER.set(path, handler);
        } else {
            const { LogUtils } = require("../common-base-module/log");
            LogUtils.e("UrlRouter.registerRouteHasSameRoute, path = " + path);
        }
    }

    static actionToPage(action: string): JSX.Element {
        for (const item of UrlRouter.ROUTER.entries()) {
            const key = item[0],
                value = item[1],
                path = UrlUtils.getUrlPath(action);
            if (key.toLowerCase() == path.toLowerCase()) {
                return value(action);
            }
        }
        return <View>未找到支持页面：{action}</View>;
    }
}

export interface UrlRoute {
    handleUrl(action: string): JSX.Element;
}
