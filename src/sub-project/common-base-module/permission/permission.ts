/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/5/26
 *
 * @description
 */
import { callNativeWithPromise } from "@hippy/react";
import { PermissionEvent } from "./permission-event";
import { PermissionPurposeType } from "./data";

export enum PermissionType {
    /// Android: Calendar
    /// iOS: Calendar (Events)
    calendar,

    /// Android: Camera
    /// iOS: Photos (Camera Roll and Camera)
    camera,

    /// Android: Contacts
    /// iOS: AddressBook
    contacts,

    /// Android: Fine and Coarse Location
    /// iOS: CoreLocation (Always and WhenInUse)
    location,

    /// Android:
    ///   When running on Android < Q: Fine and Coarse Location
    ///   When running on Android Q and above: Background Location Permission
    /// iOS: CoreLocation - Always
    locationAlways,

    /// Android: Fine and Coarse Location
    /// iOS: CoreLocation - WhenInUse

    locationWhenInUse,

    /// Android: None
    /// iOS: MPMediaLibrary
    mediaLibrary,

    /// Android: Microphone
    /// iOS: Microphone
    microphone,

    /// Android: Phone
    /// iOS: Nothing
    phone,

    /// Android: Nothing
    /// iOS: Photos
    photos,

    /// Android: Nothing
    /// iOS: Reminders
    reminders,

    /// Android: Body Sensors
    /// iOS: CoreMotion
    sensors,

    /// Android: Sms
    /// iOS: Nothing
    sms,

    /// Android: Microphone
    /// iOS: Speech
    speech,

    /// Android: External Storage
    /// iOS: Access to folders like `Documents` or `Downloads`. Implicitly
    /// granted.
    storage,

    /// Android: Ignore Battery Optimizations
    ignoreBatteryOptimizations,

    /// Android: Notification
    /// iOS: Notification
    notification,
    /// Android: Allows an application to access any geographic locations
    /// persisted in the user's shared collection.
    accessMediaLocation,

    /// When running on Android Q and above: Activity Recognition
    /// When running on Android < Q: Nothing
    /// iOS: Nothing
    activityRecognition,

    /// The unknown only used for return type, never requested
    unknown,
}

//--undetermined
/// If the permission was never requested before.

//--granted
/// If the user granted access to the requested feature.

//--denied
//If the user denied access to the requested feature.

//-- restricted
/// If the OS denied access to the requested feature. The user cannot change
/// this app's status, possibly due to active restrictions such as parental
/// controls being in place.
/// *Only supported on iOS.*

///-- permanentlyDenied If the user denied access to the requested feature and selected to never
/// again show a request for this permission. The user may still change the
/// permission status in the settings.
/// *Only supported on Android.*
export enum PermissionStatus {
    denied,
    granted,
    restricted,
    undetermined,
    permanentlyDenied,
}

const kModuleName = "PermissionHandler";

export class Permission {
    /**
     * 获取对应的权限的状态
     * @param permissionType 要检查的权限
     * @param requestIfNonDenied 在没有明确拒绝时尝试申请
     */
    static async checkPermission(permissionType: PermissionType, requestIfNonDenied = false): Promise<PermissionStatus> {
        let status: PermissionStatus = await Permission.checkPermissionStatus(permissionType);
        PermissionEvent.PermissionObservable.next(
            permissionType == PermissionType.microphone ? PermissionPurposeType.audioPermissions : PermissionPurposeType.cameraPermissions
        );
        if (status == PermissionStatus.undetermined && requestIfNonDenied) {
            status = await Permission.requestPermission(permissionType);
        }
        PermissionEvent.PermissionCloseObservable.next();
        return status;
    }

    /**
     * 检测权限状态,只检测权限状态，不负责申请
     * @param permissionType 要检测的的权限
     * @return status 权限状态
     */
    static checkPermissionStatus(permissionType: PermissionType): Promise<PermissionStatus> {
        return callNativeWithPromise(kModuleName, "checkPermissionStatus", {
            permissionType: permissionType,
        });
    }

    /**
     * 申请权限
     * @param permissionType 要申请的权限类型
     */
    static requestPermission(permissionType: PermissionType): Promise<PermissionStatus> {
        return callNativeWithPromise(kModuleName, "requestPermission", {
            permissionType: permissionType,
        });
    }

    /**
     * 打开系统app权限设置
     */
    static openAppSettings(): Promise<void> {
        return callNativeWithPromise(kModuleName, "openAppSettings", {});
    }
}
