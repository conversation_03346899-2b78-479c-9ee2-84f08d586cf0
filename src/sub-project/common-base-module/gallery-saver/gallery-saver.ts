import { callNativeWithPromise } from "@hippy/react";

/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/8/13
 *
 * @description
 */

const kModuleName = "GallerySaver";

export class GallerySaver {
    static saveImage(file: string, albumName?: string): Promise<boolean> {
        return callNativeWithPromise(kModuleName, "saveImage", {
            path: file,
            albumName: albumName,
        });
    }

    static saveVideo(file: string, albumName?: string): Promise<boolean> {
        return callNativeWithPromise(kModuleName, "saveImage", {
            path: file,
            albumName: albumName,
        });
    }
}
