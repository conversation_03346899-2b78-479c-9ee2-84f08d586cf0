/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-05-07
 *
 * @description
 */

import { DisposableTracker } from "./cleanup/disposable";
import { Observable, Subscription } from "rxjs";

import "./rxjs-ext/rxjs-ext";
import "./view-ext";

import { fromPromise } from "./rxjs-ext/rxjs-ext";
import { AnyType } from "./common-types";

///声明一些html属性，过滤掉div中未声明属性错误警告提示
// eslint-disable-next-line @typescript-eslint/no-namespace
declare namespace JSX {
    interface IntrinsicElements {
        div: { [elemName: string]: any };
        ul: { [elemName: string]: any };
        li: { [elemName: string]: any };
    }
}
declare module "rxjs" {
    interface Subscription {
        addToDisposableBag(bag: DisposableTracker): void;
    }
}

Subscription.prototype.addToDisposableBag = function (this: Subscription, bag: DisposableTracker) {
    bag.addDisposable(this);
};

declare global {
    interface Promise<T> {
        toObservable(): Observable<T>;

        catchIgnore(): Promise<T | undefined>;

        specRace<T>(values: readonly T[]): Promise<T extends PromiseLike<infer U> ? U : T>;
    }
}

Promise.prototype.toObservable = function () {
    return fromPromise(this);
};

Promise.prototype.catchIgnore = function () {
    return this.catch(() => {
        return;
    });
};

Promise.prototype.specRace = function (promises) {
    promises = Array.from(promises);
    return new Promise((resolve, reject) => {
        if (promises.length === 0) {
            // @ts-ignore
            resolve([]);
        } else {
            for (let i = 0; i < promises.length; i++) {
                Promise.resolve(promises[i]).then(
                    (data) => {
                        // @ts-ignore
                        resolve(data);
                        return;
                    },
                    (err) => {
                        reject(err);
                        return;
                    }
                );
            }
        }
    });
};

/**
 * async返回的Promise不是前面的Promise,前面在Promise上注入toObservable方法将会失败,
 * 这里先使用一个dummy方法,返回一个异步的Promise,并在原型上注入方法
 */
async function dummy(): Promise<string> {
    return "";
}

const promise = dummy();
promise.constructor.prototype.catchIgnore = function () {
    return this.catch(() => {
        return;
    });
};

promise.constructor.prototype.toObservable = function () {
    return fromPromise(this);
};

// eslint-disable-next-line @typescript-eslint/no-empty-function,@typescript-eslint/no-unused-vars
export function ignore(...param: AnyType[]): void {}
