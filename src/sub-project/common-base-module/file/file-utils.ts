/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-04-16
 *
 * @description
 */
import FileManager, { FileEncodingType } from "../../common-base-module/file/file-manager";
import { File } from "./file";
import _ from "lodash";

export default class FileUtils {
    /**
     * 获取应用的文档存储根目录
     */
    static getDocPath(): Promise<string> {
        return FileManager.getApplicationDocumentsDirectory();
    }

    ///获取插件根目录
    static async getPluginDir(): Promise<string> {
        const pluginDir = (await this.getDocPath()) + "/plugins";
        const dirExist = await FileUtils.fileExists(pluginDir);
        if (!dirExist) await FileUtils.createDir(pluginDir);

        return pluginDir;
    }

    /**
     * 获取share preferences文件路径
     */
    static async getSharedPreferencesFile(): Promise<string> {
        const docPath = await FileUtils.getDocPath();
        return `${docPath}/share_preferences.dat`;
    }

    /**
     * 获取临时文件路径
     */
    static async getTmpDir(fileName?: string): Promise<string> {
        const dir = await FileManager.getTemporaryDirectory();

        if (!(await FileUtils.fileExists(dir))) {
            await FileUtils.createDir(dir);
        }

        if (fileName == undefined) {
            return dir;
        }

        return `${dir}/${fileName}`;
    }

    static createDir(dir: string): Promise<boolean> {
        const d = new File(dir);
        return d.mkdirs();
    }

    /**
     * 判断文件是否存在
     * @param file
     */
    static fileExists(file: string): Promise<boolean> {
        if (!file) return Promise.resolve(false);

        return FileManager.exists(file);
    }

    /**
     * 以文本方式定义文件
     * @param file 要写入文件
     * @param content 要写入的内容
     * @param encode 编码方式
     */
    static async writeAsString(file: string, content: string, encode?: FileEncodingType): Promise<boolean> {
        const f = new File(file);
        return f.writeAsString(content, encode);
    }

    /**
     * 以文本方式从指定文件中读取内容
     * @param file
     * @param encode
     */
    static async readAsString(file: string, encode?: FileEncodingType): Promise<string> {
        const f = new File(file);
        return f.readAsString(encode);
    }

    /**
     * 删除文件
     * @param file 要删除的文件
     * @return Promise<boolean> true 成功， 失败以异常形式返回
     */
    static deleteFile(file: string): Promise<boolean> {
        if (!file) return Promise.resolve(false);
        const f = new File(file);
        return f.delete();
    }

    /**
     * 移动文件
     * @param from 原路径
     * @param to 目标位置
     * @return Promise<boolean> true 成功， 失败以异常形式返回
     */
    static mv(from: string, to: string): Promise<boolean> {
        const f = new File(from);
        return f.mv(to);
    }

    static getFileNameAndExt(file: File): string {
        const fullName = file.path;
        const separatorIndex = fullName.lastIndexOf("/");

        return separatorIndex < 0 ? fullName : fullName.substring(separatorIndex + 1);
    }

    static getFileExt(file: File | string): string | undefined {
        if (_.isString(file)) file = new File(file);
        const fileName = this.getFileNameAndExt(file);
        const dotIndex = fileName.indexOf(".");
        if (dotIndex > 0) {
            return fileName.substring(dotIndex);
        }
    }

    static getDirPath(file: string): string {
        const separatorIndex = file.lastIndexOf("/");

        return separatorIndex < 0 ? file : file.substring(0, separatorIndex);
    }

    static fileNameFromURL(url: string): string {
        const _url = url.split("?")[0];
        return _url.split("/").pop()!;
    }
}
