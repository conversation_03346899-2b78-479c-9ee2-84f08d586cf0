/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-04-16
 *
 * @description 文件操作 api接口
 */
import FileManager, { FileEncodingType } from "./file-manager";

export class File {
    path: string;

    constructor(path: string) {
        this.path = path;
    }

    /**
     * 判断文件是否存在
     * @return Promise<boolean> true存在，false不存在
     */
    exists(): Promise<boolean> {
        return FileManager.exists(this.path);
    }

    /**
     * 将字符按指定编码写入文件
     * @param content 写入的内容
     * @param encode 编码方式
     * @return Promise<boolean> true写入成功，注：可能出现异常
     */
    writeAsString(content: string, encode?: FileEncodingType): Promise<boolean> {
        return FileManager.writeAsString(this.path, content, encode);
    }

    /**
     * 以字符串方式读取
     * @param encode 编码方式，默认为utf8
     */
    readAsString(encode?: FileEncodingType): Promise<string> {
        return FileManager.readAsString(this.path, encode);
    }

    /**
     *创建文件夹
     * @return  Promise<string> true表示成功，其它将会以reject异常方式返回
     */
    mkdirs(): Promise<boolean> {
        return FileManager.mkdirs(this.path);
    }

    /**
     *删除文件
     * @return  Promise<string> true表示成功，其它将会以reject异常方式返回
     */
    delete(): Promise<boolean> {
        return FileManager.delete(this.path);
    }

    /**
     * 移动文件
     * @param to 目标位置
     * @param Promise<boolean> true表示成功，其它将以reject异常方式返回
     */
    mv(to: string): Promise<boolean> {
        return FileManager.mv(this.path, to);
    }
}
