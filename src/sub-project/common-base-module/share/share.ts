import { callNativeWithPromise } from "@hippy/react";

/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020/8/15
 *
 * @description
 *
 */

const kModuleName = "Share";

class Share {
    share(
        text: string,
        type: "file" | "text" | "image",
        params?: {
            sharePositionOrigin?: {
                originX: number;
                originY: number;
                originWidth: number;
                originHeight: number;
            };
            sharePanelTitle?: string;
            subject?: string;
        }
    ): Promise<void> {
        return callNativeWithPromise(kModuleName, "share", {
            list: [text],
            type: type,
            ...(params ?? {}),
        });
    }
}

const share = new Share();

export { share };
