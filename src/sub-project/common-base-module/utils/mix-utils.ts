/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020-03-20
 *
 * @description
 *
 */
import { LogUtils } from "../log";
import { ABCApiError, ABCNetworkError } from "../../net";
import _ from "lodash";
import { ABCDeviceLoginError, ABCError } from "../common-error";
import { ABCNetworkTimeoutError } from "../../net/api";
import { AnyType } from "../common-types";
import { StringUtils } from "../../base-ui/utils/string-utils";

export function defaultIfNil<T>(value: T | undefined, defaultValue: T): T {
    if (_.isNil(value)) return defaultValue;
    return value;
}

export function errorSummary(error: AnyType): string {
    if (error instanceof ABCError || error instanceof ABCDeviceLoginError) {
        error = error.detailError;
    }
    //IOS native调用返回的异常
    if (error.domain === "HippyInvokeError" && error.userInfo?.message) {
        return error.userInfo!.message;
    }

    LogUtils.e("errorToStr: " + error);
    if (error instanceof Error) {
        LogUtils.e("errorToStr: stack = " + error.stack);
    }
    if (error instanceof ABCNetworkError) {
        LogUtils.e("errorToStr: network error = " + error.detailError.stack);
        return error.summaryMessage ?? "网络错误";
    } else if (error instanceof ABCNetworkTimeoutError) {
        return "网络超时";
    } else if (error instanceof ABCApiError) {
        return `${error.msg}`;
    } else if (error instanceof Error) {
        return `${error.name}:${error.message}`;
    } else if (_.isString(error)) {
        return error;
    }

    try {
        return JSON.stringify(error);
    } catch (e) {
        return error;
    }
}

export function errorToStr(error: AnyType): string {
    if (error instanceof ABCError || error instanceof ABCDeviceLoginError) {
        error = error.detailError;
    }
    LogUtils.e("errorToStr: " + error);
    if (error instanceof Error) {
        LogUtils.e("errorToStr: stack = " + error.stack);
    }

    if (error instanceof ABCNetworkError) {
        LogUtils.e("errorToStr: network error = " + error.detailError.stack);
        return error.summaryMessage ?? "网络错误";
    } else if (error instanceof ABCNetworkTimeoutError) {
        return "网络超时";
    } else if (error instanceof ABCApiError) {
        let errorCode: number | string = error.code;
        if (error.code == 400) {
            errorCode = "异常提醒";
        } else if (error.code == 500) {
            errorCode = "异常提示";
        }
        return `${errorCode}-${StringUtils.stringBr2N(error.msg)}`;
    } else if (error instanceof Error) {
        return `${error.name}:${error.message}`;
    }

    try {
        return error + JSON.stringify(error);
    } catch (e) {
        return error;
    }
}

/**
 * 用于bugly异常上报
 * @param error
 */
export function errorToBuglyReportStr(error: AnyType): string {
    if (error instanceof ABCError) {
        error = error.detailError;
    }

    if (error instanceof ABCNetworkError) {
        return `网络错误-${error.message}`;
    } else if (error instanceof ABCNetworkTimeoutError) {
        return `网络超时- ${error.message}`;
    } else if (error instanceof ABCDeviceLoginError) {
        return `网络错误- 设备注册失败:url:${error.url}, method = ${error.method}`;
    } else if (error instanceof Error) {
        return `${error.name}:${error.message}`;
    }

    try {
        return error + JSON.stringify(error);
    } catch (e) {
        return error;
    }
}

export function applyMixins(derivedCtor: AnyType, baseCtors: AnyType[], exclude?: string[]): void {
    const derivedPropertyKeys = Object.getOwnPropertyNames(derivedCtor.prototype);

    for (let i = 0, len = baseCtors.length; i < len; i++) {
        const baseCtor = baseCtors[i];
        const propertyKeys = Object.getOwnPropertyNames(baseCtor.prototype);
        for (let j = 0, len2 = propertyKeys.length; j < len2; j++) {
            const name = propertyKeys[j];
            if (derivedPropertyKeys.includes(name) || (exclude && exclude.includes(name))) continue;

            // LogUtils.d("applyMixins derivedCtor.name = " + derivedCtor.name + ", method= " + name);
            derivedCtor.prototype[name] = baseCtor.prototype[name];
        }
    }
}

export class UUIDGen {
    static generate(): string {
        return "xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx".replace(/[xy]/g, (char) => {
            const random = (Math.random() * 16) | 0; // Nachkommastellen abschneiden
            const value = char === "x" ? random : (random % 4) + 8; // Bei x Random 0-15 (0-F), bei y Random 0-3 + 8 = 8-11 (8-b) gemäss RFC 4122
            return value.toString(16); // Hexadezimales Zeichen zurückgeben
        });
    }
}
