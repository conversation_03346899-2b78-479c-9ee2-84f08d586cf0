import { CaptureView } from "../../base-ui/views/capture-view";
import { WxApi, WxShareScene, WxShareTransactionType } from "../../base-business/wxapi/wx-api";
import FileUtils from "../../common-base-module/file/file-utils";
import { DeviceUtils } from "../../base-ui/utils/device-utils";

export interface ShareConfig {
    url: string;
    title?: string;
    desc?: string;
    scene?: WxShareScene;
    thumbSize?: {
        width: number;
        height: number;
    };
    transaction?: WxShareTransactionType;
}
export interface ShareItem {
    name: string;
    icon: string;
    shareIcon?: string;
    show?: boolean;
    shareConfig?: Partial<ShareConfig>;
    onClick?: (shareConfig: ShareConfig) => void;
}
export class ShareUtils {
    private static _shareIconRef?: CaptureView | null;

    static async shareToWx(config: ShareConfig, path?: string): Promise<void> {
        if (!path) return;

        try {
            await WxApi.share({
                scene: config.scene ?? WxShareScene.session,
                url: config.url,
                title: config.title ?? "",
                desc: config.desc ?? "",
                iconUrl: path,
                thumbSize: config.thumbSize ?? {
                    width: 60,
                    height: 60,
                },
                transaction: config.transaction ?? WxShareTransactionType.webpage,
            });
        } catch (e) {
            console.error("Share to wx failed:", e);
        }
    }

    static setShareIconRef(ref: CaptureView | null): void {
        this._shareIconRef = ref;
    }

    static async createWxShareIcon(shareIcon: string): Promise<string | null> {
        const fileName = `${shareIcon}.png`;
        const path = await FileUtils.getTmpDir(fileName);
        try {
            const result = await this._shareIconRef?.captureToFile(path);
            return result ? path : null;
        } catch (e) {
            return null;
        }
    }

    /**
     * 创建默认的微信分享项
     */
    static createDefaultWxShareItem(config: Partial<ShareConfig> = {}): ShareItem {
        return {
            name: "分享给微信好友",
            icon: "share_wx",
            shareIcon: "home_nav_qingsuandan",
            show: true,
            shareConfig: {
                scene: WxShareScene.session,
                ...config,
            },
            onClick: async (shareConfig) => {
                const path = await ShareUtils.createWxShareIcon("home_nav_qingsuandan");
                if (path) {
                    await ShareUtils.shareToWx(shareConfig, path);
                }
            },
        };
    }

    /**
     * 创建默认的浏览器打开选项
     */
    static createDefaultBrowserItem(onClick?: () => void): ShareItem {
        return {
            name: "在浏览器中打开",
            icon: "browser",
            show: DeviceUtils.isAndroid(),
            onClick: onClick ?? (() => ({})),
        };
    }
}
