/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020-03-22
 *
 * @description
 *
 */
import _ from "lodash";
import { Age } from "../../base-business/data/beans";
import { ABCUtils } from "../../base-ui/utils/utils";
import { fromJsonToDate, JsonMapper } from "../json-mapper/json-mapper";
import { MessageBodyDateCountStat } from "../../data/notification";

export class DifferenceDate {
    differenceTime: number;

    constructor(differenceTime: number) {
        this.differenceTime = differenceTime;
    }

    get inDays(): number {
        return Math.floor(this.differenceTime / (24 * 3600 * 1000));
    }

    get inHours(): number {
        // let leave1 = this.differenceTime % (24 * 3600 * 1000)    //计算天数后剩余的毫秒数
        return Math.floor(this.differenceTime / (3600 * 1000));
    }

    get inMinutes(): number {
        // let leave2 = this.inHours % (3600 * 1000)        //计算小时数后剩余的毫秒数
        return Math.floor(this.differenceTime / (60 * 1000));
    }

    get inSeconds(): number {
        // let leave3 = this.inMinutes % (60 * 1000)      //计算分钟数后剩余的毫秒数
        return Math.round(this.differenceTime / 1000);
    }

    get inMillisecond(): number {
        return Math.round(this.differenceTime);
    }
}

export default class TimeUtils {
    static ONE_A_HALF_YEAR = (365 + 365 / 2) * 864 * 10e4;
    static ONE_DAY = 24 * 60 * 60 * 1000;

    static formatDuration(durationInMS: number, chinese = true, ignoreZero = false, f2fz = false, withSec = false): string {
        const timeInSeconds = durationInMS / 1000;
        const hour = _.floor(timeInSeconds / (60 * 60));
        const minute = withSec ? _.floor((timeInSeconds % (60 * 60)) / 60) : _.ceil((timeInSeconds % (60 * 60)) / 60);
        const sec = _.floor(timeInSeconds % 60);
        if (chinese) {
            if (!ignoreZero) {
                return `${hour}小时${minute}分${f2fz ? "钟" : ""}`;
            }

            let time = "";
            if (ignoreZero && hour) {
                time = `${hour}小时`;
            }

            if (ignoreZero && minute) {
                time += `${minute}分${f2fz ? "钟" : ""}`;
            }
            return time;
        } else {
            const time = [];
            time.push(`${hour >= 10 ? hour : `0${hour}`}`);
            time.push(`${minute >= 10 ? minute : `0${minute}`}`);
            if (withSec) {
                time.push(`${sec >= 10 ? sec : `0${sec}`}`);
            }
            return time.join(":");
        }
    }

    static formatDatetimeAsRecent(
        date?: Date,
        options?: {
            year?: boolean;
            time?: boolean;
            today_zh?: boolean; // 是否展示"今天"
        }
    ): string {
        if (date == undefined) {
            return "";
        }
        if (_.isString(date)) date = new Date(date);

        let year = true;
        let time = true;
        let today_zh = true;
        if (options) {
            if (options.year != undefined) {
                year = options.year;
            }

            if (options.time != undefined) {
                time = options.time;
            }
            if (options.today_zh != undefined) {
                today_zh = options.today_zh;
            }
        }

        const diff = TimeUtils.getTodayStart().getTime() - TimeUtils.getStartOfDate(date).getTime();
        const diffDays = _.floor(diff / (24 * 60 * 60 * 1000));
        let day: string;

        switch (diffDays) {
            case -2:
                day = "后天";
                break;
            case -1:
                day = "明天";
                break;
            case 0:
                day = today_zh ? "今天" : "";
                break;
            case 1:
                day = "昨天";
                break;
            case 2:
                day = "前天";
                break;
            default:
                day = date.format((year ? "yyyy-" : "") + "MM-dd");
        }

        if (!time) return day;

        day = day + " " + date.format("HH:mm");
        return day;
    }

    /**
     * 将时间格式化为近期3天 显示中文格式
     * @param date
     * @param time
     */
    static formatDateTimeAsThreeDays(
        date?: Date,
        options?: {
            year?: boolean;
            time?: boolean;
        }
    ): string {
        if (date == undefined) {
            return "";
        }
        if (_.isString(date)) date = new Date(date);

        let year = true;
        let time = true;
        if (options) {
            if (options.year != undefined) {
                year = options.year;
            }

            if (options.time != undefined) {
                time = options.time;
            }
        }

        const diff = TimeUtils.getTodayStart().getTime() - TimeUtils.getStartOfDate(date).getTime();
        const diffDays = _.floor(diff / (24 * 60 * 60 * 1000));
        let day: string;

        switch (diffDays) {
            case -2:
                day = "后天";
                break;
            case -1:
                day = "明天";
                break;
            case 0:
                day = date.format("HH:mm");
                break;
            case 1:
                day = "昨天";
                break;
            case 2:
                day = "前天";
                break;
            default:
                day = date.format((year ? "yyyy-" : "") + "MM-dd");
        }

        if (!time) return day;

        day = day + " " + (day ? "" : date.format("HH:mm"));
        return day;
    }

    /**
     * 将时间格式化为上午 08:50格式
     * @param date
     * @param time
     */
    static formatDatetimeInOneDay(date?: Date | string, time = true): string {
        if (date == undefined) {
            return "";
        }
        if (_.isString(date)) date = new Date(date);

        let day = date.getHours() <= 12 ? "上午" : "下午";

        if (!time) return day;

        day = day + " " + date.format("HH:mm");
        return day;
    }

    static isValidDate(d: any): boolean {
        return d instanceof Date && !isNaN(d.getTime());
    }

    /**
     * 格式化一个时间，默认格式yyyy-MM-dd
     * @param date 要格式化的日期
     * @param format 格式
     * @param defaultIfNull 如果date为空，将返回此值
     */
    static formatDate(date?: Date, format = "yyyy-MM-dd", defaultIfNull = "", originDate?: string): string {
        if (!date) return defaultIfNull;
        if (!this.isValidDate(date)) return defaultIfNull;
        return date.format(format);
    }

    static tomorrow(): Date {
        return new Date(Date.now() + 24 * 3600 * 1000);
    }

    static afterTomorrow(): Date {
        return new Date(Date.now() + 24 * 3600 * 1000 * 2);
    }

    static yesterday(): Date {
        return new Date(Date.now() - 24 * 3600 * 1000);
    }

    static beforeYesterday(): Date {
        return new Date(Date.now() - 24 * 3600 * 1000 * 2);
    }

    static age2birthday(age: Age): Date {
        let { year, month, day } = age;
        year = year || 0;
        month = Math.min(11, month ?? 0);
        day = Math.min(30, day ?? 0);

        const time = new Date();
        time.setDate(time.getDate() - day);
        time.setMonth(time.getMonth() - month);
        time.setFullYear(time.getFullYear() - year);
        return time;
    }

    /**
     * @desc 根据生日推算年龄
     * <AUTHOR>
     * @date 2019/09/04 10:29:08
     * @params today 传入某天时间，计算传入时间的年龄
     */
    static birthday2age(birthday?: Date, today?: Date): Age | undefined {
        if (!birthday) return undefined;
        const birth = birthday;
        const now = today ?? new Date();

        const birthYear = birth.getFullYear();
        const birthMonth = birth.getMonth() + 1;
        const birthDay = birth.getDate();

        let nowYear = now.getFullYear();
        let nowMonth = now.getMonth() + 1;
        let nowDay = now.getDate();

        if (nowDay < birthDay) {
            nowMonth -= 1;
            const tmp = new Date(now);
            tmp.setDate(0);
            nowDay += tmp.getDate();
        }

        const day = nowDay - birthDay;

        if (nowMonth < birthMonth) {
            nowYear -= 1;
            nowMonth += 12;
        }

        const year = nowYear - birthYear; //年之差
        const month = nowMonth - birthMonth; //月之差

        return JsonMapper.deserialize(Age, {
            year,
            month,
            day,
        });
    }

    static getTodayEnd(): Date {
        return this.getEndOfDate(new Date());
    }

    static getTodayStart(): Date {
        return TimeUtils.getStartOfDate(new Date());
    }

    static getYesterdayStart(): Date {
        return TimeUtils.getStartOfDate(new Date(new Date().getTime() - 24 * 60 * 60 * 1000));
    }

    static getStartOfDate(date = new Date()): Date {
        const newDate = new Date(date.getTime());
        newDate.setHours(0);
        newDate.setMinutes(0);
        newDate.setSeconds(0);
        newDate.setMilliseconds(0);

        return newDate;
    }

    static getEndOfDate(date: Date): Date {
        const newDate = new Date(date.getTime());
        newDate.setHours(23);
        newDate.setMinutes(59);
        newDate.setSeconds(59);
        newDate.setMilliseconds(999);
        return newDate;
    }

    static getThisWeekFirstDay(data?: Date): Date {
        const now = data ?? new Date();
        let _day = now.getDay();
        //屏蔽中西方针对周统计的差异
        if (_day == 0) {
            _day = 7;
        }
        now.setDate(now.getDate() - _day + 1);
        now.setHours(0);
        now.setMinutes(0);
        now.setSeconds(0);
        now.setMilliseconds(0);
        return now;
    }

    // 下周第一天
    static getNextWeekFirstDay(date?: Date): Date {
        const _date = date ?? new Date();
        const _nextWeekDay = _date.getTime() + TimeUtils.ONE_DAY * 7;
        return TimeUtils.getThisWeekFirstDay(new Date(_nextWeekDay));
    }

    // 下周最后一天
    static getNextWeekLastDay(date?: Date): Date {
        const now = date ?? new Date();
        const _nextWeekDay = TimeUtils.getNextWeekFirstDay(now);
        const _nextWeekEndDay = _nextWeekDay.getTime() + 7 * TimeUtils.ONE_DAY - 1;
        return new Date(_nextWeekEndDay);
    }

    static getThisMonthFirstDay(date = new Date()): Date {
        const now = new Date(date);
        now.setDate(1);
        now.setHours(0);
        now.setMinutes(0);
        now.setSeconds(0);
        now.setMilliseconds(0);
        return now;
    }

    static getThisMonthEndDay(date = new Date()): Date {
        const now = new Date(date);
        const _year = now.getFullYear(),
            _month = now.getMonth() + 1;
        const maxDay = this.getMaxDaysOfMonth(_year, _month);
        now.setDate(maxDay);
        now.setHours(23);
        now.setMinutes(59);
        now.setSeconds(59);
        now.setMilliseconds(999);
        return now;
    }

    static getNextMonthFirstDay(date = new Date()): Date {
        const now = this.getThisMonthEndDay(date);

        return new Date(now.getTime() + 1);
    }

    static getLastMonthFirstDay(_date = new Date()): Date {
        const date = this.getLastMonthEndDay(_date);
        date.setDate(1);
        this.create({ year: date.getFullYear(), month: date.getMonth(), date: 1 });
        return date;
    }

    static getLastMonthEndDay(date = new Date()): Date {
        const now = this.getThisMonthFirstDay(date);
        now.setDate(now.getDate() - 1);

        now.setHours(23);
        now.setMinutes(59);
        now.setSeconds(59);
        now.setMilliseconds(999);
        return now;
    }

    static getThisYearFirstDay(): Date {
        const now = new Date();
        return this.create({ year: now.getFullYear(), month: 0, date: 1 });
    }

    static getLastYearFirstDay(): Date {
        const now = new Date();
        return this.create({ year: now.getFullYear() - 1, month: 0, date: 1 });
    }

    static getLastYearEndDay(): Date {
        const now = new Date();
        return this.create({
            year: now.getFullYear() - 1,
            month: 11,
            date: 31,
            hour: 23,
            minute: 59,
            second: 59,
            millSeconds: 999,
        });
    }

    static create(options: {
        year: number;
        month?: number;
        date?: number;
        hour?: number;
        minute?: number;
        second?: number;
        millSeconds?: number;
    }): Date {
        const now = new Date();
        const { year, month, date, hour, minute, second, millSeconds } = options;
        now.setFullYear(year);
        now.setMonth(month ?? 0);
        now.setDate(date ?? 0);
        now.setHours(hour ?? 0);
        now.setMinutes(minute ?? 0);
        now.setSeconds(second ?? 0);
        now.setMilliseconds(millSeconds ?? 0);

        return now;
    }

    static difference(date1?: Date, date2?: Date): DifferenceDate {
        if (_.isUndefined(date1) || _.isUndefined(date2)) return new DifferenceDate(0);
        const date3 = new Date(date2).getTime() - new Date(date1).getTime(); //时间差的毫秒数
        return new DifferenceDate(date3);
    }

    static isLeapYear(year: number): boolean {
        return (
            (year / 4 == Math.floor(year / 4) && year / 100 != Math.floor(year / 100)) ||
            (year / 400 == Math.floor(year / 400) && year / 3200 != Math.floor(year / 3200)) ||
            year / 172800 == Math.floor(year / 172800)
        );
    }

    static isToday(date: Date): boolean {
        const now = new Date();
        const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);

        return TimeUtils.difference(date, todayEnd)?.inDays == 0;
    }

    /**
     * 获取指定月份最大天数
     * @param year
     * @param mouth
     */
    static getMaxDaysOfMonth(year: number, mouth: number): number {
        switch (mouth) {
            case 2: {
                if (TimeUtils.isLeapYear(year)) {
                    //闰月 二月 29 天
                    return 29;
                } else {
                    //平月 二月 28 天
                    return 28;
                }
            }
            case 1:
            case 3:
            case 5:
            case 7:
            case 8:
            case 10:
            case 12: {
                //大月 一、三、五、七、八、十、腊月 31 天
                return 31;
            }
            case 4:
            case 6:
            case 9:
            case 11:
                //小月 四、六、九、十一月 30 天
                return 30;
        }

        throw "月份只能[1-12]，不能为:" + mouth;
    }

    static getDayOfWeek(time?: Date, prefix = "星期"): string {
        if (!time) return "";

        const day = time.getDay();
        let text;
        if (day == 0) {
            text = "日";
        } else {
            text = ABCUtils.toChineseNum(time.getDay());
        }

        return `${prefix}${text}`;
    }

    static getDayOfWeekRecent(time?: Date): string {
        if (!time) return "";

        const diff = _.floor((TimeUtils.getTodayStart().getTime() - TimeUtils.getStartOfDate(time).getTime()) / (24 * 60 * 60 * 1000));
        switch (diff) {
            case -1:
                return "明天";
            case 0:
                return "今天";
        }

        const day = time.getDay();
        let text;
        if (day == 0) {
            text = "日";
        } else {
            text = ABCUtils.toChineseNum(time.getDay());
        }

        return `周${text}`;
    }

    static Date2GMTByTimeZone(data?: Date, timezone = 8): Date {
        const _data = data ?? new Date();
        const offset_GMT = new Date(_data).getTimezoneOffset();
        const nowDate = new Date(_data).getTime();
        return new Date(nowDate + offset_GMT * 60 * 1000 + timezone * 60 * 60 * 1000);
    }

    /**
     * 把 String 类型的 Data 转换为分钟数
     * @param duration 08:00
     */
    static strTime2MinuteUnit(duration?: string): number {
        const strAr = duration?.split(":");
        if (strAr?.length == 2) {
            return Number(strAr?.[0] ?? 0) * 60 + Number(strAr?.[1] ?? 0);
        } else {
            return 0;
        }
    }

    /**
     * @desc 效期是否大于生产日期
     * @params
     */
    static showExpiryDateTip(productionDate?: Date, expiryDate?: Date): boolean {
        if (productionDate && expiryDate) {
            const productionTime = productionDate.getTime();
            const expiryTime = expiryDate.getTime();
            return !!productionTime && !!expiryTime && productionTime >= expiryTime;
        }
        return false;
    }

    /**
     * @desc 判断药品是否快要过期，默认快过期时间为一年半
     * @params productionDate 生产日期， expiryDate 效期 ，diffTime 时间值
     * @return
     */
    static validateExpirationTime(productionDate?: Date, expiryDate?: Date, diffTime = TimeUtils.ONE_A_HALF_YEAR): boolean {
        if (productionDate && expiryDate && diffTime) {
            const productionTime = productionDate.getTime();
            const expiryTime = expiryDate.getTime();
            return expiryTime - productionTime < diffTime;
        }
        return false;
    }

    /**
     * 生成指定月数前的时间
     * @param mouthCount
     * @param includeDay
     */
    static getRecentMonthsFirstDay(mouthCount: number, includeDay?: number): Date {
        const now = new Date();
        return TimeUtils.create({
            year: now.getFullYear(),
            month: now.getMonth() - mouthCount,
            date: includeDay ? now.getDate() + includeDay : now.getDate(),
        });
    }

    /**
     * 生成指定天数前的时间
     * @param options
     */
    static getRecentCountDay(options: { dayCount: number }): Date {
        const now = new Date();
        return TimeUtils.create({
            year: now.getFullYear(),
            month: now.getMonth(),
            date: now.getDate() - options.dayCount,
        });
    }

    static WeeksCountRecordKey: {
        key: keyof Pick<
            MessageBodyDateCountStat,
            | "mondayCount"
            | "tuesdayCount"
            | "wednesdayCount"
            | "thursdayCount"
            | "fridayCount"
            | "saturdayCount"
            | "sundayCount"
            | "morningCount"
            | "afternoonCount"
            | "nightCount"
        >;
        keyStr: string;
    }[] = [
        { key: "mondayCount", keyStr: "周一" },
        { key: "tuesdayCount", keyStr: "周二" },
        { key: "wednesdayCount", keyStr: "周三" },
        { key: "thursdayCount", keyStr: "周四" },
        { key: "fridayCount", keyStr: "周五" },
        { key: "saturdayCount", keyStr: "周六" },
        { key: "sundayCount", keyStr: "周日" },
        { key: "morningCount", keyStr: "上午" },
        { key: "afternoonCount", keyStr: "下午" },
        { key: "nightCount", keyStr: "晚上" },
    ];

    /**
     * 时间格式化
     * @param time 时间
     * @param cFormat 格式化结构
     * @param noSameYearOrDay true 跳过同年和同日判断直接返回时间
     * @returns {*}
     */
    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
    static parseTime(time?: any, cFormat?: string, noSameYearOrDay = false): string {
        if (arguments.length === 0) {
            return "";
        }
        let format = cFormat || "y-m-d h:i:s";
        let date;
        if (time && typeof time == "object") {
            date = time;
        } else {
            if (("" + time).length === 10) time = parseInt("" + time) * 1000;
            //@ts-ignore
            date = new Date(time);
        }
        const today = new Date();
        let time_str = "";
        if (
            today.getFullYear() == date.getFullYear() &&
            today.getMonth() == date.getMonth() &&
            today.getDate() == date.getDate() &&
            !noSameYearOrDay
        ) {
            time_str = "今天";
        } else if (today.getFullYear() == date.getFullYear() && !noSameYearOrDay) {
            format = "m-d";
            const formatObj = {
                m: date.getMonth() + 1,
                d: date.getDate(),
            };
            time_str = format.replace(/(y|m|d|h|i|s|a)+/g, (result, key) => {
                //@ts-ignore
                let value = formatObj[key];
                if (result.length > 0 && value < 10) {
                    value = "0" + value;
                }
                return value || 0;
            });
        } else {
            const formatObj = {
                y: date.getFullYear(),
                m: date.getMonth() + 1,
                d: date.getDate(),
                h: date.getHours(),
                i: date.getMinutes(),
                s: date.getSeconds(),
                a: date.getDay(),
            };
            time_str = format.replace(/(y|m|d|h|i|s|a)+/g, (result, key) => {
                //@ts-ignore
                let value = formatObj[key];
                if (result.length > 0 && value < 10) {
                    value = "0" + value;
                }
                return value || 0;
            });
        }

        return time_str;
    }

    /**
     * 将时间格式转换为"202407/26 22:00:00"，便于new Date()解析(SAFARI浏览器不支持"-"分隔符
     * @param time
     */
    static formatTimeForDateParse(time?: string | Date): Date {
        if (time instanceof Date) return time;
        if (!!time) return fromJsonToDate(time)!;
        return new Date();
    }

    /**
     * 判断当前时间是否是对应月份
     * @param monthNum --比对月份
     * @param date
     */
    static isCorrespondingMonth(monthNum: number, date: Date = new Date()): boolean {
        const month = date.getMonth() + 1;
        return month === monthNum;
    }

    /**
     * 判断当前时间是否是对应月份的某一段时间
     * @param monthNum --- 月份
     * @param date ---日期
     * @param startDay ---起始天数
     * @param endDay ---截止天数
     */
    static isCorrespondingMonthRange(monthNum: number, startDay: number, endDay: number, date: Date = new Date()): boolean {
        const month = date.getMonth() + 1;
        const day = date.getDate();
        return month === monthNum && day >= startDay && day <= endDay;
    }

    /**
     * @desc 当前日期是否超出目标日期limit范围， 超出-true,未超出-false
     * <AUTHOR>
     * @date 2024/02/04 11:04:12
     * @param {Date} targetDate
     * @param {Date} currentDate
     * @param {Number} limit
     * @return {Boolean}
     */
    static currentDateOutLimit(targetDate: Date, currentDate: Date, limit: number): boolean {
        const now = TimeUtils.parseTime(new Date(currentDate), "y-m-d", true);
        const target = TimeUtils.parseTime(new Date(targetDate), "y-m-d", true);
        const timeDiff = Math.abs(new Date(now).getTime() - new Date(target).getTime());
        const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));
        return daysDiff >= limit;
    }

    /**
     * @desc 按某一时间段计算天数 不足一天展示1天
     * <AUTHOR>
     * @param startDate 开始日期（Date对象）
     * @param endDate 结束日期（Date对象），如果未提供，则默认为当前日期
     * @return {number} 返回两个日期之间的天数差，至少为1
     */
    static calculateDaysByTimePeriod(startDate: Date, endDate: Date): number {
        const timeDifference = Number(!!endDate ? new Date(endDate) : new Date()) - Number(new Date(startDate));
        const daysDifference = timeDifference / (1000 * 60 * 60 * 24);
        // 如果天数差小于1，则返回1；否则返回实际天数差
        return Math.round(Math.max(1, Math.abs(daysDifference)));
    }
}
