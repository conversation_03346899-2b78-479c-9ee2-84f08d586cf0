/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020-03-20
 *
 * @description
 *
 */
import { ignore } from "../global";
import _ from "lodash";
import Big from "big.js";

export default class NumberUtils {
    static formatMaxFixed(num?: number, minimumFractionDigits?: number, maximumFractionDigits?: number): string {
        ignore(maximumFractionDigits);
        if (num == null) return "";
        // const format = Intl.NumberFormat('zh-CN', {
        //     minimumFractionDigits: minimumFractionDigits || 0,
        //     maximumFractionDigits: maximumFractionDigits || 10
        // });
        if (_.isUndefined(minimumFractionDigits)) return Number(num).toString();

        return Number(num).toFixed(minimumFractionDigits);
    }

    static percent(number?: number): string {
        if (_.isUndefined(number)) {
            return "-";
        }
        number = Number(number) || 0;
        return number.toFixed(2) + "%";
    }

    static count1(num: number): string {
        let _num = (num ?? 0).toString();

        if (_num.indexOf(".") > -1) {
            _num = Number(num).toFixed(1);
            if (+_num === 0) {
                _num = "0";
            }
        }
        return _num;
    }

    static toFixedWithoutZero(number?: number, fixed = 2, defaultValue: string | number = ""): number | string {
        if (_.isNil(number) || _.isNaN(number)) return defaultValue;
        return parseFloat(number.toFixed(fixed));
    }

    /**
     * @desc 价格格式化
     * @example 1 => 1.00
     * @example 1.1 => 1.10
     * @example 1.11 => 1.11
     * @example 1.111.. => 1.111..
     * @param number
     * @param fixed
     * @param defaultValue
     */
    static formatPriceToFixedWithoutZero(number?: number, fixed = 2, defaultValue = ""): string {
        if (_.isNil(number) || _.isNaN(number)) return defaultValue;
        const price = parseFloat(number.toFixed(5));
        if (price.toString() !== number.toFixed(fixed).toString() && price.toString().length < number.toFixed(fixed).toString().length) {
            return price.toFixed(fixed);
        } else {
            return price.toString();
        }
    }

    /**
     * 引入Big.js库处理小数 保留小数同时 截断指定数位
     * @param number
     * @param fixed 小数位阶（默认 2）
     * @param truncation 截断位数（默认 4）
     */
    static formatPriceFromBig(number: number, fixed = 2, truncation = 4): string {
        const Big = require("big.js");
        const bigNumber = new Big(number);
        const numberString = bigNumber.toString();
        const [integerPart, decimalPart = ""] = numberString.split(".");
        // 截断小数部分到{truncation}位并确保至少保留{fixed}位小数
        const truncatedDecimal = decimalPart.substring(0, truncation).padEnd(fixed, "0");

        return `${integerPart}.${truncatedDecimal}`;
    }

    static isFloat(n: number): boolean {
        return Number(n) !== parseInt(String(n));
    }

    static toFraction(str: string | number): { numerator: number; denominator: number } {
        str = str.toString();
        const fractionRegExp = new RegExp(/^([1-9]\s)?[1-9][\/][1-9]$/);

        // 不是合法的分数形式，目前只支持 1/3 || 1 1/3
        if (!fractionRegExp.test(str)) {
            return {
                numerator: Number(str),
                denominator: 1,
            };
        }
        let intor = 0;
        let fractionStr = str;

        const _arr = str.split(" ");
        // 有整数部分
        if (_arr.length === 2) {
            intor = Number(_arr[0]);
            fractionStr = _arr[1];
        }

        const fractionArr = fractionStr.split("/");
        //分子
        const numerator = Number(fractionArr[0]);
        //分母
        const denominator = Number(fractionArr[1]);

        return {
            numerator: +numerator + intor * denominator,
            denominator,
        };
    }

    // 创建精确加法工具函数
    static preciseAdd(a?: number, b?: number): number {
        try {
            return new Big(a || 0).plus(new Big(b || 0)).toNumber();
        } catch (error) {
            console.error("Precise add error:", error);
            return (a || 0) + (b || 0);
        }
    }
}
