/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020/5/10
 *
 * @description 用于监听键盘显示隐藏事件
 *
 */
import { BehaviorSubject } from "rxjs";
import { HippyEventEmitter } from "@hippy/react";
import { Disposable } from "../cleanup/disposable";

class KeyboardListener extends Disposable {
    visibleObservable = new BehaviorSubject<{
        visible: boolean;
        keyboardHeight?: number;
    }>({
        visible: false,
        keyboardHeight: 0,
    });
    hippyEventEmitterHandler: any;

    constructor() {
        super();
        const hippyEventEmitter = new HippyEventEmitter();
        this.hippyEventEmitterHandler = hippyEventEmitter.addListener(
            "keyboardVisibleChanged",
            (evt: { visible: boolean; keyboardHeight?: number }) => {
                this.visibleObservable.next(evt);
            }
        );
    }

    //override
    dispose() {
        super.dispose();

        this.hippyEventEmitterHandler?.remove();
        this.visibleObservable.unsubscribe();
    }
}

const listener = new KeyboardListener();

export const keyboardListener = listener.visibleObservable;
