/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-20
 *
 * @description
 */
import { AnyType } from "../common-types";

class Disposable {
    isDisposed = false;

    public dispose(): void {
        this.isDisposed = true;
    }

    public addToDisposableBag(bag: DisposableTracker): void {
        bag.addDisposable(this);
    }
}

class DisposableTracker {
    _disposables: Array<any> | undefined;

    addDisposable(disposable: AnyType /*Disposable | Subscription*/): void {
        if (this._disposables == undefined) {
            this._disposables = [];
        }

        this._disposables!.push(disposable);
    }

    closeDisposables(): void {
        if (!this._disposables) return;

        for (let i = this._disposables.length - 1; i >= 0; --i) {
            const item = this._disposables[i];
            if (item.dispose) {
                item.dispose();
            } else if (item.unsubscribe) {
                item.unsubscribe();
            }
        }

        this._disposables = [];
    }
}

export { Disposable, DisposableTracker };
