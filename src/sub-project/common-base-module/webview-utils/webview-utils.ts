/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/8/14
 *
 * @description 用于操作native端的cookie数据
 */

import { callNativeWithPromise } from "@hippy/react";
import { DisposableTracker } from "../cleanup/disposable";

const kModuleName = "WebViewUtils";

class WebViewUtils extends DisposableTracker {
    /**
     * 清除native端的cookie
     */
    clearCookies(): Promise<void> {
        return callNativeWithPromise(kModuleName, "clearCookies", {});
    }

    getDefaultUserAgent(): Promise<string> {
        return callNativeWithPromise(kModuleName, "getDefaultUserAgent", {});
    }

    setCookie(url: string, cookie: string): Promise<void> {
        return callNativeWithPromise(kModuleName, "setCookie", {
            url: url,
            cookie: cookie,
        });
    }

    getCookie(url: string): Promise<string | undefined> {
        return callNativeWithPromise(kModuleName, "getCookie", { url: url });
    }
}

const webviewUtils = new WebViewUtils();
export { webviewUtils };
