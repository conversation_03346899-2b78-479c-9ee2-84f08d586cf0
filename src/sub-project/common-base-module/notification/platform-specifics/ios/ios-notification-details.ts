/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/8/6
 *
 * @description
 */
import { IosNotificationAttachment } from "./ios-notification-attachment";

export class IosNotificationDetails {
    /// Display an alert when the notification is triggered while app is in the foreground.
    ///
    /// When this is set to `null`, it will use the default setting given to [IOSInitializationSettings.defaultPresentAlert].
    /// Applicable to iOS 10 and above.
    presentAlert?: boolean;

    /// Play a sound when the notification is triggered while app is in the foreground.
    ///
    /// When this is set to `null`, it will use the default setting given to [IOSInitializationSettings.defaultPresentSound].
    /// Applicable to iOS 10 and above.
    presentSound?: boolean;

    /// Apply the badge value when the notification is triggered while app is in the foreground.
    ///
    /// When this is set to `null`, it will use the default setting given to [IOSInitializationSettings.defaultPresentBadge].
    /// Applicable to iOS 10 and above.
    presentBadge?: boolean;

    /// Specifies the name of the file to play for the notification.
    ///
    /// Requires setting [presentSound] to true. If [presentSound] is set to true but [sound] isn't specified then it will use the default notification sound.
    sound?: string;

    /// Specify the number to display as the app icon's badge when the notification arrives.
    ///
    /// Specify the number `0` to remove the current badge, if present. Greater than `0` to display a badge with that number.
    /// Specify `null` to leave the current badge unchanged.
    badgeNumber?: number;

    /// Specifies the list of attachments included with the notification.
    ///
    /// Applicable to iOS 10 and above.
    attachments?: IosNotificationAttachment[];
}
