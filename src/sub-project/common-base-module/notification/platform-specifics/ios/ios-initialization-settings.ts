/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/8/11
 *
 * @description
 */

export class IosInitializationSettings {
    /// Request permission to display an alert.
    ///
    /// Default value is true.
    requestAlertPermission?: boolean;

    /// Request permission to play a sound.
    ///
    /// Default value is true.
    requestSoundPermission?: boolean;

    /// Request permission to badge app icon.
    ///
    /// Default value is true.
    requestBadgePermission?: boolean;

    /// Configures the default setting on if an alert should be displayed when a notification is triggered while app is in the foreground.
    ///
    /// Default value is true.
    /// Applicable to iOS 10 and above.

    defaultPresentAlert?: boolean;

    /// Configures the default setting on if a sound should be played when a notification is triggered while app is in the foreground by default.
    ///
    /// Default value is true.
    /// Applicable to iOS 10 and above.
    defaultPresentSound?: boolean;

    /// Configures the default setting on if a badge value should be applied when a notification is triggered while app is in the foreground by default.
    ///
    /// Default value is true.
    /// Applicable to iOS 10 and above.
    defaultPresentBadge?: boolean;

    /// Callback for handling when a notification is triggered while the app is in the foreground.
    ///
    /// Applicable to iOS versions below 10.
    //  onDidReceiveLocalNotification;

    constructor(params?: {
        requestAlertPermission?: boolean;
        requestSoundPermission?: boolean;
        requestBadgePermission?: boolean;
        defaultPresentAlert?: boolean;
        defaultPresentSound?: boolean;
        defaultPresentBadge?: boolean;
    }) {
        if (params) {
            Object.assign(this, params);
        }
    }
}
