/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/8/6
 *
 * @description
 */
import { AndroidNotificationChannelAction, GroupAlertBehavior, Importance, Priority } from "../../types";
import { AndroidNotificationStyle, StyleInformation } from "./styles/style-information";
import { DefaultStyleInformation } from "./styles/default-style-information";

export class AndroidNotificationDetails {
    // /// The icon that should be used when displaying the notification.
    // ///
    // /// When this is set to `null`, the default icon given to [AndroidInitializationSettings.defaultIcon] will be used.
    icon?: string;
    //
    // /// The channel's id.
    // ///
    // /// Required for Android 8.0+.
    channelId?: string;
    //
    // /// The channel's name.
    // ///
    // /// Required for Android 8.0+.
    channelName?: string;
    //
    // /// The channel's description.
    // ///
    // /// Required for Android 8.0+.
    channelDescription?: string;
    //
    // /// Whether notifications posted to this channel can appear as application icon badges in a Launcher
    channelShowBadge?: boolean;
    //
    // /// The importance of the notification.
    importance?: Importance;
    //
    // /// The priority of the notification
    priority?: Priority;
    //
    // /// Indicates if a sound should be played when the notification is displayed.
    // ///
    // /// For Android 8.0+, this is tied to the specified channel cannot be changed afterward the channel has been created for the first time.
    playSound?: boolean;
    //
    // /// The sound to play for the notification.
    // ///
    // /// Requires setting [playSound] to true for it to work.
    // /// If [playSound] is set to true but this is not specified then the default sound is played.
    // /// For Android 8.0+, this is tied to the specified channel cannot be changed afterward the channel has been created for the first time.
    // final AndroidNotificationSound sound?:string;
    //
    // /// Indicates if vibration should be enabled when the notification is displayed.
    // ///
    // /// For Android 8.0+, this is tied to the specified channel cannot be changed afterward the channel has been created for the first time.
    enableVibration?: boolean;
    //
    // /// Indicates if lights should be enabled when the notification is displayed.
    // ///
    // /// For Android 8.0+, this is tied to the specified channel cannot be changed afterward the channel has been created for the first time.
    enableLights?: boolean;
    //
    // /// Configures the vibration pattern.
    // ///
    // /// Requires setting [enableVibration] to true for it to work.
    // /// For Android 8.0+, this is tied to the specified channel cannot be changed afterward the channel has been created for the first time.
    // final Int64List vibrationPattern?:string;
    //
    // /// Specifies the information of the rich notification style to apply to the notification.
    style?: AndroidNotificationStyle;
    styleInformation?: StyleInformation;
    //
    // /// Specifies the group that this notification belongs to.
    // ///
    // /// For Android 7.0+ (API level 24)
    // final String groupKey?:string;
    //
    // /// Specifies if this notification will function as the summary for grouped notifications.
    setAsGroupSummary?: boolean;
    //
    // /// Specifies the group alert behavior for this notification.
    // ///
    // /// Default is AlertAll.
    // /// See https://developer.android.com/reference/android/support/v4/app/NotificationCompat.Builder.html#setGroupAlertBehavior(int) for more details.
    groupAlertBehavior?: GroupAlertBehavior;
    //
    // /// Specifies if the notification should automatically dismissed upon tapping on it.
    autoCancel?: boolean;
    //
    // /// Specifies if the notification will be "ongoing".
    ongoing?: boolean;
    //
    // /// Specifies the color.
    // final Color color?:Color;
    //
    // /// Specifics the large icon to use.
    // final AndroidBitmap largeIcon;
    //
    // /// Specifies if you would only like the sound, vibrate and ticker to be played if the notification is not already showing.
    // final bool onlyAlertOnce;
    //
    // /// Specifies if the notification should display the timestamp of when it occurred.
    // ///
    // /// To control the actual timestamp of the notification, use [when].
    showWhen?: boolean;
    //
    // /// Specifies the timestamp of the notification.
    // ///
    // /// To control whether the timestamp is shown in the notification, use
    // /// [showWhen].
    // ///
    // /// The timestamp is expressed as the number of milliseconds since the "Unix epoch" 1970-01-01T00:00:00Z (UTC).
    // /// If it's not specified but a timestamp should be shown (i.e. [showWhen] is set to `true`),
    // /// then Android will default to showing when the notification occurred.
    when?: number;
    //
    // /// Specifies if the notification will be used to show progress.
    showProgress?: boolean;
    //
    // /// The maximum progress value.
    maxProgress?: number;
    //
    // /// The current progress value.
    progress?: number;
    //
    // /// Specifies if an indeterminate progress bar will be shown.
    indeterminate?: boolean;
    //
    // /// Specifies the light color of the notification.
    // ///
    // /// For Android 8.0+, this is tied to the specified channel cannot be changed afterward the channel has been created for the first time.
    // final Color ledColor;
    //
    // /// Specifies how long the light colour will remain on.
    // ///
    // /// Not applicable for Android 8.0+
    // final int ledOnMs;
    //
    // /// Specifies how long the light colour will remain off.
    // ///
    // /// Not applicable for Android 8.0+
    // final int ledOffMs;
    //
    // /// Specifies the "ticker" text which is sent to accessibility services.
    ticker?: string;
    //
    // /// The action to take for managing notification channels.
    // ///
    // /// Defaults to creating the notification channel using the provided details if it doesn't exist
    channelAction?: AndroidNotificationChannelAction;
    //
    // /// Defines the notification visibility on the lockscreen
    // final NotificationVisibility visibility;
    //
    // /// The duration in milliseconds after which the notification will be cancelled if it hasn't already
    // final int timeoutAfter;
    //
    // /// The notification category.
    // ///
    // /// Refer to Android notification API documentation at https://developer.android.com/reference/androidx/core/app/NotificationCompat.html#constants_2 for the available categories
    // final String category;
    //
    // /// Specifies the additional flags.
    // ///
    // /// These flags will get added to the native Android notification's flags field: https://developer.android.com/reference/android/app/Notification#flags
    // /// For a list of a values, refer to the documented constants prefixed with "FLAG_" (without the quotes) at https://developer.android.com/reference/android/app/Notification.html#constants_1.
    // /// For example, use a value of 4 to allow the audio to repeat as documented at https://developer.android.com/reference/android/app/Notification.html#FLAG_INSISTEN
    // final Int32List additionalFlags;

    constructor(params?: Readonly<AndroidNotificationDetails>) {
        if (params) Object.assign(this, params);

        this.importance = this.importance ?? Importance.Default;
        this.priority = this.priority ?? Priority.Default;

        this.playSound = this.playSound ?? true;

        this.enableVibration = this.enableVibration ?? true;

        this.groupAlertBehavior = this.groupAlertBehavior ?? GroupAlertBehavior.All;

        this.autoCancel = this.autoCancel ?? true;

        this.showWhen = this.showWhen ?? true;
        this.channelShowBadge = this.channelShowBadge ?? true;
        this.showProgress = this.showProgress ?? true;
        this.maxProgress = this.maxProgress ?? 0;
        this.progress = this.progress ?? 0;
        this.indeterminate = this.indeterminate ?? false;
        this.channelAction = this.channelAction ?? AndroidNotificationChannelAction.CreateIfNotExistse;
        this.enableLights = this.enableLights ?? false;
        this.style = this.style ?? AndroidNotificationStyle.Default;
        this.styleInformation = this.styleInformation ?? new DefaultStyleInformation(false, false);
    }
}
