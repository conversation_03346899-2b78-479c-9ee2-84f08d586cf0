import { AndroidNotificationDetails } from "./platform-specifics/android/android-notification-details";
import { IosNotificationDetails } from "./platform-specifics/ios/ios-notification-details";

export class NotificationDetails {
    android?: AndroidNotificationDetails;

    iOS?: IosNotificationDetails;

    constructor(android?: AndroidNotificationDetails, iOS?: IosNotificationDetails) {
        this.android = android;
        this.iOS = iOS;
    }
}
