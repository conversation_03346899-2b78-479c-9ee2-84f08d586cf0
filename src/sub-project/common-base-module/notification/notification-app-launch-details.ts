/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/8/6
 *
 * @description
 */

export class NotificationAppLaunchDetails {
    /// Indicates if the app was launched via notification
    didNotificationLaunchApp: boolean;

    /// The payload of the notification that launched the app
    payload: string;

    constructor(didNotificationLaunchApp: boolean, payload: string) {
        this.didNotificationLaunchApp = didNotificationLaunchApp;
        this.payload = payload;
    }
}
