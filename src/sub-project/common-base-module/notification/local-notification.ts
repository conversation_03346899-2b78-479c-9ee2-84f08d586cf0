/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/5/26
 *
 * @description 用于显示本地通知栏
 */
import { callNativeWithPromise, HippyEventEmitter } from "@hippy/react";
import { urlDispatcher } from "../../url-dispatcher/url-dispatcher";
import { AndroidNotificationDetails } from "./platform-specifics/android/android-notification-details";
import { IosNotificationDetails } from "./platform-specifics/ios/ios-notification-details";
import { AndroidInitializationSettings } from "./platform-specifics/android/android-intialization-settings";
import { IosInitializationSettings } from "./platform-specifics/ios/ios-initialization-settings";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import { Importance, Priority } from "./types";

const kModuleName = "LocalNotification";

export class LocalNotification {
    hippyEventEmitterHandler: any;

    androidPlatformChannelSpecifics: AndroidNotificationDetails;
    iOSPlatformChannelSpecifics: IosNotificationDetails;

    constructor() {
        const initializationSettingsAndroid = new AndroidInitializationSettings("ic_launcher");
        const initializationSettingsIOS = new IosInitializationSettings();

        callNativeWithPromise(kModuleName, "initialize", DeviceUtils.isIOS() ? initializationSettingsIOS : initializationSettingsAndroid);

        const channelId = "default";
        const name = "默认通知";
        const description = "用于显示运营消息，系统通知";

        this.androidPlatformChannelSpecifics = new AndroidNotificationDetails({
            channelId: channelId,
            channelName: name,
            channelDescription: description,
            importance: Importance.Max,
            priority: Priority.High,
            ticker: "ticker",
        });

        this.iOSPlatformChannelSpecifics = new IosNotificationDetails();
        const hippyEventEmitter = new HippyEventEmitter();
        this.hippyEventEmitterHandler = hippyEventEmitter.addListener(
            "LocalNotification",
            (evt: { methodName: string; arguments: any }) => {
                this._handleInvokeMethod(evt.methodName, evt.arguments);
            }
        );
    }

    id = 0;

    async show(params: { id?: number; title: string; body: string; payload: string }): Promise<void> {
        const { id = this.id++, title, body, payload } = params;
        return callNativeWithPromise(kModuleName, "show", {
            id: id,
            title: title,
            body: body,
            payload: payload,
            platformSpecifics: DeviceUtils.isIOS() ? this.iOSPlatformChannelSpecifics : this.androidPlatformChannelSpecifics,
        });
    }

    private _handleInvokeMethod(methodName: string, params: { id?: number; title?: string; body?: string; payload?: string }) {
        const payload = params.payload;
        if ("didReceiveLocalNotification" === methodName) {
            payload && urlDispatcher.dispatchUrl(payload);
        } else if ("selectNotification" === methodName) {
            payload && urlDispatcher.dispatchUrl(payload);
        }
    }
}

const localNotification = new LocalNotification();
export { localNotification };
