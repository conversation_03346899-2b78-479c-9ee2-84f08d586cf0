/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/8/6
 *
 * @description
 */

/// The available intervals for periodically showing notifications
export enum RepeatInterval {
    EveryMinute,
    Hourly,
    Daily,
    Weekly,
}

export class PendingNotificationRequest {
    id?: string;
    title?: string;
    body?: string;
    payload?: string;
}

export enum Importance {
    Unspecified = -1000,
    None = 0,
    Min = 1,
    Low = 2,
    Default = 3,
    High = 4,
    Max = 5,
}

export enum Priority {
    Min = -2,
    Low = -1,
    Default = 0,
    High = 1,
    Max = 2,
}

/// The available alert behaviours for grouped notifications.
export enum GroupAlertBehavior {
    All,
    Summary,
    Children,
}

/// The available actions for managing notification channels.
///
/// [CreateIfNotExists]: will create a channel if it doesn't exist.
/// [Update]: will update the details of an existing channel. Note that some details can not be changed once a channel has been created.
export enum AndroidNotificationChannelAction {
    CreateIfNotExistse,
    Update,
}
