/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-30
 *
 * @description
 */
import { AnyType } from "../common-types";

export class Completer<T> {
    promise: Promise<T>;
    finish = false;

    private _resolve!: (value?: T | PromiseLike<T>) => void;
    private _reject!: (reason?: any) => void;

    public resolve(value?: T | PromiseLike<T>): void {
        this._resolve(value);
        this.finish = true;
    }

    public reject(reason?: AnyType): void {
        this._reject(reason);
        this.finish = true;
    }

    constructor() {
        this.promise = new Promise<T>((resolve, reject) => {
            this._resolve = resolve;
            this._reject = reject;
        });
    }
}
