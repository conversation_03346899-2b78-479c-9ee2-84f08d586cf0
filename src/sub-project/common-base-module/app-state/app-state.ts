/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/5/26
 *
 * @description
 */
import { BehaviorSubject } from "rxjs";
import { HippyEventEmitter } from "@hippy/react";
import { LogUtils } from "../log";
import { AbcTextInput } from "../../base-ui/views/abc-text-input";
import { DeviceUtils } from "../../base-ui/utils/device-utils";

class AppState {
    foregroundState = new BehaviorSubject<boolean>(true);

    private _focusInput?: AbcTextInput | null;

    constructor() {
        const hippyEventEmitter = new HippyEventEmitter();
        hippyEventEmitter.addListener("appEnterBackground", (/*ignored: any*/) => {
            LogUtils.d("AppState", "Enter appEnterBackground");
            this._focusInput = AbcTextInput.focusInput;
            DeviceUtils.isAndroid() && AbcTextInput.focusInput?.blur();
            this.foregroundState.next(false);
        });

        hippyEventEmitter.addListener("appEnterForeground", (/*ignored: any*/) => {
            LogUtils.d("AppState", "Enter appEnterForeground");
            DeviceUtils.isAndroid() && this._focusInput && this._focusInput?.focus();
            this.foregroundState.next(true);
        });
    }
}

const appState = new AppState();
export default appState;
