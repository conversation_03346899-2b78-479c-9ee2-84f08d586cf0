/**
 * 成都字节星球科技公司
 *
 * Created by <PERSON><PERSON><PERSON> on 2020/5/29
 *
 * @description
 *
 */
import { LogUtils } from "../log";

export async function ABCTrace<T>(task: Promise<T> | Function, taskName: string): Promise<T> {
    const startTime = new Date();
    let ret: T;
    if (task instanceof Function) ret = await task();
    else ret = await task;

    const endTime = new Date();
    LogUtils.d("ABCTrace", taskName + " 耗时:" + (endTime.getTime() - startTime.getTime()) + "ms");

    return ret;
}

export function ABCSyncTrace<T>(task: () => T, taskName: string): T {
    const startTime = new Date();
    const ret: T = task();

    const endTime = new Date();
    LogUtils.d("ABCTrace", taskName + " 耗时:" + (endTime.getTime() - startTime.getTime()) + "ms");

    return ret;
}
