// import {DisposableTracker} from "../cleanup/disposable";
// import {Subscription} from "rxjs";
// import {LogUtils} from "../log";
//
// /**
//  * 成都字节星期科技公司
//  * <AUTHOR>
//  * @date 2020-04-17
//  *
//  * @description
//  */
//
//
// declare module 'rxjs' {
//     interface Subscription {
//         addToDisposableBag(bag: DisposableTracker): void;
//     }
// }
//
// Subscription.prototype.addToDisposableBag = function (this: Subscription, bag: DisposableTracker) {
//     LogUtils.d("addToDisposableBag this = " + this + ", bag = " + bag);
//     bag.addDisposable(this);
// };
