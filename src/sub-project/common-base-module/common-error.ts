/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020-04-30
 *
 * @description
 *
 */
import { AnyType } from "./common-types";

export class ABCError extends Error {
    detailError: any;

    constructor(detailError: AnyType) {
        super(detailError instanceof Error ? `${detailError.message}` : "");
        this.detailError = detailError;
    }
    toString(): string {
        return this.detailError.toString();
    }
}

export class ABCDeviceLoginError extends Error {
    detailError: any;
    url?: string;
    method?: string;

    constructor(detailError?: AnyType, url?: string, method?: string) {
        super(`社备案注册失败`);
        this.detailError = detailError;
        this.url = url;
        this.method = method;
    }
}

export class UnimplementedError {
    message: string;

    constructor(message: string) {
        this.message = message;
    }
}
