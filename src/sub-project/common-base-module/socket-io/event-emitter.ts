/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/8/5
 *
 * @description
 */
import _ from "lodash";
import { AnyType } from "../common-types";

export interface EventHandler {
    (data: any): void;
}

export class EventEmitter {
    event = new Map<string, EventHandler[]>();
    eventOnce = new Map<string, EventHandler[]>();

    public emit(event: string, data: AnyType): void {
        this.event.get(event)?.forEach((callback) => {
            callback(data);
        });

        this.eventOnce.get(event)?.forEach((callback) => {
            callback(data);
        });

        this.eventOnce.delete(event);
    }

    public on(event: string, handler: EventHandler): void {
        let list = this.event.get(event);
        if (!list) {
            list = [];
            this.event.set(event, list);
        }

        if (list.indexOf(handler) >= 0) {
            return;
        }

        list.push(handler);
    }

    public once(event: string, handler: EventHandler): void {
        let list = this.eventOnce.get(event);
        if (!list) {
            list = [];
            this.eventOnce.set(event, list);
        }

        if (list.indexOf(handler) >= 0) {
            return;
        }

        list.push(handler);
    }

    public off(event: string, handler?: EventHandler): void {
        if (handler) {
            let list = this.event.get(event);
            if (list) {
                _.remove(list, (handler1) => handler == handler1);
            }

            list = this.eventOnce.get(event);
            if (list) {
                _.remove(list, (handler1) => handler == handler1);
            }
        } else {
            this.event.delete(event);
            this.eventOnce.delete(event);
        }
    }

    public hasListeners(event: string): boolean {
        return !_.isEmpty(this.event.get(event)) || !_.isEmpty(this.eventOnce.get(event));
    }
}
