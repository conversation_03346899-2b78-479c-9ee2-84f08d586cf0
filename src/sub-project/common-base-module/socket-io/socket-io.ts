/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/8/5
 *
 * @description
 */

import { callNativeWithPromise, HippyEventEmitter } from "@hippy/react";
import { UUIDGen } from "../utils";
import { LogUtils } from "../log";
import { EventEmitter, EventHandler } from "./event-emitter";
import _ from "lodash";
import { JsonMapper, TypeConverter } from "../json-mapper/json-mapper";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import { AnyType } from "../common-types";

const kModuleName = "SocketIO";

export class SocketIo {
    private readonly _sessionId: string;

    private hippyEventEmitterHandler: any;

    private eventHandler = new EventEmitter();

    constructor(
        url: string,
        params: {
            namespace?: string; //must start with '/'
            log?: boolean;
            compress?: boolean;
            forceWebsockets?: boolean;
            cookies?: string;
            extraHeaders: { [key: string]: any };
            connectParams?: { [key: string]: any }; // ios 必传
        }
    ) {
        this._sessionId = UUIDGen.generate();

        const hippyEventEmitter = new HippyEventEmitter();
        this.hippyEventEmitterHandler = hippyEventEmitter.addListener(
            "socketIOOnEvent",
            (evt: { sessionId: string; name: string; items?: any[] }) => {
                const { sessionId, name, items } = evt;
                if (sessionId == this._sessionId) {
                    this.eventHandler.emit(name, !_.isEmpty(items) ? items![0] : undefined);
                    LogUtils.d("socketIOOnEvent sessionId = " + JSON.stringify(evt));
                }
            }
        );
        callNativeWithPromise(kModuleName, "create", this._sessionId, url, params ?? {});
    }

    public connect(): void {
        callNativeWithPromise(kModuleName, "connect", this._sessionId);
    }

    on(event: string, handler: EventHandler): void {
        this.eventHandler.on(event, handler);

        if (DeviceUtils.isAndroid() || DeviceUtils.isOhos()) callNativeWithPromise(kModuleName, "on", this._sessionId, event);
    }

    public once(event: string, handler: EventHandler): void {
        this.eventHandler.once(event, handler);
    }

    public off(event: string, handler?: EventHandler): void {
        this.eventHandler.off(event, handler);

        if (DeviceUtils.isAndroid() || DeviceUtils.isOhos()) callNativeWithPromise(kModuleName, "off", this._sessionId, event);
    }

    typeConverter: TypeConverter = { dateConverter: (date) => date.toString() };

    public emit(name: string, body: AnyType): Promise<void> {
        return callNativeWithPromise(kModuleName, "emit", this._sessionId, name, JsonMapper.serialize(body, this.typeConverter));
    }

    public hasListener(event: string): boolean {
        return this.eventHandler.hasListeners(event);
    }

    public disconnect(): void {
        callNativeWithPromise(kModuleName, "disconnect", this._sessionId);

        this.hippyEventEmitterHandler?.remove();
        this.hippyEventEmitterHandler = undefined;
    }
}
