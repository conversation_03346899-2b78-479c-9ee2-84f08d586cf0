/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-20
 *
 * @description
 */

import { Observable, of } from "rxjs";
import { delay } from "rxjs/operators";
import { ABCError } from "../common-error";

export function fromAsyncIterableIgnoreError<T>(gen: AsyncGenerator<T>): Observable<T | ABCError> {
    return new Observable<T | ABCError>((subscriber) => {
        (async () => {
            try {
                let result = await gen.next();
                while (!result.done) {
                    subscriber.next(result.value);
                    result = await gen.next();
                }
            } catch (e) {
                subscriber.next(new ABCError(e));
            }
            subscriber.complete();
        })();

        return subscriber;
    });
}

export function fromPromise<T>(promise: Promise<T>): Observable<T> {
    return new Observable<T>((subscriber) => {
        (async () => {
            try {
                const result = await promise;
                subscriber.next(result);
            } catch (e) {
                subscriber.error(e);
            }
            subscriber.complete();
        })();

        return subscriber;
    });
}

export function delayed(timeInMs: number): Observable<number> {
    return of(timeInMs).pipe(delay(timeInMs));
}
