/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020/8/2
 *
 * @description
 *
 */

import { ScrollView } from "@hippy/react";
import { Fiber } from "react-reconciler";
import { Component } from "react";
import { UIUtils } from "../base-ui/utils";
import { LogUtils } from "./log";

export type RefType = string | Fiber | Element | Component;
declare module "@hippy/react" {
    interface ScrollView {
        /**
         * 滚动到指定item, firstChild, lastChild用于辅助计算滚动范围和目标滚动值
         * @param child 要显示的元素
         * @param firstChild 第一个child
         * @param lastChild 最后一个child
         * @param animated
         */
        scrollChildToVisible(child: RefType, firstChild: RefType, lastChild: RefType, animated?: boolean): Promise<void>;
    }
}

ScrollView.prototype.scrollChildToVisible = async function (child: RefType, firstChild: RefType, lastChild: RefType, animated = true) {
    LogUtils.d("scrollChildToVisible firstChildLayout = ");
    const selfLayout = await UIUtils.measureInWindow(this);
    const { width: scrollViewWidth, height: scrollViewHeight } = selfLayout;
    const firstChildLayout = await UIUtils.measureInAncestor(firstChild, this);
    const lastChildLayout = await UIUtils.measureInAncestor(lastChild, this);
    const childLayout = await UIUtils.measureInAncestor(child, this);
    const { x, width, y, height } = childLayout;

    LogUtils.d("scrollChildToVisibleArea firstChildLayout = " + JSON.stringify(firstChildLayout));
    LogUtils.d("scrollChildToVisibleArea lastChildLayout = " + JSON.stringify(lastChildLayout));
    LogUtils.d("scrollChildToVisibleArea selfLayout = " + JSON.stringify(selfLayout));
    LogUtils.d("scrollChildToVisibleArea childLayout = " + JSON.stringify(childLayout));

    const scrollDirectionX = firstChildLayout.y == lastChildLayout.y; //是否为横向滚动
    if (scrollDirectionX) {
        const scrollX = -firstChildLayout.x; //当前滚动值
        const maxScrollRange = lastChildLayout.x - firstChildLayout.x - scrollViewWidth; //最大滚动范围
        //当前已经在可见，无需滚动
        if (x >= 0 && x + width <= scrollViewWidth) {
            return;
        }
        let targetScrollX: number | undefined;
        if (x < 0) {
            //子View部分或全部在顶部不可见区域
            //-firstChildLayout.y为当前滚动值
            targetScrollX = scrollX + x;
        } else if (x + width > scrollViewWidth) {
            //子view在底部部份或完全不可见
            targetScrollX = scrollX + (x + width - scrollViewWidth);
        }
        if (targetScrollX == undefined) return;
        if (targetScrollX < 0) targetScrollX = 0;

        if (targetScrollX > maxScrollRange) targetScrollX = maxScrollRange;

        LogUtils.d("scrollChildToVisibleArea scrollTo = " + targetScrollX + ", maxScrollRange = " + maxScrollRange);
        this.scrollTo(targetScrollX, 0, animated);
        return;
    }

    const scrollY = -firstChildLayout.y; //当前滚动值
    const maxScrollRange = lastChildLayout.y - firstChildLayout.y - scrollViewHeight; //最大滚动范围
    debugger;
    //当前已经在可见，无需滚动
    if (y >= 0 && y + height <= scrollViewHeight) {
        return;
    }

    let targetScrollY: number | undefined;
    if (y < 0) {
        //子View部分或全部在顶部不可见区域
        //-firstChildLayout.y为当前滚动值
        targetScrollY = scrollY + y;
    } else if (y + height > scrollViewHeight) {
        //子view在底部部份或完全不可见
        targetScrollY = scrollY + (y + height - scrollViewHeight);
    }

    if (targetScrollY == undefined) return;
    if (targetScrollY < 0) targetScrollY = 0;

    if (targetScrollY > maxScrollRange) targetScrollY = maxScrollRange;

    LogUtils.d("scrollChildToVisibleArea scrollTo = " + targetScrollY + ", maxScrollRange = " + maxScrollRange);
    this.scrollTo(0, targetScrollY, animated);
};
