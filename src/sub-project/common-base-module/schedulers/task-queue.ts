/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-04-30
 *
 * @description
 */
import { ABCError } from "../common-error";
import { LogUtils } from "../log";
import { Completer } from "../async/completer";
import { Disposable } from "../cleanup/disposable";
import { errorToStr } from "../utils";

export interface Task {
    (): Promise<any> | void | undefined;
}

export class TaskQueue extends Disposable {
    private lock?: Completer<boolean>;

    private queue: Task[] = [];
    private _disposed = false;
    private currentProcessTask?: Task;

    constructor() {
        super();
        this._doWork();
    }

    public schedule(task: Task): void {
        LogUtils.d("TaskQueue.schedule");
        if (this._disposed) throw new ABCError(new Error("TaskQueue.schedule but scheduler has disposed"));
        this.queue.push(task);
        this.lock?.resolve(true);
    }

    private async _doWork() {
        LogUtils.d("TaskQueue._doWork");
        do {
            if (this.queue.length == 0) {
                this.lock = new Completer();
                await this.lock.promise;
            }

            if (this._disposed) break;

            const task = this.queue.shift();

            // LogUtils.d("TaskQueue._doWork process task:" + task);
            try {
                this.currentProcessTask = task;
                const taskResult = task!();
                if (taskResult != undefined) await taskResult;
            } catch (e) {
                LogUtils.e("TaskQueue, error = " + errorToStr(e));
            }

            this.currentProcessTask = undefined;
            // LogUtils.d("TaskQueue._doWork process finish task:" + task);
        } while (!this._disposed);
    }

    dispose(): void {
        this._disposed = true;
        this.lock?.resolve(true);
    }
}
