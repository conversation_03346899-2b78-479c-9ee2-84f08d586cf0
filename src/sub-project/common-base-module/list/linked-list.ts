/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020/8/15
 *
 * @description
 *
 */
class LinkedListNode<T> {
    // 链表节点
    element: T;
    next?: LinkedListNode<T>; //// 节点的指向下个节点的指针
    constructor(element: T) {
        this.element = element;
    }
}

export class LinkedList<T> implements Iterable<T> {
    head?: LinkedListNode<T>; //  初始化链表的头节点
    tail?: LinkedListNode<T>; //表头
    size = 0;

    constructor() {
        return;
    }

    /**
     * @description 插入元素
     * @param  newItem 需要插入的元素
     * @param  beforeItem 插入到某一元素之后
     */
    public insertInNext(newItem: T, beforeItem: T): void {
        const newNode = new LinkedListNode(newItem);
        const currNode = this.find(beforeItem);
        if (currNode) {
            //  判读是否是插入到指定节点后面，如果不是则插入到最后一个节点。
            newNode.next = currNode.next;
            currNode.next = newNode;
        } else {
            if (this.tail) this.tail.next = newNode;
            else {
                this.head = newNode;
            }
        }

        if (!newNode.next) {
            this.tail = newNode;
        }

        this.size++;
    }

    /**
     * @description 删除元素
     * @param  item 删除的元素
     */
    public remove(item: T): void {
        const preNode = this.findPreNode(item); //  找到前一节点，将前一节点的next指向该节点的next
        if (preNode) {
            preNode.next = preNode.next!.next;
            if (!preNode.next) this.tail = preNode;

            this.size--;
        } else if (this.head?.element == item) {
            this.head = undefined;
            this.tail = undefined;

            this.size--;
        }
    }

    pop(): T | undefined {
        if (!this.head) return;

        const data = this.head.element;
        this.head = this.head?.next;
        if (!this.head) {
            this.tail = undefined;
        }

        this.size--;

        return data;
    }

    public push(data: T): void {
        const newNode = new LinkedListNode(data);
        if (this.tail) {
            this.tail.next = newNode;
            this.tail = newNode;
            this.size++;
        } else {
            this.head = this.tail = newNode;
            this.size = 1;
        }
    }

    /**
     * @description 查找元素的节点
     * @param  item 查找的元素
     */
    find(item: T): LinkedListNode<T> | undefined {
        //  根据元素查找节点
        let currNode = this.head;
        do {
            if (!currNode) break;
            if (!currNode.next) {
                currNode = undefined;
                break;
            }

            //find target
            if (currNode.element == item) break;
        } while (true);

        return currNode;
    }

    /**
     * @description 查找元素的前一节点
     * @param  item 查找的元素
     */
    public findPreNode(item: T): LinkedListNode<T> | undefined {
        let currNode = this.head;
        do {
            if (!currNode) break;
            if (!currNode.next) {
                currNode = undefined;
                break;
            }

            if (currNode.next.element == item) {
                break;
            }

            currNode = currNode.next;
        } while (true);

        return currNode;
    }

    [Symbol.iterator](): Iterator<T> {
        let current = this.head;
        return {
            next(/*...ignore*/): IteratorResult<T, any> {
                if (!current)
                    return {
                        done: true,
                        value: undefined,
                    };

                const result = {
                    done: false,
                    value: current.element,
                };

                current = current.next;
                return result;
            },
        };
    }
}
