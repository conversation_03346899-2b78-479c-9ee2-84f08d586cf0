/**
 * Created by he<PERSON><PERSON> on 2020/3/12.
 */

import { LinkedList } from "../list/linked-list";

export enum LogLevel {
    DEBUG,
    INFO,
    WARNING,
    ERROR,
    NONE,
}

const kKeepRecentLogNum = 5000;

const timeFormat = "yyyy-MM-dd HH:mm:ss.S";

class LogUtils {
    logLevel = LogLevel.NONE;

    keepRecentLog = false; //保存最近日志

    private _network: any; //ABCApiNetwork

    private _logFuncMap = [console.debug, console.info, console.warn, console.error];

    private _recentLogs: LinkedList<string> = new LinkedList<string>();

    allLogs: LinkedList<string> = new LinkedList<string>();

    public setLogLevel(level: LogLevel, keepRecentLog = false): void {
        this.logLevel = level;
        this.keepRecentLog = keepRecentLog;
    }

    public d(...messages: any[]): void {
        this._log(LogLevel.INFO, messages);
    }

    public i(...messages: any[]): void {
        this._log(LogLevel.INFO, messages);
    }

    public w(...messages: any[]): void {
        this._log(LogLevel.WARNING, messages);
    }

    public e(...messages: any[]): void {
        this._log(LogLevel.ERROR, messages);
    }

    /**
     * 向服务器端输出实时日志
     * @param log
     * @param moduleName
     */
    public liveLog(log: string, moduleName?: string) {
        this.d(log);
        if (!this._network) {
            const { ABCApiNetwork } = require("../../net");

            this._network = ABCApiNetwork;
        }
        this._network
            .post("liveLog", {
                body: {
                    silent: true,
                    body: log,
                    module: moduleName,
                },
            })
            .catchIgnore();
    }

    /**
     * 向服务器端输出实时日志
     * @param log
     * @param moduleName
     */
    public longLog(log: any) {
        this.d(log);
        if (!this._network) {
            const { ABCApiNetwork } = require("../../net");

            this._network = ABCApiNetwork;
        }
        this._network
            .post("logs", {
                body: {
                    content: log,
                },
            })
            .catchIgnore();
    }

    public dumpLogs(): string {
        let buffer = "";
        for (const msg of this._recentLogs) {
            if (buffer.length > 0) buffer += "\n";

            buffer += msg;
        }
        return buffer;
    }

    private _log(level: LogLevel, ...messages: any[]): void {
        if (this.logLevel > level) return;

        const logFun: any = this._logFuncMap[level];
        if (logFun) {
            const msg = messages.map((item) => item.toString()).join("");
            const dateStr = new Date().format(timeFormat);
            logFun(`${dateStr}: ${msg}`);

            this.allLogs.push(dateStr + "  " + msg);

            if (this.allLogs.size > kKeepRecentLogNum) {
                this.allLogs.pop();
            }

            if (this.keepRecentLog) {
                this._recentLogs.push(dateStr + "  " + msg);
                if (this._recentLogs.size > kKeepRecentLogNum) {
                    this._recentLogs.pop();
                }
            }
        }
    }

    clearAllLogs(): void {
        this.allLogs = new LinkedList<string>();
    }
}

const logUtils = new LogUtils();

export default logUtils;
