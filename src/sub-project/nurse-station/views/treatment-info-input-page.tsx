/**
 * create by deng<PERSON>e
 * desc: 治疗反应/病因病机/治疗结果
 * create date 2020/12/23
 */
import React from "react";
import { BasePage, DividerLine, SizedBox } from "../../base-ui";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import { keyboardListener } from "../../common-base-module/utils/keyboard-listener";
import { ScrollView, Text, View } from "@hippy/react";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { AbcTextInput } from "../../base-ui/views/abc-text-input";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { showBottomPanel } from "../../base-ui/abc-app-library/panel";
import { AbcText } from "../../base-ui/views/abc-text";
import { AbcTag } from "../../base-ui/abc-app-library/common/abc-tag";

interface TreatmentInfoInputPageProps {
    title?: string;
    placeholder?: string;
    text?: string;
    labels?: Array<Array<string>>;
}

export class TreatmentInfoInputPage extends BasePage<TreatmentInfoInputPageProps> {
    private _textValue?: string;
    inputHeight = DeviceUtils.isOhos() ? 20 : 0;

    private _textInputRef?: AbcTextInput | null;

    constructor(props: TreatmentInfoInputPageProps) {
        super(props);
        this._textValue = this.props.text;
    }

    static async show<P extends TreatmentInfoInputPageProps>(options?: P): Promise<string> {
        return showBottomPanel(React.createElement(this, { ...options }), { topMaskHeight: Sizes.dp160 });
    }

    componentDidMount(): void {
        if (DeviceUtils.isIOS())
            keyboardListener
                .subscribe((/*visible*/) => {
                    this.setState({});
                })
                .addToDisposableBag(this);
    }

    getAppBar(): JSX.Element | undefined {
        return undefined;
    }

    getShowStatusBar(): boolean {
        return false;
    }

    getAppBarTitle(): string {
        return this.props.title ?? "";
    }

    onChangeText(text: string): void {
        this._textValue = text;
    }

    handleSelectChip(diseaseName: string): void {
        const array = this._textValue == "" ? [] : this._textValue?.trim()?.split("，") ?? [];
        array.push(diseaseName);
        this._textValue = array.join("，");
        this._textInputRef?.setValue(this._textValue);
    }

    saveInfo(): void {
        ABCNavigator.pop(this._textValue ?? "");
    }

    _createInput(): JSX.Element {
        const { title } = this.props;
        const isOhos = DeviceUtils.isOhos();
        return (
            <View style={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp22, Sizes.dp16, Sizes.dp20)]}>
                <View style={[ABCStyles.rowAlignCenter, { justifyContent: "space-between" }]}>
                    <Text style={[TextStyles.t16MT1, { lineHeight: Sizes.dp22 }]}>{title}</Text>
                    <AbcText style={[TextStyles.t14MM, { lineHeight: Sizes.dp22 }]} onClick={this.saveInfo.bind(this)}>
                        {"确定"}
                    </AbcText>
                </View>
                <SizedBox height={Sizes.dp17} />
                <AbcTextInput
                    style={{
                        flexGrow: 1,
                        ...TextStyles.t16NB,
                        backgroundColor: Colors.transparent,
                        underlineColorAndroid: Colors.white,
                        paddingHorizontal: DeviceUtils.isAndroid() ? -6 : undefined,
                        ...(isOhos ? { minHeight: this.inputHeight } : { height: this.inputHeight }),
                    }}
                    ref={(ref) => {
                        this._textInputRef = ref;
                    }}
                    enableDefaultToolBar={true}
                    defaultValue={this._textValue}
                    multiline={true}
                    placeholder={`请输入${this.props.title}`}
                    returnKeyType={"done"}
                    selectTextOnFocus={false}
                    placeholderTextColor={Colors.t4}
                    onContentSizeChange={(event) => {
                        this.inputHeight = Math.min(event.contentSize.height, Sizes.dp95);
                        this.forceUpdate();
                    }}
                    onChangeText={this.onChangeText.bind(this)}
                    // maxLength={this.maxLength}
                    // formatter={this.formatter}
                    // onFocus={this.onFocus?.bind(this)}
                    // onBlur={this.onBlur?.bind(this)}
                />
            </View>
        );
    }

    _createItem(list: Array<string>): JSX.Element {
        return (
            <View style={{ flexDirection: "row", flexWrap: "wrap" }}>
                {list.map((item, index) => (
                    <AbcTag
                        key={index}
                        text={item}
                        textStyle={[TextStyles.t14NT2.copyWith({ color: Colors.t2 }), { flexShrink: 1, lineHeight: Sizes.dp20 }]}
                        style={[
                            ABCStyles.rowAlignCenter,
                            Sizes.marginLTRB(0, Sizes.dp0, Sizes.dp8, Sizes.dp8),
                            { backgroundColor: Colors.bg1, flexShrink: 1, borderColor: undefined },
                        ]}
                        textNumberOfLines={1}
                        value={item}
                        onClick={(value) => {
                            this.handleSelectChip(value);
                            this.forceUpdate();
                        }}
                    />
                ))}
            </View>
        );
    }

    _createLabelGroup(list: Array<string>, showBottomLine?: boolean): JSX.Element {
        return (
            <View
                style={[
                    Sizes.paddingLTRB(Sizes.dp16, Sizes.dp12),
                    { backgroundColor: Colors.white },
                    showBottomLine ? ABCStyles.bottomLine : {},
                ]}
            >
                <View>{this._createItem(list)}</View>
            </View>
        );
    }

    renderContent(): JSX.Element {
        const { labels } = this.props;
        return (
            <View style={{ flex: 1, backgroundColor: Colors.white }}>
                {this._createInput()}
                <DividerLine
                    lineHeight={Sizes.dpHalf}
                    color={Colors.dividerLineColor}
                    style={{ marginHorizontal: Sizes.listHorizontalMargin }}
                />
                <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
                    {labels?.map((item, index) => {
                        return this._createLabelGroup(item, index != labels.length - 1);
                    })}
                </ScrollView>
            </View>
        );
    }
}
