/**
 * create by deng<PERSON><PERSON>
 * desc: 治疗部位
 * create date 2020/12/23
 */
import React from "react";
import { <PERSON>View, ScrollView, Text, View } from "@hippy/react";
import { BaseComponent } from "../../base-ui/base-component";
import { AbcTextInput } from "../../base-ui/views/abc-text-input";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { DividerLine, SizedBox, Tab, Tabs } from "../../base-ui";
import { TreatmentSiteOption } from "../data/nurse-station-data";
import { AbcIntelligenceChip } from "../../outpatient/outpatient-views";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import { keyboardListener } from "../../common-base-module/utils/keyboard-listener";
import { BaseMedicalRecordPage, BaseMedicalRecordPageProps } from "../../outpatient/medical-record-page/base-medical-record-page";
import { AbcText } from "../../base-ui/views/abc-text";
import _ from "lodash";

interface TreatmentSiteDialogProps extends BaseMedicalRecordPageProps {
    text?: string;
    labels?: TreatmentSiteOption[];
}

export default class TreatmentSiteDialog extends BaseMedicalRecordPage<TreatmentSiteDialogProps, any> {
    treatmentSiteArray: Array<any> = [];
    constructor(props: TreatmentSiteDialogProps) {
        super(props);
        this.inputValue = props.text ?? "";
        this.pageDisplayName = "治疗部位";
    }

    getShowStatusBar(): boolean {
        return false;
    }

    getAppBar(): JSX.Element | undefined {
        return undefined;
    }

    componentDidMount(): void {
        if (DeviceUtils.isIOS())
            keyboardListener
                .subscribe((/*visible*/) => {
                    this.setState({});
                })
                .addToDisposableBag(this);
    }

    private _handleChangeInputText(text: string, type?: string): void {
        switch (type) {
            case _BodyPositionType.body: {
                const _lastItem = this.treatmentSiteArray.pop();
                if (position.some((p) => p == _lastItem)) {
                    const str = `${_lastItem}${text}`;
                    if (!this.treatmentSiteArray.some((it) => it == str)) {
                        this.treatmentSiteArray.push(`${_lastItem}${text}`);
                    }
                } else {
                    this.treatmentSiteArray.push(_lastItem);
                    if (!this.treatmentSiteArray.some((it) => it == text)) {
                        this.treatmentSiteArray.push(text);
                    }
                }
                break;
            }
            case _BodyPositionType.position: {
                const _lastItem = _.last(this.treatmentSiteArray);
                if (position.some((p) => p == _lastItem)) {
                    return;
                }
                this.treatmentSiteArray.push(text);
                break;
            }
            case _AcupuncturePointType.acupuncture: {
                let _shouldAdd = true;
                this.treatmentSiteArray.forEach((item) => {
                    if (text == item) {
                        _shouldAdd = false; // false 不可重复添加
                    }
                });
                if (_shouldAdd) this.treatmentSiteArray.push(text);
                break;
            }
        }

        this.inputValue = this.treatmentSiteArray.length ? this.treatmentSiteArray.join("，") : "";
        this._textInputRef?.setValue(this.inputValue);
        this.forceUpdate();
    }

    protected renderMedicalRecordInput(): JSX.Element {
        return (
            <View style={Sizes.paddingLTRB(Sizes.dp16, Sizes.dp22, Sizes.dp16, Sizes.dp20)}>
                <View style={[ABCStyles.rowAlignCenter, { justifyContent: "space-between" }]}>
                    <Text style={[TextStyles.t16MT1, { lineHeight: Sizes.dp22 }]}>{this.pageDisplayName}</Text>
                    <AbcText style={[TextStyles.t14MM, { lineHeight: Sizes.dp22 }]} onClick={this.saveInputValue.bind(this)}>
                        {"确定"}
                    </AbcText>
                </View>
                <SizedBox height={Sizes.dp17} />
                <AbcTextInput
                    style={{
                        flexGrow: 1,
                        ...TextStyles.t16NB,
                        backgroundColor: Colors.transparent,
                        underlineColorAndroid: Colors.white,
                        ...(DeviceUtils.isOhos() ? { minHeight: this.inputHeight } : { height: this.inputHeight }),
                        paddingHorizontal: DeviceUtils.isAndroid() ? -6 : undefined,
                    }}
                    ref={(ref) => {
                        this._textInputRef = ref;
                    }}
                    enableDefaultToolBar={true}
                    defaultValue={this.inputValue}
                    multiline={true}
                    placeholder={`请输入治疗部位`}
                    returnKeyType={"done"}
                    selectTextOnFocus={false}
                    placeholderTextColor={Colors.t4}
                    onContentSizeChange={(event) => {
                        this.inputHeight = Math.min(event.contentSize.height, Sizes.dp95);
                        this.forceUpdate();
                    }}
                    onChangeText={this.handleChangeInputValue.bind(this)}
                    maxLength={this.maxLength}
                    formatter={this.formatter}
                    onFocus={this.onFocus?.bind(this)}
                    onBlur={this.onBlur?.bind(this)}
                />
            </View>
        );
    }

    private _createAcupunctureView(item: TreatmentSiteOption): JSX.Element {
        return <_AcupuncturePage labels={item} inputText={this.inputValue} onClick={this._handleChangeInputText.bind(this)} />;
    }

    private _createBodyPosition(item: TreatmentSiteOption): JSX.Element {
        return <_BodyPositionPage labels={item} onClick={this._handleChangeInputText.bind(this)} />;
    }

    private _createTabPage(): JSX.Element {
        const { labels } = this.props;
        if (!labels?.length) return <View style={{ flex: 1 }} />;
        return (
            <View style={{ flex: 1 }}>
                <Tabs
                    tabsStyle={{
                        flex: undefined,
                        height: Sizes.dp44,
                        justifyContent: "space-between",
                        paddingHorizontal: Sizes.listHorizontalMargin,
                    }}
                    tabStyle={{ marginRight: Sizes.dp24, ...TextStyles.t16NT2.copyWith({ color: Colors.t2 }) }}
                    currentStyle={{ ...TextStyles.t16MT1 }}
                    initialPage={0}
                    scrollBeginDragInputBlur={true}
                >
                    {labels?.map((item) => {
                        if (item.name == "身体部位") {
                            return (
                                <Tab key={item.name} title={"身体部位"}>
                                    {this._createBodyPosition(item)}
                                </Tab>
                            );
                        } else {
                            return (
                                <Tab key={item.name} title={item.name}>
                                    {this._createAcupunctureView(item)}
                                </Tab>
                            );
                        }
                    })}
                </Tabs>
            </View>
        );
    }

    renderContent(): JSX.Element {
        return (
            <View style={{ flex: 1, backgroundColor: Colors.white }}>
                {this.renderMedicalRecordInput()}
                <DividerLine
                    lineHeight={Sizes.dpHalf}
                    color={Colors.dividerLineColor}
                    style={{ marginHorizontal: Sizes.listHorizontalMargin }}
                />
                {this._createTabPage()}
            </View>
        );
    }
}

class _BodyPositionType {
    static body = "body";
    static position = "position";
}

class _AcupuncturePointType {
    static acupuncture = "acupuncture";
}

interface _BodyPositionPageProps {
    labels?: TreatmentSiteOption;

    inputText?: string;

    onClick?(text: string, type?: string): void;
}

const position = ["上", "下", "左", "右"];

class _BodyPositionPage extends BaseComponent<_BodyPositionPageProps> {
    private _createPositionItem(): JSX.Element {
        return (
            <View
                style={[
                    ABCStyles.bottomLine,
                    {
                        backgroundColor: Colors.white,
                        marginHorizontal: Sizes.listHorizontalMargin,
                        paddingTop: Sizes.dp4,
                        paddingBottom: Sizes.dp12,
                    },
                ]}
            >
                <View style={{ flexDirection: "row", flexWrap: "wrap" }}>
                    {position.map((item, index) => (
                        <AbcIntelligenceChip
                            key={index}
                            text={item}
                            type={_BodyPositionType.position}
                            onClick={(event) => this.props.onClick?.(event.info, event.type)}
                        />
                    ))}
                </View>
            </View>
        );
    }

    private _createItem(list: Array<TreatmentSiteOption>, type: string): JSX.Element {
        return (
            <View style={{ flexDirection: "row", flexWrap: "wrap" }}>
                {list.map((item, index) => (
                    <AbcIntelligenceChip
                        key={index}
                        text={item.name}
                        type={type}
                        onClick={(event) => this.props.onClick?.(event.info, event.type)}
                    />
                ))}
            </View>
        );
    }

    private _createBodyList(labels: TreatmentSiteOption, index: number, type: string, showLine?: boolean): JSX.Element {
        return (
            <View
                key={index}
                style={[
                    showLine ? ABCStyles.bottomLine : {},
                    {
                        backgroundColor: Colors.white,
                        marginHorizontal: Sizes.listHorizontalMargin,
                        paddingTop: Sizes.dp4,
                        paddingBottom: Sizes.dp12,
                    },
                ]}
            >
                <View>{this._createItem(labels.children, type)}</View>
            </View>
        );
    }

    render() {
        return (
            <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
                {this._createPositionItem()}
                {this.props.labels?.children.map((item, index) => {
                    return this._createBodyList(
                        item,
                        index,
                        _BodyPositionType.body,
                        index != (this.props.labels?.children.length ?? 0) - 1
                    );
                })}
            </ScrollView>
        );
    }
}

// 十四经穴 筛选换行默认字符串
const TwelveMeridiansWrapContent = [
    "手阳明",
    "手少阴",
    "手厥阴",
    "足少阴",
    "手太阳",
    "手太阴",
    "足阳明",
    "足少阳",
    "手少阳",
    "足太阴",
    "足太阳",
    "足厥阴",
];

interface _AcupuncturePageStates {
    currentList?: TreatmentSiteOption;
}

class _AcupuncturePage extends BaseComponent<_BodyPositionPageProps, _AcupuncturePageStates> {
    constructor(props: _BodyPositionPageProps) {
        super(props);
        this.state = {
            currentList: props.labels?.children[0],
        };
    }

    private _createItem(list: Array<TreatmentSiteOption>): JSX.Element {
        const { inputText } = this.props;
        return (
            <View style={{ flexDirection: "row", flexWrap: "wrap" }}>
                {list.map((item, index) => (
                    <AbcIntelligenceChip
                        key={index}
                        text={item.name}
                        type={_AcupuncturePointType.acupuncture}
                        check={inputText?.includes(item.name)}
                        onClick={(event) => this.props.onClick?.(event.info, event.type)}
                    />
                ))}
            </View>
        );
    }

    private _createContentView(): JSX.Element {
        return (
            <View style={{ flex: 3, padding: Sizes.dp16 }}>
                <ScrollView showsVerticalScrollIndicator={false}>{this._createItem(this.state.currentList?.children ?? [])}</ScrollView>
            </View>
        );
    }

    private _createCatalogueList(): JSX.Element {
        const { labels } = this.props;
        return (
            <View style={{ flex: 1, backgroundColor: Colors.D2 }}>
                <ListView
                    style={{ flex: 1 }}
                    numberOfRows={labels?.children.length ?? 0}
                    dataSource={labels?.children ?? []}
                    scrollEventThrottle={300}
                    showScrollIndicator={false}
                    renderRow={(data) => {
                        const current = data.name == this.state.currentList?.name;
                        return (
                            <View
                                style={[
                                    current ? { backgroundColor: Colors.white } : { backgroundColor: Colors.bg1 },
                                    {
                                        flexDirection: "row",
                                        height: Sizes.listItemHeight,
                                        justifyContent: "center",
                                        paddingHorizontal: Sizes.listHorizontalMargin,
                                    },
                                ]}
                                onClick={() => {
                                    this.setState({ currentList: data });
                                }}
                            >
                                <Text
                                    style={[
                                        TextStyles.t14NT2.copyWith(current ? { color: Colors.T1 } : { color: Colors.t2 }),
                                        { flexShrink: 1, textAlign: "center", alignSelf: "center" },
                                    ]}
                                >
                                    {this._renderLinebreak(data.name)}
                                </Text>
                            </View>
                        );
                    }}
                />
            </View>
        );
    }

    // 十二经穴 换行方法
    private _renderLinebreak(text: string): string {
        const showText = text;
        for (const item of TwelveMeridiansWrapContent) {
            if (showText.includes(item)) {
                return showText.replace(item, item + "\n");
            }
        }
        return showText;
    }

    render(): JSX.Element {
        return (
            <View style={[{ flex: 1, flexDirection: "row" }]}>
                {this._createCatalogueList()}
                {this._createContentView()}
            </View>
        );
    }
}
