/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2022/3/7
 */
import React from "react";
import { BasePage, IconFontView, Spacer, ToolBarButtonStyle1 } from "../../base-ui";
import { AbcTextInput } from "../../base-ui/views/abc-text-input";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import { keyboardListener } from "../../common-base-module/utils/keyboard-listener";
import { View } from "@hippy/react";
import sizes from "../../theme/sizes";
import { Colors, Sizes, TextStyles } from "../../theme";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { AbcView } from "../../base-ui/views/abc-view";
import { KeyboardHeightIOSView } from "../../base-ui/views/keyboard-listener-view";

interface HomeCareAddressInputPageProps {
    content?: string;
}

export class HomeCareAddressInputPage extends BasePage<HomeCareAddressInputPageProps> {
    inputHeight = 0;
    private _textInputRef?: AbcTextInput | null;
    private _content?: string;

    constructor(props: HomeCareAddressInputPageProps) {
        super(props);
        this._content = props.content;
    }

    getAppBarTitle(): string {
        return "上门地点";
    }

    componentDidMount(): void {
        if (DeviceUtils.isIOS())
            keyboardListener
                .subscribe((/*visible*/) => {
                    this.setState({});
                })
                .addToDisposableBag(this);
    }

    handleChangeContent(text: string): void {
        this._content = text;
    }

    renderContent(): JSX.Element {
        return (
            <View style={{ flex: 1, backgroundColor: Colors.white }}>
                {this._createInput()}
                <Spacer />
                <View
                    style={[
                        Sizes.paddingLTRB(sizes.listHorizontalMargin, sizes.dp12, 0, sizes.dp12),
                        {
                            flexDirection: "row",
                            backgroundColor: Colors.white,
                            borderTopWidth: 1,
                            borderColor: Colors.dividerLineColor,
                        },
                    ]}
                >
                    <ToolBarButtonStyle1 style={TextStyles.t16NW} onClick={() => ABCNavigator.pop(this._content)} text="确定" />
                    {this._textInputRef?.focused && DeviceUtils.isIOS() ? (
                        <AbcView
                            style={{ width: Sizes.dp56, justifyContent: "center", alignItems: "center" }}
                            onClick={() => {
                                this._textInputRef?.blur();
                            }}
                        >
                            <IconFontView name={"Dropdown_Triangle"} size={Sizes.dp28} color={Colors.P1} />
                        </AbcView>
                    ) : (
                        <View style={{ width: Sizes.listHorizontalMargin }} />
                    )}
                </View>
                <KeyboardHeightIOSView />
            </View>
        );
    }

    private _createInput(): JSX.Element {
        return (
            <View style={[Sizes.paddingLTRB(Sizes.dp16), { backgroundColor: Colors.cardYellowBackgroundColor }]}>
                <AbcTextInput
                    style={{
                        ...TextStyles.t16NB,
                        flexGrow: 1,
                        backgroundColor: Colors.transparent,
                        underlineColorAndroid: Colors.white,
                        height: Math.min(this.inputHeight, Sizes.dp148),
                    }}
                    ref={(ref) => {
                        this._textInputRef = ref;
                    }}
                    autoFocus={true}
                    enableDefaultToolBar={false}
                    defaultValue={this._content}
                    multiline={true}
                    placeholder="输入上门地点"
                    placeholderTextColor={Colors.T3}
                    onContentSizeChange={(event) => {
                        this.inputHeight = event.contentSize.height;
                        this.forceUpdate();
                    }}
                    onChangeText={this.handleChangeContent.bind(this)}
                />
            </View>
        );
    }
}
