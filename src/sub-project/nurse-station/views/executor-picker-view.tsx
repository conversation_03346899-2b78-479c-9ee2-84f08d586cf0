/**
 * create by dengjie
 * desc:
 * create date 2020/12/14
 */
import { BaseComponent } from "../../base-ui/base-component";
import { showOptionsBottomSheet } from "../../base-ui/dialog/bottom_sheet";
import { ClinicAgent, EmployeeSimpleInfo } from "../../base-business/data/clinic-agent";
import { ABCError } from "../../common-base-module/common-error";
import { userCenter } from "../../user-center";

interface ExecutorPickerViewProps {}

export class ExecutorPickerView extends BaseComponent<ExecutorPickerViewProps> {
    static async show(init?: EmployeeSimpleInfo): Promise<EmployeeSimpleInfo | undefined> {
        const employeeList = await ClinicAgent.getClinicEmployeesSimpleInfo().catch((error) => new ABCError(error));
        if (employeeList instanceof ABCError) return;
        employeeList.sort((last) => (last.id == userCenter.employee?.id ? -1 : 1));

        const initIndex = employeeList.findIndex((item) => item.id == init?.id);
        let initialSelectIndexes;
        if (initIndex > -1) {
            initialSelectIndexes = new Set([initIndex]);
        }
        const employee = await showOptionsBottomSheet({
            title: "选择开单人",
            initialSelectIndexes: initialSelectIndexes,
            options: employeeList.map((item) => item.name),
        });
        if (employee) return employeeList[employee[0]];
    }
}
