/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description 执行站列表视图
 * <AUTHOR>
 * @CreateDate 2024/3/13
 * @Copyright 成都字节流科技有限公司© 2024
 */
import { ChargeExecuteStatus, ExecuteSourceType, ExecuteTabType } from "./data/bean";
import { ExecuteListViewBloc } from "./execute-list-view-bloc";
import { ABCNetworkPageContentStatus, BaseBlocNetworkView } from "../base-ui/base-page";
import _ from "lodash";
import { AbcEmptyItemView } from "../base-ui/views/empty-view";
import { ABCUtils } from "../base-ui/utils/utils";
import { View } from "@hippy/react";
import { Colors } from "../theme";
import { AbcListView } from "../base-ui/list/abc-list-view";
import { UniqueKey } from "../base-ui";
import React from "react";
import { DataHolder } from "../views/list-data-holder";
import { ChargeInvoiceData } from "../charge/data/charge-beans";
import { NewPatientListItemView, PatientListItemStatusTextStyle } from "../outpatient/views/patient-list-item-view";
import { TimeUtils } from "../common-base-module/utils";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { ExecuteInvoiceCreatePage } from "./execute-create/execute-invoice-create-page";
import ExecuteInvoicePage from "./execute-invoice-page";
import { PatientOrderDataAgent } from "./data/nurse-station-data";
import { Toast } from "../base-ui/dialog/toast";

interface PatientOrderListViewProps {
    sourceType?: ExecuteSourceType; // 单店或连锁
    tabType?: ExecuteTabType; // Tab类型
    bloc?: ExecuteListViewBloc;
    isFromSearchPage?: boolean; // 是否来源于搜索页面（如果是，则初始不需要调用接口）
}

export class PatientOrderListView extends BaseBlocNetworkView<PatientOrderListViewProps, ExecuteListViewBloc> {
    sourceType: ExecuteSourceType;

    constructor(props: PatientOrderListViewProps) {
        super(props);
        this.bloc =
            props.bloc ??
            new ExecuteListViewBloc({
                tabType: this.props.tabType,
                sourceType: props.sourceType,
                isFromSearchPage: props.isFromSearchPage,
            });
        this.sourceType = props.sourceType ?? ExecuteSourceType.clinic;
    }

    componentDidMount(): void {
        this.bloc.state
            .subscribe((state) => {
                const dataHolders = state._dataHolders.get(this.sourceType) ?? [];
                let status = ABCNetworkPageContentStatus.show_data;

                if (state.loading) {
                    if (!_.isEmpty(state.keyword) && !dataHolders.length) {
                        //搜索加载更多
                        status = ABCNetworkPageContentStatus.loading;
                    } else if (!state.enableDraft) {
                    } else if (!dataHolders.length) {
                        status = ABCNetworkPageContentStatus.loading;
                    } else if (!!state._searchResult?.fromDraft) {
                        status = ABCNetworkPageContentStatus.loading;
                    }
                } else if (state.loadError) {
                    status = ABCNetworkPageContentStatus.error;
                } else if (_.isEmpty(dataHolders) && !state.enableTherapyAppointment) {
                    status = ABCNetworkPageContentStatus.empty;
                }

                this.setContentStatus(status, state.loadError);
            })
            .addToDisposableBag(this);
    }

    emptyContent(): JSX.Element {
        let emptyStr = "";
        switch (this.props.tabType) {
            case ExecuteTabType.allBill:
                emptyStr = "开单";
                break;
            case ExecuteTabType.myBill:
                emptyStr = "开单";
                break;
            case ExecuteTabType.myExecute:
                emptyStr = "执行";
                break;
        }
        return <AbcEmptyItemView tips={`暂无${emptyStr}记录`} />;
    }

    reloadData(): void {
        this.bloc.requestReload();
    }

    renderContent(): JSX.Element {
        const state = this.bloc.currentState;
        const dataHolders = state.dataHolders ?? [];
        if (ABCUtils.isEmpty(dataHolders)) return this.emptyContent();
        // if (ABCUtils.isEmpty(dataHolders)) return <View />;
        const itemCount = dataHolders?.length ?? 0;

        return (
            <View style={{ flex: 1, backgroundColor: Colors.white }}>
                <AbcListView
                    style={{ flex: 1, backgroundColor: Colors.white }}
                    loading={state.loading}
                    scrollEventThrottle={300}
                    renderRow={(item) => {
                        return item.createView() ?? <View />;
                    }}
                    numberOfRows={itemCount}
                    dataSource={dataHolders ?? []}
                    getRowKey={(index) => `${dataHolders[index]?.data?.id ?? UniqueKey()}`}
                    onRefresh={() => {
                        this.bloc.requestReload();
                    }}
                    onEndReached={() => {
                        this.bloc.requestLoadMore();
                    }}
                />
            </View>
        );
    }

    protected takeBlocOwnership(): boolean {
        return !this.props.bloc;
    }
}

export class _ExecutionOrderItemHolder extends DataHolder<ChargeInvoiceData> {
    private readonly isOwn?: boolean;
    constructor(data: ChargeInvoiceData, isOwn?: boolean) {
        super(data);
        this.isOwn = isOwn;
    }

    createView(): JSX.Element {
        let statusTextStyle = PatientListItemStatusTextStyle.visited;
        //FIXME: 这里本来不应该直接判断data.statusName == '待执行'条件
        //目前为了处理执行后，返回的结果ChargeExecuteResult里只能拿到chargeSheetStatusName这个状态名
        if (this.data?.executeStatus == ChargeExecuteStatus.EXECUTION_ORDER_STATUS_UNEXECUTED && this.data.statusName == "待执行") {
            statusTextStyle = PatientListItemStatusTextStyle.waitVisit;
        } else if (this.data?.isLocalDraft) {
            statusTextStyle = PatientListItemStatusTextStyle.draft;
        }

        return (
            <NewPatientListItemView
                patient={this.data?.patient}
                time={this.isOwn ? this.data?.lastExecuteTime : this.data?.created}
                status={this.data?.statusName}
                statusTextStyle={statusTextStyle}
                anonymousName={"匿名患者"}
                timeOverride={TimeUtils.formatDateTimeAsThreeDays(
                    new Date((this.isOwn ? this.data?.lastExecuteTime : this.data?.created) ?? new Date()),
                    {
                        year: false,
                        time: true,
                    }
                )}
                onClick={async () => {
                    if (this.data?.isLocalDraft) {
                        ABCNavigator.navigateToPage(<ExecuteInvoiceCreatePage draftId={this.data?.localDraftId} />).then();
                    } else {
                        const chargeSheetId = this.data?.id;
                        if (!chargeSheetId) return;
                        const executePrivilegeConfig = await PatientOrderDataAgent.getExecutedOrderPermissionVerification(
                            chargeSheetId
                        ).catchIgnore();
                        if (!executePrivilegeConfig?.enableShowHistorySheet) {
                            await Toast.show("当前账号无权限查看本单");
                        } else {
                            // 是否可访问已执行单据详情
                            ABCNavigator.navigateToPage(<ExecuteInvoicePage orderId={chargeSheetId} />).then();
                        }
                    }
                }}
                bottomExpansionStr={this.data?.nurseAbstractInfo}
            />
        );
    }
}
