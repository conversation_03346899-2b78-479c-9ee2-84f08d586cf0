/**
 * create by deng<PERSON>e
 * desc: 批量执行页面bloc
 * create date 2020/9/1
 */

import { Bloc, BlocEvent } from "../bloc";
import React from "react";
import { actionEvent, EventName } from "../bloc/bloc";
import { EmployeeSimpleInfo } from "../base-business/data/clinic-agent";
import { AnyType } from "../common-base-module/common-types";
import { userCenter } from "../user-center";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { SearchExecutorDialog } from "./search-executor-page";
import { ChargeFormItem } from "../charge/data/charge-beans";
import { ExecuteItemUsage, PatientOrderExecuteProductDialog } from "./patient-order-execute-product-dialog";
import {
    ExecuteEffectTemplate,
    ExecuteHabits,
    ExecuteRecordItem,
    NeedHomeCareStatus,
    PatientOrderDataAgent,
    TreatmentSiteOption,
    TreatmentStaticOptions,
} from "./data/nurse-station-data";
import { Toast } from "../base-ui/dialog/toast";
import _, { isNil } from "lodash";
import { PatientOrderExecutePageMode } from "./patient-order-execute-page";
import { LoadingDialog } from "../base-ui/dialog/loading-dialog";
import { ABCError } from "../common-base-module/common-error";
import { errorToStr } from "../common-base-module/utils";
import { DialogIndex, showQueryDialog } from "../base-ui/dialog/dialog-builder";
import { ExecuteAttachmentItem, ExecuteEffectGroup, ExecuteEffectGroupAddType } from "./data/bean";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { UniqueKey } from "../base-ui";
import { showOptionsBottomSheet } from "../base-ui/dialog/bottom_sheet";
import { TreatmentInfoInputPage } from "./views/treatment-info-input-page";
import { OssUpdateModules, Patient } from "../base-business/data/beans";
import { BaseLoadingState } from "../bloc/bloc-helper";
import { clinicSharedPreferences } from "../base-business/preferences/scoped-shared-preferences";
import { ApiMixService } from "../data/ApiMixService";
import FileUtils from "../common-base-module/file/file-utils";
import { File } from "../common-base-module/file/file";
import { HomeCareAddressInputPage } from "./views/home-care-address-input-page";
import { FullTimePicker } from "../base-ui/picker/time-picker";
import { TextStyles } from "../theme";
import { pxToDp } from "../base-ui/utils/ui-utils";
import { AbcImagePicker } from "../base-business/image-picker/abc-image-picker";
import TreatmentSiteDialog from "./views/treatment-site-dialog";
import { CopyExecuteEffectGroupDialog } from "./copy-execute-effect-group-dialog";
import { MedicineTemplateAddType } from "../outpatient/medicine-add-page/views/medicine-template-add-type-dialog";
import { Subject } from "rxjs";
import { switchMap } from "rxjs/operators";
import { fromPromise } from "../common-base-module/rxjs-ext/rxjs-ext";

const EXECUTE_EFFECT_SWITCH_STATUS = "executeEffectSwitchStatus";

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventChangeExecutor extends _Event {}

class _EventChangeExecuteProduct extends _Event {}

class _EventChangeEffectStatus extends _Event {}

class _EventModifyEffectContent extends _Event {}

class _EventSubmitEffectInfo extends _Event {}

class _EventUndoEffectInfo extends _Event {}

class _EventEffectVisibleForPatient extends _Event {}

class _EventSaveEffect extends _Event {}

class _EventAddExecuteEffectGroup extends _Event {}

class _ExecuteEffectGroup extends _Event {
    group: ExecuteEffectGroup;

    constructor(group: ExecuteEffectGroup) {
        super();
        this.group = group;
    }
}

class _EventDeleteExecuteEffectGroup extends _ExecuteEffectGroup {}

class _EventCopyExecuteEffectGroup extends _ExecuteEffectGroup {}

class _EventModifyTreatmentMethod extends _ExecuteEffectGroup {}

class _EventModifyTreatmentResponse extends _ExecuteEffectGroup {}

class _EventModifyTreatmentResult extends _ExecuteEffectGroup {}

class _EventModifyEtiologyPathogenesis extends _ExecuteEffectGroup {}

class _EventModifyTreatmentSite extends _ExecuteEffectGroup {}

class _EventChangeNeedHomeCareEffectStatus extends _Event {}

class _EventModifyHomeCareAddress extends _Event {}

class _EventModifyHomeCareStartTime extends _Event {}

class _EventModifyHomeCareStartTimeYYYY extends _Event {}
class _EventModifyHomeCareStartTimeHH extends _Event {}

class _EventModifyHomeCareEndTimeYYYY extends _Event {}
class _EventModifyHomeCareEndTimeHH extends _Event {}

class _EventModifyHomeCareEndTime extends _Event {}

class _EventUploadImage extends _Event {
    group: ExecuteEffectGroup;
    constructor(group: ExecuteEffectGroup) {
        super();
        this.group = group;
    }
}

class _EventDeleteImage extends _Event {
    group: ExecuteEffectGroup;
    index: number;
    constructor(group: ExecuteEffectGroup, index: number) {
        super();
        this.group = group;
        this.index = index;
    }
}

export class State extends BaseLoadingState {
    loading = true;
    mode?: PatientOrderExecutePageMode;
    executors: EmployeeSimpleInfo[] = [];
    executeProductMap: Map<ChargeFormItem, ExecuteItemUsage> = new Map<ChargeFormItem, ExecuteItemUsage>();
    effectStatus = false;
    needHomeCare = false;
    homeCareAddress = "";
    homeCareStartTime?: Date;
    homeCareEndTime?: Date;
    homeCareStartTimeYYYY?: Date;
    homeCareStartTimeHH?: Date;
    homeCareEndTimeYYYY?: Date;
    homeCareEndTimeHH?: Date;

    effectVisibleForPatientStatus = false;

    executeDetailInfo?: ExecuteRecordItem;
    executeDetailInfos: ExecuteRecordItem[] = [];

    invoicePartCharged?: boolean;

    //执行模板相关

    executeTemplateList: ExecuteEffectTemplate[] = [];
    executeEffectGroups: ExecuteEffectGroup[] = [];
    treatmentStaticOptions?: TreatmentStaticOptions;

    patient?: Patient;

    effectComment?: string;

    showPatientVisibleSwitch = false;
    detailDataId?: string;

    get shouldChange(): boolean {
        return this.mode == PatientOrderExecutePageMode.normal;
    }

    hasChange = false;

    clone(): State {
        return Object.assign(new State(), this);
    }
}

export class ScrollToGroup extends State {
    static fromState(state: State): ScrollToGroup {
        const newState = new ScrollToGroup();
        Object.assign(newState, state);
        return newState;
    }
}

export class PatientOrderExecutePageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<PatientOrderExecutePageBloc | undefined>(undefined);
    private _executors?: EmployeeSimpleInfo[];
    private _executeHabits?: ExecuteHabits;
    private isHospitalizationSheet?: boolean; //判断当前是否是长护单子
    _executeRecordTrigger = new Subject(); // 执行记录触发器

    get hasChange(): boolean {
        return this.innerState.hasChange;
    }

    set hasChange(status: boolean) {
        this.innerState.hasChange = status;
    }

    static fromContext(context: AnyType): PatientOrderExecutePageBloc {
        return context;
    }

    constructor(params: {
        executeProductMap?: Map<ChargeFormItem, ExecuteItemUsage>;
        mode: PatientOrderExecutePageMode;
        executeDetailInfo?: ExecuteRecordItem;
        invoicePartCharged?: boolean;
        patient?: Patient;
        isHospitalizationSheet?: boolean;
        detailDataId?: string;
    }) {
        super();
        this.innerState.executeProductMap = params.executeProductMap ?? new Map<ChargeFormItem, ExecuteItemUsage>();
        this.innerState.mode = params.mode ?? PatientOrderExecutePageMode.normal;
        this.innerState.executeDetailInfo = params.executeDetailInfo;
        this.innerState.effectComment = params.executeDetailInfo?.executeEffect;
        this.innerState.invoicePartCharged = params.invoicePartCharged;
        this._executors = params.executeDetailInfo?.executors;
        this.innerState.patient = params.patient;

        this.innerState.needHomeCare = !!params.executeDetailInfo?.needHomeCare;
        this.innerState.homeCareAddress = params.executeDetailInfo?.homeCareAddress ?? "";
        this.innerState.homeCareStartTime = params.executeDetailInfo?.homeCareStartTime;
        this.innerState.homeCareEndTime = params.executeDetailInfo?.homeCareEndTime;

        this.isHospitalizationSheet = params.isHospitalizationSheet ?? false;
        this.innerState.detailDataId = params.detailDataId;

        this.dispatch(new _EventInit());
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        return new Map<EventName, Function>();
    }

    update(state?: State): void {
        this.dispatch(new _EventUpdate(state));
    }

    @actionEvent(_EventInit)
    private async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        this.innerState.loading = true;
        this.innerState.loadError = undefined;
        //初始化执行人
        if (this._executors) {
            this.innerState.executors = this._executors;
        } else {
            this.innerState.executors = [];
            const ownInfo = userCenter.employee;
            this.innerState.executors.push(ownInfo as EmployeeSimpleInfo);
        }

        //获取微诊所状态
        ApiMixService.getMCConfig()
            .toObservable()
            .subscribe((rsp) => {
                this.innerState.showPatientVisibleSwitch = rsp.isWeClinicOpen ?? false;
                this.update();
            })
            .addToDisposableBag(this);

        //初始化执行效果组
        this.innerState.executeEffectGroups =
            this.innerState.executeDetailInfo?.effects?.map((item) => {
                return JsonMapper.deserialize(ExecuteEffectGroup, {
                    ...item,
                    __type: ExecuteEffectGroupAddType.fromOnline,
                });
            }) ?? [];

        let habitsInit = false;
        let templateList = false;
        //新增执行效果时获取勾选信息
        if (this.innerState.mode == PatientOrderExecutePageMode.normal) {
            this.innerState.effectStatus = clinicSharedPreferences.getObject(EXECUTE_EFFECT_SWITCH_STATUS)?.status ?? false;
            PatientOrderDataAgent.getExecuteEffectEmployeeHabits()
                .toObservable()
                .subscribe(
                    (habits) => {
                        habitsInit = true;
                        this._executeHabits = habits;
                        this.innerState.effectStatus = !!this._executeHabits.needExecuteEffect;
                        this.innerState.effectVisibleForPatientStatus = !!this._executeHabits.effectVisibleForPatient;
                        if (habitsInit && templateList) {
                            this.innerState.loading = false;
                            this.update();
                        }
                    },
                    () => {
                        this.innerState.loading = false;
                        if (!habitsInit && !templateList) {
                            this.innerState.loadError = "加载异常，点击刷新";
                        }
                        this.update();
                    }
                )
                .addToDisposableBag(this);
        } else {
            habitsInit = true;
            this.innerState.effectStatus = !!this.innerState.executeDetailInfo?.needExecuteEffect;
            this.innerState.effectVisibleForPatientStatus = !!this.innerState.executeDetailInfo?.effectVisibleForPatient;
        }
        //当前单子为长护单子并且执行记录开启的情况下，默认选择上门护理
        if (this.isHospitalizationSheet && this.innerState.effectStatus) {
            this.innerState.needHomeCare = true;
        }

        //初始新建一个空选项
        if (!this.innerState.executeEffectGroups.length) {
            this.innerState.executeEffectGroups.push(
                JsonMapper.deserialize(ExecuteEffectGroup, {
                    id: UniqueKey(),
                    __type: ExecuteEffectGroupAddType.fromLocal,
                })
            );
        }

        //初始化选穴位列表
        PatientOrderDataAgent.getExecuteEffectTreatmentStaticOptions().then((rsp) => {
            this.innerState.treatmentStaticOptions = rsp;
        });

        //加载治疗方法模板
        PatientOrderDataAgent.getExecuteEffectTemplate()
            .toObservable()
            .subscribe(
                (rsp) => {
                    templateList = true;
                    this.innerState.executeTemplateList = rsp;

                    if (habitsInit && templateList) {
                        this.innerState.loading = false;
                        this.update();
                    }

                    this.innerState.executeEffectGroups.forEach((item, index, self) => {
                        if (!item) return;

                        const template = this.innerState.executeTemplateList.find(
                            (tempItem) => tempItem.treatmentMethod == item.treatmentMethod
                        );
                        if (!template) return;
                        PatientOrderDataAgent.getExecuteEffectTemplateById(template.id!).then((rsp) => {
                            self[index].templateDetail = rsp;
                        });
                    });
                },
                () => {
                    this.innerState.loading = false;
                    if (!habitsInit && !templateList) {
                        this.innerState.loadError = "加载异常，点击刷新";
                    }
                    this.update();
                }
            )
            .addToDisposableBag(this);

        /**
         * 获取执行记录历史列表
         */
        const _executeRecordTrigger = this._executeRecordTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.loadError = false;
                    this.innerState.loading = true;
                    this.update();
                    return fromPromise(
                        PatientOrderDataAgent.getExecuteRecordList(this.innerState.detailDataId!).catch((error) => {
                            return new ABCError(error);
                        })
                    );
                })
            )
            .subscribe(
                (res) => {
                    if (res instanceof ABCError) return;
                    this.innerState.executeDetailInfos = res.executeRecords ?? [];
                    this.update();
                },
                (error) => {
                    this.innerState.loading = false;
                    this.innerState.loadError = error;
                    this.update();
                }
            );
        this.addDisposable(_executeRecordTrigger);
        PatientOrderDataAgent.patientOrderExecuteInvoicePublisher.subscribe(() => {
            this._executeRecordTrigger.next();
        });
        if (this.innerState.detailDataId) {
            this._executeRecordTrigger.next();
        }

        this.update();
    }

    @actionEvent(_EventUpdate)
    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    @actionEvent(_EventChangeExecutor)
    private async *_mapEventChangeExecutor(): AsyncGenerator<State> {
        const executors = await SearchExecutorDialog.show({ employees: this.innerState.executors });
        if (executors) {
            this.hasChange = true;
            this.innerState.executors = executors;
            this.update();
        }
    }

    @actionEvent(_EventChangeExecuteProduct)
    private async *_mapEventChangeExecuteProduct(): AsyncGenerator<State> {
        const _cloneExecuteProduct = _.cloneDeep(this.innerState.executeProductMap);
        const _executeProduct = await PatientOrderExecuteProductDialog.show(_cloneExecuteProduct, this.innerState.invoicePartCharged);
        if (_executeProduct) {
            this.hasChange = true;
            this.innerState.executeProductMap = _executeProduct;
        }
        this.update();
    }

    @actionEvent(_EventChangeEffectStatus)
    private async *_mapEventChangeEffectStatus(): AsyncGenerator<State> {
        this.innerState.effectStatus = !this.innerState.effectStatus;
        clinicSharedPreferences.setObject(EXECUTE_EFFECT_SWITCH_STATUS, { status: this.innerState.effectStatus });
        this.hasChange = true;
        this.update();
    }

    @actionEvent(_EventModifyEffectContent)
    private async *_mapEventModifyEffectContent(): AsyncGenerator<State> {
        const result = await TreatmentInfoInputPage.show({ title: "备注", text: this.innerState.effectComment });
        if (!_.isUndefined(result)) {
            this.innerState.effectComment = result.trim();
            this.hasChange = true;
        }
        this.update();
    }

    @actionEvent(_EventSubmitEffectInfo)
    private async *_mapEventSubmitEffectInfo(): AsyncGenerator<State> {
        const executorIds = this.innerState.executors.map((item) => item.id);
        const items: { chargeFormItemId: string; count: number }[] = [];
        this.innerState.executeProductMap.forEach((usage, item) => {
            if (item.isCompose) {
                usage.composeUsage?.forEach((composeUsage, composeItem) => {
                    if (composeUsage.checked) {
                        items.push({ chargeFormItemId: composeItem.id!, count: composeUsage.count });
                    }
                });
            } else {
                if (usage.checked) {
                    items.push({ chargeFormItemId: item.id!, count: usage.count });
                }
            }
        });
        if (!executorIds.length) {
            return Toast.show("请选择执行人", { warning: true });
        }
        if (!items.length) {
            return Toast.show("请选择执行项目", { warning: true });
        }

        let newGroup: ExecuteEffectGroup[] = [];
        if (this.innerState.effectStatus) {
            //过滤空数据
            newGroup = this.innerState.executeEffectGroups.filter(
                (item) =>
                    item.__type != ExecuteEffectGroupAddType.fromLocal ||
                    (Object.keys(item) as Array<keyof ExecuteEffectGroup>).filter((key) => !isNil(item[key])).length != 2
            );
        }

        const _showLoadingDialog = new LoadingDialog();
        _showLoadingDialog.show();
        // 执行划扣
        const status = await PatientOrderDataAgent.chargeBatchExecute({
            executorIds,
            items,
            comment: this.innerState.effectComment ?? "",
            needExecuteEffect: this.innerState.effectStatus ? 1 : 0,
            effectVisibleForPatient: this.innerState.effectVisibleForPatientStatus ? 1 : 0,
            effects: newGroup,
            needHomeCare: this.innerState.needHomeCare ? NeedHomeCareStatus.need : NeedHomeCareStatus.none,
            homeCareAddress: this.innerState.homeCareAddress,
            homeCareStartTime: this.innerState.homeCareStartTime,
            homeCareEndTime: this.innerState.homeCareEndTime,
        });
        await _showLoadingDialog.hide();
        if (status instanceof ABCError) {
            await Toast.show(errorToStr(status), { warning: true });
        } else if (status) {
            await Toast.show("执行成功", { success: true });
            ABCNavigator.pop();
        } else {
            await Toast.show("执行失败", { warning: true });
        }
    }

    @actionEvent(_EventUndoEffectInfo)
    private async *_mapEventUndoEffectInfo(): AsyncGenerator<State> {
        const select = await showQueryDialog("撤销执行记录，将返还已执行项目的划扣次数。是否确定撤销？", "");
        if (select != DialogIndex.positive) return;

        const _showLoadingDialog = new LoadingDialog();
        _showLoadingDialog.show();
        const status = await PatientOrderDataAgent.undoExecuteRecordV2(this.innerState.executeDetailInfo!.id!);
        await _showLoadingDialog.hide();
        if (status instanceof ABCError) {
            await Toast.show(errorToStr(status), { warning: true });
        } else if (status) {
            await Toast.show("撤销成功", { success: true });
            ABCNavigator.pop();
        } else {
            await Toast.show("撤销失败", { warning: true });
        }
    }

    @actionEvent(_EventSaveEffect)
    private async *_mapEventSaveEffect(): AsyncGenerator<State> {
        let newGroup = _.cloneDeep(this.innerState.executeEffectGroups);
        for (const group of newGroup) {
            if (group.__type == ExecuteEffectGroupAddType.fromLocal) {
                delete group?.id;
            }
        }
        // 过滤空数据（解决传入effects:[{__type:1}]）会报错-缺少参数执行效果信息
        newGroup = newGroup.filter(
            (item) =>
                item.__type != ExecuteEffectGroupAddType.fromLocal ||
                (Object.keys(item) as Array<keyof ExecuteEffectGroup>).filter((key) => !isNil(item[key])).length > 1
        );

        const _effectContent = this.innerState.effectStatus ? this.innerState.effectComment! : "";
        const _showLoadingDialog = new LoadingDialog();
        _showLoadingDialog.show();
        const status = await PatientOrderDataAgent.changeExecuteEffect(_effectContent, this.innerState.executeDetailInfo!.id!, {
            effects: newGroup,
            comment: _effectContent,
            effectVisibleForPatient: this.innerState.effectVisibleForPatientStatus ? 1 : 0,
            needExecuteEffect: this.innerState.effectStatus ? 1 : 0,
            needHomeCare: this.innerState.needHomeCare ? NeedHomeCareStatus.need : NeedHomeCareStatus.none,
            homeCareAddress: this.innerState.homeCareAddress,
            homeCareStartTime: this.innerState.homeCareStartTime,
            homeCareEndTime: this.innerState.homeCareEndTime,
        });
        await _showLoadingDialog.hide();
        if (status instanceof ABCError) {
            await Toast.show(errorToStr(status), { warning: true });
        } else if (status) {
            await Toast.show("保存成功", { success: true });
            ABCNavigator.pop();
        } else {
            await Toast.show("保存失败", { warning: true });
        }
    }

    @actionEvent(_EventEffectVisibleForPatient)
    private async *_mapEventEffectVisibleForPatient(): AsyncGenerator<State> {
        this.innerState.effectVisibleForPatientStatus = !this.innerState.effectVisibleForPatientStatus;
        this.hasChange = true;
        this.update();
    }

    @actionEvent(_EventAddExecuteEffectGroup)
    private async *_mapEventAddExecuteEffectGroup(): AsyncGenerator<State> {
        const newGroup = JsonMapper.deserialize(ExecuteEffectGroup, {
            id: UniqueKey(),
            __type: ExecuteEffectGroupAddType.fromLocal,
        });
        this.innerState.executeEffectGroups.push(newGroup);
        this.hasChange = true;
        this.update(ScrollToGroup.fromState(this.innerState));
    }

    @actionEvent(_EventDeleteExecuteEffectGroup)
    private async *_mapEventDeleteExecuteEffectGroup(event: _EventDeleteExecuteEffectGroup): AsyncGenerator<State> {
        const result = await showQueryDialog("确认删除？", "");
        if (result != DialogIndex.positive) return;
        _.remove(this.innerState.executeEffectGroups, (item) => event.group.id == item.id);
        this.hasChange = true;
        this.update();
    }

    @actionEvent(_EventCopyExecuteEffectGroup)
    private async *_mapEventCopyExecuteEffectGroup(event: _EventCopyExecuteEffectGroup): AsyncGenerator<State> {
        const isCompleted =
            !!event.group.treatmentMethod ||
            !!event.group.treatmentSite ||
            !!event.group.treatmentResponse ||
            !!event.group.etiologyPathogenesis ||
            !!event.group.treatmentResult;
        const result: { addType?: MedicineTemplateAddType; executeEffectGroup?: ExecuteEffectGroup } =
            await CopyExecuteEffectGroupDialog.show({
                isCompleted: isCompleted,
                executeDetailInfos: this.innerState.executeDetailInfos,
            });
        if (result == undefined) return;
        const source = result.executeEffectGroup;
        this.innerState.executeEffectGroups = this.innerState.executeEffectGroups?.map((item) => {
            if (item.id == event.group.id) {
                if (result.addType === MedicineTemplateAddType.push) {
                    // 只有当_templateDetail等属性为空时才从source中赋值
                    item.treatmentMethod = (item.treatmentMethod ?? "") + (source?.treatmentMethod ?? "");
                    item.treatmentSite = (item.treatmentSite ?? "") + (source?.treatmentSite ?? "");
                    item.treatmentResponse = (item.treatmentResponse ?? "") + (source?.treatmentResponse ?? "");
                    item.etiologyPathogenesis = (item.etiologyPathogenesis ?? "") + (source?.etiologyPathogenesis ?? "");
                    item.treatmentResult = (item.treatmentResult ?? "") + (source?.treatmentResult ?? "");
                } else if (result.addType === MedicineTemplateAddType.reset || result.addType == undefined) {
                    // 覆盖或者从总是从source中赋值
                    item.treatmentMethod = source?.treatmentMethod;
                    item.treatmentSite = source?.treatmentSite;
                    item.treatmentResponse = source?.treatmentResponse;
                    item.etiologyPathogenesis = source?.etiologyPathogenesis;
                    item.treatmentResult = source?.treatmentResult;
                }
            }
            return item;
        });
        this.hasChange = true;
        this.update();
    }

    @actionEvent(_EventModifyTreatmentMethod)
    private async *_mapEventModifyTreatmentMethod(event: _EventModifyTreatmentMethod): AsyncGenerator<State> {
        const options = this.innerState.executeTemplateList.map((item) => item.treatmentMethod ?? "");
        options.unshift("不使用模板");
        const initIndex = options.findIndex((item) => event.group.treatmentMethod == item);
        const initialSelectIndexes = new Set<number>([Math.max(initIndex, 0)]);
        const select = await showOptionsBottomSheet({
            title: "选择治疗方法",
            options: options,
            initialSelectIndexes: initialSelectIndexes,
            height: pxToDp(400),
            showConfirmBtn: false,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
        });
        if (select && select[0] != initIndex) {
            if (select[0] == 0) {
                event.group.templateDetail = undefined;
                event.group.treatmentMethod = undefined;
            } else {
                const template = this.innerState.executeTemplateList[select[0] - 1];
                PatientOrderDataAgent.getExecuteEffectTemplateById(template.id!).then((rsp) => {
                    event.group.templateDetail = rsp;
                });
                event.group.treatmentMethod = template.treatmentMethod;
            }
            this.hasChange = true;
        }
        this.update();
    }

    @actionEvent(_EventModifyTreatmentSite)
    private async *_mapEventModifyTreatmentSite(event: _EventModifyTreatmentSite): AsyncGenerator<State> {
        const treatmentSite = event.group.templateDetail?.treatmentSite,
            bodyPosition = this.innerState.treatmentStaticOptions?.bodyPosition,
            acupuncture = this.innerState.treatmentStaticOptions?.acupuncture;
        let labels: TreatmentSiteOption[] = [];
        if (treatmentSite) {
            if (_.includes(treatmentSite, bodyPosition?.name) && bodyPosition) {
                labels = labels.concat([bodyPosition]);
            }
            if (_.includes(treatmentSite, acupuncture?.name)) {
                labels = labels.concat(acupuncture?.children ?? []);
            }
        } else {
            if (!labels.length) {
                bodyPosition && (labels = labels.concat([bodyPosition]));
                labels = labels.concat(acupuncture?.children ?? []);
            }
        }

        const result = await TreatmentSiteDialog.show({ text: event.group.treatmentSite, labels: labels });
        if (!_.isUndefined(result)) {
            event.group.treatmentSite = result.trim();
            this.hasChange = true;
        }
        this.update();
    }

    @actionEvent(_EventModifyTreatmentResponse)
    private async *_mapEventModifyTreatmentResponse(event: _EventModifyTreatmentResponse): AsyncGenerator<State> {
        const labels: string[][] | undefined = [];
        event.group.templateDetail?.treatmentResponse?.forEach((item) => {
            labels.push(item.list ?? []);
        });
        const result = await TreatmentInfoInputPage.show({ title: "治疗反应", text: event.group?.treatmentResponse, labels: labels });
        if (!_.isUndefined(result)) {
            event.group.treatmentResponse = result.trim();
            this.hasChange = true;
        }
        this.update();
    }

    @actionEvent(_EventModifyEtiologyPathogenesis)
    private async *_mapEventModifyEtiologyPathogenesis(event: _EventModifyEtiologyPathogenesis): AsyncGenerator<State> {
        const labels: string[][] | undefined = [];
        event.group.templateDetail?.etiologyPathogenesis?.forEach((item) => {
            labels.push(item.list ?? []);
        });
        const result = await TreatmentInfoInputPage.show({ title: "病因病机", text: event.group?.etiologyPathogenesis, labels: labels });
        if (!_.isUndefined(result)) {
            event.group.etiologyPathogenesis = result.trim();
            this.hasChange = true;
        }
        this.update();
    }

    @actionEvent(_EventModifyTreatmentResult)
    private async *_mapEventModifyTreatmentResult(event: _EventModifyTreatmentResult): AsyncGenerator<State> {
        const labels: string[][] | undefined = [];
        event.group.templateDetail?.treatmentResult?.forEach((item) => {
            labels.push(item.list ?? []);
        });
        const result = await TreatmentInfoInputPage.show({ title: "治疗结果", text: event.group?.treatmentResult, labels: labels });
        if (!_.isUndefined(result)) {
            event.group.treatmentResult = result.trim();
            this.hasChange = true;
        }
        this.update();
    }

    @actionEvent(_EventUploadImage)
    private async *_mapEventUploadImage(event: _EventUploadImage): AsyncGenerator<State> {
        AbcImagePicker.pickImageAndUpload(
            null,
            `${userCenter.clinic?.clinicId ?? ""}/${OssUpdateModules.EXECUTE_RECORD}`,
            OssUpdateModules.EXECUTE_RECORD
        ).then((result) => {
            if (result == null || _.isEmpty(result.url)) return;
            const item = JsonMapper.deserialize(ExecuteAttachmentItem, {
                fileName: FileUtils.getFileNameAndExt(new File(result.filePath!)),
                url: result.url,
            });
            event.group.attachments = event.group.attachments ?? [];
            event.group.attachments.push(item);
            this.innerState.hasChange = true;
            this.update();
        });
    }

    @actionEvent(_EventDeleteImage)
    private async *_mapEventDeleteImage(event: _EventDeleteImage): AsyncGenerator<State> {
        event.group.attachments?.splice(event.index, 1);
        this.innerState.hasChange = true;
        this.update();
    }

    @actionEvent(_EventChangeNeedHomeCareEffectStatus)
    private async *_mapEventChangeNeedHomeCareEffectStatus(): AsyncGenerator<State> {
        this.innerState.needHomeCare = !this.innerState.needHomeCare;
        this.hasChange = true;
        this.update();
    }

    @actionEvent(_EventModifyHomeCareAddress)
    private async *_mapEventModifyHomeCareAddress(): AsyncGenerator<State> {
        const addressStr = await ABCNavigator.navigateToPage<string>(
            <HomeCareAddressInputPage content={this.innerState.homeCareAddress} />
        );
        if (addressStr) {
            this.innerState.homeCareAddress = addressStr;
            this.hasChange = true;
        }
        this.update();
    }

    @actionEvent(_EventModifyHomeCareStartTime)
    private async *_mapEventModifyHomeCareStartTime(): AsyncGenerator<State> {
        const startTime = await FullTimePicker.show(this.innerState.homeCareStartTime, { maxDate: this.innerState.homeCareEndTime });
        if (!!startTime) {
            this.innerState.homeCareStartTime = startTime;
            this.hasChange = true;
        }
        this.update();
    }

    @actionEvent(_EventModifyHomeCareStartTimeYYYY)
    private async *_mapEventModifyHomeCareStartTimeYYYY(): AsyncGenerator<State> {
        const startTime = await FullTimePicker.show(this.innerState.homeCareStartTimeYYYY, {
            maxDate: this.innerState.homeCareEndTimeYYYY,
            showYYYY: true,
        });
        if (!!startTime) {
            this.innerState.homeCareStartTimeYYYY = startTime;
            this.hasChange = true;
        }
        this.update();
    }

    @actionEvent(_EventModifyHomeCareStartTimeHH)
    private async *_mapEventModifyHomeCareStartTimeHH(): AsyncGenerator<State> {
        const startTime = await FullTimePicker.show(this.innerState.homeCareStartTimeHH, {
            maxDate: this.innerState.homeCareEndTimeHH,
            showYYYY: false,
        });
        if (!!startTime) {
            this.innerState.homeCareStartTimeHH = startTime;
            this.hasChange = true;
        }
        this.update();
    }

    @actionEvent(_EventModifyHomeCareEndTimeYYYY)
    private async *_mapEventModifyHomeCareEndTimeYYYY(): AsyncGenerator<State> {
        const endTime = await FullTimePicker.show(this.innerState.homeCareEndTimeYYYY, { minDate: this.innerState.homeCareStartTimeYYYY });
        if (!!endTime) {
            this.innerState.homeCareEndTimeYYYY = endTime;
            this.hasChange = true;
        }
        this.update();
    }

    @actionEvent(_EventModifyHomeCareEndTimeHH)
    private async *_mapEventModifyHomeCareEndTimeHH(): AsyncGenerator<State> {
        const startTime = await FullTimePicker.show(this.innerState.homeCareEndTimeHH, {
            minDate: this.innerState.homeCareStartTimeHH,
            showYYYY: false,
        });
        if (!!startTime) {
            this.innerState.homeCareEndTimeHH = startTime;
            this.hasChange = true;
        }
        this.update();
    }

    @actionEvent(_EventModifyHomeCareEndTime)
    private async *_mapEventModifyHomeCareEndTime(): AsyncGenerator<State> {
        const endTime = await FullTimePicker.show(this.innerState.homeCareEndTime, { minDate: this.innerState.homeCareStartTime });
        if (!!endTime) {
            this.innerState.homeCareEndTime = endTime;
            this.hasChange = true;
        }
        this.update();
    }

    requestReload(): void {
        this.dispatch(new _EventInit());
    }

    requestChangeExecutor(): void {
        if (!this.innerState.shouldChange) return;
        this.dispatch(new _EventChangeExecutor());
    }

    requestChangeExecuteProduct(): void {
        if (!this.innerState.shouldChange) return;
        this.dispatch(new _EventChangeExecuteProduct());
    }

    requestChangeEffectStatus(): void {
        this.dispatch(new _EventChangeEffectStatus());
    }

    requestModifyEffectContent(): void {
        this.dispatch(new _EventModifyEffectContent());
    }

    requestSubmitEffectInfo(): void {
        this.dispatch(new _EventSubmitEffectInfo());
    }

    requestUndoEffectInfo(): void {
        this.dispatch(new _EventUndoEffectInfo());
    }

    requestSaveEffect(): void {
        this.dispatch(new _EventSaveEffect());
    }

    requestChangeEffectVisibleForPatientStatus(): void {
        this.dispatch(new _EventEffectVisibleForPatient());
    }

    requestAddExecuteEffectGroup(): void {
        this.dispatch(new _EventAddExecuteEffectGroup());
    }

    requestDeleteExecuteEffectGroup(group: ExecuteEffectGroup): void {
        this.dispatch(new _EventDeleteExecuteEffectGroup(group));
    }

    // 请求复制执行效果组
    requestCopyExecuteEffectGroup(group: ExecuteEffectGroup): void {
        this.dispatch(new _EventCopyExecuteEffectGroup(group));
    }

    requestModifyTreatmentMethod(group: ExecuteEffectGroup): void {
        this.dispatch(new _EventModifyTreatmentMethod(group));
    }

    requestModifyTreatmentSite(group: ExecuteEffectGroup): void {
        this.dispatch(new _EventModifyTreatmentSite(group));
    }

    requestModifyTreatmentResponse(group: ExecuteEffectGroup): void {
        this.dispatch(new _EventModifyTreatmentResponse(group));
    }

    requestModifyEtiologyPathogenesis(group: ExecuteEffectGroup): void {
        this.dispatch(new _EventModifyEtiologyPathogenesis(group));
    }

    requestModifyTreatmentResult(group: ExecuteEffectGroup): void {
        this.dispatch(new _EventModifyTreatmentResult(group));
    }

    requestUploadImage(group: ExecuteEffectGroup): void {
        this.dispatch(new _EventUploadImage(group));
    }

    requestDeleteImage(group: ExecuteEffectGroup, index: number): void {
        this.dispatch(new _EventDeleteImage(group, index));
    }

    requestModifyHomeCareAddress(): void {
        this.dispatch(new _EventModifyHomeCareAddress());
    }

    requestChangeNeedHomeCareEffectStatus(): void {
        this.dispatch(new _EventChangeNeedHomeCareEffectStatus());
    }

    requestModifyHomeCareStartTime(): void {
        this.dispatch(new _EventModifyHomeCareStartTime());
    }

    requestModifyHomeCareEndTime(): void {
        this.dispatch(new _EventModifyHomeCareEndTime());
    }

    //选择开始年份
    requestModifyHomeCareStartTimeYYYY(): void {
        this.dispatch(new _EventModifyHomeCareStartTimeYYYY());
    }

    //选择开始时间
    requestModifyHomeCareStartTimeHH(): void {
        this.dispatch(new _EventModifyHomeCareStartTimeHH());
    }

    //选择结束年份
    requestModifyHomeCareEndTimeYYYY(): void {
        this.dispatch(new _EventModifyHomeCareEndTimeYYYY());
    }

    //选择结束时间
    requestModifyHomeCareEndTimeHH(): void {
        this.dispatch(new _EventModifyHomeCareEndTimeHH());
    }
}
