/**
 * create by dengjie
 * desc:
 * create date 2020/8/3
 */

import React from "react";
import { View } from "@hippy/react";
import { AppSearchBar } from "../base-ui/app-bar";
import { ABCNetworkPageContentStatus, BaseBlocNetworkPage } from "../base-ui/base-page";
import { Colors, Sizes, TextStyles } from "../theme";
import { ExecuteListViewBloc } from "./execute-list-view-bloc";
import _ from "lodash";
import { PatientOrderListView } from "./execute-list-view";
import { Tab, Tabs } from "../base-ui";
import { ExecuteSourceType } from "./data/bean";
import { AbcEmptyItemView } from "../base-ui/views/empty-view";

interface PatientOrderSearchPageProps {
    name?: string;
}

export class PatientOrderSearchPage extends BaseBlocNetworkPage<PatientOrderSearchPageProps, ExecuteListViewBloc> {
    private firstFlag = true;
    private switchPatientOrderPage = ExecuteSourceType.clinic;
    private patientOrderSearchPageBlocMap: Map<ExecuteSourceType, ExecuteListViewBloc>;

    constructor(props: PatientOrderSearchPageProps) {
        super(props);

        this.patientOrderSearchPageBlocMap = new Map<ExecuteSourceType, ExecuteListViewBloc>([
            [
                ExecuteSourceType.clinic,
                new ExecuteListViewBloc({
                    group: false,
                    initLoad: false,
                    initialValue: this.props.name,
                    enableDraft: false,
                    isFromSearchPage: true,
                }),
            ],
            [
                ExecuteSourceType.chain,
                new ExecuteListViewBloc({
                    group: false,
                    initLoad: false,
                    initialValue: this.props.name,
                    enableDraft: false,
                    sourceType: ExecuteSourceType.chain,
                    isFromSearchPage: true,
                }),
            ],
        ]);
        this.bloc = this._bloc;
    }

    get _bloc(): ExecuteListViewBloc {
        return (
            this.patientOrderSearchPageBlocMap.get(this.switchPatientOrderPage) ??
            new ExecuteListViewBloc({
                group: false,
                initLoad: false,
                initialValue: this.props.name,
                enableDraft: false,
                isFromSearchPage: true,
            })
        );
    }

    getAppBar(): JSX.Element {
        return (
            <AppSearchBar
                placeholder={"姓名/手机/项目/诊号"}
                rightPart={this.getRightAppBarIcons()}
                onBackClick={this.onBackClick.bind(this)}
                autoFocus={true}
                defaultValue={this.bloc.currentState.keyword ?? this.props.name}
                onChangeText={(value) => {
                    // 只在当前激活的 bloc 实例上调用 requestSearchPatient 方法
                    // 这样只会发送一次请求
                    this.bloc.requestSearchPatient(value);
                }}
            />
        );
    }

    reloadData(): void {
        this.bloc.requestReload();
    }

    componentDidMount(): void {
        super.componentDidMount();

        this.bloc.state.subscribe((state) => {
            let status = ABCNetworkPageContentStatus.show_data;
            if (state.loading && state.keyword) {
                if (!state.dataHolders.length && !state.enableCross) {
                    status = ABCNetworkPageContentStatus.loading;
                }
            }

            this.setContentStatus(status);
        });

        this.patientOrderSearchPageBlocMap.get(ExecuteSourceType.clinic)?.state.subscribe((state) => {
            let status = ABCNetworkPageContentStatus.show_data;
            if (state.loading && state.keyword) {
                if (!state.dataHolders.length && !state.enableCross) {
                    status = ABCNetworkPageContentStatus.loading;
                }
            }

            this.setContentStatus(status);
        });

        this.patientOrderSearchPageBlocMap.get(ExecuteSourceType.chain)?.state.subscribe((state) => {
            let status = ABCNetworkPageContentStatus.show_data;
            if (state.loading && state.keyword) {
                if (!state.dataHolders.length && !state.enableCross) {
                    status = ABCNetworkPageContentStatus.loading;
                }
            }

            this.setContentStatus(status);
        });

        this.addDisposable(this.bloc);
        this.addDisposable(this.patientOrderSearchPageBlocMap.get(ExecuteSourceType.clinic));
        this.addDisposable(this.patientOrderSearchPageBlocMap.get(ExecuteSourceType.chain));
    }

    renderContent(): JSX.Element {
        const state = this.bloc.currentState;

        if (_.isEmpty(state.keyword)) return <View style={{ flex: 1, backgroundColor: Colors.white }} />;

        if (state.keyword && !((state._searchResult?.countInOtherClinics ?? 0) + (state._searchResult?.totalCount ?? 0))) {
            return <AbcEmptyItemView type={"search"} />;
        }

        return (
            <View style={{ flex: 1, backgroundColor: Colors.white }}>
                {state.enableCross ? (
                    <Tabs
                        tabsStyle={{
                            marginLeft: Sizes.dp32,
                            justifyContent: "space-between",
                            paddingHorizontal: Sizes.listHorizontalMargin,
                        }}
                        lazy={false}
                        onChange={(index) => {
                            if (this.firstFlag) {
                                this.firstFlag = false;
                            }
                            this.switchPatientOrderPage = index;
                            if (!!state.keyword)
                                this.patientOrderSearchPageBlocMap.get(this.switchPatientOrderPage)?.requestSearchPatient(state.keyword);
                        }}
                        tabStyle={{ ...TextStyles.t16NT1 }}
                    >
                        <Tab title={`本店(${state._patientOrderCount.get(ExecuteSourceType.clinic) ?? 0})`}>
                            <PatientOrderListView
                                bloc={this.patientOrderSearchPageBlocMap.get(ExecuteSourceType.clinic)}
                                sourceType={ExecuteSourceType.clinic}
                                isFromSearchPage={true}
                            />
                        </Tab>
                        <Tab title={`其他门店(${state._patientOrderCount.get(ExecuteSourceType.chain) ?? 0})`}>
                            <PatientOrderListView
                                bloc={this.patientOrderSearchPageBlocMap.get(ExecuteSourceType.chain)}
                                sourceType={ExecuteSourceType.chain}
                                isFromSearchPage={true}
                            />
                        </Tab>
                    </Tabs>
                ) : (
                    <PatientOrderListView bloc={this.bloc} />
                )}
            </View>
        );
    }
}
