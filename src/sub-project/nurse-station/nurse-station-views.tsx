/**
 * create by deng<PERSON>e
 * desc:
 * create date 2020/6/9
 */
import React from "react";
import { Style, Text, View } from "@hippy/react";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { ChargeForm, ChargeFormItem, ChargeFormItemStatus, ChargeFormStatus, ChargeSourceFormType } from "../charge/data/charge-beans";
import { GoodsInfo, GoodsSubType, GoodsType } from "../base-business/data/beans";
import { ExecuteChargeStatusView } from "../charge/view/charge-views";
import { SizedBox, Spacer } from "../base-ui";
import { ExecuteInvoicePageBloc } from "./execute-invoice-page-bloc";
import _ from "lodash";
import { AbcLoadingButton } from "../base-ui/views/abc-button";
import { BaseComponent } from "../base-ui/base-component";
import { ABCUtils } from "../base-ui/utils/utils";
import { ExecuteUtils } from "./data/execute-utils";
import { Toast } from "../base-ui/dialog/toast";
import { GoodsUtils } from "../base-business/utils/utils";
import { AbcBasePanel } from "../base-ui/abc-app-library";
import { ListSettingItem, ListSettingItemStyle } from "../base-ui/views/list-setting-item";
import { AbcCardHeader } from "../base-ui/abc-app-library/common/abc-card-header";
import { AbcView } from "../base-ui/views/abc-view";
import { GridView } from "../base-ui/views/grid-view";
import { TextWithErrorHint } from "../outpatient/outpatient-views";

interface MedicalRecordCardViewProps {
    diagnosis?: string;
    diagnosisHix?: string;
    doctorName?: string;
    sellerName?: string;
    sellerDepartmentName?: string;
}

export class MedicalRecordCardView extends React.Component<MedicalRecordCardViewProps> {
    constructor(props: MedicalRecordCardViewProps) {
        super(props);
    }

    render(): JSX.Element {
        const { diagnosis, doctorName, sellerName, sellerDepartmentName } = this.props;
        const sellerNameStr = sellerName ? `${sellerName ?? ""}${!!sellerDepartmentName ? ` - ${sellerDepartmentName}` : ""}` : undefined;
        if (!diagnosis && !doctorName && !sellerName) return <View />;

        const itemStyle = ListSettingItemStyle.normal;
        const contentStyle = { alignItems: "flex-end" };
        return (
            <AbcBasePanel
                panelStyle={Sizes.marginLTRB(Sizes.dp8, Sizes.dp18, Sizes.dp8, 0)}
                contentStyle={{ paddingHorizontal: Sizes.listHorizontalMargin }}
            >
                {diagnosis && (
                    <ListSettingItem
                        title={"诊断"}
                        content={diagnosis ?? ""}
                        itemStyle={itemStyle}
                        bottomLine={!!sellerName || !!doctorName}
                        contentStyle={contentStyle}
                    />
                )}
                {!!sellerName && (
                    <ListSettingItem title={"开单人"} content={sellerNameStr} itemStyle={itemStyle} contentStyle={contentStyle} />
                )}
                {!!doctorName && <ListSettingItem title={"医生"} content={doctorName} itemStyle={itemStyle} contentStyle={contentStyle} />}
            </AbcBasePanel>
        );
    }
}

interface ProjectCardViewProps {
    chargeForms: ChargeForm[];
    isLockingOrder?: boolean; // 是否锁单中
}

export class ProjectCardView extends React.Component<ProjectCardViewProps> {
    static contextType = ExecuteInvoicePageBloc.Context;
    constructor(props: ProjectCardViewProps) {
        super(props);
    }

    render(): JSX.Element {
        const { chargeForms, isLockingOrder } = this.props;
        const views: JSX.Element[] = [];
        const treatments: ChargeFormItem[] = []; //治疗理疗项
        const packages: ChargeFormItem[] = []; //套餐
        const ast: ChargeFormItem[] = []; //皮试项
        chargeForms.forEach((item) => {
            item.chargeFormItems?.forEach((formItem) => {
                formItem.sourceFormType__ = item.sourceFormType;
                const productType = formItem.productType;
                if (
                    productType == GoodsType.treatment ||
                    productType == GoodsType.examination ||
                    productType == GoodsType.nurseProduct ||
                    productType == GoodsType.package ||
                    productType == GoodsType.glasses ||
                    productType == GoodsType.otherGoods49 ||
                    (productType == GoodsType.goods && formItem.needExecutive !== 1) ||
                    (productType == GoodsType.material && formItem.needExecutive !== 1 && formItem.ast == undefined) // 这里用于排除成药或输注物资类型中有ast的重复药品
                ) {
                    if (formItem.isCompose) {
                        packages.push(formItem);
                    } else {
                        treatments.push(formItem);
                    }
                }
            });
        });

        let _packages: ChargeFormItem[] = [];
        packages.forEach((item) => {
            _packages = _packages.concat(item.composeChildren ?? []);
        });
        const formStatus = ExecuteUtils.executeFormStatus(
            [...treatments, ...ast, ..._packages],
            ExecuteInvoicePageBloc.fromContext(this.context).currentState.invoicePartCharged ? ChargeFormItemStatus.partCharged : undefined
        );

        // 诊疗普通
        treatments.forEach((item, index) => {
            views.push(
                <_ProjectItemItemView
                    key={item.id}
                    formItem={item}
                    formCardHeaderStatus={formStatus}
                    showBottomLine={index !== treatments.length - 1}
                    style={[{ paddingBottom: Sizes.dp10 }]}
                    isLockingOrder={isLockingOrder}
                />
            );
        });

        // 诊疗套餐
        packages.forEach((item, index) => {
            views.push(
                <_TreatmentPackage
                    key={item.id}
                    formItem={item}
                    showTopLine={!!treatments.length || index + 1 !== packages.length}
                    formCardHeaderStatus={formStatus}
                />
            );
        });

        if (_.isEmpty(views)) return <View />;

        return (
            <AbcBasePanel panelStyle={{ marginTop: Sizes.dp18, marginHorizontal: Sizes.dp8, paddingTop: Sizes.dp20 }}>
                <AbcCardHeader
                    title={"诊疗项目"}
                    titleStyle={TextStyles.t18MT1}
                    style={{ height: Sizes.dp25 }}
                    showCardLeftLine={false}
                    titleSuffix={() => (
                        <View style={{ marginLeft: Sizes.dp4 }}>
                            <ExecuteChargeStatusView showChargedStatus={true} chargeStatus={formStatus} />
                        </View>
                    )}
                    cardLeftLineSpace={Sizes.dp8}
                    padding={0}
                />
                <SizedBox height={Sizes.dp16} />
                <View style={{ backgroundColor: Colors.white, marginHorizontal: Sizes.listHorizontalMargin }}>{views}</View>
            </AbcBasePanel>
        );
    }
}

interface ExternalMedicineGroupViewProps {
    chargeForms: ChargeForm[];
    isLockingOrder?: boolean; // 是否锁单中
}
export class ExternalMedicineGroupView extends BaseComponent<ExternalMedicineGroupViewProps> {
    constructor(props: ExternalMedicineGroupViewProps) {
        super(props);
    }
    render(): JSX.Element {
        const { chargeForms, isLockingOrder } = this.props;
        const views: JSX.Element[] = [];
        chargeForms.forEach((item, index) => {
            let groupSuffix = "";
            if (chargeForms.length > 1) groupSuffix = ABCUtils.toChineseNum(index + 1);
            views.push(<ExternalPrescriptionCardView key={index} chargeForms={item} index={groupSuffix} isLockingOrder={isLockingOrder} />);
        });

        if (_.isEmpty(views)) {
            return <View />;
        }
        return <AbcBasePanel panelStyle={{ marginTop: Sizes.dp18, marginHorizontal: Sizes.dp8 }}>{views}</AbcBasePanel>;
    }
}

interface ExternalPrescriptionCardViewProps {
    index: string;
    chargeForms: ChargeForm;
    isLockingOrder?: boolean; // 是否锁单中
}

/**
 * 外治处方卡片
 */
export class ExternalPrescriptionCardView extends React.Component<ExternalPrescriptionCardViewProps> {
    static contextType = ExecuteInvoicePageBloc.Context;
    constructor(props: ExternalPrescriptionCardViewProps) {
        super(props);
    }

    // 穴位
    _renderAcuPointGroupView(formItem: ChargeFormItem): JSX.Element {
        const acuPointsView: JSX.Element[] = [];

        formItem.acupoints?.forEach((acuPointsItem) => {
            acuPointsView.push(
                <Text style={[TextStyles.t14NT1, { flex: 1, lineHeight: Sizes.dp20 }]} numberOfLines={1}>
                    {`${acuPointsItem.position}｜${acuPointsItem.name}`}
                </Text>
            );
        });
        if (!acuPointsView.length) {
            return <View />;
        }
        return (
            <View style={{ marginTop: Sizes.dp6 }}>
                <GridView
                    style={{ flex: 1 }}
                    itemHeight={Sizes.dp20}
                    crossAxisCount={2}
                    mainAxisSpacing={Sizes.dp8}
                    crossAxisSpacing={Sizes.dp16}
                >
                    {acuPointsView}
                </GridView>
            </View>
        );
    }

    // 配方
    _renderDrugFormulaView(formItem: ChargeFormItem): JSX.Element {
        const drugFormulaView: JSX.Element[] = [];
        if (!!formItem.externalGoodsItems?.length) {
            formItem.externalGoodsItems?.map((item) => {
                drugFormulaView.push(
                    <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                        <Text style={[TextStyles.t14NT1, { lineHeight: Sizes.dp20, flexShrink: 1 }]} numberOfLines={1}>
                            {item.displayName}
                        </Text>
                        <Text style={[TextStyles.t14NT1, { lineHeight: Sizes.dp20 }]} numberOfLines={1}>
                            {` ${item.unitCount}${item.unit}`}
                        </Text>
                    </View>
                );
            });
        }
        if (!drugFormulaView.length) {
            return <View />;
        }
        return (
            <View style={{ marginTop: Sizes.dp6 }}>
                <GridView
                    style={{ flex: 1 }}
                    itemHeight={Sizes.dp20}
                    crossAxisCount={2}
                    mainAxisSpacing={Sizes.dp8}
                    crossAxisSpacing={Sizes.dp16}
                >
                    {drugFormulaView}
                </GridView>
            </View>
        );
    }

    render(): JSX.Element {
        const { chargeForms, index, isLockingOrder } = this.props;
        const views: JSX.Element[] = [];
        const state = ExecuteInvoicePageBloc.fromContext(this.context).currentState;
        const cardHeaderStatus = state.invoicePartCharged ? ChargeFormItemStatus.partCharged : chargeForms.status;
        let usageTypeName = "";
        if (chargeForms.usageInfo?.usageType !== null) {
            usageTypeName = "穴";
        }

        chargeForms.chargeFormItems?.forEach((formItem) => {
            // const perCount =
            //     (formItem.unitCount ?? 1) / (Number(formItem.usageInfo?.dosage) ?? 1) / (formItem.usageInfo?.externalUnitCount ?? 1); // 每次贴数
            const perCount = formItem.acuPointsCount; // 每次数量

            formItem.sourceFormType__ = chargeForms.sourceFormType;
            const productType = formItem.productType;
            const usageItems: string[] = [];
            !!formItem.usageInfo?.dosage && formItem.usageInfo?.dosage != "0" ? usageItems.push(`共${formItem.usageInfo?.dosage}次`) : "";
            !!formItem.usageInfo?.freq ? usageItems.push(formItem.usageInfo?.freq) : "";
            !!formItem.specialRequirement ? usageItems.push(formItem.specialRequirement) : "";
            !!formItem.unitCount && !!formItem.usageInfo?.dosage ? usageItems.push(`每次${perCount}${usageTypeName}`) : "";
            !!formItem.unitCount ? usageItems.push(`共${perCount * Number(formItem.usageInfo?.dosage ?? 1)}${usageTypeName}`) : "";

            if (productType == GoodsType.treatment || productType == GoodsType.examination || productType == GoodsType.otherGoods49) {
                views.push(
                    <View key={formItem.id} style={[ABCStyles.bottomLine, { paddingTop: Sizes.dp8, marginHorizontal: Sizes.dp16 }]}>
                        <_ProjectItemItemView
                            formItem={formItem}
                            title={`${formItem.name}`}
                            formCardHeaderStatus={cardHeaderStatus}
                            chargeForms={chargeForms}
                            isLockingOrder={isLockingOrder}
                        />
                        {this._renderDrugFormulaView(formItem)}
                        {this._renderAcuPointGroupView(formItem)}
                        <View
                            style={[
                                ABCStyles.rowAlignCenter,
                                {
                                    marginTop:
                                        !!formItem.externalGoodsItems?.length || !!formItem.acupoints?.length ? Sizes.dp8 : undefined,
                                    marginBottom: Sizes.dp16,
                                },
                            ]}
                        >
                            <TextWithErrorHint text={usageItems.join("，")} numberOfLines={2} />
                        </View>
                    </View>
                );
            }
        });

        if (_.isEmpty(views)) return <View />;

        return (
            <View style={{ paddingTop: Sizes.dp20 }}>
                <AbcCardHeader
                    title={`外治处方${index}`}
                    titleStyle={TextStyles.t18MT1}
                    style={{ height: Sizes.dp25 }}
                    showCardLeftLine={false}
                    titleSuffix={() => (
                        <View style={{ marginLeft: Sizes.dp4 }}>
                            <ExecuteChargeStatusView chargeStatus={cardHeaderStatus} showChargedStatus={true} />
                        </View>
                    )}
                    cardLeftLineSpace={Sizes.dp8}
                    padding={0}
                />
                <SizedBox height={Sizes.dp10} />
                <View style={{ backgroundColor: Colors.white }}>{views}</View>
            </View>
        );
    }
}

interface _ProjectItemItemViewProps {
    formItem: ChargeFormItem;
    parentForm?: ChargeFormItem;
    formCardHeaderStatus?: ChargeFormStatus | ChargeFormItemStatus;

    title?: string; //如果不指定将从formItem.name里算出
    subTitle?: string;
    showBottomLine?: boolean;
    chargeForms?: ChargeForm;
    style?: Style | Style[];

    isPackage?: boolean; // 是套餐
    isLockingOrder?: boolean; // 是否锁单中
}

class _ProjectItemItemView extends React.Component<_ProjectItemItemViewProps> {
    static contextType = ExecuteInvoicePageBloc.Context;
    private readonly _productIsExecuteItem: boolean;

    constructor(props: _ProjectItemItemViewProps) {
        super(props);
        this._productIsExecuteItem = this.productIsExecuteItem(props.formItem.productType, props.formItem.productSubType);
    }

    static defaultProps = {
        isPackage: false,
    };

    productIsExecuteItem(productType?: number, productSubType?: number): boolean {
        let status = false;
        switch (productType) {
            case GoodsType.examination: {
                status = true;
                break;
            }
            case GoodsType.treatment: {
                switch (productSubType) {
                    case GoodsSubType.treatmentPhysiotherapy:
                        status = true;
                        break;
                    case GoodsSubType.treatment:
                        status = true;
                        break;
                    case GoodsSubType.treatmentOther:
                        status = true;
                }
                break;
            }
            case GoodsType.otherGoods49: {
                status = true;
                break;
            }
            default:
                break;
        }
        return status;
    }

    renderPackageSpec(): JSX.Element {
        const { formItem, chargeForms } = this.props;
        let unitCount = Math.round(formItem?.unitCount ?? 0);
        if (formItem.sourceFormType__ == ChargeSourceFormType.externalPrescription) {
            unitCount = formItem.externalPrescriptionUnitCount;
        }

        let unit = formItem?.unit;
        if (chargeForms?.usageInfo?.usageType !== null && chargeForms?.sourceFormType == ChargeSourceFormType.externalPrescription) {
            unit = "穴";
            // 外治处方仅有贴、穴单位
            // if (chargeForms?.usageInfo?.usageType == ExternalPRUsageTypeEnum.tieFu) {
            //     unit = "贴";
            // } else {
            //     unit = "穴";
            // }
        }

        if (this._productIsExecuteItem) {
            const notNeedExecute = !ExecuteUtils.needExecutive(formItem);

            return (
                <View>
                    <Text style={[TextStyles.t14NB, { flexShrink: 1, textAlign: "right" }]} numberOfLines={1} ellipsizeMode={"clip"}>
                        {`x${formItem?.executedUnitCount || !notNeedExecute ? `${formItem?.executedUnitCount}/` : ""}${unitCount}${
                            GoodsUtils.unitIsCustom(formItem?.unit?.trim()) ? "*" : ""
                        }${unit?.trim() == "" ? "次" : unit?.trim() ?? "次"}`}
                    </Text>
                </View>
            );
        } else {
            return (
                <View>
                    <Text style={[TextStyles.t14NB, { flexShrink: 1, textAlign: "right" }]} numberOfLines={1} ellipsizeMode={"clip"}>
                        {`x${unitCount}${GoodsUtils.unitIsCustom(formItem?.unit?.trim()) ? "*" : ""} ${
                            formItem?.unit?.trim() == "" ? "次" : formItem?.unit?.trim() ?? "次"
                        }`}
                    </Text>
                </View>
            );
        }
    }

    render() {
        const { formItem, title, style, isPackage, isLockingOrder } = this.props;

        const executed = !ExecuteUtils.needExecutive(formItem);
        const canExecute = ExecuteUtils.needExecutive(
            formItem,
            !!ExecuteInvoicePageBloc.fromContext(this.context).currentState.detailData?.onlyExecuteAfterPaid
        );
        let projectName = title ?? formItem.name ?? "";
        if (_.isEmpty(projectName) && formItem.productInfo instanceof GoodsInfo) {
            projectName = projectName + (formItem.productInfo.medicineCadn ?? "");
        }
        const showCanExecute = this._productIsExecuteItem && !executed;

        const unit = formItem?.unit;

        let unitCount = Math.round(formItem?.unitCount ?? 0);
        if (formItem.sourceFormType__ == ChargeSourceFormType.externalPrescription) {
            unitCount = formItem.externalPrescriptionUnitCount;
        }
        return (
            <View key={formItem.id} style={{ flexShrink: 1, ...style }}>
                <View style={[ABCStyles.rowAlignCenter, { height: showCanExecute ? Sizes.dp34 : Sizes.dp22 }]}>
                    <View style={{ flexShrink: 1, marginRight: Sizes.dp12 }}>
                        <Text style={[ABCStyles.rowAlignCenter, TextStyles.t16NB, { flex: 1 }]} numberOfLines={1} ellipsizeMode={"tail"}>
                            {`${projectName} ${formItem.sourceFormType__ == ChargeSourceFormType.gift ? "(赠)" : ""}`}
                        </Text>
                    </View>
                    <Spacer />

                    {(this._productIsExecuteItem || !!formItem.needExecutive) && !executed ? (
                        <AbcView
                            style={[
                                ABCStyles.rowAlignCenter,
                                {
                                    justifyContent: "center",
                                    paddingHorizontal: Sizes.dp2,
                                    borderRadius: Sizes.dp4,
                                    width: Sizes.dp117,
                                    height: Sizes.dp34,
                                    backgroundColor: !isLockingOrder && canExecute ? Colors.theme2Mask8 : Colors.bottomBtnDisable,
                                },
                            ]}
                            onClick={() => {
                                if (isLockingOrder) return;
                                if (!canExecute) {
                                    Toast.show("收费后才可执行", { warning: true }).then();
                                } else {
                                    if (executed) return;
                                    ExecuteInvoicePageBloc.fromContext(this.context).requestExecute(
                                        this.props.formItem,
                                        this.props.parentForm
                                    );
                                }
                            }}
                        >
                            <Text style={[!isLockingOrder && canExecute ? TextStyles.t14MM2 : TextStyles.t14MW]} numberOfLines={1}>
                                {`执行(${formItem?.executedUnitCount}/${unitCount})${unit}`}
                            </Text>
                        </AbcView>
                    ) : (
                        this.renderPackageSpec()
                    )}
                </View>
                {(!!formItem.outpatientRemark || !!formItem.remark) && !isPackage && (
                    <View>
                        <Text
                            style={[TextStyles.t14NT3.copyWith({ color: Colors.t3, lineHeight: Sizes.dp20 }), { flexShrink: 1 }]}
                            numberOfLines={2}
                        >
                            {formItem.outpatientRemark ?? formItem.remark}
                        </Text>
                    </View>
                )}
            </View>
        );
    }
}

interface AstItemViewProps {
    formItem: ChargeFormItem;
    formCardHeaderStatus?: ChargeFormStatus | ChargeFormItemStatus;
}

export class AstItemView extends React.Component<AstItemViewProps> {
    static contextType = ExecuteInvoicePageBloc.Context;

    constructor(props: AstItemViewProps) {
        super(props);
    }

    render(): JSX.Element {
        const { formItem } = this.props;

        let astStrColor = Colors.theme2;
        let astStr = "皮试";

        switch (formItem.astResult?.result) {
            case "阴性":
                formItem.astResult.result = "阴性";
                astStrColor = Colors.B1;
                astStr = "阴性(-)";
                break;
            case "阳性":
                formItem.astResult.result = "阳性";
                astStrColor = Colors.red;
                astStr = "阳性(+)";
                break;
        }
        return (
            <View>
                {!ExecuteInvoicePageBloc.fromContext(this.context).currentState.isCross && (
                    <AbcLoadingButton
                        style={{
                            width: Sizes.dp60,
                            backgroundColor: Colors.bg1,
                        }}
                        onClick={() => {
                            ExecuteInvoicePageBloc.fromContext(this.context).requestExecuteAst(formItem);
                        }}
                    >
                        <Text style={TextStyles.t14MM.copyWith({ color: astStrColor, lineHeight: Sizes.dp20 })} numberOfLines={1}>
                            {astStr}
                        </Text>
                    </AbcLoadingButton>
                )}
            </View>
        );
    }
}

interface _TreatmentPackageProps {
    showTopLine?: boolean;
    formItem: ChargeFormItem;
    formCardHeaderStatus?: ChargeFormItemStatus;
}

class _TreatmentPackage extends React.Component<_TreatmentPackageProps, any> {
    constructor(props: _TreatmentPackageProps) {
        super(props);
    }

    static defaultProps = {
        showTopLine: true,
    };

    render() {
        const { formItem, showTopLine, formCardHeaderStatus } = this.props;
        return (
            <View style={[showTopLine ? ABCStyles.topLine : undefined]}>
                <View
                    style={{
                        paddingTop: showTopLine ? Sizes.listHorizontalMargin : undefined,
                        paddingBottom: Sizes.dp10,
                    }}
                >
                    <Text
                        style={[
                            TextStyles.t16NT1,
                            {
                                flexShrink: 1,
                            },
                        ]}
                        numberOfLines={1}
                    >
                        {`${formItem.displayName} 【套餐】${formItem.sourceFormType__ == ChargeSourceFormType.gift ? "(赠)" : ""}`}
                    </Text>
                    {(!!formItem.outpatientRemark || !!formItem.remark) && (
                        <Text
                            style={[
                                TextStyles.t14NT3.copyWith({ color: Colors.t3, lineHeight: Sizes.dp20 }),
                                { flexShrink: 1, marginTop: Sizes.dp4 },
                            ]}
                            numberOfLines={2}
                        >
                            {formItem.outpatientRemark ?? formItem.remark}
                        </Text>
                    )}
                </View>
                {formItem.composeChildren?.map((item, index) => (
                    <_ProjectItemItemView
                        key={index}
                        formItem={item}
                        parentForm={formItem}
                        formCardHeaderStatus={formCardHeaderStatus}
                        style={{ paddingBottom: Sizes.dp10 }}
                        isPackage={true}
                    />
                ))}
            </View>
        );
    }
}

/**
 * 成药、输注 项目可执行按钮
 *
 */
interface ItemExecutableButtonProps {
    isShow: boolean; // 是否显示
    isExecutable: boolean; // 是否可执行(点击)
    style?: Style | Style[];
    onClick?: () => void;
}

export class ItemExecutableButton extends React.Component<ItemExecutableButtonProps, any> {
    constructor(props: ItemExecutableButtonProps) {
        super(props);
    }

    render(): JSX.Element {
        const { isShow, onClick, isExecutable, style } = this.props;
        if (!isShow) return <View />;
        return (
            <View
                style={{
                    justifyContent: "center",
                    alignItems: "center",
                    borderRadius: Sizes.dp4,
                    width: Sizes.dp60,
                    height: Sizes.dp32,
                    backgroundColor: isExecutable ? Colors.theme2Mask8 : Colors.bdColor,
                    ...style,
                }}
                onClick={() => onClick?.()}
            >
                <Text
                    style={[TextStyles.t14MW, { lineHeight: Sizes.dp20, color: isExecutable ? Colors.theme2 : Colors.S2 }]}
                    numberOfLines={1}
                >
                    {"执行"}
                </Text>
            </View>
        );
    }
}
