/**
 * create by deng<PERSON>e
 * desc:
 * create date 2020/6/11
 */

import React from "react";
import { _ExecuteListItemView } from "./patient-order-execute-dialog";
import { ChargeFormItem } from "../charge/data/charge-beans";
import { ABCStyles, ABCStyleSheet, AbcViewProps, Colors, flattenStyles, Sizes, TextStyles } from "../theme";
import { SizedBox, ToolBar, ToolBarButtonStyle1 } from "../base-ui";
import { Text, View } from "@hippy/react";
import { AbcTextInput, SimpleFinishPanel } from "../base-ui/views/abc-text-input";
import { userCenter } from "../user-center";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { AbcBasePanel, BottomSheetHelper } from "../base-ui/abc-app-library";
import { SafeAreaBottomView } from "../base-ui/safe_area_view";
import { BaseComponent } from "../base-ui/base-component";
import { GridView } from "../base-ui/views/grid-view";
import { pxToDp } from "../base-ui/utils/ui-utils";

//药敏执行对话框执行结果
class AstExecuteDialogResult {
    result?: string;
    description?: string;
}

interface AstExecuteDialogProps {
    chargeFormItem: ChargeFormItem;
}

export default class AstExecuteDialog extends React.Component<AstExecuteDialogProps> {
    private astResult: string;
    private astDescription: string;

    inputSize = 0;

    static async show(chargeFormItem: ChargeFormItem): Promise<any> {
        return ABCNavigator.showBottomSheet(<AstExecuteDialog chargeFormItem={chargeFormItem} />);
    }

    constructor(props: AstExecuteDialogProps) {
        super(props);

        const self = userCenter.employee;
        this.astResult = props.chargeFormItem.astResult?.result ?? "阴性";
        this.astDescription =
            props.chargeFormItem.astResult?.description ?? `执行人：${self?.name}，时间：${new Date().format("yyyy-MM-dd")}`;
    }

    _submitExecute(): void {
        ABCNavigator.pop(
            JsonMapper.deserialize(AstExecuteDialogResult, {
                result: this.astResult,
                description: this.astDescription,
            })
        );
    }

    renderMedicineInfo(): JSX.Element {
        const { chargeFormItem } = this.props;
        return (
            <View
                style={[
                    ABCStyles.bottomLine,
                    { flexDirection: "row", marginHorizontal: Sizes.listHorizontalMargin, paddingVertical: Sizes.listHorizontalMargin },
                ]}
            >
                <Text style={[TextStyles.t16MB, { flexShrink: 1 }]} numberOfLines={1}>
                    {chargeFormItem.displayName}
                </Text>
                <SizedBox width={Sizes.dp4} />
                <Text style={TextStyles.subTitleStyle2}>{chargeFormItem.goodsInfo.packageSpec}</Text>
            </View>
        );
    }

    renderSkinTest(): JSX.Element {
        return (
            <_ExecuteListItemView title={"皮试结果"}>
                <View style={[ABCStyles.rowAlignCenter, { justifyContent: "space-between" }]}>
                    <GridView itemHeight={Sizes.dp36} crossAxisCount={2} crossAxisSpacing={Sizes.dp8} mainAxisSpacing={Sizes.dp8}>
                        {["阴性", "阳性"]?.map((item, index) => {
                            return (
                                <AstOptionButton
                                    key={index.toString()}
                                    style={{
                                        backgroundColor: item === this.astResult ? Colors.theme2Mask8 : Colors.bg1,
                                    }}
                                    select={item === this.astResult}
                                    value={item!}
                                    onChanged={(item) => {
                                        this.astResult = item;
                                        this.setState({});
                                    }}
                                    editable={true}
                                />
                            );
                        })}
                    </GridView>
                </View>
            </_ExecuteListItemView>
        );
    }

    renderExecuteCount(): JSX.Element {
        return (
            <_ExecuteListItemView title={"备注"}>
                <View style={{ flex: 1, justifyContent: "center", borderRadius: Sizes.dp4, backgroundColor: Colors.bg1 }}>
                    <View style={{ flex: 1, padding: Sizes.dp12 }}>
                        <AbcTextInput
                            style={{ flex: 1, height: this.inputSize, ...TextStyles.t14NT1 }}
                            customPanelBuilder={() => <SimpleFinishPanel />}
                            defaultValue={this.astDescription}
                            placeholder={""}
                            selectTextOnFocus={false}
                            onContentSizeChange={(event) => {
                                this.inputSize = event.contentSize.height;
                                this.forceUpdate();
                            }}
                            onChangeText={(text) => {
                                this.astDescription = text;
                            }}
                        />
                    </View>
                </View>
            </_ExecuteListItemView>
        );
    }

    render(): JSX.Element {
        return (
            <AbcBasePanel>
                {BottomSheetHelper.createTitleBar("皮试", true)}
                {this.renderMedicineInfo()}
                <SizedBox height={Sizes.listHorizontalMargin} />
                {this.renderSkinTest()}
                {this.renderExecuteCount()}
                <ToolBar>
                    <ToolBarButtonStyle1
                        style={TextStyles.t16NW}
                        onClick={() => {
                            this._submitExecute();
                        }}
                        text={"确定"}
                    />
                </ToolBar>
                <SafeAreaBottomView bottomSafeAreaColor={Colors.white} />
            </AbcBasePanel>
        );
    }
}

interface AstOptionButtonProps extends AbcViewProps {
    select: boolean;
    value: string;
    editable?: boolean;
    onChanged?(value: string): void;
}

const styles = ABCStyleSheet.create({
    processButton: {
        width: pxToDp(168),
        borderRadius: Sizes.dp4,
        paddingVertical: Sizes.dp9,
    },
});

class AstOptionButton extends BaseComponent<AstOptionButtonProps> {
    constructor(props: AstOptionButtonProps) {
        super(props);
    }

    render() {
        const { select, value, style, editable, onChanged, ...otherProps } = this.props;
        return (
            <View {...otherProps} style={[styles.processButton, {}, flattenStyles(style) ?? {}]} onClick={() => onChanged?.(value)}>
                <Text
                    style={[
                        TextStyles.t14NT2.copyWith({
                            color: select ? Colors.mainColor : editable ? Colors.t2 : Colors.T4,
                        }),
                        { flex: 1, textAlign: "center" },
                    ]}
                >
                    {value}
                </Text>
            </View>
        );
    }
}
