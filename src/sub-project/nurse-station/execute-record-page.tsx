/**
 * create by deng<PERSON>e
 * desc:
 * create date 2020/6/11
 */

import React from "react";
import { ChargeFormItem, ChargeInvoiceDetailData } from "../charge/data/charge-beans";
import { Divider<PERSON>ine, SizedBox, Spacer, UniqueKey } from "../base-ui";
import { EffectItem, ExecuteRecordItem, GetExecuteRecordRsp, PrescriptionChargeExecuteForms } from "./data/nurse-station-data";
import { ListView, Text, View } from "@hippy/react";
import { TimeUtils } from "../common-base-module/utils";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { BlocBuilder } from "../bloc";
import { ExecuteRecordPageBloc } from "./execute-record-page-bloc";
import { ABCNetworkPageContentStatus, BaseBlocNetworkPage } from "../base-ui/base-page";
import { BaseComponent } from "../base-ui/base-component";
import { AssetImageView } from "../base-ui/views/asset-image-view";
import { AbcText } from "../base-ui/views/abc-text";
import { ImageUpdateView } from "../base-ui/image-update-view";
import { ExecuteAttachmentItem } from "./data/bean";
import { ABCUtils } from "../base-ui/utils/utils";
import _ from "lodash";
import { DeviceUtils } from "../base-ui/utils/device-utils";

interface ExecuteRecordPageProps {
    detailData: ChargeInvoiceDetailData;
}

export default class ExecuteRecordPage extends BaseBlocNetworkPage<ExecuteRecordPageProps, ExecuteRecordPageBloc> {
    constructor(props: ExecuteRecordPageProps) {
        super(props);
        this.bloc = new ExecuteRecordPageBloc(props.detailData);
    }

    getAppBar(): JSX.Element {
        return <BlocBuilder bloc={this.bloc} condition={(/*preState, newState*/) => true} build={(/*state*/) => super.getAppBar()} />;
    }

    getAppBarTitle(): string {
        return "执行记录";
    }

    componentDidMount(): void {
        super.componentDidMount();

        this.setContentStatus(ABCNetworkPageContentStatus.loading, null);
        const stateObserver = this.bloc.state.subscribe((state) => {
            let status = ABCNetworkPageContentStatus.show_data;
            let error: any;
            if (state.loading) {
                status = ABCNetworkPageContentStatus.loading;
            } else if (state.loadError) {
                status = ABCNetworkPageContentStatus.error;
                error = state.loadError;
            }

            this.setContentStatus(status, error);
        });

        this.addDisposable(stateObserver);
    }

    reloadData(): void {
        this.bloc.requestReload();
    }

    renderContent(): JSX.Element {
        if (!this.bloc.currentState.detailData) return <View />;
        const state = this.bloc.currentState;
        const _view: JSX.Element[] = state.items.map((item, index, self) => (
            <_ExecuteItemView
                key={index}
                editMode={state.editing}
                executeRecordItem={item}
                prescriptionChargeExecuteForms={item.prescriptionChargeExecuteForms}
                bottomLine={index < self.length - 1}
            />
        ));
        _view.push(<_ExecuteItemView creator={state.executeDetail} />);
        const isOhos = DeviceUtils.isOhos();
        return (
            <View style={{ ...(isOhos ? {} : { backgroundColor: Colors.white }), flex: 1 }}>
                <ListView
                    style={{ flex: 1 }}
                    numberOfRows={_view.length}
                    dataSource={_view}
                    scrollEventThrottle={300}
                    getRowKey={(index) => {
                        //撤销的状态变更
                        try {
                            const info = state.items[index];
                            return `${info.id}${info.status}`;
                        } catch (e) {
                            return UniqueKey();
                        }
                    }}
                    renderRow={(item) => item}
                    showScrollIndicator={false}
                />
            </View>
        );
    }
}

interface _ExecuteItemViewProps {
    creator?: GetExecuteRecordRsp;
    executeRecordItem?: ExecuteRecordItem;
    prescriptionChargeExecuteForms?: PrescriptionChargeExecuteForms[];
    chargeFormItem?: ChargeFormItem;
    editMode?: boolean;
    bottomLine?: boolean;
}

enum _ExecuteItemViewMode {
    execute,
    record,
    create,
}

class _ExecuteItemView extends BaseComponent<_ExecuteItemViewProps> {
    private _mode: _ExecuteItemViewMode;
    constructor(props: _ExecuteItemViewProps) {
        super(props);
        if (props.creator) {
            this._mode = _ExecuteItemViewMode.create;
        } else {
            this._mode = props.executeRecordItem?.status ?? _ExecuteItemViewMode.execute;
        }
    }

    static contextType = ExecuteRecordPageBloc.Context;

    createItem(title: string, content?: string): JSX.Element {
        if (!content) return <View />;
        return (
            <View style={{ flexDirection: "row", marginHorizontal: Sizes.dp8 }}>
                <Text style={[TextStyles.t16NT2.copyWith({ color: Colors.t2 }).copyWith({ color: Colors.t2 }), { lineHeight: Sizes.dp24 }]}>
                    {`${title}：`}
                </Text>
                <Text style={[TextStyles.t16NT2.copyWith({ color: Colors.t2 }), { flexShrink: 1, lineHeight: Sizes.dp24 }]}>{content}</Text>
            </View>
        );
    }

    // 处方项目
    createPrescriptionItem(title: string, content?: PrescriptionChargeExecuteForms[]): JSX.Element {
        if (!content) return <View />;
        const prescriptionItemsView: JSX.Element[] = [];
        const indexList: number[] = [];
        let _sortIndex = 0;

        content
            ?.map((forms, index) => {
                const titlePrefix =
                    content.length > 1
                        ? `${forms.sourceFormTypeName}${_.indexOf(indexList, _sortIndex) && ABCUtils.toChineseNum(_sortIndex + 1)} `
                        : `${forms.sourceFormTypeName}`;

                indexList.push(index);
                _sortIndex++;
                forms?.chargeFormGroups?.map((groups, index2) => {
                    prescriptionItemsView.push(
                        <View key={index}>
                            {index2 == 0 && (
                                <Text
                                    style={[
                                        TextStyles.t16NT2.copyWith({ color: Colors.t2 }),
                                        ABCStyles.rowAlignCenter,
                                        { lineHeight: Sizes.dp24 },
                                    ]}
                                >
                                    {`${titlePrefix}`}
                                </Text>
                            )}

                            <Text
                                style={[
                                    TextStyles.t16NT2.copyWith({ color: Colors.t2 }),
                                    { flex: 1, flexShrink: 1, lineHeight: Sizes.dp24, paddingBottom: Sizes.dp4 },
                                ]}
                            >
                                {`${groups.groupId ? ABCUtils.toCircledNum(groups.groupId) : ""}${groups.usage}${groups.count}次：${
                                    groups.groupItems
                                } ${
                                    groups.groupItems?.length && groups.groupItems?.length > 1 ? `等${groups.groupItems?.length}种药` : ""
                                }`}
                            </Text>
                        </View>
                    );
                });
            })
            .join("、");

        return (
            <View style={{ flexDirection: "row", marginLeft: Sizes.dp8 }}>
                <Text style={[TextStyles.t16NT2.copyWith({ color: Colors.t2 }), ABCStyles.rowAlignCenter, { lineHeight: Sizes.dp24 }]}>
                    {`${title}：`}
                </Text>
                <View style={{ flex: 1, marginBottom: Sizes.dp4 }}>{prescriptionItemsView}</View>
            </View>
        );
    }

    createImageGroup(title: string, content?: ExecuteAttachmentItem[]): JSX.Element {
        if (!content?.length) return <View />;
        return (
            <View style={{ flexDirection: "row", marginHorizontal: Sizes.dp8 }}>
                <Text style={[TextStyles.t16NT2.copyWith({ color: Colors.t2 }), { lineHeight: Sizes.dp24 }]}>{`${title}：`}</Text>
                <View style={{ flex: 1 }}>
                    <ImageUpdateView
                        contentStyle={{ flex: undefined }}
                        ableUpdate={false}
                        imageSize={Sizes.dp60}
                        imageList={content}
                        showAddIcon={false}
                    />
                </View>
            </View>
        );
    }

    createRecodeView(): JSX.Element {
        const { executeRecordItem } = this.props;
        return (
            <View style={[ABCStyles.rowAlignCenter, { marginHorizontal: Sizes.dp8 }]}>
                <Text style={[TextStyles.t16NT2.copyWith({ color: Colors.t2 }), { lineHeight: Sizes.dp24 }]}>
                    {`${executeRecordItem?.canceledEmployeeName}`}
                </Text>
                <Text style={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}>：</Text>
                <Text style={[TextStyles.t16NT2.copyWith({ color: Colors.t2 }), { flexShrink: 1, lineHeight: Sizes.dp24 }]}>
                    {`在${TimeUtils.formatDatetimeAsRecent(executeRecordItem?.lastModifiedDate, {
                        year: false,
                        time: false,
                    })}撤销了此次执行`}
                </Text>
            </View>
        );
    }

    renderEffectsItem(item: EffectItem, showTopLine: boolean, showBottomLine = true): JSX.Element {
        return (
            <View key={item.id}>
                {showTopLine && <DividerLine lineHeight={Sizes.dpHalf} lineMargin={Sizes.dp8} />}
                {this.createItem("方法", item.treatmentMethod)}
                {this.createItem("部位", item.treatmentSite)}
                {this.createItem("反应", item.treatmentResponse)}
                {this.createItem("病因", item.etiologyPathogenesis)}
                {this.createItem("结果", item.treatmentResult)}
                {this.createImageGroup("附件", item.attachments)}
                {showBottomLine && <DividerLine lineHeight={Sizes.dpHalf} lineMargin={Sizes.dp8} />}
            </View>
        );
    }

    render() {
        const state = ExecuteRecordPageBloc.fromContext(this.context).currentState;
        const { executeRecordItem, prescriptionChargeExecuteForms, creator } = this.props;

        return (
            <View style={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp16, Sizes.dp16, Sizes.dp8), { backgroundColor: Colors.white }]}>
                <View
                    style={[
                        ABCStyles.rowAlignCenter,
                        Sizes.paddingLTRB(Sizes.dp4, 0, Sizes.dp12, 0),
                        { backgroundColor: Colors.D2, borderRadius: Sizes.dp16, minHeight: Sizes.dp32 },
                    ]}
                >
                    {this._mode == _ExecuteItemViewMode.execute && (
                        <AssetImageView name={"treat_1"} style={{ width: Sizes.dp24, height: Sizes.dp24 }} />
                    )}
                    {this._mode == _ExecuteItemViewMode.record && (
                        <AssetImageView name={"treat_2"} style={{ width: Sizes.dp24, height: Sizes.dp24 }} />
                    )}
                    {this._mode == _ExecuteItemViewMode.create && (
                        <AssetImageView name={"treat_3"} style={{ width: Sizes.dp24, height: Sizes.dp24 }} />
                    )}
                    <SizedBox width={Sizes.dp8} />
                    <Text style={[this._mode == _ExecuteItemViewMode.record ? TextStyles.t14NT2 : TextStyles.t14NT1]}>
                        {this._mode == _ExecuteItemViewMode.create
                            ? TimeUtils.formatDatetimeAsRecent(creator?.chargeSheetCreated)
                            : TimeUtils.formatDatetimeAsRecent(executeRecordItem?.executeDate)}
                    </Text>
                    <Spacer />
                    {this._mode != _ExecuteItemViewMode.create &&
                        (this._mode == _ExecuteItemViewMode.execute ? (
                            executeRecordItem?.canModify ? (
                                <AbcText
                                    style={[TextStyles.t14NB1]}
                                    onClick={() => {
                                        ExecuteRecordPageBloc.fromContext(this.context).requestUndoExecuteRecord(executeRecordItem!);
                                    }}
                                >
                                    {"编辑"}
                                </AbcText>
                            ) : (
                                <View />
                            )
                        ) : (
                            <Text style={[TextStyles.t14NT2]}>{"已撤销"}</Text>
                        ))}
                </View>
                <SizedBox height={Sizes.dp8} />
                {this._mode == _ExecuteItemViewMode.record && this.createRecodeView()}
                {this._mode == _ExecuteItemViewMode.create && this.createItem("开单", `${creator?.defaultOwnerAndDepartmentName}`)}
                {this._mode == _ExecuteItemViewMode.create && this.createItem("登记", creator?.chargeSheetCreatedByName)}
                {this._mode == _ExecuteItemViewMode.create &&
                    state.showChainName &&
                    this.createItem("门店", creator?.chargeSheetCreateClinicName)}
                {this._mode != _ExecuteItemViewMode.create &&
                    this.createItem(
                        "执行",
                        `${executeRecordItem?.executors?.map((item) => item.name).join("，")}${
                            executeRecordItem?.needHomeCare ? "(上门)" : ""
                        }`
                    )}
                {this._mode != _ExecuteItemViewMode.create && this.createItem("登记", executeRecordItem?.createdByName)}
                {this._mode != _ExecuteItemViewMode.create &&
                    state.showChainName &&
                    this.createItem("门店", executeRecordItem?.executeClinicName)}
                {this._mode != _ExecuteItemViewMode.create &&
                    this.createItem("项目", executeRecordItem?.items?.map((item) => `${item.executeItemName}(${item.count})`).join("，"))}
                {this._mode != _ExecuteItemViewMode.create &&
                    !!executeRecordItem?.needHomeCare &&
                    this.createItem("地点", executeRecordItem.homeCareAddress)}
                {this._mode != _ExecuteItemViewMode.create &&
                    !!executeRecordItem?.needHomeCare &&
                    this.createItem(
                        "时间",
                        `${executeRecordItem.homeCareStartTime?.format("MM-dd HH:mm")}至${executeRecordItem.homeCareEndTime?.format(
                            "MM-dd HH:mm"
                        )}`
                    )}
                {this._mode != _ExecuteItemViewMode.create && this.createPrescriptionItem("项目", prescriptionChargeExecuteForms)}
                {executeRecordItem?.effects?.map((item, index, self) =>
                    this.renderEffectsItem(item, !index, index != self.length - 1 || !!executeRecordItem?.executeEffect)
                )}
                {this._mode != _ExecuteItemViewMode.create && this.createItem("备注", executeRecordItem?.executeEffect)}
            </View>
        );
    }
}
