/**
 * create by deng<PERSON><PERSON>
 * desc: 执行人搜索列表
 * create date 2020/8/31
 */
import React from "react";
import { ListView, Text, View } from "@hippy/react";
import { IconFontView, SizedBox, Spacer, ToolBarButtonStyle1 } from "../base-ui";
import { Subject } from "rxjs";
import { _EmployeeSimpleInfo, SearchExecutorPageBloc } from "./search-executor-page-bloc";
import { ABCNetworkPageContentStatus, BaseBlocNetworkPage } from "../base-ui/base-page";
import { AppSearchBar } from "../base-ui/app-bar";
import { ABCStyles, ABCStyleSheet, Colors, Sizes, TextStyles } from "../theme";
import { AbcCheckbox } from "../base-ui/views/abc-checkbox";
import sizes from "../theme/sizes";
import { DeviceUtils } from "../base-ui/utils/device-utils";
import { Abc<PERSON>ie<PERSON> } from "../base-ui/views/abc-view";
import { KeyboardHeightIOSView } from "../base-ui/views/keyboard-listener-view";
import { AbcTextInput } from "../base-ui/views/abc-text-input";
import { EmployeeSimpleInfo } from "../base-business/data/clinic-agent";
import { ignore } from "../common-base-module/global";
import { showBottomPanel } from "../base-ui/abc-app-library/panel";
import { pxToDp } from "../base-ui/utils/ui-utils";
import { BaseBlocComponent } from "../base-ui/base-component";
import { AbcBasePanel } from "../base-ui/abc-app-library";

const styles = ABCStyleSheet.create({
    itemStyle: {
        ...ABCStyles.rowAlignCenter,
        ...ABCStyles.bottomLine,
        ...ABCStyles.listHorizontalPadding,
        backgroundColor: Colors.white,
        height: Sizes.listItemHeight,
    },
});
interface SearchExecutorPageProps {
    placeholder?: string;
    employees?: EmployeeSimpleInfo[];
}

export class SearchExecutorPage extends BaseBlocNetworkPage<SearchExecutorPageProps, SearchExecutorPageBloc> {
    _loadDataTrigger = new Subject();

    constructor(props: SearchExecutorPageProps) {
        super(props);
        this.bloc = new SearchExecutorPageBloc(props.employees);
    }

    componentDidMount(): void {
        this.bloc.state
            .subscribe((state) => {
                let status = ABCNetworkPageContentStatus.show_data;
                if (state.loading) {
                    status = ABCNetworkPageContentStatus.loading;
                } else if (state.loadError) {
                    status = ABCNetworkPageContentStatus.error;
                } else if (!state.matchEmployees.length) {
                    status = ABCNetworkPageContentStatus.empty;
                }

                this.setContentStatus(status, state.loadError);
            })
            .addToDisposableBag(this);
    }

    reloadData(): void {
        this.bloc.requestReloadData();
    }

    getAppBar(): any {
        return (
            <AppSearchBar
                placeholder={this.props.placeholder ?? "输入执行人姓名"}
                rightPart={this.getRightAppBarIcons()}
                onBackClick={this.onBackClick.bind(this)}
                enableDefaultToolBar={false}
                autoFocus={true}
                onFocus={() => this.forceUpdate()}
                onBlur={() => this.forceUpdate()}
                onChangeText={(value) => this.bloc.requestSearch(value)}
            />
        );
    }

    public renderContent(): JSX.Element {
        const { matchEmployees } = this.bloc.currentState;
        return (
            <View style={{ flex: 1 }}>
                <ListView
                    style={{ flex: 1 }}
                    dataSource={matchEmployees}
                    numberOfRows={matchEmployees.length}
                    getRowKey={(index) => index.toString()}
                    renderRow={(data, unknown, index) => this._renderRow(data, index)}
                    scrollEventThrottle={300}
                />
                <View
                    style={[
                        Sizes.paddingLTRB(sizes.listHorizontalMargin, sizes.dp12, 0, sizes.dp12),
                        { flexDirection: "row", backgroundColor: Colors.white, borderTopWidth: 1, borderColor: Colors.dividerLineColor },
                    ]}
                >
                    <ToolBarButtonStyle1
                        style={TextStyles.t16NW}
                        onClick={() => {
                            this.bloc.requestConfirm();
                        }}
                        text="确定"
                    />
                    {AbcTextInput.focusInput?.focused && DeviceUtils.isIOS() ? (
                        <AbcView
                            style={{ width: Sizes.dp56, justifyContent: "center", alignItems: "center" }}
                            onClick={() => {
                                AbcTextInput.focusInput?.blur();
                            }}
                        >
                            <IconFontView name={"Dropdown_Triangle"} size={Sizes.dp28} color={Colors.P1} />
                        </AbcView>
                    ) : (
                        <View style={{ width: Sizes.listHorizontalMargin }} />
                    )}
                </View>
                <KeyboardHeightIOSView />
            </View>
        );
    }

    private _renderRow(data: _EmployeeSimpleInfo, _ignore: number | undefined) {
        ignore(_ignore);
        return (
            <View style={styles.itemStyle} onClick={() => this.bloc.requestSelectItem(data)}>
                <AbcCheckbox check={data.selected} onChange={() => this.bloc.requestSelectItem(data)} />
                <SizedBox width={Sizes.dp9} />
                <Text style={TextStyles.t14NT1}>{data.name}</Text>
            </View>
        );
    }
}

/**
 * 新的执行人搜索列表弹窗
 */
interface SearchExecutorDialogProps {
    placeholder?: string;
    employees?: EmployeeSimpleInfo[];
}

export class SearchExecutorDialog extends BaseBlocComponent<SearchExecutorPageProps, SearchExecutorPageBloc> {
    static async show(params?: SearchExecutorDialogProps): Promise<EmployeeSimpleInfo[]> {
        return showBottomPanel(<SearchExecutorDialog {...params} />);
    }

    _loadDataTrigger = new Subject();

    constructor(props: SearchExecutorPageProps) {
        super(props);
        this.bloc = new SearchExecutorPageBloc(props.employees);
    }

    // componentDidMount(): void {
    //     this.bloc.state
    //         .subscribe((state) => {
    //             let status = ABCNetworkPageContentStatus.show_data;
    //             if (state.loading) {
    //                 status = ABCNetworkPageContentStatus.loading;
    //             } else if (state.loadError) {
    //                 status = ABCNetworkPageContentStatus.error;
    //             } else if (!state.matchEmployees.length) {
    //                 status = ABCNetworkPageContentStatus.empty;
    //             }
    //
    //             this.setContentStatus(status, state.loadError);
    //         })
    //         .addToDisposableBag(this);
    // }

    reloadData(): void {
        this.bloc.requestReloadData();
    }

    public renderContent(): JSX.Element {
        const { matchEmployees } = this.bloc.currentState;
        const rightPartViews: JSX.Element[] = [
            <AbcView
                key={"submit"}
                style={{ paddingVertical: Sizes.dp10, flexDirection: "row" }}
                onClick={() => {
                    this.bloc.requestConfirm();
                }}
            >
                <Text style={TextStyles.t14NM2}>
                    {`确定(${this.bloc.currentState.allEmployees.filter((item) => item.selected).length ?? 0})`}
                </Text>
            </AbcView>,
        ];

        return (
            <AbcBasePanel panelStyle={{ flex: 1, height: pxToDp(652), backgroundColor: Colors.S2 }}>
                <AppSearchBar
                    style={Sizes.marginLTRB(Sizes.listHorizontalMargin, Sizes.listHorizontalMargin, Sizes.dp6)}
                    showBackIcon={false}
                    bottomLine={false}
                    placeholder={this.props.placeholder ?? "输入执行人姓名"}
                    rightPart={rightPartViews}
                    // onBackClick={this.onBackClick.bind(this)}
                    enableDefaultToolBar={false}
                    autoFocus={true}
                    onFocus={() => this.forceUpdate()}
                    onBlur={() => this.forceUpdate()}
                    onChangeText={(value) => this.bloc.requestSearch(value)}
                />
                <ListView
                    style={{ flex: 1 }}
                    dataSource={matchEmployees}
                    numberOfRows={matchEmployees.length}
                    getRowKey={(index) => index.toString()}
                    renderRow={(data, unknown, index) => this._renderRow(data, index)}
                    scrollEventThrottle={300}
                />

                <KeyboardHeightIOSView />
            </AbcBasePanel>
        );
    }

    private _renderRow(data: _EmployeeSimpleInfo, _ignore: number | undefined) {
        ignore(_ignore);
        return (
            <AbcView
                style={[
                    ABCStyles.rowAlignCenter,
                    ABCStyles.bottomLine,
                    { marginHorizontal: Sizes.listHorizontalMargin, paddingVertical: Sizes.listHorizontalMargin },
                ]}
                onClick={() => this.bloc.requestSelectItem(data)}
            >
                <SizedBox width={Sizes.dp8} />
                <Text style={[TextStyles.t16NT1, { width: Sizes.dp96 }]}>{data.name}</Text>
                <Spacer />
                {data.selected ? (
                    <IconFontView name={"Chosen"} size={Sizes.dp16} color={Colors.mainColor} />
                ) : (
                    <AbcView
                        style={{
                            width: Sizes.dp16,
                            height: Sizes.dp16,
                            borderWidth: Sizes.dpHalf,
                            borderRadius: Sizes.dp16,
                            borderColor: Colors.P1,
                        }}
                        onClick={() => this.bloc.requestSelectItem(data)}
                    />
                )}
            </AbcView>
        );
    }
}
