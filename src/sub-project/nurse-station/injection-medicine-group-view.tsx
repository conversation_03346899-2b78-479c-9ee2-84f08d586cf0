/**
 * create by deng<PERSON>e
 * desc:
 * create date 2020/6/10
 */

import React from "react";
import {
    ChargeForm,
    ChargeFormItem,
    ChargeFormItemStatus,
    ChargeInvoiceDetailData,
    ExecuteInfusionSkinTestStatus,
} from "../charge/data/charge-beans";
import { Text, View } from "@hippy/react";
import _ from "lodash";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { SizedBox, Spacer } from "../base-ui";
import { ChargeUtils } from "../charge/utils/charge-utils";
import { ABCUtils } from "../base-ui/utils/utils";
import { ExecuteChargeStatusView } from "../charge/view/charge-views";
import { ExecuteInvoicePageBloc } from "./execute-invoice-page-bloc";
import { AstItemView, ItemExecutableButton } from "./nurse-station-views";
import { Toast } from "../base-ui/dialog/toast";
import { AbcBasePanel } from "../base-ui/abc-app-library";
import { AbcCardHeader } from "../base-ui/abc-app-library/common/abc-card-header";
import { AbcView } from "../base-ui/views/abc-view";
import { TextWithErrorHint } from "../outpatient/outpatient-views";
import { PharmacyTagView } from "../outpatient/views/pharmacy-tag-view";

interface InjectionMedicineGroupViewProps {
    patientOrderDetailData: ChargeInvoiceDetailData;
    chargeForms: Array<ChargeForm>;
}

export class InjectionMedicineGroupView extends React.Component<InjectionMedicineGroupViewProps> {
    constructor(props: InjectionMedicineGroupViewProps) {
        super(props);
    }

    render(): JSX.Element {
        const { chargeForms, patientOrderDetailData } = this.props;
        const views: JSX.Element[] = [];
        chargeForms.forEach((item, index) => {
            let groupSuffix = "";
            if (chargeForms.length > 1) groupSuffix = ABCUtils.toChineseNum(index + 1);
            views.push(
                <_InjectionMedicineGroupItemView
                    key={index}
                    chargeForms={item}
                    index={groupSuffix}
                    departmentId={patientOrderDetailData?.departmentId}
                />
            );
        });

        if (_.isEmpty(views)) {
            return <View />;
        }
        return <View>{views}</View>;
    }
}

interface _InjectionMedicineGroupItemViewProps {
    chargeForms: ChargeForm;
    index: string;
    departmentId?: string;
}

// 输注药物组项目视图
class _InjectionMedicineGroupItemView extends React.Component<_InjectionMedicineGroupItemViewProps> {
    static contextType = ExecuteInvoicePageBloc.Context;
    constructor(props: _InjectionMedicineGroupItemViewProps) {
        super(props);
    }

    renderTextWithErrorHintInfo(options: { text: string }[]): JSX.Element[] {
        const views: JSX.Element[] = [];
        if (!options.length) return [<View key={"null"} />];

        options.map((item, index) => {
            views.push(<TextWithErrorHint key={index} text={item.text} />);
        });
        return views;
    }
    renderItem(options: {
        groupIndex?: number;
        formItem: ChargeFormItem;
        showBottomLine?: boolean;
        showTailView?: boolean;
        hasGroupId?: boolean;
    }): JSX.Element {
        const { groupIndex, formItem, showBottomLine, showTailView, hasGroupId } = options;
        const { pharmacyInfoConfig } = ExecuteInvoicePageBloc.fromContext(this.context).currentState;
        const defaultPharmacyNo = pharmacyInfoConfig?.getDefaultPharmacy({
            departmentId: this.props?.departmentId,
            goodsInfo: { typeId: options.formItem?.goodsInfo?.typeId },
        })?.no;
        let _astStatusName = "";
        let _astStatusColor = Colors.T2;
        switch (formItem.ast) {
            case ExecuteInfusionSkinTestStatus.continuedUse: {
                _astStatusName = "续用";
                _astStatusColor = Colors.theme2;
                break;
            }
            case ExecuteInfusionSkinTestStatus.noSkin: {
                _astStatusName = "免试";
                _astStatusColor = Colors.theme2;
                break;
            }
        }

        const tailViewInfo: { text: string }[] = [
            { text: `${formItem.usageInfo?.usage ?? "--"}，` },
            { text: `${formItem.usageInfo?.freq ?? "--"}，` },
            { text: formItem.usageInfo?.days != null ? `${formItem.usageInfo?.days}天，` : "--" },
            {
                text: `${
                    formItem.usageInfo?.ivgtt != null && !_.isNaN(formItem.usageInfo?.ivgtt) && formItem.usageInfo?.ivgtt != 0
                        ? `${formItem.usageInfo?.ivgtt}${formItem.usageInfo?.ivgttUnit}`
                        : "--"
                }`,
            },
        ];

        const tailView = (
            <AbcView
                style={[
                    ABCStyles.rowAlignCenter,
                    Sizes.paddingLTRB(Sizes.dp26, Sizes.dp10, Sizes.dp26, Sizes.dp10),
                    { marginTop: Sizes.dp16, borderRadius: Sizes.dp3, backgroundColor: Colors.bg1 },
                ]}
            >
                {this.renderTextWithErrorHintInfo(tailViewInfo)}
            </AbcView>
        );

        const hasGroupIdWidth = Sizes.dp26; // 有分组id时，需要预留的宽度

        return (
            <View
                key={formItem.id}
                style={[
                    showBottomLine ? ABCStyles.bottomLine : {},
                    Sizes.marginLTRB(Sizes.listHorizontalMargin, 0, Sizes.listHorizontalMargin, Sizes.listHorizontalMargin),
                    { paddingBottom: Sizes.dp16 },
                ]}
            >
                <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                    <View style={{ width: hasGroupId ? hasGroupIdWidth : undefined }}>
                        {!!groupIndex && (
                            <View style={{ marginRight: !!formItem.groupId ? Sizes.dp10 : undefined }}>
                                <Text style={[TextStyles.t16NM, { textAlign: "center" }]}>{ABCUtils.toCircledNum(groupIndex ?? 99)}</Text>
                            </View>
                        )}
                    </View>
                    <Text style={[TextStyles.t16NB, { flexShrink: 1 }]} numberOfLines={1}>
                        {formItem.name ?? ""}
                    </Text>
                    <Text style={[TextStyles.t14NT3.copyWith({ color: Colors.t3 }), { marginLeft: Sizes.dp4 }]} numberOfLines={1}>
                        {`${formItem.goodsInfo?.packageSpec ?? ""}`}
                    </Text>
                    {pharmacyInfoConfig?.isOpenMultiplePharmacy && (
                        <PharmacyTagView
                            style={{ marginLeft: Sizes.dp4 }}
                            pharmacyNo={formItem.pharmacyNo}
                            defaultPharmacyNo={defaultPharmacyNo}
                        />
                    )}
                    <Spacer />
                    <SizedBox width={Sizes.dp4} />
                    {formItem.ast == 1 && <AstItemView key={formItem.id} formItem={formItem} />}
                </View>

                <View style={[ABCStyles.rowAlignCenter, { marginTop: Sizes.dp6, flexShrink: 1 }]}>
                    {hasGroupId && <SizedBox width={hasGroupIdWidth} />}
                    {!!_astStatusName && <Text style={[TextStyles.t14NT1, { color: _astStatusColor }]}>{_astStatusName}</Text>}
                    {!!formItem?.usageInfo?.dosage && !!formItem.usageInfo?.dosageUnit && (
                        <Text
                            style={[TextStyles.t14NT1, { marginLeft: !!_astStatusName ? Sizes.dp12 : 0, flexShrink: 1 }]}
                            numberOfLines={1}
                        >
                            {`${formItem?.usageInfo?.dosage}${formItem.usageInfo?.dosageUnit}/次`}
                        </Text>
                    )}
                    {(!!formItem.specialRequirement || !!formItem.remark) && (
                        <Text style={[TextStyles.t14NT1, { marginLeft: Sizes.dp12, flexShrink: 1 }]} numberOfLines={1}>
                            {`${formItem.specialRequirement ?? formItem.remark ?? ""}`}
                        </Text>
                    )}
                    <Spacer />
                    {!!formItem.unitCount && (
                        <Text style={[TextStyles.t14NT1, { marginLeft: Sizes.dp16 }]}>
                            {`x${formItem.unitCount}${formItem.unit ?? ""}`}
                        </Text>
                    )}
                </View>
                {(formItem?.usageInfo?.executedTotalCount ?? 0) > 0 && (
                    <View style={[ABCStyles.rowAlignCenter, { marginTop: Sizes.dp4, justifyContent: "flex-end" }]}>
                        {hasGroupId && <SizedBox width={hasGroupIdWidth} />}
                        {!!formItem?.needExecutive && (
                            <Text style={[TextStyles.t14NT1, { lineHeight: Sizes.dp20 }]} numberOfLines={1}>
                                {`执行进度：${Math.round(formItem?.executedUnitCount ?? 0)}/${Math.round(
                                    formItem?.usageInfo?.executedTotalCount ?? 0
                                )}次`}
                            </Text>
                        )}
                    </View>
                )}
                {showTailView && tailView}
            </View>
        );
    }

    renderGroup(items: ChargeFormItem[], index: number, showBottomLine?: boolean) {
        const views: JSX.Element[] = [];
        items?.forEach((formItem, index) => {
            views.push(
                this.renderItem({
                    groupIndex: index == 0 && !!_.first(items)?.groupId ? _.first(items)?.groupId : undefined,
                    formItem: formItem,
                    showBottomLine: showBottomLine || index < items.length - 1,
                    showTailView: index + 1 == items.length,
                    hasGroupId: !!items.find((item) => item.groupId),
                })
            );
        });
        return <View key={index}>{views}</View>;
    }

    render() {
        const { chargeForms, index } = this.props;
        const views: JSX.Element[] = [];
        const groups = ChargeUtils.splitToGroupByGroupId(chargeForms.chargeFormItems!);
        groups.forEach((formItems, groupIndex) => {
            views.push(this.renderGroup(formItems, groupIndex, _.last(chargeForms.chargeFormItems)?.groupId !== groupIndex));
        });

        const { executionStationBill, hasExecuteItem, onlyExecuteAfterPaid, detailData } = ExecuteInvoicePageBloc.fromContext(
            this.context
        ).currentState;

        const _onlyExecuteAfterPaid = !onlyExecuteAfterPaid && chargeForms.status == ChargeFormItemStatus.unCharged; // 项目需在收费后可执行

        const filterChargeForm = chargeForms.chargeFormItems?.filter((f) => f.drugsUsageExecutive && f.drugsCanExecutive); // 去除用法不可执行的药品
        const _hasAstUnExecuted = !!filterChargeForm?.find((item) => item.astCanExecuted); // 项目包含未执行的皮试
        const _hasDrugsExecutiveCount = !!filterChargeForm?.find((item) => item.drugsExecutiveCount); // 项目包含有剩余可执行次数

        return (
            <AbcBasePanel panelStyle={{ marginHorizontal: Sizes.dp8, marginTop: Sizes.dp18, paddingTop: Sizes.dp20 }}>
                <AbcCardHeader
                    title={`输注处方${index}`}
                    titleStyle={TextStyles.t18MT1}
                    style={{ height: Sizes.dp32 }}
                    showCardLeftLine={false}
                    titleSuffix={() => (
                        <View style={{ marginLeft: Sizes.dp4 }}>
                            <ExecuteChargeStatusView
                                chargeStatus={
                                    ExecuteInvoicePageBloc.fromContext(this.context).currentState.invoicePartCharged
                                        ? ChargeFormItemStatus.partCharged
                                        : chargeForms.status
                                }
                                showChargedStatus={true}
                            />
                        </View>
                    )}
                    cardLeftLineSpace={Sizes.dp8}
                    padding={0}
                    rightRender={() => {
                        return (
                            <ItemExecutableButton
                                style={{ marginRight: Sizes.dp16 }}
                                isShow={executionStationBill && detailData?.statusName == "待执行" && _hasDrugsExecutiveCount}
                                isExecutable={
                                    chargeForms.status != ChargeFormItemStatus.refunded &&
                                    hasExecuteItem &&
                                    !_hasAstUnExecuted &&
                                    !_onlyExecuteAfterPaid
                                }
                                onClick={() => {
                                    if (_onlyExecuteAfterPaid) {
                                        Toast.show("收费后才可执行").then();
                                        return;
                                    } else if (_hasAstUnExecuted) {
                                        Toast.show("请先完成皮试后才可执行").then();
                                        return;
                                    }
                                    ExecuteInvoicePageBloc.fromContext(this.context).requestExecute(chargeForms);
                                }}
                            />
                        );
                    }}
                />
                <View style={{ backgroundColor: Colors.white, marginTop: Sizes.dp8 }}>{views}</View>
            </AbcBasePanel>
        );
    }
}
