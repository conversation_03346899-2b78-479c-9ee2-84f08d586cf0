/**
 * create by deng<PERSON>e
 * desc:
 * create date 2020/6/9
 */

import { Bloc, BlocEvent } from "../bloc";
import React from "react";
import { actionEvent, EventName } from "../bloc/bloc";
import {
    ChargeConfig,
    ChargeForm,
    ChargeFormItem,
    ChargeFormItemStatus,
    ChargeInvoiceDetailData,
    ChargeInvoiceType,
    ChargeSourceFormType,
    ChargeStatus,
} from "../charge/data/charge-beans";
import { AstExecuteRsp, ExecuteSheetPrivilegeCheckRsp, PatientOrderDataAgent } from "./data/nurse-station-data";
import { Subject } from "rxjs";
import { fromPromise } from "rxjs/internal-compatibility";
import { switchMap } from "rxjs/operators";
import { ABCError } from "../common-base-module/common-error";
import { DialogIndex, showConfirmDialog, showQueryDialog } from "../base-ui/dialog/dialog-builder";
import { errorSummary, errorToStr } from "../common-base-module/utils";
import { Toast } from "../base-ui/dialog/toast";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import ExecuteRecordPage from "./execute-record-page";
import { LoadingDialog } from "../base-ui/dialog/loading-dialog";
import AstExecuteDialog from "./ast-execute-dialog";
import { ExecuteItemUsage, PatientOrderExecuteProductDialog } from "./patient-order-execute-product-dialog";
import { GoodsType, WxBindStatus } from "../base-business/data/beans";
import _ from "lodash";
import { ChargeExecuteStatus, ExecuteListUtils } from "./data/bean";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { PatientOrderExecutePage } from "./patient-order-execute-page";
import { userCenter } from "../user-center";
import { ExecuteUtils } from "./data/execute-utils";
import { GoodsUtils } from "../base-business/utils/utils";
import { PushScanSceneType, PushToPayDialog } from "../views/push-to-pay-dialog/push-to-pay-dialog";
import { CrmAgent } from "../patients/data/crm-agent";
import { ChargeAgent } from "../charge/data/charge-agent";
import { NurseStationConfig, OnlinePropertyConfigProvider } from "../data/online-property-config-provder";
import { InventoryClinicConfig } from "../inventory/data/inventory-bean";
import { ExecuteInvoiceCreatePage } from "./execute-create/execute-invoice-create-page";
import { ClinicAgent, EmployeesMeConfig } from "../base-business/data/clinic-agent";
import { onlineMessageManager } from "../base-business/msg/online-message-manager";
import { PatientOrderLockType } from "../base-business/data/patient-order/patient-order-bean";

class State {
    showLoadingAnimation = true;
    loading = false;
    loadError: any;

    detailData?: ChargeInvoiceDetailData;

    isCross = false;

    canSendToPatient?: boolean; //是否可以推送给患者
    isMeetpushPayment?: boolean; //自助支付推送的前提条件（必须开通微诊所和ABC支付）

    // 执行站配置
    nurseStationConfig?: NurseStationConfig;

    // 执行站详情权限校验
    executePrivilegeConfig?: ExecuteSheetPrivilegeCheckRsp;
    //是否有查看执行记录权限
    get isAccessible(): boolean {
        return !!this.executePrivilegeConfig?.enableShowExecutedRecords;
    }

    //多药房相关配置
    pharmacyInfoConfig?: InventoryClinicConfig;

    employeesMeConfig?: EmployeesMeConfig;
    chargeConfig?: ChargeConfig;

    // 开启后未收费单据也可执行
    get onlyExecuteAfterPaid(): boolean {
        return (this.detailData?.onlyExecuteAfterPaid ?? 0) <= 0;
    }

    // 开启后可在执行站进行开单
    get executionStationBill(): boolean {
        return (this.nurseStationConfig?.enableOpenSheet ?? 0) > 0;
    }

    //能够查看患者手机号
    get canSeePatientPhone(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.nurse?.isCanSeePatientMobile;
    }

    clone(): State {
        return Object.assign(new State(), this);
    }

    // 是否有执行记录
    get hasExecuteRecord(): boolean {
        if (this.detailData == null) return false;

        for (const form of this.detailData.chargeForms ?? []) {
            for (const formItem of form.chargeFormItems ?? []) {
                if ((formItem.executedUnitCount ?? 0) > 0) return true;

                if (formItem.composeChildren == null) continue;

                for (const subFormItem of formItem.composeChildren) {
                    if ((subFormItem.executedUnitCount ?? 0) > 0) return true;
                }
            }
        }

        return false;
    }

    /**
     * 执行单是否可被修改
     */
    get executeSheetCanModified(): boolean {
        if (this.detailData)
            if (
                this.detailData?.type == ChargeInvoiceType.therapy &&
                (this.detailData?.status as Number) == ChargeFormItemStatus.unCharged &&
                (this.detailData?.executeStatus ?? 0) <= ChargeExecuteStatus.EXECUTION_ORDER_STATUS_UNEXECUTED
            ) {
                return true;
            }
        return false;
    }

    /**
     * 执行单是否可被删除
     */
    get executeSheetCanDelete(): boolean {
        return this.executeSheetCanModified && !this.hasExecuteRecord;
    }

    /**
     * 是否含有可执行项目
     */
    get hasExecuteItem(): boolean {
        if (this.detailData == null || this.detailData.isRefundLocking) return false;

        for (const form of this.detailData.chargeForms ?? []) {
            for (const formItem of form.chargeFormItems ?? []) {
                const productType = formItem.productType;
                if (
                    productType == GoodsType.treatment ||
                    productType == GoodsType.examination ||
                    productType == GoodsType.package ||
                    productType == GoodsType.medicine ||
                    productType == GoodsType.otherGoods49
                ) {
                    if (formItem.isCompose) {
                        for (const subFormItem of formItem.composeChildren ?? []) {
                            if (this.canExecute({ formItem: subFormItem, sourceFormType: form?.sourceFormType })) return true;
                        }
                    } else {
                        if (this.canExecute({ formItem, sourceFormType: form?.sourceFormType })) return true;
                    }
                }
            }
        }

        return false;
    }

    canExecute(options: { formItem: ChargeFormItem; onlyExecuteAfterPaid?: boolean; sourceFormType?: ChargeSourceFormType }): boolean {
        const { formItem, onlyExecuteAfterPaid = !!this.detailData?.onlyExecuteAfterPaid, sourceFormType } = options;
        return ExecuteUtils.needExecutive(formItem, onlyExecuteAfterPaid, sourceFormType);
    }

    get invoicePartCharged(): boolean {
        return this.detailData?.status == ChargeStatus.partCharged;
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {}

class _EventReload extends _Event {}

class _EventExecuteTap extends _Event {
    formItem: ChargeFormItem | ChargeForm;
    parentForm?: ChargeFormItem;

    constructor(formItem: ChargeFormItem | ChargeForm, parentForm?: ChargeFormItem) {
        super();
        this.formItem = formItem;
        this.parentForm = parentForm;
    }
}

class _EventAstTap extends _Event {
    formItem: ChargeFormItem;

    constructor(formItem: ChargeFormItem) {
        super();
        this.formItem = formItem;
    }
}

class _EventViewExecuteRecord extends _Event {}

class _EventBatchExecute extends _Event {}

class _EventPushToScan extends _Event {
    patientOrderId: string;
    constructor(patientOrderId: string) {
        super();
        this.patientOrderId = patientOrderId;
    }
}

class _EventModifyInvoice extends _Event {}

class _EventDeleteInvoice extends _Event {}

class ExecuteInvoicePageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<ExecuteInvoicePageBloc | undefined>(undefined);

    private executeInvoiceId?: string;

    private _executorTrigger = new Subject();

    constructor(options: { executeInvoiceId?: string }) {
        super();
        this.executeInvoiceId = options.executeInvoiceId;
        this.dispatch(new _EventInit());
    }

    private _innerState!: State;

    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    static fromContext(context: ExecuteInvoicePageBloc): ExecuteInvoicePageBloc {
        return context;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventReload, this._mapEventReload);
        map.set(_EventExecuteTap, this._mapEventExecuteTap);
        map.set(_EventAstTap, this._mapEventAstTap);
        map.set(_EventViewExecuteRecord, this._mapEventViewExecuteRecord);
        map.set(_EventBatchExecute, this._mapEventBatchExecute); // 批量执行

        return map;
    }

    async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        this.innerState.employeesMeConfig = await ClinicAgent.getEmployeesMeConfig().catchIgnore();

        //多药房相关
        userCenter
            .getInventoryChainConfig(false)
            .catchIgnore()
            .then((rsp) => {
                this.innerState.pharmacyInfoConfig = rsp;
            });
        // 拉取执行站配置
        await OnlinePropertyConfigProvider.instance
            .getNurseStationConfig()
            .then((rsp) => {
                this.innerState.nurseStationConfig = rsp;
            })
            .catchIgnore();

        const _executorTrigger = this._executorTrigger
            .pipe(
                switchMap(() => {
                    if (this.innerState.showLoadingAnimation) {
                        this.innerState.loadError = null;
                        this.innerState.loading = true;
                        this.update();
                    }
                    return fromPromise(
                        PatientOrderDataAgent.getPatientOrderDetailInfo(this.executeInvoiceId!).catch((error) => {
                            return new ABCError(error);
                        })
                    );
                })
            )
            .subscribe(
                (res) => {
                    if (res instanceof ABCError) {
                        this.innerState.loading = false;
                        this.innerState.loadError = res;
                        this.update();
                    } else {
                        this.innerState.detailData = res;
                        this.innerState.isCross = this.innerState.detailData.clinicId != userCenter.clinic?.clinicId;
                        this.innerState.loading = false;
                        this._initPatientWxBindInfo().then();
                        this.update();
                    }
                },
                (error) => {
                    this.innerState.loading = false;
                    this.innerState.loadError = error;
                    this.update();
                }
            );
        this.addDisposable(_executorTrigger);
        PatientOrderDataAgent.patientOrderExecuteInvoicePublisher
            .subscribe(() => {
                this.innerState.showLoadingAnimation = false;
                this._executorTrigger.next();
                if (!!this.innerState.isAccessible) return; // 如果没有权限 再去效验当前是否有查看权限（减少接口调用频率）
                PatientOrderDataAgent.getExecutedOrderPermissionVerification(this.innerState.detailData?.id ?? "").then((rsp) => {
                    this.innerState.executePrivilegeConfig = rsp;
                });
            })
            .addToDisposableBag(this);

        PatientOrderDataAgent.getExecutedOrderPermissionVerification(this.executeInvoiceId ?? "").then((rsp) => {
            this.innerState.executePrivilegeConfig = rsp;
        });

        // 锁单socket接口（执行站模块只显示退费锁单）
        onlineMessageManager.patientOrderSheetLockMsgObserver.subscribe((data) => {
            if (data.key != this.innerState.detailData?.patientOrderId) return;
            if (data.businessKey != PatientOrderLockType.chargeSheet) return;
            if (data.businessKey == PatientOrderLockType.chargeSheet && data.status && !data.value?.chargeRefundOrder) return;
            this.innerState.detailData = this.innerState.detailData ?? new ChargeInvoiceDetailData();
            this.innerState.detailData.copyPatientOrderLocks = this.innerState.detailData.copyPatientOrderLocks ?? [];
            const chargeSheetRefundLock = this.innerState.detailData.copyPatientOrderLocks?.find((t) => t.businessKey == data.businessKey);
            if (!!chargeSheetRefundLock) {
                Object.assign(chargeSheetRefundLock, {
                    ...data,
                });
            } else {
                this.innerState.detailData.copyPatientOrderLocks.push(data);
            }
            this.update();
        });

        GoodsUtils.initGoodsCustomUnitIfNeed().catch((/*error*/) => false); //初始化拉取自goods的自定义单位，在这里不使用，在界面上显示自定义理疗单位时要使用
        this._executorTrigger.next();
        const isPushPaymentCondition = await ChargeAgent.canShowChargePush().catch((error) => new ABCError(error));
        if (!(isPushPaymentCondition instanceof ABCError)) this.innerState.isMeetpushPayment = isPushPaymentCondition;
        this.innerState.chargeConfig = await OnlinePropertyConfigProvider.instance.getChargeConfig(false).catchIgnore();
    }

    //判断当前患者是否可以推送
    async _initPatientWxBindInfo(): Promise<void> {
        const result = await CrmAgent.postPatientFamilyWxStatus(
            this.innerState.detailData?.patient?.chainId ?? userCenter.clinic?.chainId ?? "",
            [this.innerState.detailData?.patient?.id ?? ""]
        ).catch((/*error*/) => undefined);
        // isPush，这个为1就能推，不会管微信绑定和关注状态；要是这个isPush不为1，就再去判断这个患者是否绑定且关注微信，达到条件就可以推送
        if (result && !_.isEmpty(result.list)) {
            this.innerState.canSendToPatient =
                (result.list![0].isPush == 1 ||
                    (result.list![0].isPush != 1 && result.list![0].wxStatus == WxBindStatus.SUBSCRIBE_AND_BIND)) ??
                false;
        }
        this.update();
    }

    async *_mapEventUpdate(/*event: _EventUpdate*/): AsyncGenerator<State> {
        yield this.innerState;
    }

    async *_mapEventReload(): AsyncGenerator<State> {
        this._executorTrigger.next();
    }

    async *_loadData(): AsyncGenerator<State> {
        this._executorTrigger.next();
    }

    async *_mapEventExecuteTap(event: _EventExecuteTap): AsyncGenerator<State> {
        const _executeProductMap = new Map<ChargeFormItem, ExecuteItemUsage>();
        const _composeUsage = new Map<ChargeFormItem, ExecuteItemUsage>();
        if (event.parentForm) {
            //套餐执行
            event.parentForm.composeChildren?.forEach((composeItem) => {
                /**
                 * 删除套餐中无可执行次数的项目
                 * 不加入到执行内容的map中
                 */
                if (!ExecuteUtils.needExecutive(composeItem, !!this.innerState.detailData?.onlyExecuteAfterPaid)) return;
                _composeUsage.set(
                    composeItem,
                    JsonMapper.deserialize(ExecuteItemUsage, {
                        checked: event.formItem == composeItem,
                        count: !!composeItem?.usageInfo?.dailyDosage
                            ? Number(composeItem.usageInfo?.dailyDosage)
                            : this._getExecuteAcuPointCount(composeItem) ?? 1,
                    })
                );
            });
        }

        // 输注/成药组(全退后，该药品无需再执行)
        if (event.formItem instanceof ChargeForm) {
            event.formItem?.chargeFormItems
                ?.filter(
                    (f) =>
                        f.status != ChargeFormItemStatus.refunded &&
                        ((f?.sourceFormType__ == ChargeSourceFormType.westernPrescription &&
                            f.drugsExecutiveCount &&
                            f.drugsCanExecutive) ||
                            (f?.sourceFormType__ == ChargeSourceFormType.infusionPrescription &&
                                f.drugsExecutiveCount &&
                                f.drugsCanExecutive))
                )
                .forEach((chargeFormItem) => {
                    _executeProductMap.set(
                        chargeFormItem,
                        JsonMapper.deserialize(ExecuteItemUsage, {
                            checked: true,
                            count: !!chargeFormItem?.usageInfo?.dailyDosage
                                ? Number(chargeFormItem.usageInfo.dailyDosage)
                                : this._getExecuteAcuPointCount(chargeFormItem) ?? 1,
                        })
                    );
                });
        } else {
            _executeProductMap.set(
                event.parentForm ?? event.formItem,
                JsonMapper.deserialize(ExecuteItemUsage, {
                    count: !!(event.parentForm?.usageInfo?.dailyDosage ?? event.formItem.usageInfo?.dailyDosage)
                        ? Number(event.parentForm?.usageInfo?.dailyDosage ?? event.formItem.usageInfo?.dailyDosage)
                        : this._getExecuteAcuPointCount(event.parentForm ?? event.formItem) ?? 1,
                    checked: true,
                    composeUsage: _composeUsage,
                })
            );
        }

        let executeForm = await PatientOrderExecuteProductDialog.show(
            _executeProductMap,
            this.innerState.invoicePartCharged,
            !!this.innerState.detailData?.onlyExecuteAfterPaid
        );
        if (executeForm) {
            executeForm = ExecuteListUtils.fillCanExecuteListUtils(executeForm, this._getCanExecuteList());
            await ABCNavigator.navigateToPage(
                <PatientOrderExecutePage
                    executeProductMap={executeForm}
                    invoicePartCharged={this.innerState.invoicePartCharged}
                    patient={this.innerState.detailData?.patient}
                    isHospitalizationSheet={this.innerState.detailData?.isHospitalizationSheet}
                    detailDataId={this.innerState.detailData?.id}
                />
            );
        }
    }

    async *_mapEventAstTap(event: _EventAstTap): AsyncGenerator<State> {
        const result = await AstExecuteDialog.show(event.formItem);
        if (result) {
            const loadingDialog = new LoadingDialog("正在执行...");
            loadingDialog.show();
            try {
                const executeResult: AstExecuteRsp = await PatientOrderDataAgent.astExecute(
                    event.formItem.sourceFormItemId!,
                    result.result,
                    result.description
                );
                event.formItem.astResult = executeResult.astResult;
                this.update();
                await loadingDialog.hide();
                await Toast.show("执行成功", { success: true });
            } catch (error) {
                await loadingDialog.hide();
                await showQueryDialog("执行失败", `${errorToStr(error)}`);
            } finally {
                await loadingDialog.hide();
            }
        }
        this.update();
    }

    async *_mapEventViewExecuteRecord(): AsyncGenerator<State> {
        this.innerState.detailData && ABCNavigator.navigateToPage(<ExecuteRecordPage detailData={this.innerState.detailData!} />).then();
    }

    async *_mapEventBatchExecute(/*event: _EventBatchExecute*/): AsyncGenerator<State> {
        const _executeProductMap = this._getCanExecuteList();

        let _batchExecuteResult = await PatientOrderExecuteProductDialog.show(
            _executeProductMap,
            this.innerState.invoicePartCharged,
            !!this.innerState.detailData?.onlyExecuteAfterPaid
        );
        if (_batchExecuteResult) {
            _batchExecuteResult = ExecuteListUtils.fillCanExecuteListUtils(_batchExecuteResult, _executeProductMap);
            await ABCNavigator.navigateToPage(
                <PatientOrderExecutePage
                    executeProductMap={_batchExecuteResult}
                    invoicePartCharged={this.innerState.invoicePartCharged}
                    patient={this.innerState.detailData?.patient}
                    isHospitalizationSheet={this.innerState.detailData?.isHospitalizationSheet}
                    detailDataId={this.innerState.detailData?.id}
                />
            );
        }
    }

    @actionEvent(_EventPushToScan)
    async *_mapEventPushToScan(event: _EventPushToScan): AsyncGenerator<State> {
        await PushToPayDialog.show({
            patientOrderId: event.patientOrderId,
            type: PushScanSceneType.performStaion,
            isShowSendBtn: this.innerState.canSendToPatient,
            pushToPatient: async (chargeSheetId) => {
                this.PushOrderToPatient(chargeSheetId ?? "").then();
            },
            onPaySuccess: () => {
                PatientOrderDataAgent.patientOrderExecuteInvoicePublisher.next();
            },
        });
        this.update();
    }

    //推送支付--推送给患者
    async PushOrderToPatient(chargeSheetId: string): Promise<void> {
        if (!chargeSheetId) return;
        const result = await showQueryDialog("", "将推送至患者微信，是否确认推送？", undefined, undefined, undefined, "rgba(0,0,0,0.9)");
        if (result == DialogIndex.positive) {
            //推送
            ChargeAgent.pushOrdertoPatient(chargeSheetId)
                .then(() => {
                    Toast.show("推送成功", { success: true });
                })
                .catch((e) => {
                    showConfirmDialog("", errorSummary(e));
                });
        }
        this.update();
    }

    _getExecuteAcuPointCount(form?: ChargeFormItem): number {
        let canExecuteCount; // 处方的总执行次数 字段有所区别
        if (
            form?.sourceFormType__ == ChargeSourceFormType.infusionPrescription ||
            form?.sourceFormType__ == ChargeSourceFormType.westernPrescription
        ) {
            canExecuteCount = Math.round((form?.usageInfo?.executedTotalCount ?? 0) - (form?.executedUnitCount ?? 0));
        } else {
            canExecuteCount = Math.round((form?.unitCount ?? 0) - (form?.executedUnitCount ?? 0));
        }

        if (form?.acupoints?.length) {
            let count = 0;
            form?.acupoints.forEach((item) => {
                if (item.position == "双") {
                    count += 2;
                } else {
                    count++;
                }
            });
            return Math.min(canExecuteCount, count);
        } else {
            return Math.min(canExecuteCount, 1);
        }
    }

    _getCanExecuteList(): Map<ChargeFormItem, ExecuteItemUsage> {
        const treatments: ChargeFormItem[] = []; //治疗理疗项
        const packages: ChargeFormItem[] = []; //套餐
        const westernFormsItem: ChargeFormItem[] = []; // 成药处方
        const infusionFormsItem: ChargeFormItem[] = []; // 输注处方

        this.innerState.detailData?.chargeForms
            ?.filter((f) => !f.chargeFormItems?.find((find) => find.astCanExecuted)) // 如果处方组中含有未执行皮试项目 则改处方单中的所有药品都不会加入执行列表中
            .forEach((chargeForm) => {
                chargeForm.chargeFormItems
                    ?.filter((t) => t.status != ChargeFormItemStatus.refunded)
                    ?.forEach((formItem) => {
                        formItem.sourceFormType__ = chargeForm.sourceFormType;
                        const productType = formItem.productType;
                        if (
                            productType == GoodsType.treatment || //治疗，理疗
                            productType == GoodsType.examination || //检查检验
                            productType == GoodsType.package || //套餐
                            productType == GoodsType.material || // 物资
                            productType == GoodsType.otherGoods49 || // 其他
                            productType == GoodsType.nurseProduct || // 护理
                            formItem.drugsUsageExecutive // 有用法可执行
                        ) {
                            if (formItem.isCompose) {
                                packages.push(formItem); // 复合项目
                            } else if (
                                formItem.drugsCanExecutive && // 可执行
                                !formItem.astCanExecuted // 没有未执行的皮试
                            ) {
                                treatments.push(formItem); // 单项目
                            }
                            // (可执行 && 有用法可执行 && 有剩余可执行次数 && 无可执行的皮试)
                            if (formItem.drugsCanExecutive && formItem.drugsUsageExecutive && formItem.drugsExecutiveCount) {
                                if (chargeForm.sourceFormType == ChargeSourceFormType.infusionPrescription && !formItem.astCanExecuted) {
                                    infusionFormsItem.push(formItem); // 将可执行的输注处方药品可加入到执行项目列表中
                                } else if (chargeForm.sourceFormType == ChargeSourceFormType.westernPrescription) {
                                    westernFormsItem.push(formItem); // 将可执行的成药处方药品才可加入到执行项目列表中
                                }
                            }
                        }
                    });
            });

        const executeForm = _.concat(treatments, packages, westernFormsItem, infusionFormsItem);
        const _executeProductMap = new Map<ChargeFormItem, ExecuteItemUsage>();
        executeForm.forEach((item) => {
            const _composeUsage = new Map<ChargeFormItem, ExecuteItemUsage>();
            if (item.isCompose) {
                item.composeChildren?.forEach((composeItem) => {
                    /**
                     * 删除套餐中无可执行次数的项目
                     * 不加入到执行内容的map中
                     */
                    if (!ExecuteUtils.needExecutive(composeItem, !!this.innerState.detailData?.onlyExecuteAfterPaid)) return;

                    _composeUsage.set(
                        composeItem,
                        JsonMapper.deserialize(ExecuteItemUsage, {
                            checked: false,
                            count: !!composeItem?.usageInfo?.dailyDosage
                                ? Number(composeItem.usageInfo.dailyDosage)
                                : this._getExecuteAcuPointCount(composeItem) ?? 1,
                        })
                    );
                });
            }
            _executeProductMap.set(
                item,
                JsonMapper.deserialize(ExecuteItemUsage, {
                    count: !!item.usageInfo?.dailyDosage ? Number(item.usageInfo?.dailyDosage) : this._getExecuteAcuPointCount(item) ?? 1,
                    checked: false,
                    composeUsage: _composeUsage,
                })
            );
        });
        return _executeProductMap;
    }

    @actionEvent(_EventModifyInvoice)
    async *_mapEventModifyInvoice(/*event: _EventModifyInvoice*/): AsyncGenerator<State> {
        // TODO 修改执行单 - 跳转添加页面进行处理
        ABCNavigator.navigateToPage(
            <ExecuteInvoiceCreatePage
                patient={this.innerState.detailData?.patient}
                draftId={this.innerState.detailData?.localDraftId}
                registrationId={this.innerState.detailData?.registrationId__}
                chargeSheetId={this.innerState.detailData?.id}
            />
        ).then();
    }

    @actionEvent(_EventDeleteInvoice)
    async *_mapEventDeleteInvoice(/*event: _EventDeleteInvoice*/): AsyncGenerator<State> {
        const id = this.innerState.detailData?.id;
        if (!id) return;
        const select = await showQueryDialog("确认删除本次执行开单？", "删除后不能修复");
        if (select != DialogIndex.positive) return;
        const rsp = await PatientOrderDataAgent.deleteExecuteInvoice(id).catch((error) => new ABCError(error));
        if (rsp instanceof ABCError) {
            await Toast.show(errorToStr(rsp), { warning: true });
        } else if (rsp) {
            await Toast.show("删除成功", { success: true }).then(() => {
                ABCNavigator.pop();
            });
        } else {
            await Toast.show("删除失败", { warning: true });
        }
    }

    update(): void {
        this.dispatch(new _EventUpdate());
    }

    requestReload(): void {
        this.dispatch(new _EventReload());
    }

    //点击执行
    requestExecute(formItem: ChargeFormItem | ChargeForm, parentForm?: ChargeFormItem): void {
        this.dispatch(new _EventExecuteTap(formItem, parentForm));
    }

    //批量执行
    requestBatchExecute(): void {
        this.dispatch(new _EventBatchExecute());
    }

    //点击执行皮肤药敏测试
    requestExecuteAst(formItem: ChargeFormItem): void {
        this.dispatch(new _EventAstTap(formItem));
    }

    //查看执行记录
    requestViewExecuteRecord(): void {
        this.dispatch(new _EventViewExecuteRecord());
    }

    //推送支付
    requestPushToScan(patientOrderId: string): void {
        this.dispatch(new _EventPushToScan(patientOrderId));
    }

    /**
     * 执行单详情修改
     */
    requestModifyInvoice(): void {
        this.dispatch(new _EventModifyInvoice());
    }

    /**
     * 执行单删除
     */
    requestDeleteInvoice(): void {
        this.dispatch(new _EventDeleteInvoice());
    }
}

export { ExecuteInvoicePageBloc, State };
