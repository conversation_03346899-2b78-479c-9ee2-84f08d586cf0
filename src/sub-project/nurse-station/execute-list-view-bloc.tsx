/**
 * create by deng<PERSON>e
 * desc:
 * create date 2020/8/3
 */

import { Bloc, BlocEvent } from "../bloc";
import React from "react";
import { EventName } from "../bloc/bloc";
import { of, Subject } from "rxjs";
import { debounce, switchMap } from "rxjs/operators";
import { ABCError } from "../common-base-module/common-error";
import {
    MyExecuteListRsp,
    PatientOrderDataAgent,
    PatientOrderExecuteStatusType,
    PatientOrderSearchResult,
} from "./data/nurse-station-data";
import { Range } from "../base-ui/utils/value-holder";
import { ChargeInvoiceData } from "../charge/data/charge-beans";
import { DataHolder, DataHolderHelper, LoadingHolder } from "../views/list-data-holder";
import { ABCUtils } from "../base-ui/utils/utils";
import { ChargeExecuteStatus, ExecuteSourceType, ExecuteTabType } from "./data/bean";
import { Filters } from "../base-ui/searchBar/search-bar-bean";
import { userCenter } from "../user-center";
import _ from "lodash";
import { ExecuteInvoiceDraftManager } from "./data/execute-invoice-draft-manager";
import { OnlinePropertyConfigProvider } from "../data/online-property-config-provder";
import { delayed } from "../common-base-module/rxjs-ext/rxjs-ext";
import { EmployeeSimpleInfo, departmentInfo } from "../base-business/data/clinic-agent";
import { DentistryAgent } from "../registration/dentistry/data/dentistry-agent";
import { TimeUtils } from "../common-base-module/utils";
import { _ExecutionOrderItemHolder } from "./execute-list-view";
import { departmentNotSpecifiedItem } from "../base-ui/searchBar/search-bar";

class State {
    loading = true;
    loadError?: any;
    keyword?: string;
    sourceType: ExecuteSourceType = ExecuteSourceType.clinic; // 门店来源
    enableDraft = true;
    _patientOrderCount: Map<ExecuteSourceType, number> = new Map<ExecuteSourceType, number>();

    _dataHolders: Map<ExecuteSourceType, DataHolder<ChargeInvoiceData>[]> = new Map<ExecuteSourceType, DataHolder<ChargeInvoiceData>[]>();
    get dataHolders(): DataHolder<ChargeInvoiceData>[] {
        return this._dataHolders.get(this.sourceType) ?? [];
    }

    set dataHolders(data: DataHolder<ChargeInvoiceData>[]) {
        this._dataHolders.set(this.sourceType, data);
    }

    _filters: PatientOrderExecuteStatusType[] = [PatientOrderExecuteStatusType.EAll];
    _searchResult?: PatientOrderSearchResult;

    _timeRange?: Range<Date>;

    owner?: EmployeeSimpleInfo;
    departmentIdList?: departmentInfo[];

    //今日理疗预约数
    therapyRegistrationCount?: number;

    //草稿列表
    _draftList: ChargeInvoiceData[] = [];

    //是否开启跨店执行
    enableCross = false;

    //是否开启理疗预约
    enableTherapyAppointment = false;
    //是否存在有效预约
    existAvailableTherapyRegistration = false;

    _searchStatus?: boolean;

    /**
     * 是否显示理疗预约面板
     */
    get canShowTherapyAppointmentPanel(): boolean {
        /**
         * 开启了理疗预约或存在有效预约（可能预约后，诊所又关闭了理疗预约，也显示出来）
         */
        return (
            (!!userCenter.clinic?.isTherapist || !!userCenter.clinic?.isManager) &&
            (this.enableTherapyAppointment || this.existAvailableTherapyRegistration)
        );
    }

    get _hasMore(): boolean {
        return (
            !this._searchResult ||
            this._searchResult.fromDraft ||
            ((this._searchResult.result?.length ?? 0) == (this._searchResult.limit ?? 0) &&
                (this._searchResult.offset ?? 0) + (this._searchResult.result?.length ?? 0) < (this._searchResult.totalCount ?? 0))
        );
    }

    /**
     * 当前的查询条件是否要显示本地草稿
     */
    get _includeDraft(): boolean {
        return (
            this._timeRange == undefined &&
            (this._filters.length == 0 || (this._filters.length == 1 && this._filters[0] == PatientOrderExecuteStatusType.EAll)) &&
            this.enableDraft
        );
    }

    get isDentistry(): boolean {
        return !!userCenter.clinic?.isDentistryClinic;
    }

    clone(): State {
        return Object.assign(new State(), this);
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {
    value?: string;

    constructor(value?: string) {
        super();

        this.value = value;
    }
}

class _EventUpdate extends _Event {}

class _EventChangeFilter extends _Event {
    filter: Filters;

    constructor(filter: Filters) {
        super();
        this.filter = filter;
    }
}

class _EventSearchPatient extends _Event {
    keyword: string;

    constructor(keyword: string) {
        super();
        this.keyword = keyword;
    }
}

class _EventReload extends _Event {}

class _EventLoadMore extends _Event {}

class ExecuteListViewBloc extends Bloc<_Event, State> {
    _group?: boolean;
    _initLoad: boolean;
    _enableDraft: boolean;
    static Context = React.createContext<ExecuteListViewBloc | undefined>(undefined);
    private _loadDataTrigger: Subject<boolean> = new Subject<boolean>();
    private _loadTherapyRegistrationTrigger: Subject<number> = new Subject<number>();
    _tabType: ExecuteTabType;
    _isFromSearchPage?: boolean;

    constructor(options?: {
        group?: boolean;
        initLoad?: boolean;
        initialValue?: string;
        enableDraft?: boolean;
        tabType?: ExecuteTabType;
        sourceType?: ExecuteSourceType;
        isFromSearchPage?: boolean;
    }) {
        super();
        this._group = options?.group ?? true;
        this._initLoad = options?.initLoad ?? true;
        this._enableDraft = options?.enableDraft ?? true;
        this._tabType = options?.tabType ?? ExecuteTabType.allBill;

        if (this._tabType == ExecuteTabType.myBill) {
            this.innerState.owner = userCenter.employee as EmployeeSimpleInfo;
        }

        this.innerState.sourceType = options?.sourceType ?? this.innerState.sourceType;
        this._isFromSearchPage = options?.isFromSearchPage ?? false;

        this.dispatch(new _EventInit(options?.initialValue));
    }

    static fromContext(context: ExecuteListViewBloc): ExecuteListViewBloc {
        return context;
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);

        map.set(_EventChangeFilter, this._mapEventChangeFilter);
        map.set(_EventReload, this._mapEventReload);
        map.set(_EventSearchPatient, this._mapEventSearchPatient);
        map.set(_EventLoadMore, this._mapEventLoadMore);

        return map;
    }

    async loadDraft(): Promise<void> {
        const drafts = await ExecuteInvoiceDraftManager.instance.getAllDrafts();
        this.innerState._draftList = [];
        if (drafts) {
            this.innerState._draftList = this.innerState._draftList.concat(drafts);
        }
    }

    async _initPageConfig(initValue?: string): Promise<void> {
        this.innerState.enableDraft = this._enableDraft;
        this.innerState.dataHolders = [];
        this.innerState.keyword = initValue;
        this.loadDraft().then();
        if (!this._enableDraft) {
            this.innerState.enableCross = await OnlinePropertyConfigProvider.instance.getNurseExecuteCross().catch(() => false);
        }
        /**
         * 搜索时不用草稿，判断是否需要加载预约入口
         */
        if ((userCenter.clinic?.isTherapist || userCenter.clinic?.isManager) && this._enableDraft) {
            if (userCenter.isAllowedRegUpgrade) {
                const rsp = await DentistryAgent.queryDentistryRegistrationConfig(1).catchIgnore();
                this.innerState.enableTherapyAppointment = !!rsp?.isOpen;
            } else {
                const result = await PatientOrderDataAgent.getTherapyAppointmentSetting().catchIgnore();
                this.innerState.enableTherapyAppointment = result?.enableAppointment == 1;
            }
        }
    }
    _initPageTrigger(): void {
        PatientOrderDataAgent.patientOrderExecuteInvoicePublisher
            .subscribe(() => {
                this.dispatch(new _EventReload());
            })
            .addToDisposableBag(this);

        this._loadDataTrigger
            .pipe(
                debounce((status) => {
                    if (status) {
                        return of(0);
                    }
                    return delayed(500); // 有本地草稿时，延迟加载
                }),
                switchMap((/*_*/) => {
                    this.innerState.loading = true;
                    this.innerState.loadError = null;
                    if (this.innerState.dataHolders.length != 0) this.update();

                    let offset = 0;
                    if (this.innerState._searchResult?.offset != undefined && !this.innerState._searchResult.fromDraft) {
                        offset = this.innerState._searchResult.offset + (this.innerState._searchResult.result?.length ?? 0);
                    }
                    //第一次拉取，未收费单，先读取本地草稿
                    if (!this.innerState._searchResult && this.innerState._includeDraft) {
                        return ExecuteInvoiceDraftManager.instance.getAllDrafts().then((res) => {
                            const _result = new PatientOrderSearchResult();
                            _result.result = res.map((item) => {
                                item.statusName = "草稿";
                                return item;
                            });
                            _result.offset = 0;
                            _result.fromDraft = true;
                            return _result;
                        });
                    }
                    let executeStatus = "";
                    switch (this.innerState._filters?.[0]) {
                        case PatientOrderExecuteStatusType.EExecuted:
                            executeStatus = "executed";
                            break;
                        case PatientOrderExecuteStatusType.EUnExecuted:
                            executeStatus = "waiting";
                            break;
                        case PatientOrderExecuteStatusType.EAll:
                            break;
                    }
                    /**
                     * 我的执行单列表
                     */
                    if (this._tabType == ExecuteTabType.myExecute) {
                        return PatientOrderDataAgent.getMyExecuteList({
                            beginDate: this.innerState._timeRange?.start ?? new Date(TimeUtils.getThisMonthFirstDay()),
                            endTime: this.innerState._timeRange?.end ?? new Date(TimeUtils.getTodayEnd()),
                            limit: 20,
                            offset: offset,
                        })
                            .catch((error) => new ABCError(error))
                            .toObservable();
                    }
                    /**
                     * 执行站列表（全部开单、我的开单）
                     */
                    const nonAppointDepartmentItem = this.innerState.departmentIdList?.find(
                        (item) => item.id === departmentNotSpecifiedItem.id && item.name === departmentNotSpecifiedItem.name
                    );
                    const nonAppointDepartmentId = nonAppointDepartmentItem ? 1 : null;
                    return PatientOrderDataAgent.getPatientOrderList({
                        keyword: this.innerState.keyword,
                        offset: offset,
                        pageSize: 30,
                        executeStatus: executeStatus,
                        beginDate: this.innerState._timeRange?.start ?? new Date(),
                        endTime: this.innerState._timeRange?.end ?? new Date(),
                        clinicScope: this.innerState.sourceType,
                        ownerId: this.innerState.owner?.id,
                        departmentIdList: nonAppointDepartmentId ? "" : this.innerState.departmentIdList?.map((item) => item.id).join(","),
                        nonAppointDepartmentId,
                    })
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe(
                (rsp) => {
                    if (rsp instanceof ABCError) {
                        this.innerState.loading = false;
                        this.innerState.loadError = rsp;
                        this.update();
                    } else if (rsp instanceof PatientOrderSearchResult) {
                        if (!rsp.fromDraft && rsp.tabIndex != this.innerState.sourceType) return;
                        this.innerState._searchResult = rsp;
                        this.innerState._patientOrderCount.set(ExecuteSourceType.clinic, rsp.countInSelfClinic ?? 0);
                        this.innerState._patientOrderCount.set(ExecuteSourceType.chain, rsp.countInOtherClinics ?? 0);

                        const newDataHolders: DataHolder<ChargeInvoiceData>[] = DataHolderHelper.buildDataHolderList(
                            !ABCUtils.isEmpty(this.innerState.dataHolders) ? ABCUtils.last(this.innerState.dataHolders!)! : null,
                            this.innerState._searchResult?.result?.filter((item) => this._statusMatch(item.executeStatus!)) ?? [],
                            (data) => {
                                return new _ExecutionOrderItemHolder(data as ChargeInvoiceData);
                            },
                            this._group
                        );
                        this.innerState.dataHolders = this.innerState.dataHolders?.concat(newDataHolders);
                        if (this.innerState._searchResult?.fromDraft) {
                            this._loadDataTrigger.next(false);
                            return;
                        }
                        if (!this.innerState._hasMore && this.innerState.dataHolders.length) {
                            this.innerState.dataHolders.push(
                                new LoadingHolder({
                                    title: "没有更多数据了",
                                    showLoading: false,
                                })
                            );
                        }
                        if (!(this.innerState._searchResult?.fromDraft && this.innerState.dataHolders.length == 0)) {
                            this.innerState.loading = false;
                            this.update();
                        }
                    } else if (rsp instanceof MyExecuteListRsp) {
                        this.innerState._searchResult = rsp;
                        const newDataHolders: DataHolder<ChargeInvoiceData>[] = DataHolderHelper.buildDataHolderList(
                            !ABCUtils.isEmpty(this.innerState.dataHolders) ? ABCUtils.last(this.innerState.dataHolders!)! : null,
                            this.innerState._searchResult?.result?.filter((item) => this._statusMatch(item.executeStatus!)) ?? [],
                            (data) => {
                                return new _ExecutionOrderItemHolder(data as ChargeInvoiceData, true);
                            },
                            this._group
                        );
                        this.innerState.dataHolders = this.innerState.dataHolders?.concat(newDataHolders);
                        if (this.innerState._searchResult?.fromDraft) {
                            this._loadDataTrigger.next(false);
                        }
                        if (!this.innerState._hasMore && this.innerState.dataHolders.length) {
                            this.innerState.dataHolders.push(
                                new LoadingHolder({
                                    title: "没有更多数据了",
                                    showLoading: false,
                                })
                            );
                        }
                        if (!(this.innerState._searchResult?.fromDraft && this.innerState.dataHolders.length == 0)) {
                            this.innerState.loading = false;
                            this.update();
                        }
                    }
                },
                (error) => {
                    this.innerState.loading = false;
                    this.innerState.loadError = error;
                    this.update();
                }
            )
            .addToDisposableBag(this);

        this._loadTherapyRegistrationTrigger
            .pipe(
                switchMap(() => {
                    return Promise.all([
                        userCenter.isAllowedRegUpgrade
                            ? DentistryAgent.getExistAvailableTherapyRegistration(1)
                            : PatientOrderDataAgent.getExistAvailableTherapyRegistration(),
                        userCenter.isAllowedRegUpgrade
                            ? PatientOrderDataAgent.getTherapyRegistrations(userCenter.employee!.id!, new Date())
                            : PatientOrderDataAgent.getNormalTherapyRegistrations(userCenter.employee!.id!, new Date()),
                    ]).toObservable();
                })
            )
            .subscribe((rsp) => {
                this.innerState.existAvailableTherapyRegistration = rsp[0];
                this.innerState.therapyRegistrationCount = rsp[1].todayTotalCount;
                this.update();
            })
            .addToDisposableBag(this);

        //监听草稿单变化刷新页面
        ExecuteInvoiceDraftManager.instance.changeListener
            .subscribe((/*result*/) => {
                //草稿变化后，强制重新刷新
                if (this.innerState._includeDraft) this.dispatch(new _EventReload());
                this.loadDraft();
            })
            .addToDisposableBag(this);
    }
    async *_mapEventInit(event: _EventInit): AsyncGenerator<State> {
        await this._initPageConfig(event.value);
        this._initPageTrigger();
        if (this._initLoad || !_.isEmpty(this.innerState.keyword)) {
            this._loadTherapyRegistrationTrigger.next(0);
            this._loadDataTrigger.next(false);
        }
    }

    async *_mapEventUpdate(/*ignored: _EventUpdate*/): AsyncGenerator<State> {
        yield this.innerState;
    }

    update(): void {
        this.dispatch(new _EventUpdate());
    }

    _statusMatch(status: number): boolean {
        let matched = false;
        for (const filter of this.innerState._filters ?? []) {
            if (filter == PatientOrderExecuteStatusType.EUnExecuted)
                matched = status == ChargeExecuteStatus.EXECUTION_ORDER_STATUS_UNEXECUTED;
            else if (filter == PatientOrderExecuteStatusType.EExecuted)
                matched = status == ChargeExecuteStatus.EXECUTION_ORDER_STATUS_EXECUTED;
            else matched = true;

            if (matched) return true;
        }

        return false;
    }

    async *_mapEventChangeFilter(event: _EventChangeFilter): AsyncGenerator<State> {
        this.innerState.dataHolders = [];
        this.innerState._searchResult = undefined;

        this.innerState._timeRange = event.filter.filters?.[0].filters?.[0]?.timeRange; // 自定义时间范围
        if (this._tabType == ExecuteTabType.allBill) {
            this.innerState.owner = event.filter.filters?.[1]?.filters?.[0]?.info; // 选择开单人员信息
        }
        this.innerState.departmentIdList = event.filter.filters?.[2]?.filters?.[0]?.info; // 选择开单科室信息

        this._loadDataTrigger.next(true);
    }

    async *_mapEventReload(/*event: _EventReload*/): AsyncGenerator<State> {
        this.innerState.dataHolders = [];
        this.innerState.loading = true;
        yield this.innerState;
        this.innerState._searchResult = undefined;
        this._loadDataTrigger.next(true);
        this._loadTherapyRegistrationTrigger.next(0);
    }

    async *_mapEventSearchPatient(event: _EventSearchPatient): AsyncGenerator<State> {
        this.innerState.dataHolders = [];
        this.innerState._searchResult = undefined;
        if (!event.keyword) {
            this.innerState.keyword = undefined;
            this.innerState._searchStatus = false;
            this.update();
            return;
        }
        this.innerState.keyword = event.keyword;
        this.innerState._searchStatus = true;

        // 只有当前激活的标签页才发送请求
        this._loadDataTrigger.next(true);
    }

    async *_mapEventLoadMore(/*event: _EventLoadMore*/): AsyncGenerator<State> {
        if (this.innerState._hasMore && !this.innerState.loading) {
            this._loadDataTrigger.next(true);
        }
    }

    requestLoadMore(): void {
        this.dispatch(new _EventLoadMore());
    }

    requestReload(): void {
        this.dispatch(new _EventReload());
    }

    requestChangeFilter(filter: Filters): void {
        this.dispatch(new _EventChangeFilter(filter));
    }

    requestSearchPatient(keyword: string): void {
        this.dispatch(new _EventSearchPatient(keyword));
    }
}

export { ExecuteListViewBloc, State };
