import React from "react";
import { Style, Text, View } from "@hippy/react";
import { ExecuteEffectGroup } from "./data/bean";
import { BasePage } from "../base-ui/base-page";
import { showBottomPanel } from "../base-ui/abc-app-library/panel";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { AbcBasePanel, BottomSheetHelper } from "../base-ui/abc-app-library";
import { SafeAreaBottomView } from "../base-ui/safe_area_view";
import { pxToDp } from "../base-ui/utils/ui-utils";
import { BaseComponent } from "../base-ui/base-component";
import { IconFontView, SizedBox, UniqueKey } from "../base-ui";
import { ExecuteRecordItem } from "./data/nurse-station-data";
import { AbcView } from "../base-ui/views/abc-view";
import { AbcText } from "../base-ui/views/abc-text";
import { DialogBuilder, DialogButtonBuilder } from "../base-ui/dialog/dialog-builder";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { AbcButton } from "../base-ui/views/abc-button";
import { MedicineTemplateAddType } from "../outpatient/medicine-add-page/views/medicine-template-add-type-dialog";
import { ProductTemplateAddType } from "../outpatient/product-add-page/views/product-template-add-type-dialog";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { ABCEmptyView } from "../base-ui/views/empty-view";

interface CopyExecuteEffectGroupDialogProps {
    executeDetailInfos?: ExecuteRecordItem[];
    isCompleted?: boolean; // 是否有已填写项目（为true时需要弹窗）
}
interface CopyExecuteEffectGroupDialogState {
    isSelectId?: string;
}
export class CopyExecuteEffectGroupDialog extends BasePage<CopyExecuteEffectGroupDialogProps, CopyExecuteEffectGroupDialogState> {
    public static show(
        props: CopyExecuteEffectGroupDialogProps
    ): Promise<{ addType?: MedicineTemplateAddType; executeEffectGroup?: ExecuteEffectGroup }> {
        return showBottomPanel(<CopyExecuteEffectGroupDialog {...props} />, { topMaskHeight: pxToDp(200) });
    }

    constructor(props: CopyExecuteEffectGroupDialogProps) {
        super(props);
        this.state = {
            isSelectId: props.executeDetailInfos?.[0]?.items?.[0]?.executeRecordId ?? "", // 初始化第一条记录默认展开
        };
    }

    getAppBar(): JSX.Element | undefined {
        return undefined;
    }

    getShowStatusBar(): boolean {
        return false;
    }

    async renderPop(info: {
        executorItemNameAndCount?: string; // 执行项目名称和数量
        executorName?: string; // 执行人名称
        executorDate?: string; // 执行时间
        treatmentMethod?: string; // 治疗方法
        treatmentSite?: string; // 治疗部位
        treatmentResponse?: string; // 治疗反应
        etiologyPathogenesis?: string; // 病因病机
        treatmentResult?: string; // 治疗结果
        id?: string; // 执行记录id
    }): Promise<void> {
        const { treatmentMethod, treatmentSite, treatmentResponse, etiologyPathogenesis, treatmentResult } = info;
        const executeEffectGroupInfo = JsonMapper.deserialize(ExecuteEffectGroup, {
            id: this.state.isSelectId,
            treatmentMethod: treatmentMethod, //治疗方法
            treatmentSite: treatmentSite, //治疗部位
            treatmentResponse: treatmentResponse, //治疗反应
            etiologyPathogenesis: etiologyPathogenesis, //病因病机;
            treatmentResult: treatmentResult, //治疗结果
        });
        // 是新增草稿不展示弹窗直接复制执行记录
        if (!this.props.isCompleted) {
            ABCNavigator.pop({ executeEffectGroup: executeEffectGroupInfo });
        } else {
            const addType: MedicineTemplateAddType = await CopyExecutionRecordPopUpWithCancelDialog.show().then();
            ABCNavigator.pop({
                addType: addType,
                executeEffectGroup: executeEffectGroupInfo,
            });
        }
    }

    renderItemView(info: {
        executorItemNameAndCount?: string; // 执行项目名称和数量
        executorName?: string; // 执行人名称
        executorDate?: string; // 执行时间
        treatmentMethod?: string; // 治疗方法
        treatmentSite?: string; // 治疗部位
        treatmentResponse?: string; // 治疗反应
        etiologyPathogenesis?: string; // 病因病机
        treatmentResult?: string; // 治疗结果
        id?: string; // 执行记录id
    }): JSX.Element {
        const {
            executorItemNameAndCount,
            executorName,
            executorDate,
            treatmentMethod,
            treatmentSite,
            treatmentResponse,
            etiologyPathogenesis,
            treatmentResult,
            id,
        } = info;
        const textStyle = TextStyles.t16NT2.copyWith({ color: Colors.t2 });
        const marginBottomSize = Sizes.dp8;
        return (
            <AbcBasePanel
                panelStyle={{
                    marginBottom: Sizes.listHorizontalMargin,
                    paddingHorizontal: Sizes.listHorizontalMargin,
                    backgroundColor: Colors.bankPropagandaBg,
                }}
            >
                <AbcView
                    style={[
                        ABCStyles.rowAlignCenterSpaceBetween,
                        this.state.isSelectId == id ? ABCStyles.bottomLine : undefined,
                        { paddingVertical: Sizes.listHorizontalMargin },
                    ]}
                    onClick={() => this.setState({ isSelectId: id })}
                >
                    <Text style={[TextStyles.t14NT1, { flexShrink: 1 }]} numberOfLines={1}>
                        {executorItemNameAndCount}
                    </Text>
                    <SizedBox width={Sizes.dp8} />
                    <View style={ABCStyles.rowAlignCenter}>
                        <Text style={textStyle}>{`${executorName} ${executorDate}`}</Text>
                        <SizedBox width={Sizes.dp8} />
                        {this.state.isSelectId == id ? (
                            <IconFontView name={"arrow_up"} color={Colors.T6} size={Sizes.dp16} />
                        ) : (
                            <IconFontView name={"arrow_down"} size={Sizes.dp16} color={Colors.T6} />
                        )}
                    </View>
                </AbcView>
                {this.state.isSelectId == id && (
                    <View style={{ paddingVertical: Sizes.dp8 }}>
                        <View style={[ABCStyles.rowAlignCenterSpaceBetween, { marginBottom: marginBottomSize }]}>
                            <Text style={textStyle}>{"执行记录"}</Text>
                            <AbcText style={TextStyles.t16NT} onClick={() => this.renderPop(info)}>
                                {"复制"}
                            </AbcText>
                        </View>
                        {!!treatmentMethod && (
                            <Text style={[textStyle, { marginBottom: marginBottomSize }]}>{`治疗方法：${treatmentMethod ?? ""}`}</Text>
                        )}
                        {!!treatmentSite && (
                            <Text style={[textStyle, { marginBottom: marginBottomSize }]}>{`治疗部位：${treatmentSite ?? ""}`}</Text>
                        )}
                        {!!treatmentResponse && (
                            <Text style={[textStyle, { marginBottom: marginBottomSize }]}>{`治疗反应：${treatmentResponse ?? ""}`}</Text>
                        )}
                        {!!etiologyPathogenesis && (
                            <Text style={[textStyle, { marginBottom: marginBottomSize }]}>{`病因病机：${etiologyPathogenesis ?? ""}`}</Text>
                        )}
                        {!!treatmentResult && (
                            <Text style={[textStyle, { marginBottom: marginBottomSize }]}>{`治疗结果：${treatmentResult ?? ""}`}</Text>
                        )}
                    </View>
                )}
            </AbcBasePanel>
        );
    }

    renderListView(): JSX.Element[] {
        const { executeDetailInfos } = this.props;
        const views: JSX.Element[] = [];
        executeDetailInfos?.forEach((info) => {
            if (!!info.id) {
                const executorItemNameAndCounts: string[] = [];
                info.items?.forEach((item) => {
                    executorItemNameAndCounts.push(`${item.executeItemName}(${item.count})`);
                }); // 执行项目名称(执行次数)
                const executorName = info?.executors?.map((item) => item.name).join("、"); // 执行人
                const effect = info.effects?.find((f) => f.executeRecordId == info.id);
                views.push(
                    this.renderItemView({
                        executorItemNameAndCount: executorItemNameAndCounts.join("、"),
                        executorName: executorName,
                        executorDate: info?.executeDate?.format("MM-dd") ?? "--",
                        treatmentMethod: effect?.treatmentMethod,
                        treatmentSite: effect?.treatmentSite,
                        treatmentResponse: effect?.treatmentResponse,
                        etiologyPathogenesis: effect?.etiologyPathogenesis,
                        treatmentResult: effect?.treatmentResult,
                        id: info.id,
                    })
                );
            }
        });
        return views;
    }

    renderContent(): JSX.Element {
        const { executeDetailInfos } = this.props;
        return (
            <View style={{ flex: 1, backgroundColor: Colors.white }}>
                {BottomSheetHelper.createTitleBar("执行历史")}
                <View style={{ backgroundColor: Colors.white, margin: Sizes.listHorizontalMargin }}>
                    {!!executeDetailInfos?.length ? this.renderListView() : <ABCEmptyView tips={"暂无执行记录"} />}
                    <SafeAreaBottomView bottomSafeAreaColor={Colors.white} />
                </View>
            </View>
        );
    }
}

interface CopyExecutionRecordPopUpWithCancelDialogProps {
    executeEffectGroups: ExecuteEffectGroup[];
}

export class CopyExecutionRecordPopUpWithCancelDialog extends BaseComponent<CopyExecutionRecordPopUpWithCancelDialogProps> {
    constructor(props: CopyExecutionRecordPopUpWithCancelDialogProps) {
        super(props);
    }

    static async show(): Promise<ProductTemplateAddType> {
        const DefaultShowButtonStyle: {
            button: Style;
            buttonText: Style;
        } = {
            button: {
                justifyContent: "center",
                alignItems: "center",
                paddingHorizontal: Sizes.dp6,
                backgroundColor: Colors.white,
            },
            buttonText: {
                ...TextStyles.t16NM,
                textAlign: "center",
                lineHeight: Sizes.dp24,
            },
        };

        const builder = new DialogBuilder();
        builder.title = "复制执行记录";
        builder.content = <Text style={TextStyles.t18NT1}>{"已填写执行记录，插入到已填写的内容中，还是覆盖已填写内容？"}</Text>;
        builder.contentPadding = Sizes.paddingLTRB(Sizes.dp24, Sizes.dp32);
        const buttons = new DialogButtonBuilder();

        buttons.appendCustomButton(
            <AbcButton
                key={UniqueKey()}
                text={"取消"}
                style={[DefaultShowButtonStyle.button, { flex: 1 }]}
                textStyle={TextStyles.t18NT1}
                pressColor={Colors.dialogBtnPress}
                onClick={() => ABCNavigator.pop()}
            />
        );

        buttons.appendCustomButton(
            <AbcButton
                key={UniqueKey()}
                text={"添加"}
                style={[DefaultShowButtonStyle.button, { flex: 1 }]}
                textStyle={TextStyles.t18NM}
                pressColor={Colors.dialogBtnPress}
                onClick={() => ABCNavigator.pop(MedicineTemplateAddType.push)}
            />
        );

        buttons.appendCustomButton(
            <AbcButton
                key={UniqueKey()}
                text={"覆盖"}
                style={[DefaultShowButtonStyle.button, { flex: 1 }]}
                textStyle={TextStyles.t18NM}
                pressColor={Colors.dialogBtnPress}
                onClick={() => ABCNavigator.pop(MedicineTemplateAddType.reset)}
            />
        );

        builder.button = buttons;
        return await builder.show({ borderRadius: Sizes.dp6, isHasCloseBtn: false });
    }

    render(): JSX.Element {
        return <View></View>;
    }
}
