/**
 * create by dengjie
 * desc:
 * create date 2020/6/9
 */

import { ABCApiNetwork } from "../../net";
import { AstResult, ChargeInvoiceData, ChargeInvoiceDetailData } from "../../charge/data/charge-beans";
import { fromJsonToDate, JsonMapper, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { Subject } from "rxjs";
import { EmployeeSimpleInfo } from "../../base-business/data/clinic-agent";
import { TimeUtils } from "../../common-base-module/utils";
import { Patient } from "../../base-business/data/beans";
import { ABCError } from "../../common-base-module/common-error";
import { CommonFilterId } from "../../base-ui/searchBar/search-bar";
import { ExecuteAttachmentItem, ExecuteEffectGroup, ExecuteEffectTemplateDetail } from "./bean";
import { ChargeUtils } from "../../charge/utils/charge-utils";
import { ChargeInvoiceSaveDraftReq } from "../../charge/data/charge-agent";
import { ignore } from "../../common-base-module/global";
import { RegistrationFormItem } from "../../registration/data/bean";

export enum PatientOrderExecuteStatusType {
    EExecuted,
    EUnExecuted,
    EAll,
}

export enum ExecutorType {
    mine = 0,
    others = CommonFilterId.asyncFilter + 1,
}

export enum NeedHomeCareStatus {
    none = 0,
    need = 1,
}

export class ExecuteRecordItemStatus {
    static execute = 0; //执行
    static undo = 1; //撤销
}

export class PatientOrderSearchResult {
    keyword?: string;

    @JsonProperty({ type: Array, clazz: ChargeInvoiceData })
    result?: ChargeInvoiceData[];
    offset?: number;
    limit?: number;
    totalCount?: number;
    countInOtherClinics?: number;
    countInSelfClinic?: number;

    //终端使用字段
    fromDraft?: boolean;
    tabIndex?: number;
}

/**
 * 我的执行列表
 */
export class MyExecuteListRspSummary {
    allCount?: number;
    draftCount?: number;
    normalCount?: number;
    onlineCount?: number;
    owedCount?: number;
    unchargedCount?: number;
}
export class MyExecuteListRsp {
    countInOtherClinics?: number;
    countInSelfClinic?: number;
    keyword?: string;
    limit?: number;
    offset?: number;
    @JsonProperty({ type: Array, clazz: ChargeInvoiceData })
    result?: ChargeInvoiceData[];
    summary?: MyExecuteListRspSummary;
    tab?: number;
    totalCount?: number;
}

export class ExecutorInfo {
    handSign?: string;
    id?: string;
    name?: string;
}

export class ExecuteProductInfo {
    chargeFormItemId?: string;
    count?: number;
    executeItemExecutedCount?: number;
    executeItemId?: string;
    executeItemName?: string;
    executeItemTotalUnitCount?: number;
    executeRecordId?: string;
    id?: string;
    productSubType?: number;
    productType?: number;
    unit?: string;
}

function toExecuteEffect(json?: string) {
    return json?.replace(/<br[^>]*>/g, "\n");
}

export interface EffectItem {
    etiologyPathogenesis: string;
    executeRecordId: string;
    id: string;
    treatmentMethod: string;
    treatmentResponse: string;
    treatmentResult: string;
    treatmentSite: string;
    attachments?: ExecuteAttachmentItem[]; //上传的附件地址
}
export class ChargeFormGroups extends ExecuteProductInfo {
    chargeFormId?: string;
    groupId?: number;
    usage?: string;
    count?: number;
    groupItems?: string[];
}

export class PrescriptionChargeExecuteForms {
    chargeFormId?: string;
    @JsonProperty({ type: Array, clazz: ChargeFormGroups })
    chargeFormGroups?: ChargeFormGroups[];
    sourceFormTypeChargeFormDisplayNo?: number;
    sourceFormTypeName?: string;
}

export class ExecuteRecordItem {
    id?: string;
    count?: number;
    chainId?: string;
    clinicId?: string;
    clinicName?: string;
    executeClinicId?: string;
    executeClinicName?: string;

    chargeFormItemViews?: Array<any>;
    chargeSheetId?: string;
    executeItemId?: string;
    executorName?: string;

    createdByName?: string;
    //pc中的换行使用<br/> 转换成 \n
    @JsonProperty({ fromJson: toExecuteEffect })
    executeEffect?: string;
    canceledEmployeeName?: string;

    executors?: Array<EmployeeSimpleInfo>;

    @JsonProperty({ fromJson: fromJsonToDate })
    executeDate?: Date;

    @JsonProperty({ fromJson: fromJsonToDate })
    lastModifiedDate?: Date;

    @JsonProperty({ type: Array, clazz: PrescriptionChargeExecuteForms })
    prescriptionChargeExecuteForms?: PrescriptionChargeExecuteForms[];

    @JsonProperty({ type: Array, clazz: ExecuteProductInfo })
    items?: Array<ExecuteProductInfo>;

    lastModifiedByName?: string;
    status?: number; //ExecuteRecordItemStatus
    canModify?: number;
    effects?: EffectItem[];
    effectVisibleForPatient?: number;
    needExecuteEffect?: number;

    //住院执行相关内容
    needHomeCare?: NeedHomeCareStatus;
    homeCareAddress?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    homeCareEndTime?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    homeCareStartTime?: Date;
}

// 执行记录历史列表
export class GetExecuteRecordRsp {
    chargeSheetCreateClinicId?: string;
    chargeSheetCreateClinicName?: string;
    chargeSheetCreatedBy?: string;
    chargeSheetCreatedByName?: string;
    chargeSheetOwnerId?: string;
    chargeSheetOwnerName?: string;
    chargeSheetOwnerDepartmentId?: string;
    chargeSheetOwnerDepartmentName?: string;

    @JsonProperty({ type: Array, clazz: ExecuteRecordItem })
    executeRecords?: Array<ExecuteRecordItem>;

    @JsonProperty({ fromJson: fromJsonToDate })
    chargeSheetCreated?: Date;
    existCrossExecuteClinic?: number;

    get defaultOwnerAndDepartmentName(): string {
        let _defaultName = this.chargeSheetOwnerName ?? "";
        if (!!this.chargeSheetOwnerName && !!this.chargeSheetOwnerDepartmentName) {
            _defaultName += ` - ${this.chargeSheetOwnerDepartmentName}`;
        }
        return _defaultName;
    }
}

class ChargeExecuteItem {
    id?: string;
    chargeFormItemId?: string;
    productId?: string;
    productType?: number;
    productSubType?: number;
    name?: string;
    unitCount?: number;
    executedCount?: number;
    unit?: string;
    executeStatus?: number;
    executeStatusName?: string;
}

export class ChargeExecuteResult {
    @JsonProperty({ type: ChargeExecuteItem })
    chargeExecuteItem?: ChargeExecuteItem;
    chargeSheetId?: string;
    chargeSheetStatusName?: string;
}

export class AstExecuteRsp {
    id?: string;
    patientOrderId?: string;
    clinicId?: string;
    outpatientSheetId?: string;
    prescriptionFormId?: string;
    goodsId?: string;
    domainMedicineId?: string;
    type?: number;
    subType?: number;
    composeType?: number;

    //  Null composeParentFormItemId;
    medicineCadn?: string;
    name?: string;
    specification?: string;
    manufacturer?: string;
    ast?: number;
    usage?: string;
    ivgtt?: number;
    ivgttUnit?: string;
    freq?: string;
    dosage?: string;
    dosageUnit?: string;
    days?: number;
    specialRequirement?: string;
    useDismounting?: number;
    unitCount?: number;
    unit?: string;
    unitPrice?: number;
    costUnitPrice?: number;
    expectedUnitPrice?: number;
    sourceUnitPrice?: number;
    fractionPrice?: number;
    sort?: number;
    groupId?: number;
    keyId?: string;

    //  Null stockPieceCount;
    //  Null stockPackageCount;
    astResult?: AstResult;
    chargeStatus?: number;
}

class ChargeExecuteEvent {}

//执行
export class ChargeExecuteDoEvent extends ChargeExecuteEvent {
    rsp: ChargeExecuteResult;

    constructor(executeResult: ChargeExecuteResult) {
        super();
        this.rsp = executeResult;
    }
}

//撤销
export class ChargeExecuteUndoEvent extends ChargeExecuteEvent {
    rsp: ExecuteRecordItem[];

    constructor(executeRecordList: ExecuteRecordItem[]) {
        super();
        this.rsp = executeRecordList;
    }
}

export interface CreateExecuteInvoiceRsp {
    id: string;
    patientOrderId: string;
    status: number;
    statusName: string;
}

export interface TherapyDoctorBasicInfo {
    doctorId: string;
    doctorName: string;
}

export interface TherapyRegistrationOrderNo {
    orderNo: number;
    reserveStart: string;
    reserveEnd: string;
    timeOfDay: string;
}

export enum TherapyRegistrationStatus {
    registered = 0, //已预约
    waitSign = 10, //待签到
    signed = 20, //已签到
    canceled = 91, //已取消
}

export enum TherapyRegistrationSignStatus {
    notNeed = 0, //无需签到
    waitSign = 1, //待签到
    signed = 2, //已签到
}

export class ExecuteActionsItem {
    id?: string;
    displayName?: string;
}

export class TherapyRegistration {
    id?: string;
    chainId?: string;
    clinicId?: string;
    patientId?: string;
    doctorId?: string;
    doctorName?: string;
    orderNos?: TherapyRegistrationOrderNo[];
    @JsonProperty({ fromJson: fromJsonToDate })
    reserveDate?: Date;
    reserveStart?: string;
    reserveEnd?: string;
    status?: TherapyRegistrationStatus;
    signInStatus?: TherapyRegistrationSignStatus;
    // oldSimpleInfo?: any;
    // executeActions: any[];
    isExecuted?: number;
    displayOrderNo?: string;

    @JsonProperty({ type: Array, clazz: ExecuteActionsItem })
    registrationProducts?: ExecuteActionsItem[];
}

export class NormalTherapyRegistration {
    id?: string;
    chainId?: string;
    clinicId?: string;
    patientId?: string;
    doctorId?: string;
    doctorName?: string;
    orderNos?: TherapyRegistrationOrderNo[];
    @JsonProperty({ fromJson: fromJsonToDate })
    reserveDate?: Date;
    reserveStart?: string;
    reserveEnd?: string;
    status?: TherapyRegistrationStatus;
    signInStatus?: TherapyRegistrationSignStatus;
    // oldSimpleInfo?: any;
    // executeActions: any[];
    isExecuted?: number;
    displayOrderNo?: string;
}

export class TherapyRegistrationItem {
    id?: string;
    @JsonProperty({ type: Patient })
    patient?: Patient;

    chargeSheetQueryExceptionType?: number;

    @JsonProperty({ type: RegistrationFormItem })
    registrationFormItem?: RegistrationFormItem;
}

export class NormalTherapyRegistrationItem {
    registrationId?: string;
    @JsonProperty({ type: Patient })
    patient?: Patient;

    @JsonProperty({ type: NormalTherapyRegistration })
    therapyRegistration?: NormalTherapyRegistration;
}

//升级诊所预约看板列表
export class GetTherapyRegistrationsRsp {
    todayTotalCount?: number;
    tomorrowTotalCount?: number;
    dayAfterTomorrowTotalCount?: number;
    today?: string;
    totalCount?: number;
    @JsonProperty({ type: Array, clazz: TherapyRegistrationItem })
    rows?: TherapyRegistrationItem[];
}

//普通诊所预约看板列表
export class NormalTherapyRegistrationsRsp {
    todayTotalCount?: number;
    tomorrowTotalCount?: number;
    dayAfterTomorrowTotalCount?: number;
    today?: string;
    totalCount?: number;
    @JsonProperty({ type: Array, clazz: NormalTherapyRegistrationItem })
    rows?: NormalTherapyRegistrationItem[];
}

//理疗预约设置项
export interface TherapyAppointmentSetting {
    chainId: string;
    clinicId: string;
    enableAppointment: number;
    mode: number;
    //....
}

export interface ExecuteHabits {
    effectVisibleForPatient: number;
    needExecuteEffect: number;
}

export class ExecuteEffectTemplate {
    chainId?: string;
    id?: string;
    isSystem?: number; // 1
    sort?: number;
    treatmentMethod?: string;
}

export class GetExecuteEffectTemplatesRsp {
    @JsonProperty({ type: Array, clazz: ExecuteEffectTemplate })
    templates?: ExecuteEffectTemplate[];
}

export interface TreatmentSiteOption {
    name: string;
    children: TreatmentSiteOption[];
}

export class TreatmentStaticOptions {
    acupuncture?: TreatmentSiteOption;
    bodyPosition?: TreatmentSiteOption;
}

export class GetExecuteEffectTreatmentStaticOptions {
    @JsonProperty({ type: TreatmentStaticOptions })
    treatmentSiteOption?: TreatmentStaticOptions;
}
export class ExecuteSheetPrivilegeCheckRsp {
    enableShowHistorySheet?: number; //是否展示历史单据
    enableShowExecutedRecords?: number; // 是否展示执行记录
}

export class PatientOrderDataAgent {
    static sPatientOrderExecutePublisher = new Subject<ChargeExecuteEvent>();

    static patientOrderExecuteInvoicePublisher = new Subject();

    /**
     * 获取执行单列表
     * @param patientOrderId 执行单ID
     */
    static async getPatientOrderDetailInfo(patientOrderId: string): Promise<ChargeInvoiceDetailData> {
        const path = `nurse/${patientOrderId}`;
        return await ABCApiNetwork.get(path, { clazz: ChargeInvoiceDetailData });
    }

    /**
     * 执行项目
     * @param id 项目ID
     * @param executorId 执行者ID
     * @param count 次数
     */
    static async chargeExecute(id: string, executorId: string, count: number): Promise<ChargeExecuteResult> {
        const path = `nurse/execute/${id}`;
        const rsp: ChargeExecuteResult = await ABCApiNetwork.put(path, {
            clazz: ChargeExecuteResult,
            body: {
                count: count,
                executorId: executorId,
            },
        });
        PatientOrderDataAgent.sPatientOrderExecutePublisher.next(new ChargeExecuteDoEvent(rsp));
        return rsp;
    }

    /**
     * 执行药敏
     * @param id 项目ID
     * @param result 皮试结果
     * @param description 备注信息
     */
    static astExecute(id: string, result: string, description: string): Promise<AstExecuteRsp> {
        const path = `nurse/ast/${id}`;
        return ABCApiNetwork.put(path, {
            clazz: AstExecuteRsp,
            body: {
                result: result ?? "",
                description: description ?? "",
            },
        });
    }

    /**
     * 获取执行记录历史列表
     * @param chargeSheetId 执行表ID
     */
    static async getExecuteRecordList(chargeSheetId: string): Promise<GetExecuteRecordRsp> {
        const path = `charge/execute-records/charge-sheet/${chargeSheetId}`;
        return await ABCApiNetwork.get(path, { clazz: GetExecuteRecordRsp });
    }

    /**
     * 取消执行记录
     * @param executeItemId 执行项目ID
     */
    static async undoExecuteRecord(executeItemId: string): Promise<Array<ExecuteRecordItem>> {
        const path = `nurse/execute/${executeItemId}/actions/undo`;
        const rsp: GetExecuteRecordRsp = await ABCApiNetwork.put(path, {
            clazz: GetExecuteRecordRsp,
        });
        PatientOrderDataAgent.sPatientOrderExecutePublisher.next(new ChargeExecuteUndoEvent(rsp.executeRecords ?? []));
        return rsp.executeRecords!;
    }

    /**
     * 取消执行记录 V2
     * @param executeItemId
     */
    static async undoExecuteRecordV2(executeItemId: string): Promise<Boolean | ABCError> {
        const path = `nurse/execute-records/${executeItemId}/cancel`;
        return ABCApiNetwork.put(path, {
            clazz: GetExecuteRecordRsp,
        })
            .then((rsp: GetExecuteRecordRsp) => {
                PatientOrderDataAgent.sPatientOrderExecutePublisher.next(new ChargeExecuteUndoEvent(rsp.executeRecords ?? []));
                PatientOrderDataAgent.patientOrderExecuteInvoicePublisher.next();
                return !!rsp;
            })
            .catch((error) => new ABCError(error));
    }

    //加载列表
    static async getPatientOrderList(params: {
        keyword?: string;
        offset?: number;
        pageSize?: number;
        executeStatus?: string;
        beginDate?: Date;
        endTime?: Date;
        clinicScope?: number;
        ownerId?: string;
        departmentIdList?: string;
        nonAppointDepartmentId?: number | null;
    }): Promise<PatientOrderSearchResult> {
        const {
            keyword,
            offset,
            pageSize,
            executeStatus,
            beginDate,
            endTime,
            clinicScope,
            ownerId,
            departmentIdList,
            nonAppointDepartmentId,
        } = params;
        const queryParameters = {
            keyword: keyword ?? "",
            offset: (offset ?? 0)?.toString(),
            limit: (pageSize ?? 30)?.toString(),
            executeStatus: executeStatus,
            clinicScope: clinicScope,
            ownerId,
            departmentIdList,
            nonAppointDepartmentId,
            beginDate: beginDate?.format("yyyy-MM-dd"),
            endDate: endTime?.format("yyyy-MM-dd"),
        };
        return ABCApiNetwork.get("nurse", {
            queryParameters: queryParameters,
            clazz: PatientOrderSearchResult,
            clearUndefined: true,
        }).then((rsp) => {
            rsp.tabIndex = clinicScope;
            return rsp;
        });
    }

    /**
     * 创建执行单
     * @param detailData
     */
    static createExecuteInvoice(detailData: ChargeInvoiceDetailData): Promise<CreateExecuteInvoiceRsp> {
        detailData = JsonMapper.deserialize(ChargeInvoiceDetailData, detailData);
        detailData.chargeForms?.forEach((form) => {
            form.chargeFormItems?.forEach((formItem) => {
                formItem.productInfo = undefined;
            });
        });

        detailData.therapySheet!.sellerDepartmentId = detailData.therapySheet?.departmentId ?? "";
        // 执行站开单的协议调整 ：①不再传入therapySheet对象（将therapySheet所有属性移到最外层传递）②将之前的doctorId改为sellerId
        const { therapySheet, ...detailDataOthers } = detailData;
        const sellerId = detailData.therapySheet?.doctorId;
        const { doctorId, ...req } = { ...detailDataOthers, ...therapySheet, sellerId };
        ignore(doctorId);

        return ABCApiNetwork.post("nurse", { body: req });
    }

    /**
     * 执行项目
     * @param params
     */
    static async chargeBatchExecute(params: {
        items: { chargeFormItemId: string; count: number }[];
        executorIds: string[];
        comment: string;
        effectVisibleForPatient: number;
        needExecuteEffect: number;
        effects: ExecuteEffectGroup[];

        needHomeCare?: NeedHomeCareStatus;
        homeCareAddress?: string;
        homeCareStartTime?: Date;
        homeCareEndTime?: Date;
    }): Promise<boolean | ABCError> {
        return ABCApiNetwork.post("nurse/execute-records", {
            //执行效果中换行转换成pc中的<Br/>
            body: {
                items: params.items,
                executorIds: params.executorIds,
                comment: params.comment.replace(/\n/g, "<br/>"),
                effectVisibleForPatient: params.effectVisibleForPatient,
                needExecuteEffect: params.needExecuteEffect,
                effects: params.effects,

                needHomeCare: params.needHomeCare ?? NeedHomeCareStatus.none,
                homeCareAddress: params.homeCareAddress,
                homeCareStartTime: TimeUtils.formatDate(params.homeCareStartTime, "yyyy-MM-dd HH:mm"),
                homeCareEndTime: TimeUtils.formatDate(params.homeCareEndTime, "yyyy-MM-dd HH:mm"),
            },
        })
            .then((res) => {
                PatientOrderDataAgent.patientOrderExecuteInvoicePublisher.next();
                return !!res;
            })
            .catch((error) => new ABCError(error));
    }

    /**
     * 修改执行效果
     */
    static async changeExecuteEffect(
        executeEffect: string,
        chargeSheetId: string,
        params: {
            effects?: ExecuteEffectGroup[];
            comment?: string;
            effectVisibleForPatient?: number;
            needExecuteEffect?: number;

            needHomeCare?: NeedHomeCareStatus;
            homeCareAddress?: string;
            homeCareStartTime?: Date;
            homeCareEndTime?: Date;
        }
    ): Promise<boolean | ABCError> {
        return ABCApiNetwork.put(`nurse/execute-records/${chargeSheetId}`, {
            body: {
                //执行效果中换行转换成pc中的<Br/>
                comment: params.comment?.replace(/\n/g, "<br/>"),
                effects: params.effects,
                effectVisibleForPatient: params.effectVisibleForPatient,
                needExecuteEffect: params.needExecuteEffect,

                needHomeCare: params.needHomeCare ?? NeedHomeCareStatus.none,
                homeCareAddress: params.homeCareAddress,
                homeCareStartTime: TimeUtils.formatDate(params.homeCareStartTime, "yyyy-MM-dd HH:mm"),
                homeCareEndTime: TimeUtils.formatDate(params.homeCareEndTime, "yyyy-MM-dd HH:mm"),
            },
        })
            .then((res) => {
                PatientOrderDataAgent.patientOrderExecuteInvoicePublisher.next();
                return !!res;
            })
            .catch((error) => new ABCError(error));
    }

    /**
     * 预约看板理疗师列表（老）
     * RegistrationDataProvider.getDoctorList(RegistrationType.therapyAppointment)（新 预约升级）
     */
    public static getTherapyDoctorsBasic(): Promise<TherapyDoctorBasicInfo[]> {
        return ABCApiNetwork.get<{ doctors: TherapyDoctorBasicInfo[] }>("nurse/therapy-doctors/basic").then((rsp) => rsp.doctors);
    }

    /**
     * 理疗预约列表
     */
    public static getTherapyRegistrations(doctorId: string, date: Date): Promise<GetTherapyRegistrationsRsp> {
        return ABCApiNetwork.get("registrations/query/therapy-kan-ban/for-nurse", {
            queryParameters: {
                doctorId: doctorId,
                date: TimeUtils.formatDate(date),
            },
            clazz: GetTherapyRegistrationsRsp,
        });
    }

    /**
     * 普通诊所理疗预约列表
     */
    public static getNormalTherapyRegistrations(doctorId: string, date: Date): Promise<NormalTherapyRegistrationsRsp> {
        return ABCApiNetwork.get("nurse/therapy-registrations/kan-ban", {
            queryParameters: {
                doctorId: doctorId,
                date: TimeUtils.formatDate(date),
            },
            clazz: NormalTherapyRegistrationsRsp,
        });
    }

    /**
     * 我的执行列表
     * @param params 参数
     */
    public static getMyExecuteList(params: {
        beginDate?: Date;
        endTime?: Date;
        limit?: number;
        offset?: number;
    }): Promise<MyExecuteListRsp> {
        const { beginDate, endTime, limit, offset } = params;
        return ABCApiNetwork.get("nurse/my-executed", {
            clearUndefined: true,
            queryParameters: {
                beginDate: beginDate?.format("yyyy-MM-dd"),
                endDate: endTime?.format("yyyy-MM-dd"),
                limit: limit,
                offset: offset,
            },
            clazz: MyExecuteListRsp,
        }).then();
    }

    /**
     * 已执行单据详情权限校验
     * @param chargeSheetId 收费单Id
     */
    public static getExecutedOrderPermissionVerification(chargeSheetId: string): Promise<ExecuteSheetPrivilegeCheckRsp> {
        return ABCApiNetwork.get(`nurse/${chargeSheetId}/sheet-privilege/check`, { clazz: ExecuteSheetPrivilegeCheckRsp });
    }

    /**
     *查看是否有有效理疗预约
     * 理疗预约列表
     */
    public static getExistAvailableTherapyRegistration(): Promise<boolean> {
        return ABCApiNetwork.get<{ isYes: 1 }>("nurse/therapy-registrations/exist-available").then((rsp) => rsp.isYes == 1);
    }

    /**
     * 理疗预约列表设置
     */
    public static getTherapyAppointmentSetting(): Promise<TherapyAppointmentSetting> {
        return ABCApiNetwork.get<TherapyAppointmentSetting>("nurse/therapy-appointment");
    }

    /**
     * 获取用户的操作习惯
     */
    public static getExecuteEffectEmployeeHabits(): Promise<ExecuteHabits> {
        return ABCApiNetwork.get<ExecuteHabits>("charge/execute-records/execute-effect/employee-habits");
    }

    /**
     * 获取治疗部位信息
     */
    public static getExecuteEffectTreatmentStaticOptions(): Promise<TreatmentStaticOptions | undefined> {
        return ABCApiNetwork.get("charge/execute-effect-templates/treatment-site/static-options", {
            clazz: GetExecuteEffectTreatmentStaticOptions,
        }).then((rsp) => rsp.treatmentSiteOption);
    }

    /**
     * 获取模板信息列表
     */
    public static getExecuteEffectTemplate(): Promise<ExecuteEffectTemplate[]> {
        return ABCApiNetwork.get("charge/execute-effect-templates", { clazz: GetExecuteEffectTemplatesRsp }).then(
            (rsp) => rsp.templates ?? []
        );
    }

    /**
     * 获取模板详细信息
     * @param templateId
     */
    public static getExecuteEffectTemplateById(templateId: string): Promise<ExecuteEffectTemplateDetail> {
        return ABCApiNetwork.get(`charge/execute-effect-templates/${templateId}`, { clazz: ExecuteEffectTemplateDetail });
    }

    /**
     * 删除执行单
     * @param chargeId
     */
    public static deleteExecuteInvoice(chargeId: string): Promise<boolean> {
        return ABCApiNetwork.delete(`charge/${chargeId}`).then((/*rsp*/) => {
            this.patientOrderExecuteInvoicePublisher.next();
            return true;
        });
    }

    /**
     * 修改执行单
     * @param chargeId
     */
    public static modifyExecuteInvoice(chargeInvoice: ChargeInvoiceDetailData): Promise<ChargeInvoiceDetailData> {
        const detailClone = JsonMapper.deserialize(ChargeInvoiceDetailData, chargeInvoice);

        const req = new ChargeInvoiceSaveDraftReq();
        ChargeUtils.fillChargeRequest(req, detailClone);
        detailClone.chargeForms?.forEach((form) => {
            form.chargeFormItems?.forEach((formItem) => {
                formItem.productInfo = undefined;
            });
        });

        req.id = detailClone.id;
        req.patient = detailClone.patient;
        req.chargeForms = detailClone.chargeForms;
        req.memberId = detailClone.memberId ?? "";
        req.receivableFee = chargeInvoice.chargeSheetSummary?.receivableFee;
        req.sellerId = detailClone.sellerId;
        req.cash = 0;
        req.cashDiff = 0;
        req.isRenew = detailClone.reCharge__ ?? false ? 1 : 0;
        req.draftTime = new Date().getTime();
        // req.deliveryType = detailClone.deliveryType__
        req.deliveryInfo = detailClone.deliveryInfo;
        req.dataSignature = detailClone.dataSignature;
        req.contactMobile = detailClone.contactMobile;
        req.expectedAdjustmentFee = detailClone.chargeSheetSummary?.expectedAdjustmentFee__;
        req.sellerDepartmentId = detailClone.sellerDepartmentId;

        return ABCApiNetwork.put("charge/save", {
            clazz: ChargeInvoiceDetailData,
            body: req,
        }).then((rsp) => {
            PatientOrderDataAgent.patientOrderExecuteInvoicePublisher.next();
            return rsp;
        });
    }
}
