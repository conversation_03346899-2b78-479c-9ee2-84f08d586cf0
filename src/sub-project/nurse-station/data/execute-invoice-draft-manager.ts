/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/9/17
 *
 * @description
 */
import { Patient } from "../../base-business/data/beans";
import { UUIDGen } from "../../common-base-module/utils";
import { LogUtils } from "../../common-base-module/log";
import _ from "lodash";
import { JsonMapper, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { Subject } from "rxjs";
import FileUtils from "../../common-base-module/file/file-utils";
import { userCenter } from "../../user-center";
import { ChargeInvoiceData, ChargeInvoiceDetailData, ChargeStatus } from "../../charge/data/charge-beans";
import { ExecuteConst } from "../execute-const";

class ExecuteCreateDraft {
    @JsonProperty({ type: Array, clazz: ChargeInvoiceData })
    allDraft?: ChargeInvoiceData[];
}

class ExecuteCreateEvent {}

class ExecuteCreateEventDelete extends ExecuteCreateEvent {
    localDraftId: string;

    constructor(localDraftId: string) {
        super();
        this.localDraftId = localDraftId;
    }
}

class ExecuteCreateEventUpdate extends ExecuteCreateEvent {
    localDraftId: string;

    constructor(localDraftId: string) {
        super();
        this.localDraftId = localDraftId;
    }
}

const TAG = "ExecuteInvoiceDraftManager";

export class ExecuteInvoiceDraftManager {
    static instance = new ExecuteInvoiceDraftManager();

    _allDrafts?: ChargeInvoiceData[];

    changeListener = new Subject<ExecuteCreateEvent>();

    private constructor() {
        this.changeListener.subscribe((/*event*/) => {
            // let body: ToHostMessageChargeEvent = {draft: true};
            // AbcHostBridge.sendMessage(ToHostMessageType.charge, body);
        });
        userCenter.sClinicChangeObserver.subscribe(() => {
            this._allDrafts = undefined;
        });

        this.getAllDrafts().then();
    }

    async getAllDrafts(): Promise<ChargeInvoiceData[]> {
        LogUtils.d(`${TAG}: getAllDrafts`);
        if (this._allDrafts) {
            LogUtils.d(`${TAG} :getAllDrafts,length = ${this._allDrafts.length}`);
            return this._allDrafts;
        }

        const indexFile = await this._indexFile();
        if (await FileUtils.fileExists(indexFile)) {
            const content = await FileUtils.readAsString(indexFile);

            if (!_.isEmpty(indexFile)) {
                const draft = JsonMapper.deserialize(ExecuteCreateDraft, JSON.parse(content))!;
                this._allDrafts = draft.allDraft;
            }
        }

        if (this._allDrafts == undefined) this._allDrafts = [];

        LogUtils.d(`${TAG} :getAllDrafts,length = ${this._allDrafts.length}`);
        return this._allDrafts;
    }

    async loadDraftChargeDetailData(id: string): Promise<ChargeInvoiceDetailData> {
        LogUtils.d(`${TAG} :loadDraftChargeDetailData id = ${id}`);
        const fileName = await ExecuteConst.getDraftFile(id);
        const str = await FileUtils.readAsString(fileName);

        const data = JsonMapper.deserialize(ChargeInvoiceDetailData, JSON.parse(str));

        data?.fillKeyIds();

        LogUtils.d(`${TAG} :loadDraftChargeDetailData id = $id, data= $data, name = ${data?.patient?.name}`);
        return data;
    }

    public createDraft(): ChargeInvoiceDetailData {
        const detailData = new ChargeInvoiceDetailData();

        detailData.patient = new Patient();
        detailData.patient.sex = ExecuteConst.defaultSex;
        detailData.localDraftId = this.generateDraftId();
        detailData.status = ChargeStatus.draft;

        return detailData;
    }

    async saveDraft(detailData: ChargeInvoiceDetailData): Promise<boolean> {
        LogUtils.d(`${TAG} :saveDraft detailData.id  = ${detailData.localDraftId}, name = ${detailData.patient?.name}`);
        let indexData = this._allDrafts?.find((index) => index.localDraftId == detailData.localDraftId);
        _.remove(this._allDrafts!, (index) => index.localDraftId == detailData.localDraftId);

        //如果是新添加的草稿，需要更新索引文件
        if (indexData == undefined) {
            indexData = new ChargeInvoiceData();
            indexData.id = detailData.id;
            indexData.localDraftId = detailData.localDraftId;
            indexData.created = new Date();
            indexData.isDraft = detailData.isDraft;
            indexData.status = ChargeStatus.draft;
            indexData.statusName = "草稿";
        }

        indexData.status = ChargeStatus.draft;
        indexData.patient = detailData.patient;
        indexData.registrationId__ = detailData.registrationId__;
        this._allDrafts?.splice(0, 0, indexData!);

        await this._saveIndexFile();

        const detailStr = JSON.stringify(detailData);
        const fileName = await ExecuteConst.getDraftFile(detailData.localDraftId!);

        LogUtils.d(`${TAG} fileName = ${fileName}`);
        await FileUtils.writeAsString(fileName, detailStr);

        LogUtils.d(`${TAG} :saveDraft finish, count = ${this._allDrafts?.length}`);

        this.changeListener.next(new ExecuteCreateEventUpdate(detailData.localDraftId!));
        return true;
    }

    async removeDraft(id: string): Promise<void> {
        LogUtils.d(`${TAG}:removeDraft id = ${id}, _allDrafts.count = ${this._allDrafts?.length}`);
        const indexData = this._allDrafts?.find((index) => index.localDraftId === id);

        if (this._allDrafts) _.remove(this._allDrafts, (index) => index.localDraftId === id);

        if (indexData != undefined) {
            await this._saveIndexFile().catchIgnore();
        }

        try {
            const fileName = await ExecuteConst.getDraftFile(id);

            const fileExist = await FileUtils.fileExists(fileName);
            if (fileExist) await FileUtils.deleteFile(fileName);
        } catch (e) {}

        this.changeListener.next(new ExecuteCreateEventDelete(id));
    }

    public generateDraftId(): string {
        return UUIDGen.generate();
    }

    async _saveIndexFile(): Promise<void> {
        const indexFile = await this._indexFile();

        const draft = new ExecuteCreateDraft();
        draft.allDraft = this._allDrafts;
        const jsonContent = JSON.stringify(draft);
        await FileUtils.writeAsString(indexFile, jsonContent);
    }

    async _indexFile(): Promise<string> {
        return `${await ExecuteConst.getDraftDir()}/index.json`;
    }
}
