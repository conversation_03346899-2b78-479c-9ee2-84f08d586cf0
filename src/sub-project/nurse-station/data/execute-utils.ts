/**
 * create by dengjie
 * desc:
 * create date 2020/11/3
 */
import { ChargeFormItem, ChargeFormItemComposeType, ChargeFormItemStatus, ChargeSourceFormType } from "../../charge/data/charge-beans";
import { GoodsInfo, GoodsType } from "../../base-business/data/beans";

export class ExecuteUtils {
    static executeFormStatus(chargeFormItems: ChargeFormItem[], overWriteStatus?: ChargeFormItemStatus): ChargeFormItemStatus {
        if (overWriteStatus) {
            return overWriteStatus;
        }
        if (chargeFormItems.every((formItem) => formItem.status == ChargeFormItemStatus.unCharged)) {
            return ChargeFormItemStatus.unCharged;
        }
        if (
            chargeFormItems.every(
                (formItem) =>
                    formItem.status == ChargeFormItemStatus.refunded ||
                    formItem.status == ChargeFormItemStatus.partRefund ||
                    formItem.status == ChargeFormItemStatus.chargeBack
            )
        ) {
            return ChargeFormItemStatus.refunded;
        }
        return ChargeFormItemStatus.charged;
    }

    static needExecutive(item: ChargeFormItem, onlyExecuteAfterPaid?: boolean, sourceFormType?: ChargeSourceFormType): boolean {
        const { composeType, needExecutive: needExecutiveItem, productInfo } = item;
        // 输注处方、成药处方
        if (sourceFormType == ChargeSourceFormType.infusionPrescription || sourceFormType == ChargeSourceFormType.westernPrescription) {
            if (item) {
                return (
                    item.needExecutive === 1 &&
                    (item.executedUnitCount ?? 0) < (item.usageInfo?.executedTotalCount ?? 0) &&
                    (item.status ?? 0) <= 1
                );
            }
            return false;
        } else if (sourceFormType == ChargeSourceFormType.externalPrescription) {
            return !!(
                item &&
                (item.executedUnitCount ?? 0) < (item.unitCount ?? 0) / (item.usageInfo?.externalUnitCount || 1) &&
                (item.status ?? 0) <= 1 &&
                needExecutiveItem
            );
        } else {
            let _needExecutive; // 药品项目可否执行
            // 套餐的执行项能否执行取productInfo里面的needExecutive
            // 非套餐执行项能否执行取item上的needExecutive

            if (composeType !== ChargeFormItemComposeType.nonCompose) {
                _needExecutive = needExecutiveItem;
            } else {
                _needExecutive = productInfo && (item?.productInfo as GoodsInfo)?.needExecutive;
            }

            if (onlyExecuteAfterPaid && item.status !== ChargeFormItemStatus.charged) return false; // 项目是否仅在收费后可执行

            return Boolean(
                item &&
                    (item.productType == GoodsType.treatment ||
                        item.productType == GoodsType.medicine ||
                        item.productType == GoodsType.nurseProduct ||
                        item.productType == GoodsType.otherGoods49) &&
                    (item.executedUnitCount ?? 0) < (item.unitCount ?? 0) &&
                    (item.status ?? 0) <= ChargeFormItemStatus.charged &&
                    _needExecutive
            );
        }
    }
}
