import { ChargeFormItem } from "../../charge/data/charge-beans";
import { ExecuteItemUsage } from "../patient-order-execute-product-dialog";
import { AttachmentItem } from "../../base-business/data/beans";
import { JsonProperty } from "../../common-base-module/json-mapper/json-mapper";

/**
 * create by dengjie
 * desc:
 * create date 2020/6/9
 */

export class ChargeExecuteStatus {
    static EXECUTION_ORDER_STATUS_UNCHARGED = 0; // 未收费 已退费
    static EXECUTION_ORDER_STATUS_UNEXECUTED = 1; // 待执行
    static EXECUTION_ORDER_STATUS_EXECUTED = 2; // 已执行
}
export enum ExecuteTabType {
    allBill, // 全部开单
    myBill, // 我的开单
    myExecute, // 我的执行
}

export enum ExecuteSourceType {
    clinic,
    chain,
}

export class ExecuteListUtils {
    static fillCanExecuteListUtils(
        oldList: Map<ChargeFormItem, ExecuteItemUsage>,
        fillToList: Map<ChargeFormItem, ExecuteItemUsage>
    ): Map<ChargeFormItem, ExecuteItemUsage> {
        if (!oldList.size) return fillToList;
        for (const items of fillToList) {
            const _chargeForm = items[0];
            if (oldList.has(_chargeForm)) {
                fillToList.set(_chargeForm, oldList.get(_chargeForm)!);
            }
        }
        return fillToList;
    }
}

export class ExecuteEffectTemplateTreatmentItem {
    groupName?: string;
    list?: string[];
}

export class ExecuteEffectTemplateDetail {
    chainId?: string;
    createdBy?: string;
    createdByName?: string;
    treatmentMethod?: string;
    etiologyPathogenesis?: ExecuteEffectTemplateTreatmentItem[];
    treatmentResponse?: ExecuteEffectTemplateTreatmentItem[];
    treatmentResult?: ExecuteEffectTemplateTreatmentItem[];
    treatmentSite?: string[];
}

export enum ExecuteEffectGroupAddType {
    fromOnline,
    fromLocal,
}

export class ExecuteAttachmentItem extends AttachmentItem {
    fileSize?: number;
}

export class ExecuteEffectGroup {
    id?: string;

    templateDetail?: ExecuteEffectTemplateDetail;

    treatmentMethod?: string; //治疗方法
    treatmentSite?: string; //治疗部位
    treatmentResponse?: string; //治疗反应
    etiologyPathogenesis?: string; //病因病机;
    treatmentResult?: string; //治疗结果

    @JsonProperty({ type: Array, clazz: ExecuteAttachmentItem })
    attachments?: ExecuteAttachmentItem[]; //上传的图片地址

    __type?: ExecuteEffectGroupAddType; //是否新增
}
