/**
 * create by deng<PERSON>e
 * desc: 选择执行项目弹窗
 * create date 2020/9/9
 */

import { Abc<PERSON>ie<PERSON> } from "../base-ui/views/abc-view";
import { ABCNavigator, TransitionType } from "../base-ui/views/abc-navigator";
import { ScrollView, Text, View } from "@hippy/react";
import React from "react";
import { BaseComponent } from "../base-ui/base-component";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { AbcBasePanel, BottomSheetHelper } from "../base-ui/abc-app-library";
import { AbcIndeterminateNewCheckbox, IndeterminateCheckboxStatus } from "../base-ui/views/abc-checkbox";
import { NewStyleNumberStepperInputView } from "../base-ui/views/number-stepper-input-view";
import { IconFontView, SizedBox, ToolBarButtonStyle1 } from "../base-ui";
import { SafeAreaBottomView } from "../base-ui/safe_area_view";
import { ExecuteChargeStatusViewText } from "../charge/view/charge-views";
import { ChargeFormItem, ChargeFormItemStatus, ChargeSourceFormType } from "../charge/data/charge-beans";
import { pxToDp } from "../base-ui/utils/ui-utils";
import { UIUtils } from "../base-ui/utils";
import { Toast } from "../base-ui/dialog/toast";
import _ from "lodash";
import { ExecuteUtils } from "./data/execute-utils";
import { GoodsUtils } from "../base-business/utils/utils";
import { ABCUtils } from "../base-ui/utils/utils";
import { AbcMap } from "../base-ui/utils/abc-map";

interface PatientOrderExecuteProductDialogProps {
    form: Map<ChargeFormItem, ExecuteItemUsage>;
    invoicePartCharged?: boolean;
    onlyExecuteAfterPaid?: boolean;
}
export class PatientOrderExecuteProductDialog extends BaseComponent<PatientOrderExecuteProductDialogProps> {
    private _executeForm: Map<ChargeFormItem, ExecuteItemUsage> = new Map<ChargeFormItem, ExecuteItemUsage>();
    private _executeItemCount = 0;
    private _infusionFilters: ChargeFormItem[][] | undefined;
    private _westernFilters: ChargeFormItem[][] | undefined;

    static async show(
        form: Map<ChargeFormItem, ExecuteItemUsage>,
        invoicePartCharged?: boolean,
        onlyExecuteAfterPaid?: boolean
    ): Promise<Map<ChargeFormItem, ExecuteItemUsage>> {
        const view = (
            <View collapsable={false} style={{ flex: 1 }}>
                <PatientOrderExecuteProductDialog
                    form={form}
                    invoicePartCharged={invoicePartCharged}
                    onlyExecuteAfterPaid={onlyExecuteAfterPaid}
                />
                <SafeAreaBottomView bottomSafeAreaColor={Colors.white} />
            </View>
        );
        return ABCNavigator.navigateToPage(view, {
            transitionType: TransitionType.inFromBottom,
            backgroundColor: 0x33000000,
            enableBackgroundAnimation: true,
        });
    }
    constructor(props: PatientOrderExecuteProductDialogProps) {
        super(props);
        this._executeForm = new Map<ChargeFormItem, ExecuteItemUsage>(props.form);

        for (const items of this.props.form) {
            const formItem = items[0];
            const usage = items[1];
            if (formItem.isCompose) {
                this._executeItemCount += usage.composeUsage?.size ?? 0;
            } else {
                this._executeItemCount++;
            }
        }

        if (props.form) {
            this._infusionFilters = [...this._renderInfusionFilters([...props.form.keys()]).values()];
            this._westernFilters = [...this._renderWesternFilters([...props.form.keys()]).values()];
        }
    }

    get hasExecuteItem(): IndeterminateCheckboxStatus {
        let _status = IndeterminateCheckboxStatus.none;
        let _allStatus = IndeterminateCheckboxStatus.all;

        for (const items of this._executeForm) {
            const formItem = items[0];
            const usage = items[1];
            if (formItem.isCompose) {
                for (const composeItems of usage.composeUsage ?? []) {
                    const composeUsage = composeItems[1];
                    if (composeUsage.checked) {
                        _status = IndeterminateCheckboxStatus.some;
                    } else {
                        _allStatus = IndeterminateCheckboxStatus.some;
                    }
                }
            } else {
                if (usage.checked) {
                    _status = IndeterminateCheckboxStatus.some;
                } else {
                    _allStatus = IndeterminateCheckboxStatus.some;
                }
            }
        }
        return _status ? _allStatus : _status;
    }

    private _validInfo(): boolean {
        let _validStatus = true;
        // 需要过滤掉没有checked的项
        this._executeForm.forEach((usage) => {
            if (usage.checked) {
                if (_.isNaN(usage.count)) {
                    _validStatus = false;
                    return;
                }
                if ((usage.count ?? 0) <= 0) {
                    _validStatus = false;
                }
                if (usage.composeUsage?.size) {
                    usage.composeUsage.forEach((composeUsage) => {
                        if (composeUsage.checked) {
                            if (_.isNaN(composeUsage.count)) {
                                _validStatus = false;
                                return;
                            }
                            if ((composeUsage.count ?? 0) <= 0) {
                                _validStatus = false;
                            }
                        }
                    });
                }
            }
        });
        return _validStatus;
    }

    // 单项
    changeExecuteItemCount(executeItem: ChargeFormItem, usage: ExecuteItemUsage): void {
        if (usage.checked) {
            //修改
            this._executeForm.set(executeItem, usage);
        } else {
            // 删除项目
            if (!executeItem.isCompose) {
                usage.checked = false;
            } else {
                executeItem.composeChildren?.forEach((composeItem) => {
                    if (usage.checked) {
                        this._executeForm.set(composeItem, usage);
                    } else {
                        composeItem.checked = false;
                    }
                });
            }
        }
        this.setState({});
    }

    // 选择处方号
    changeExecuteGroupCount(chargeFormId: string, list: ChargeFormItem[] = [], status?: boolean): void {
        const formItemList = list.filter((f) => f.chargeFormId == chargeFormId) ?? [];
        formItemList.forEach((it) => {
            let usage = this._executeForm.get(it);
            if (usage) {
                usage.checked = !status;
            } else {
                usage = new ExecuteItemUsage();
                usage.checked = true;
            }

            this.changeExecuteItemCount(it, usage);
        });
    }

    // 选择处方组
    changeExecuteGroup(chargeFormId: string, groupId: string, list: ChargeFormItem[] = [], usage: ExecuteItemUsage): void {
        const formItemList = list.filter((it) => it.chargeFormId == chargeFormId && it.groupId?.toString() == groupId) ?? [];

        formItemList.forEach((it) => {
            const _usage = this._executeForm.get(it);

            if (_usage) {
                _usage.checked = usage.checked;
                _usage.count = usage.count;
            } else {
                usage = new ExecuteItemUsage();
                usage.checked = true;
            }
        });
        this.setState({});
    }

    // 选择处方组状态
    checkGroupStatus(chargeFormId: string, list: ChargeFormItem[] = []): boolean {
        const allList = list;
        const formItemList = allList.filter((it) => it.chargeFormId == chargeFormId) ?? [];
        let status = IndeterminateCheckboxStatus.some;
        const usageList = formItemList.map((it) => {
            return this._executeForm.get(it);
        });

        if (usageList.every((it) => !!it?.checked)) {
            status = IndeterminateCheckboxStatus.all;
        } else if (usageList.every((it) => !it?.checked)) {
            status = IndeterminateCheckboxStatus.none;
        }
        switch (status) {
            case IndeterminateCheckboxStatus.all: {
                return true;
            }
            case IndeterminateCheckboxStatus.some: {
                return false;
            }
            case IndeterminateCheckboxStatus.none: {
                return false;
            }
        }
    }

    // 全选
    private _checkedAll(status: boolean): void {
        this._executeForm.forEach((usage) => {
            usage.checked = status;
            if (usage.composeUsage?.size) {
                usage.composeUsage.forEach((composeUsage) => {
                    composeUsage.checked = status;
                });
            }
        });
        this.setState({});
    }

    _renderInfusionFilters(chargeFormItems: ChargeFormItem[]): Map<string, ChargeFormItem[]> {
        const infusionForm = chargeFormItems.filter(
            (f) => f.sourceFormType__ == ChargeSourceFormType.infusionPrescription && f.drugsCanExecutive && f.drugsExecutiveCount
        ); // 去除输注处方可执行药品中没有剩余执行次数的药品
        const infusionFormSet: Map<string, ChargeFormItem[]> = new Map();
        infusionForm.forEach((it) => {
            let forms: ChargeFormItem[] = [];
            if (infusionFormSet.has(it.chargeFormId ?? "")) {
                forms = infusionFormSet.get(it.chargeFormId ?? "") ?? [];
            }
            forms.push(it);
            infusionFormSet.set(it.chargeFormId ?? "", forms);
        });

        return infusionFormSet;
    }

    _renderWesternFilters(chargeFormItems: ChargeFormItem[]): Map<string, ChargeFormItem[]> {
        const westernForm = chargeFormItems.filter(
            (it) => it.sourceFormType__ == ChargeSourceFormType.westernPrescription && it.drugsCanExecutive && it.drugsExecutiveCount
        ); // 去除成药处方可执行药品中没有剩余执行次数的药品

        const westernFormSet: Map<string, ChargeFormItem[]> = new Map();
        westernForm.forEach((it) => {
            let forms: ChargeFormItem[] = [];
            if (westernFormSet.has(it.chargeFormId ?? "")) {
                forms = westernFormSet.get(it.chargeFormId ?? "") ?? [];
            }
            forms.push(it);
            westernFormSet.set(it.chargeFormId ?? "", forms);
        });

        return westernFormSet;
    }

    render(): JSX.Element {
        const form = [...this.props.form.keys()];
        const normalFormFilter = form.filter(
            (it) =>
                !(
                    it.sourceFormType__ == ChargeSourceFormType.westernPrescription ||
                    it.sourceFormType__ == ChargeSourceFormType.infusionPrescription
                )
        );
        return (
            <View style={{ flex: 1 }}>
                <View style={{ height: UIUtils.safeStatusHeight() }} />
                <AbcView
                    style={{ flex: 1 }}
                    onClick={() => {
                        ABCNavigator.pop();
                    }}
                />
                <AbcBasePanel panelStyle={[{ flexShrink: 1, minHeight: pxToDp(400) }]}>
                    {BottomSheetHelper.createTitleBar("选择执行项目")}
                    <ScrollView>
                        {normalFormFilter.map((item, index) => (
                            <_ExecuteProductItem
                                key={index}
                                formItem={item}
                                normalForm={normalFormFilter}
                                usage={this._executeForm.get(item)}
                                overWriteStatus={this.props.invoicePartCharged ? ChargeFormItemStatus.partCharged : undefined}
                                onlyExecuteAfterPaid={this.props.onlyExecuteAfterPaid}
                                onChange={(formItem, usage) => {
                                    this.changeExecuteItemCount(formItem, usage);
                                }}
                            />
                        ))}

                        {this._westernFilters?.map((it, index) => {
                            const showUsage = new Map();
                            const groupIdCollect: number[] = [];
                            it.forEach((k) => {
                                if (!!k.groupId && groupIdCollect.indexOf(k.groupId) == -1) {
                                    groupIdCollect.push(k.groupId);
                                }
                            });
                            const groupList = new AbcMap<number, ChargeFormItem[]>();
                            groupIdCollect.forEach((groupId) => {
                                const group = it.filter((t) => t.groupId == groupId);
                                if (group.length > 0 && !groupList.has(groupId)) groupList.set(groupId, group);
                            });
                            return (
                                <View
                                    key={index}
                                    style={[ABCStyles.bottomLine, { marginHorizontal: Sizes.dp16, paddingBottom: Sizes.dp16 }]}
                                >
                                    {it.map((item, indexx) => {
                                        const checkGroupStatus = this.checkGroupStatus(item.chargeFormId ?? "", it);
                                        const _showUsage = showUsage.get(item.groupId);
                                        showUsage.set(item.groupId, true);
                                        let lastChargeFormItem: ChargeFormItem | undefined = undefined;
                                        if (!_.isNil(item.groupId)) {
                                            const list = groupList.get(item.groupId);
                                            if (!!list?.length) lastChargeFormItem = list[list.length - 1];
                                        }

                                        //处方只有一个分组也需要显示对应的执行次数及状态
                                        const onlyOneGroup =
                                            !_.isNil(item.groupId) && it?.filter((t) => t?.groupId == item?.groupId)?.length == 1;

                                        return (
                                            <View key={`${item.compareKey()}`}>
                                                {!indexx && (
                                                    <AbcView
                                                        style={[ABCStyles.rowAlignCenter, { height: Sizes.dp56 }]}
                                                        onClick={() => {
                                                            this.changeExecuteGroupCount(item.chargeFormId ?? "", it, checkGroupStatus);
                                                        }}
                                                    >
                                                        {checkGroupStatus ? (
                                                            <AbcView
                                                                style={{ width: Sizes.dp16, height: Sizes.dp16 }}
                                                                onClick={() =>
                                                                    this.changeExecuteGroupCount(
                                                                        item.chargeFormId ?? "",
                                                                        it,
                                                                        checkGroupStatus
                                                                    )
                                                                }
                                                            >
                                                                <IconFontView name={"Chosen"} size={Sizes.dp16} color={Colors.mainColor} />
                                                            </AbcView>
                                                        ) : (
                                                            <AbcView
                                                                style={{
                                                                    width: Sizes.dp16,
                                                                    height: Sizes.dp16,
                                                                    borderWidth: Sizes.dpHalf,
                                                                    borderRadius: Sizes.dp16,
                                                                    borderColor: Colors.P1,
                                                                }}
                                                                onClick={() =>
                                                                    this.changeExecuteGroupCount(
                                                                        item.chargeFormId ?? "",
                                                                        it,
                                                                        checkGroupStatus
                                                                    )
                                                                }
                                                            />
                                                        )}
                                                        <SizedBox width={Sizes.dp8} />
                                                        <Text style={TextStyles.t16MT1}>
                                                            {`成药处方${
                                                                (this._westernFilters?.length ?? index) > 1
                                                                    ? ABCUtils.toChineseNum((index ?? 0) + 1)
                                                                    : ""
                                                            }`}
                                                        </Text>
                                                    </AbcView>
                                                )}
                                                <_ExecuteProductItem
                                                    key={`${index}${indexx}`}
                                                    formItem={item}
                                                    usage={this._executeForm.get(item)}
                                                    overWriteStatus={
                                                        this.props.invoicePartCharged ? ChargeFormItemStatus.partCharged : undefined
                                                    }
                                                    onlyExecuteAfterPaid={this.props.onlyExecuteAfterPaid}
                                                    showUsage={!_showUsage}
                                                    onChange={(formItem, usage) => {
                                                        this.changeExecuteGroup(
                                                            formItem.chargeFormId ?? "",
                                                            formItem.groupId?.toString() ?? "",
                                                            it,
                                                            usage
                                                        );
                                                    }}
                                                    showFlag={item.id == lastChargeFormItem?.id}
                                                    onlyOneGroup={onlyOneGroup}
                                                />
                                            </View>
                                        );
                                    })}
                                </View>
                            );
                        })}

                        {this._infusionFilters?.map((it, index) => {
                            const showUsage = new Map();
                            const groupIdCollect: number[] = [];
                            it.forEach((k) => {
                                if (!!k.groupId && groupIdCollect.indexOf(k.groupId) == -1) {
                                    groupIdCollect.push(k.groupId);
                                }
                            });
                            const groupList = new AbcMap<number, ChargeFormItem[]>();
                            groupIdCollect.forEach((groupId) => {
                                const group = it.filter((t) => t.groupId == groupId);
                                if (group.length > 0 && !groupList.has(groupId)) groupList.set(groupId, group);
                            });

                            return (
                                <View
                                    key={index}
                                    style={[ABCStyles.bottomLine, { marginHorizontal: Sizes.dp16, paddingBottom: Sizes.dp16 }]}
                                >
                                    {it.map((item, indexx) => {
                                        const checkGroupStatus = this.checkGroupStatus(item.chargeFormId ?? "", it);
                                        const _showUsage = showUsage.get(item.groupId);
                                        showUsage.set(item.groupId, true);
                                        let lastChargeFormItem: ChargeFormItem | undefined = undefined;
                                        if (!_.isNil(item.groupId)) {
                                            const list = groupList.get(item.groupId);
                                            if (!!list?.length) lastChargeFormItem = list[list.length - 1];
                                        }
                                        //处方只有一个分组也需要显示对应的执行次数及状态
                                        const onlyOneGroup =
                                            !_.isNil(item.groupId) && it?.filter((t) => t?.groupId == item?.groupId)?.length == 1;

                                        return (
                                            <View key={`${item.compareKey()}`}>
                                                {!indexx && (
                                                    <AbcView
                                                        style={[
                                                            ABCStyles.rowAlignCenter,
                                                            { height: Sizes.dp22, marginTop: Sizes.listHorizontalMargin },
                                                        ]}
                                                        onClick={() => {
                                                            this.changeExecuteGroupCount(item.chargeFormId ?? "", it, checkGroupStatus);
                                                        }}
                                                    >
                                                        {this.checkGroupStatus(item.chargeFormId ?? "", it) ? (
                                                            <AbcView
                                                                style={{ width: Sizes.dp16, height: Sizes.dp16 }}
                                                                onClick={() => {
                                                                    this.changeExecuteGroupCount(
                                                                        item.chargeFormId ?? "",
                                                                        it,
                                                                        checkGroupStatus
                                                                    );
                                                                }}
                                                            >
                                                                <IconFontView name={"Chosen"} size={Sizes.dp16} color={Colors.mainColor} />
                                                            </AbcView>
                                                        ) : (
                                                            <AbcView
                                                                style={{
                                                                    width: Sizes.dp16,
                                                                    height: Sizes.dp16,
                                                                    borderWidth: Sizes.dpHalf,
                                                                    borderRadius: Sizes.dp16,
                                                                    borderColor: Colors.P1,
                                                                }}
                                                                onClick={() => {
                                                                    this.changeExecuteGroupCount(
                                                                        item.chargeFormId ?? "",
                                                                        it,
                                                                        checkGroupStatus
                                                                    );
                                                                }}
                                                            />
                                                        )}
                                                        <SizedBox width={Sizes.dp8} />
                                                        <Text style={TextStyles.t16MT1}>
                                                            {`输注处方${
                                                                (this._infusionFilters?.length ?? index) > 1
                                                                    ? ABCUtils.toChineseNum((index ?? 0) + 1)
                                                                    : ""
                                                            }`}
                                                        </Text>
                                                    </AbcView>
                                                )}
                                                <_ExecuteProductItem
                                                    formItem={item}
                                                    usage={this._executeForm.get(item)}
                                                    overWriteStatus={
                                                        this.props.invoicePartCharged ? ChargeFormItemStatus.partCharged : undefined
                                                    }
                                                    onlyExecuteAfterPaid={this.props.onlyExecuteAfterPaid}
                                                    showUsage={!_showUsage}
                                                    onChange={(formItem, usage) => {
                                                        this.changeExecuteGroup(
                                                            formItem.chargeFormId ?? "",
                                                            formItem.groupId?.toString() ?? "",
                                                            it,
                                                            usage
                                                        );
                                                    }}
                                                    showFlag={item.id == lastChargeFormItem?.id}
                                                    onlyOneGroup={onlyOneGroup}
                                                />
                                            </View>
                                        );
                                    })}
                                </View>
                            );
                        })}
                    </ScrollView>
                    <View style={[ABCStyles.rowAlignCenter, { height: Sizes.dp68, padding: Sizes.dp12, backgroundColor: Colors.white }]}>
                        {this._executeItemCount > 1 && (
                            <AbcIndeterminateNewCheckbox
                                check={!!this.hasExecuteItem}
                                checkStatus={this.hasExecuteItem}
                                style={{ marginRight: Sizes.dp43 }}
                                textStyle={{ marginLeft: Sizes.dp8, ...TextStyles.t16MT1 }}
                                text={"全选"}
                                onChange={(status) => {
                                    this._checkedAll(status);
                                }}
                            />
                        )}
                        <View style={{ flex: 1 }}>
                            <ToolBarButtonStyle1
                                style={
                                    this.hasExecuteItem
                                        ? {}
                                        : { backgroundColor: Colors.bdColor, borderColor: Colors.bdColor, fontColor: Colors.S2 }
                                }
                                text={"完成"}
                                onClick={() => {
                                    if (!this.hasExecuteItem) return;

                                    if (this._validInfo()) {
                                        ABCNavigator.pop(this._executeForm);
                                    } else {
                                        Toast.show("执行次数必须大于0", { warning: true }).then();
                                    }
                                }}
                            />
                        </View>
                    </View>
                </AbcBasePanel>
            </View>
        );
    }
}

export class ExecuteItemUsage {
    count = 1;
    checked?: boolean;

    composeUsage?: Map<ChargeFormItem, ExecuteItemUsage>;
}

interface _ExecuteProductItemProps {
    formItem: ChargeFormItem;
    normalForm?: ChargeFormItem[]; // 非处方类型表单
    usage?: ExecuteItemUsage;
    overWriteStatus?: ChargeFormItemStatus;
    onlyExecuteAfterPaid?: boolean;
    showUsage?: boolean;
    showFlag?: boolean;
    onlyOneGroup?: boolean; //争对分组只有一个的情况

    onChange?(arg1: ChargeFormItem, arg2: ExecuteItemUsage): void;
}

class _ExecuteProductItem extends BaseComponent<_ExecuteProductItemProps> {
    private _executeItemMap: Map<ChargeFormItem, ExecuteItemUsage> = new Map<ChargeFormItem, ExecuteItemUsage>();

    constructor(props: _ExecuteProductItemProps) {
        super(props);
        this._executeItemMap.set(props.formItem, props.usage ?? new ExecuteItemUsage());
    }

    static defaultProps = {
        showUsage: true,
    };

    componentWillReceiveProps(nextProps: Readonly<_ExecuteProductItemProps>) {
        this._executeItemMap.set(nextProps.formItem, nextProps.usage ?? new ExecuteItemUsage());
    }

    private _changeCount(formItem: ChargeFormItem, count: number) {
        if (this.props.formItem.isCompose) {
            //套餐
            const _allUsage = this._executeItemMap.get(this.props.formItem) ?? new ExecuteItemUsage();
            // _allUsage.count = count;
            _allUsage.checked = true;
            const _usage = _allUsage.composeUsage?.get(formItem) ?? new ExecuteItemUsage();
            _usage.count = count;
            _usage.checked = true;
            _allUsage.composeUsage?.set(formItem, _usage);
            this._executeItemMap.set(this.props.formItem, _allUsage);
            this.setState({});
            this.props.onChange?.(this.props.formItem, _allUsage);
        } else {
            //普通
            const _usage = this._executeItemMap.get(formItem) ?? new ExecuteItemUsage();
            _usage.count = count;
            _usage.checked = true;
            this._executeItemMap.set(this.props.formItem, _usage);
            this.setState({});
            this.props.onChange?.(formItem, _usage);
        }
    }

    private _changeChecked(formItem: ChargeFormItem, check: boolean) {
        if (this.props.formItem.isCompose) {
            //套餐
            const _allUsage = this._executeItemMap.get(this.props.formItem) ?? new ExecuteItemUsage();
            _allUsage.checked = false;

            const _usage = _allUsage.composeUsage?.get(formItem) ?? new ExecuteItemUsage();
            if (_usage.checked == check) return;
            _usage.checked = check;
            _allUsage.composeUsage?.set(formItem, _usage);

            _allUsage.composeUsage?.forEach((item) => {
                if (item.checked) {
                    _allUsage.checked = true;
                }
            });
            this._executeItemMap.set(this.props.formItem, _allUsage);
            this.props.onChange?.(this.props.formItem, _allUsage);
        } else {
            //普通
            const _usage = this._executeItemMap.get(formItem) ?? new ExecuteItemUsage();
            if (_usage.checked == check) return;
            _usage.checked = check;
            this._executeItemMap.set(formItem, _usage);
            this.props.onChange?.(formItem, _usage);
        }
        this.setState({});
    }

    // 渲染单项执行项目
    createItem(options: {
        formItem: ChargeFormItem;
        index?: number;
        length?: number;
        isCompose?: boolean; // 套餐
        composeTitle?: string;
        paddingTop?: number;
        paddingBottom?: number;
        showBottomLine?: boolean;
    }): JSX.Element {
        const { formItem, index, isCompose, showBottomLine, composeTitle, paddingTop, paddingBottom } = options;
        const { overWriteStatus } = this.props;
        const notNeedExecute = !ExecuteUtils.needExecutive(formItem, this.props.onlyExecuteAfterPaid);
        const _usage = isCompose
            ? this._executeItemMap.get(this.props.formItem)?.composeUsage?.get(formItem)
            : this._executeItemMap.get(formItem);

        //剔除套餐中无法执行的项目
        if (!_usage) return <View />;

        let unitCount = Math.round(formItem?.unitCount ?? 0);
        if (formItem.sourceFormType__ == ChargeSourceFormType.externalPrescription) {
            unitCount = formItem.externalPrescriptionUnitCount;
        }
        return (
            <View key={formItem.id} style={{ backgroundColor: Colors.white }}>
                {isCompose && composeTitle && index == 0 && (
                    <View style={[ABCStyles.topLine, ABCStyles.rowAlignCenter, { height: Sizes.dp56 }]}>
                        <Text style={[TextStyles.t16NT1, { flexShrink: 1 }]} numberOfLines={1}>
                            {`${composeTitle}【套餐】`}
                        </Text>
                    </View>
                )}
                <View
                    style={[
                        showBottomLine ? ABCStyles.bottomLine : {},
                        { backgroundColor: Colors.white, paddingTop: paddingTop, paddingBottom: paddingBottom },
                    ]}
                >
                    <AbcView
                        style={[ABCStyles.rowAlignCenter, { flex: 1, height: Sizes.dp28 }]}
                        onClick={() => {
                            this._changeChecked(formItem, !_usage?.checked);
                        }}
                    >
                        {_usage?.checked ? (
                            <AbcView style={{ width: Sizes.dp16, height: Sizes.dp16 }} onClick={() => this._changeChecked(formItem, false)}>
                                <IconFontView name={"Chosen"} size={Sizes.dp16} color={Colors.mainColor} />
                            </AbcView>
                        ) : (
                            <AbcView
                                style={{
                                    width: Sizes.dp16,
                                    height: Sizes.dp16,
                                    borderWidth: Sizes.dpHalf,
                                    borderRadius: Sizes.dp16,
                                    borderColor: Colors.P1,
                                }}
                                onClick={() => this._changeChecked(formItem, true)}
                            />
                        )}
                        <SizedBox width={Sizes.dp8} />
                        <Text style={[TextStyles.t16MT1, { flex: 1 }]} numberOfLines={1}>
                            {formItem.name ?? ""}
                        </Text>
                        {/*{_usage?.checked && (*/}
                        <NewStyleNumberStepperInputView
                            style={{ width: Sizes.dp100 }}
                            value={_usage?.checked ? _usage?.count ?? 1 : undefined}
                            minCount={1}
                            maxCount={unitCount - (formItem?.executedUnitCount ?? 0)}
                            onChanged={(value) => {
                                this._changeCount(formItem, value);
                            }}
                        />
                        {/*)}*/}
                    </AbcView>
                    <SizedBox height={Sizes.dp8} />
                    <View style={[ABCStyles.rowAlignCenter, { justifyContent: "flex-end" }]}>
                        <Text style={TextStyles.t16MT2}>{formItem.sourceFormType__ == ChargeSourceFormType.gift ? "(赠)" : ""}</Text>
                        {(formItem.status != ChargeFormItemStatus.charged || overWriteStatus) && (
                            <ExecuteChargeStatusViewText showChargedStatus={true} chargeStatus={overWriteStatus ?? formItem.status} />
                        )}

                        <Text style={[TextStyles.t14NT4]}>
                            {`${formItem?.executedUnitCount || !notNeedExecute ? `${formItem?.executedUnitCount}/` : ""}${unitCount}${
                                GoodsUtils.unitIsCustom(formItem?.unit?.trim()) ? "*" : ""
                            }${formItem?.unit?.trim() ?? "次"}`}
                        </Text>
                    </View>
                </View>
            </View>
        );
    }

    // 普通（非套餐、处方项目）
    createNormalItem(): JSX.Element {
        const { normalForm } = this.props;
        const normaFormItems = normalForm?.filter((formItem) => !formItem.isCompose); // 排除套餐
        const views: JSX.Element[] = [];

        normaFormItems?.map((item, index) => {
            views.push(
                this.createItem({
                    formItem: item,
                    paddingTop: index == 0 ? Sizes.dp16 : undefined,
                    paddingBottom: Sizes.listHorizontalMargin,
                })
            );
        });

        return <View style={{ backgroundColor: Colors.white }}>{views}</View>;
    }

    // 套餐
    createCompose(options: { formItem: ChargeFormItem }): JSX.Element {
        const { formItem } = options;
        const views: JSX.Element[] = [];
        const filterNoExecutableItems: ChargeFormItem[] | undefined = formItem.composeChildren?.filter(
            (item) => !!this._executeItemMap.get(this.props.formItem)?.composeUsage?.get(item)
        );

        filterNoExecutableItems?.forEach((item, index, self) => {
            views.push(
                this.createItem({
                    formItem: item,
                    index: index,
                    length: self.length,
                    isCompose: true,
                    composeTitle: formItem.name,
                    paddingBottom: Sizes.listHorizontalMargin,
                    showBottomLine: (index ?? 0) + 1 == self.length,
                })
            );
        });
        if (!views.length) return <View />;
        return <View style={{ backgroundColor: Colors.white }}>{views}</View>;
    }

    // 处方组
    createPrescriptionGroup(formItem: ChargeFormItem): JSX.Element {
        const { showUsage, overWriteStatus, showFlag, onlyOneGroup } = this.props;
        const _chargeFormItems: ChargeFormItem[] = [];
        _chargeFormItems.push(formItem);

        const groupsView: JSX.Element[] = [];

        const _usage = this._executeItemMap.get(formItem);

        if (!_usage) return <View />;

        const filterExecutableItems: ChargeFormItem[] | undefined = _chargeFormItems.filter(
            (filter) => filter.status !== ChargeFormItemStatus.chargeBack
        );

        filterExecutableItems.forEach((item, index) => {
            let marginTop = 0;
            if (!showUsage && index == 0) {
                marginTop = Sizes.dp8;
            }

            if ((!!item.groupId && showUsage) || (!(!showUsage && showFlag) && formItem.groupId == null)) {
                marginTop = Sizes.listHorizontalMargin;
            }

            const isUnGroupedDrugs = !(!showUsage && showFlag) && formItem.groupId == null; // 未分组药品

            groupsView.push(
                <View key={index} style={{ backgroundColor: Colors.white }}>
                    <AbcView
                        key={formItem.id}
                        style={[ABCStyles.rowAlignCenter, { flex: 1, marginTop: marginTop }]}
                        onClick={() => {
                            this._changeChecked(formItem, !_usage?.checked);
                        }}
                    >
                        <View style={[ABCStyles.rowAlignCenter, { marginRight: Sizes.dp8 }]}>
                            {(!!item.groupId && showUsage) || isUnGroupedDrugs ? (
                                _usage?.checked ? (
                                    <AbcView
                                        style={{ width: Sizes.dp16, height: Sizes.dp16 }}
                                        onClick={() => this._changeChecked(formItem, !_usage?.checked)}
                                    >
                                        <IconFontView name={"Chosen"} size={Sizes.dp16} color={Colors.mainColor} />
                                    </AbcView>
                                ) : (
                                    <AbcView
                                        style={{
                                            width: Sizes.dp16,
                                            height: Sizes.dp16,
                                            borderWidth: Sizes.dpHalf,
                                            borderRadius: Sizes.dp16,
                                            borderColor: Colors.P1,
                                        }}
                                        onClick={() => this._changeChecked(formItem, !_usage?.checked)}
                                    />
                                )
                            ) : (
                                <View style={{ width: Sizes.dp16 }} />
                            )}
                        </View>

                        {!!item.groupId && showUsage && (
                            <Text style={[TextStyles.t16MT1, { textAlign: "center", paddingRight: Sizes.dp4 }]}>
                                {formItem.groupId ? ABCUtils.toCircledNum(formItem.groupId) : ""}
                            </Text>
                        )}

                        <Text style={[TextStyles.t16MT1, { flex: 1 }]} numberOfLines={1}>
                            {formItem.name ?? ""}
                        </Text>

                        {(showUsage || isUnGroupedDrugs) && (
                            <NewStyleNumberStepperInputView
                                style={{ width: Sizes.dp100 }}
                                value={_usage?.checked ? _usage?.count ?? 1 : undefined}
                                minCount={1}
                                maxCount={(formItem?.usageInfo?.executedTotalCount ?? 0) - (formItem?.executedUnitCount ?? 0)}
                                onChanged={(value) => {
                                    this._changeCount(formItem, value);
                                }}
                            />
                        )}

                        {!showUsage && showFlag && (
                            // 药品组最后一项药品显示状态
                            <View style={[ABCStyles.rowAlignCenter, { justifyContent: "flex-end" }]}>
                                {(formItem.status != ChargeFormItemStatus.charged || overWriteStatus) && (
                                    <ExecuteChargeStatusViewText
                                        showChargedStatus={true}
                                        chargeStatus={overWriteStatus ?? formItem.status}
                                    />
                                )}
                                <Text style={TextStyles.t14NT4}>
                                    {`${formItem?.executedUnitCount}/${formItem?.usageInfo?.executedTotalCount}次`}
                                </Text>
                            </View>
                        )}
                    </AbcView>
                    {(isUnGroupedDrugs || (onlyOneGroup && showFlag)) && (
                        // 单个药品显示状态
                        <View style={[ABCStyles.rowAlignCenter, { justifyContent: "flex-end", marginTop: Sizes.dp8 }]}>
                            {(formItem.status != ChargeFormItemStatus.charged || overWriteStatus) && (
                                <ExecuteChargeStatusViewText showChargedStatus={true} chargeStatus={overWriteStatus ?? formItem.status} />
                            )}
                            <Text style={TextStyles.t14NT4}>
                                {`${formItem?.executedUnitCount}/${formItem?.usageInfo?.executedTotalCount}次`}
                            </Text>
                        </View>
                    )}
                </View>
            );
        });

        return <View>{groupsView}</View>;
    }

    render() {
        const formItem = this.props.formItem;

        // 输注处方项目线条在内部调整
        const noMarginHorizontal =
            formItem?.sourceFormType__ == ChargeSourceFormType.infusionPrescription ||
            formItem?.sourceFormType__ == ChargeSourceFormType.westernPrescription;

        const isComposeFormItems = [];
        const isWesternAndInfusionPrescription = [];

        // 套餐项目
        if (formItem.isCompose) {
            isComposeFormItems.push(this.createCompose({ formItem: formItem }));
        }

        // 处方项目
        if (noMarginHorizontal) {
            isWesternAndInfusionPrescription.push(this.createPrescriptionGroup(formItem));
        }

        return (
            <View style={{ marginHorizontal: noMarginHorizontal ? undefined : Sizes.listHorizontalMargin, backgroundColor: Colors.white }}>
                {this.createNormalItem()}
                {isComposeFormItems}
                {isWesternAndInfusionPrescription}
            </View>
        );
    }
}
