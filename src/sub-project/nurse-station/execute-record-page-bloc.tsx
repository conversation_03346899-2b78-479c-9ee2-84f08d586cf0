/**
 * create by dengjie
 * desc:
 * create date 2020/6/11
 */

import { Bloc, BlocEvent } from "../bloc";
import React from "react";
import { EventName } from "../bloc/bloc";
import { ExecuteRecordItem, GetExecuteRecordRsp, PatientOrderDataAgent } from "./data/nurse-station-data";
import { ChargeFormItem, ChargeInvoiceDetailData } from "../charge/data/charge-beans";
import { LogUtils } from "../common-base-module/log";
import { switchMap } from "rxjs/operators";
import { fromPromise } from "rxjs/internal-compatibility";
import { ABCError } from "../common-base-module/common-error";
import { Subject } from "rxjs";
import _ from "lodash";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { PatientOrderExecutePage, PatientOrderExecutePageMode } from "./patient-order-execute-page";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { ExecuteItemUsage } from "./patient-order-execute-product-dialog";

class State {
    items: Array<ExecuteRecordItem> = [];

    executeDetail?: GetExecuteRecordRsp;

    loading = false;
    loadError: any = null;

    editing = false;

    detailData?: ChargeInvoiceDetailData;

    showChainName?: boolean;

    getChargeFormItemById(id: string): ChargeFormItem | null {
        LogUtils.d("getChargeFormItemById id = $id");
        for (const form of this.detailData?.chargeForms ?? []) {
            for (const formItem of form.chargeFormItems ?? []) {
                if (formItem.id == id) return formItem;
            }
        }
        return null;
    }

    get isEmpty(): boolean {
        return _.isEmpty(this.items);
    }

    clone(): State {
        return Object.assign(new State(), this);
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {}

class _EventToggleEditMode extends _Event {}

class _EventReload extends _Event {}

class _EventUndoExecuteRecord extends _Event {
    executeRecordItem: ExecuteRecordItem;

    constructor(executeRecordItem: ExecuteRecordItem) {
        super();
        this.executeRecordItem = executeRecordItem;
    }
}

class ExecuteRecordPageBloc extends Bloc<_Event, State> {
    _executeRecordTrigger = new Subject();
    static Context = React.createContext<ExecuteRecordPageBloc | undefined>(undefined);

    constructor(detailData: ChargeInvoiceDetailData) {
        super();

        this.innerState.detailData = detailData;
        this.dispatch(new _EventInit());
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    static fromContext(context: ExecuteRecordPageBloc): ExecuteRecordPageBloc {
        return context;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventToggleEditMode, this._mapEventToggleEditMode);
        map.set(_EventReload, this._mapEventReload);
        map.set(_EventUndoExecuteRecord, this._mapEventUndoExecuteRecord);

        return map;
    }

    async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        const _executeRecordTrigger = this._executeRecordTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.loadError = false;
                    this.innerState.loading = true;
                    this.update();
                    return fromPromise(
                        PatientOrderDataAgent.getExecuteRecordList(this.innerState.detailData!.id!).catch((error) => {
                            return new ABCError(error);
                        })
                    );
                })
            )
            .subscribe(
                (res) => {
                    if (res instanceof ABCError) return;
                    this.innerState.executeDetail = res;
                    this.innerState.items = res.executeRecords ?? [];
                    this.innerState.loading = false;
                    this.innerState.showChainName = !!res.existCrossExecuteClinic;
                    this.update();
                },
                (error) => {
                    this.innerState.loading = false;
                    this.innerState.loadError = error;
                    this.update();
                }
            );
        this.addDisposable(_executeRecordTrigger);

        PatientOrderDataAgent.patientOrderExecuteInvoicePublisher.subscribe(() => {
            this._executeRecordTrigger.next();
        });

        this._executeRecordTrigger.next();
    }

    async *_mapEventUpdate(/*event: _EventUpdate*/): AsyncGenerator<State> {
        yield this.innerState;
    }

    async *_mapEventToggleEditMode(/*event: _EventToggleEditMode*/): AsyncGenerator<State> {
        this.innerState.editing = !this.innerState.editing;
        this.update();
    }

    async *_mapEventReload(/*event: _EventReload*/): AsyncGenerator<State> {
        this._executeRecordTrigger.next();
    }

    async *_mapEventUndoExecuteRecord(event: _EventUndoExecuteRecord): AsyncGenerator<State> {
        const _chargeFormMap: Map<ChargeFormItem, ExecuteItemUsage> = new Map<ChargeFormItem, ExecuteItemUsage>();
        event.executeRecordItem.items?.forEach((item) => {
            _chargeFormMap.set(
                JsonMapper.deserialize(ChargeFormItem, {
                    name: item.executeItemName,
                    unit: item.unit,
                }),
                JsonMapper.deserialize(ExecuteItemUsage, {
                    count: item.count,
                    checked: true,
                })
            );
        });
        //添加输注处方项目
        event.executeRecordItem.prescriptionChargeExecuteForms?.forEach((form) => {
            form.chargeFormGroups?.forEach((item) => {
                item.groupItems?.forEach((itt) => {
                    _chargeFormMap.set(
                        JsonMapper.deserialize(ChargeFormItem, {
                            groupId: item.groupId,
                            name: itt,
                            unit: item.unit,
                        }),
                        JsonMapper.deserialize(ExecuteItemUsage, {
                            count: item.count,
                            checked: true,
                        })
                    );
                });
            });
        });
        ABCNavigator.navigateToPage(
            <PatientOrderExecutePage
                executeProductMap={_chargeFormMap}
                mode={PatientOrderExecutePageMode.undo}
                executeDetailInfo={event.executeRecordItem}
                patient={this.innerState.detailData?.patient}
                isHospitalizationSheet={this.innerState.detailData?.isHospitalizationSheet}
                detailDataId={this.innerState.detailData?.id}
            />
        ).then();
    }

    update(): void {
        this.dispatch(new _EventUpdate());
    }

    requestToggleEditMode(): void {
        this.dispatch(new _EventToggleEditMode());
    }

    requestReload(): void {
        this.dispatch(new _EventReload());
    }

    //撤销执行记录
    requestUndoExecuteRecord(executeRecordItem: ExecuteRecordItem): void {
        this.dispatch(new _EventUndoExecuteRecord(executeRecordItem));
    }
}

export { ExecuteRecordPageBloc, State };
