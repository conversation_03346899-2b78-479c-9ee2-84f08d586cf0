/**
 * create by deng<PERSON>e
 * desc:
 * create date 2020/6/10
 */

import React from "react";
import { Text, View } from "@hippy/react";
import { Sizes, TextStyles } from "../theme";

interface _ExecuteListItemViewProps {
    title?: string;
}

export class _ExecuteListItemView extends React.Component<_ExecuteListItemViewProps> {
    constructor(props: _ExecuteListItemViewProps) {
        super(props);
    }

    render(): JSX.Element {
        return (
            <View style={Sizes.paddingLTRB(Sizes.listHorizontalMargin, 0, Sizes.listHorizontalMargin, Sizes.dp24)}>
                <View style={{ marginBottom: Sizes.listHorizontalMargin }}>
                    <Text style={TextStyles.t16NT1}>{this.props.title ?? ""}</Text>
                </View>
                {this.props.children}
            </View>
        );
    }
}
