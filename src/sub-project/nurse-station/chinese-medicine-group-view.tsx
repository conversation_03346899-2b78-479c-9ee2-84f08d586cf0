/**
 * create by deng<PERSON>e
 * desc:
 * create date 2020/6/10
 */
import React from "react";
import {
    ChargeForm,
    ChargeFormItem,
    ChargeFormItemStatus,
    ChargeInvoiceDetailData,
    ChargeInvoiceSource,
} from "../charge/data/charge-beans";
import { Text, View } from "@hippy/react";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { ExecuteChargeStatusView } from "../charge/view/charge-views";
import { NumberUtils } from "../common-base-module/utils";
import { ABCUtils } from "../base-ui/utils/utils";
import { ExecuteInvoicePageBloc } from "./execute-invoice-page-bloc";
import { GoodsType } from "../base-business/data/beans";
import { PharmacyTagView } from "../outpatient/views/pharmacy-tag-view";
import { AbcCardHeader } from "../base-ui/abc-app-library/common/abc-card-header";
import { AbcBasePanel } from "../base-ui/abc-app-library";
import { pxToDp } from "../base-ui/utils/ui-utils";

interface ChineseMedicineGroupViewProps {
    patientOrderDetailData: ChargeInvoiceDetailData;
    chargeForms: Array<ChargeForm>;
}

export class ChineseMedicineGroupView extends React.Component<ChineseMedicineGroupViewProps, any> {
    constructor(props: ChineseMedicineGroupViewProps) {
        super(props);
    }

    render(): JSX.Element {
        const { chargeForms, patientOrderDetailData } = this.props;
        const views: JSX.Element[] = [];

        const total = chargeForms.length;
        chargeForms.forEach((formItem, index) => {
            views.push(
                <_ChineseMedicineGroupItemView
                    key={index}
                    chargeForm={formItem}
                    index={index}
                    total={total}
                    source={patientOrderDetailData.source}
                />
            );
        });
        return <View>{views}</View>;
    }
}

interface _ChineseMedicineGroupItemViewProps {
    index: number;
    total: number;
    chargeForm: ChargeForm;
    source?: ChargeInvoiceSource;
}

class _ChineseMedicineGroupItemView extends React.Component<_ChineseMedicineGroupItemViewProps> {
    static contextType = ExecuteInvoicePageBloc.Context;
    renderGroupUsage() {
        const { chargeForm, source } = this.props;

        const usageItems: string[] = [];
        !!chargeForm?.usageInfo?.doseCount ? usageItems.push(`共${chargeForm.usageInfo?.doseCount}剂`) : usageItems.push("--剂");
        !!chargeForm.usageInfo?.usage ? usageItems.push(`${chargeForm.usageInfo?.usage}`) : usageItems.push("--");
        if (!!chargeForm.usageInfo?.dailyDosage) usageItems.push(`${chargeForm.usageInfo?.dailyDosage}`);
        if (!!chargeForm.usageInfo?.freq) usageItems.push(`${chargeForm.usageInfo?.freq}`);
        if (!!chargeForm.usageInfo?.usageLevel) usageItems.push(`${chargeForm.usageInfo?.usageLevel}`);
        if (!!chargeForm.usageInfo?.usageDays) usageItems.push(`${chargeForm.usageInfo?.usageDays}`);
        if (!!chargeForm.usageInfo?.requirement) usageItems.push(`${chargeForm.usageInfo?.requirement}`);

        if (!Boolean(chargeForm.chargeFormItems?.[0]?.doseCount)) return <View />;
        return (
            <View
                style={[
                    source == ChargeInvoiceSource.outpatient ? ABCStyles.topLine : {},
                    {
                        backgroundColor: Colors.white,
                        marginHorizontal: Sizes.listHorizontalMargin,
                        paddingVertical: Sizes.listHorizontalMargin,
                    },
                ]}
            >
                <View style={{ alignItems: "flex-start", justifyContent: "center" }}>
                    <Text style={TextStyles.t16NT1} numberOfLines={2}>
                        {usageItems.join("，")}
                    </Text>
                </View>
            </View>
        );
    }

    _renderOutpatientChineseMedicineGroupView(): JSX.Element {
        const { chargeForm } = this.props;

        const chineseView: JSX.Element[] = [];

        chargeForm.chargeFormItems?.forEach((formItem, index) => {
            chineseView.push(
                <View key={index} style={{ width: pxToDp(150), paddingBottom: Sizes.dp12 }}>
                    <View style={ABCStyles.rowAlignCenter}>
                        <Text style={[TextStyles.t16NT1, { flexShrink: 1 }]} numberOfLines={1}>
                            {formItem?.goodsInfo?.displayName}
                        </Text>
                        <Text style={[TextStyles.t16NT1, { marginLeft: Sizes.dp4 }]} numberOfLines={1}>
                            {`${NumberUtils.formatMaxFixed(formItem.unitCount)}${formItem.unit}`}
                        </Text>
                    </View>
                    {!!formItem.usageInfo?.specialRequirement && (
                        <Text style={[TextStyles.t12NT3.copyWith({ color: Colors.t3 }), { flexShrink: 1 }]} numberOfLines={1}>
                            {`(${formItem.usageInfo?.specialRequirement})` ?? ""}
                        </Text>
                    )}
                </View>
            );
        });

        return (
            <View
                style={{
                    marginTop: Sizes.dp18,
                    flexDirection: "row",
                    flexWrap: "wrap",
                    marginHorizontal: Sizes.dp16,
                    justifyContent: "space-between",
                }}
            >
                {chineseView}
            </View>
        );
    }

    render() {
        const { chargeForm, total, index, source } = this.props;
        const views: JSX.Element[] = [];
        const chargeFormItemsFilter = chargeForm.chargeFormItems?.filter(
            (item) =>
                !(
                    item.productType == GoodsType.deliveryFee ||
                    item.productType == GoodsType.decoctionFee ||
                    item.productType == GoodsType.ingredient
                )
        );
        if (source == ChargeInvoiceSource.outpatient) {
            // 门诊开单
            views.push(this._renderOutpatientChineseMedicineGroupView());
        } else {
            // 执行站开单
            chargeFormItemsFilter?.forEach((formItem, index) => {
                views.push(<_ChineseMedicineItemView key={index} formItem={formItem} />);
            });
        }

        return (
            <AbcBasePanel key={chargeForm.id} panelStyle={{ marginHorizontal: Sizes.dp9, marginTop: Sizes.dp18, paddingTop: Sizes.dp20 }}>
                <AbcCardHeader
                    title={`中药处方${total == 1 ? "" : `${ABCUtils.toChineseNum(index + 1)}`}`}
                    titleStyle={TextStyles.t18MT1}
                    style={{ height: Sizes.dp25 }}
                    showCardLeftLine={false}
                    titleSuffix={() => (
                        <View style={[ABCStyles.rowAlignCenter, { marginLeft: Sizes.dp4 }]}>
                            <PharmacyTagView pharmacyNo={chargeForm.pharmacyNo} />
                            <ExecuteChargeStatusView
                                chargeStatus={
                                    ExecuteInvoicePageBloc.fromContext(this.context).currentState.invoicePartCharged
                                        ? ChargeFormItemStatus.partCharged
                                        : chargeForm.status
                                }
                                showChargedStatus={true}
                            />
                        </View>
                    )}
                    cardLeftLineSpace={Sizes.dp8}
                    padding={0}
                />
                {views}
                {this.renderGroupUsage()}
            </AbcBasePanel>
        );
    }
}

interface _ChineseMedicineItemViewProps {
    formItem: ChargeFormItem;
}

class _ChineseMedicineItemView extends React.Component<_ChineseMedicineItemViewProps> {
    render(): JSX.Element | JSX.Element[] {
        const { formItem } = this.props;
        return (
            // 执行站开单
            <View style={{ flex: 1, marginHorizontal: Sizes.listHorizontalMargin, marginTop: Sizes.listHorizontalMargin }}>
                <View style={[ABCStyles.rowAlignCenter, { justifyContent: "space-between", flexShrink: 1 }]}>
                    <Text numberOfLines={1} style={TextStyles.t16NT1}>
                        {formItem?.goodsInfo?.displayName}
                    </Text>
                    <Text numberOfLines={1} style={[TextStyles.t16NT1, { marginLeft: Sizes.dp12 }]}>
                        {`x${NumberUtils.formatMaxFixed(formItem.unitCount)}${formItem.unit}`}
                    </Text>
                </View>
                {!!formItem.usageInfo?.specialRequirement && (
                    <Text style={[TextStyles.t12NT3.copyWith({ color: Colors.t3 }), { flexShrink: 1 }]} numberOfLines={1}>
                        {`(${formItem.usageInfo?.specialRequirement})` ?? ""}
                    </Text>
                )}
            </View>
        );
    }
}
