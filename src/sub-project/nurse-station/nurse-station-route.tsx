/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/3/24
 */
import React from "react";
import { Route } from "../url-dispatcher/router-decorator";
import URLProtocols from "../url-dispatcher/url-protocols";
import { UrlRoute } from "../url-dispatcher/url-router";
import { UniqueKey } from "../base-ui";
import { UrlUtils } from "../common-base-module/utils";

@Route({ path: URLProtocols.EXECUTION_DETAIL })
export class NurseStationExecutionDetailRoute implements UrlRoute {
    handleUrl(action: string): JSX.Element {
        const { ExecuteInvoicePage } = require("./execute-invoice-page");
        const params = UrlUtils.getUrlParams(action);
        const orderId = params.get("orderId");
        return <ExecuteInvoicePage key={UniqueKey()} orderId={orderId} />;
    }
}

@Route({ path: URLProtocols.EXECUTE_TAB })
export class ExecuteTabPageRoute implements UrlRoute {
    handleUrl(): JSX.Element {
        const { ExecuteTabPage } = require("../nurse-station/execute-tab-page");
        return <ExecuteTabPage />;
    }
}
