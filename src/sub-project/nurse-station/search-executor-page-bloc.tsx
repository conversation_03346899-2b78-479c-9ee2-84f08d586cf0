/**
 * create by dengjie
 * desc:
 * create date 2020/8/31
 */

import { Bloc, BlocEvent } from "../bloc";
import React from "react";
import { EventName } from "../bloc/bloc";
import { ClinicAgent, EmployeeSimpleInfo } from "../base-business/data/clinic-agent";
import { switchMap } from "rxjs/operators";
import { ABCError } from "../common-base-module/common-error";
import { Subject } from "rxjs";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { AnyType } from "../common-base-module/common-types";
import _ from "lodash";

export interface _EmployeeSimpleInfo extends EmployeeSimpleInfo {
    selected: boolean;
}

export class State {
    keyword?: string;
    allEmployees: _EmployeeSimpleInfo[] = [];
    _matchEmployees: _EmployeeSimpleInfo[] = [];
    get matchEmployees(): _EmployeeSimpleInfo[] {
        return this._matchEmployees;
    }
    set matchEmployees(list: _EmployeeSimpleInfo[]) {
        this._matchEmployees = list.sort((item) => {
            return item.selected ? -1 : 1;
        });
    }

    loading = false;
    loadError: any;

    clone(): State {
        return Object.assign(new State(), this);
    }
}

export class SearchExecutorPageBloc extends Bloc<_Event, State> {
    private defaultEmployeeSimpleInfo?: EmployeeSimpleInfo[];
    static Context = React.createContext<SearchExecutorPageBloc | undefined>(undefined);

    static fromContext(context: AnyType): SearchExecutorPageBloc {
        return context;
    }

    constructor(params?: EmployeeSimpleInfo[]) {
        super();

        this.defaultEmployeeSimpleInfo = params;
        this.dispatch(new _EventInit());
    }

    _loadDataTrigger = new Subject<number>();

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventSearch, this._mapEventSearch);
        map.set(_EventSelectItem, this._mapEventSelectItem);
        map.set(_EventConfirm, this._mapEventConfirm);

        return map;
    }

    //过滤出符合条件的项
    private _collectMatchData() {
        const keyword = (this.innerState.keyword ?? "").toLowerCase();
        if (keyword.length === 0) {
            this.innerState.matchEmployees = this.innerState.allEmployees;
        }

        this.innerState.matchEmployees = this.innerState.allEmployees.filter(
            (item) =>
                item.name.toLowerCase().indexOf(keyword) >= 0 ||
                item.namePyFirst.toLowerCase().indexOf(keyword) >= 0 ||
                item.namePy.toLowerCase().indexOf(keyword) >= 0
        );
    }

    update(state?: State): void {
        this.dispatch(new _EventUpdate(state));
    }

    async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        this._loadDataTrigger
            .pipe(
                switchMap((/*_*/) => {
                    this.innerState.loading = true;
                    this.innerState.loadError = null;
                    this.update();

                    return ClinicAgent.getClinicEmployeesSimpleInfo()
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((data) => {
                this.innerState.loading = false;
                if (data instanceof ABCError) {
                    this.innerState.loadError = data.detailError;
                    this.update();
                } else {
                    this.innerState.allEmployees = data.map((item) => {
                        const _item = item as _EmployeeSimpleInfo;
                        _item.selected = !!_.find(this.defaultEmployeeSimpleInfo, (defaultItem) => item.id == defaultItem.id);
                        return _item;
                    });
                    this._collectMatchData();
                    this.update();
                }
            })
            .addToDisposableBag(this);
        this._loadDataTrigger.next(0);
    }

    async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    async *_mapEventSearch(event: _EventSearch): AsyncGenerator<State> {
        this.innerState.keyword = event.keyword;
        this._collectMatchData();
        yield this.innerState.clone();
    }

    async *_mapEventSelectItem(event: _EventSelectItem): AsyncGenerator<State> {
        event.data.selected = !event.data.selected;
        this.update();
    }

    async *_mapEventConfirm(/*event: _EventConfirm*/): AsyncGenerator<State> {
        const _employeesList = this.innerState.allEmployees.filter((item) => item.selected);
        ABCNavigator.pop(_employeesList);
    }

    requestSearch(value: string): void {
        this.dispatch(new _EventSearch(value));
    }

    requestReloadData(): void {
        this._loadDataTrigger.next(0);
    }

    requestSelectItem(data: _EmployeeSimpleInfo): void {
        this.dispatch(new _EventSelectItem(data));
    }

    requestConfirm(): void {
        this.dispatch(new _EventConfirm());
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventSearch extends _Event {
    keyword: string;

    constructor(keyword: string) {
        super();
        this.keyword = keyword;
    }
}

class _EventSelectItem extends _Event {
    data: _EmployeeSimpleInfo;

    constructor(data: _EmployeeSimpleInfo) {
        super();
        this.data = data;
    }
}

class _EventConfirm extends _Event {}
