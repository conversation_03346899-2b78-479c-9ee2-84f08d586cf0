/**
 * create by dengjie
 * desc:
 * create date 2020/6/10
 */
import React from "react";
import {
    ChargeForm,
    ChargeFormItem,
    ChargeFormItemStatus,
    ChargeInvoiceDetailData,
    ChargeInvoiceSource,
    ExecuteInfusionSkinTestStatus,
} from "../charge/data/charge-beans";
import { ABCUtils } from "../base-ui/utils/utils";
import { Style, Text, View } from "@hippy/react";
import _ from "lodash";
import { ExecuteChargeStatusView } from "../charge/view/charge-views";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { ExecuteInvoicePageBloc } from "./execute-invoice-page-bloc";
import { AstItemView, ItemExecutableButton } from "./nurse-station-views";
import { Toast } from "../base-ui/dialog/toast";
import { SizedBox, Spacer } from "../base-ui";
import { PharmacyTagView } from "../outpatient/views/pharmacy-tag-view";
import { AbcBasePanel } from "../base-ui/abc-app-library";
import { AbcCardHeader } from "../base-ui/abc-app-library/common/abc-card-header";
import { TextWithErrorHint } from "../outpatient/outpatient-views";

interface WesternMedicineGroupViewProps {
    patientOrderDetailData: ChargeInvoiceDetailData;
    chargeForms: Array<ChargeForm>;
    titlePrefix: string;
    onAddWesternMedicineTap?: () => void;
    isLockingOrder?: boolean; // 是否锁单中
}

export class WesternMedicineGroupView extends React.Component<WesternMedicineGroupViewProps> {
    constructor(props: WesternMedicineGroupViewProps) {
        super(props);
    }

    render(): JSX.Element {
        const { chargeForms, titlePrefix, patientOrderDetailData, isLockingOrder } = this.props;
        const westernChargeForms: ChargeForm[] = [];
        chargeForms.forEach((item) => {
            westernChargeForms.push(item);
        });

        const views = [];
        const length = westernChargeForms.length;
        let index = 1;
        for (const chargeFrom of chargeForms) {
            let groupSuffix = "";
            if (length > 1) groupSuffix = ABCUtils.toChineseNum(index);
            views.push(
                <_WesternMedicineGroupItemView
                    key={index}
                    title={`${titlePrefix}${groupSuffix}`}
                    chargeForm={chargeFrom}
                    source={patientOrderDetailData.source}
                    departmentId={patientOrderDetailData?.departmentId}
                    isLockingOrder={isLockingOrder}
                />
            );
            index++;
        }

        if (_.isEmpty(views)) {
            return <View />;
        }
        return <View>{views}</View>;
    }
}

interface _WesternMedicineGroupItemViewProps {
    chargeForm: ChargeForm;
    title: string;
    source?: ChargeInvoiceSource;
    departmentId?: string;
    isLockingOrder?: boolean; // 是否锁单中
}

class _WesternMedicineGroupItemView extends React.Component<_WesternMedicineGroupItemViewProps> {
    static contextType = ExecuteInvoicePageBloc.Context;
    constructor(props: _WesternMedicineGroupItemViewProps) {
        super(props);
    }

    groupTitle() {
        const { title, chargeForm, isLockingOrder } = this.props;
        const { executionStationBill, hasExecuteItem, onlyExecuteAfterPaid, detailData } = ExecuteInvoicePageBloc.fromContext(
            this.context
        ).currentState;

        const _onlyExecuteAfterPaid = !onlyExecuteAfterPaid && chargeForm.status == ChargeFormItemStatus.unCharged; // 项目需在收费后可执行

        const filterChargeForm = chargeForm.chargeFormItems?.filter((f) => f.drugsUsageExecutive && f.drugsCanExecutive); // 去除用法不可执行的药品
        const _hasAstUnExecuted = !!filterChargeForm?.find((item) => item.astCanExecuted); // 项目包含未执行的皮试
        const _hasDrugsExecutiveCount = !!filterChargeForm?.find((item) => item.drugsExecutiveCount); // 项目包含有剩余可执行次数

        return (
            <AbcCardHeader
                title={title}
                titleStyle={TextStyles.t18MT1}
                style={{ height: Sizes.dp32 }}
                showCardLeftLine={false}
                titleSuffix={() => (
                    <View style={{ marginLeft: Sizes.dp4 }}>
                        <ExecuteChargeStatusView
                            showChargedStatus={true}
                            chargeStatus={
                                ExecuteInvoicePageBloc.fromContext(this.context).currentState.invoicePartCharged
                                    ? ChargeFormItemStatus.partCharged
                                    : chargeForm.status
                            }
                        />
                    </View>
                )}
                cardLeftLineSpace={Sizes.dp8}
                padding={0}
                rightRender={() => {
                    return (
                        <ItemExecutableButton
                            style={{ marginRight: Sizes.dp16 }}
                            isShow={executionStationBill && detailData?.statusName == "待执行" && _hasDrugsExecutiveCount}
                            isExecutable={
                                !isLockingOrder &&
                                chargeForm.status != ChargeFormItemStatus.refunded &&
                                hasExecuteItem &&
                                !_hasAstUnExecuted &&
                                !_onlyExecuteAfterPaid
                            }
                            onClick={() => {
                                if (isLockingOrder) return;
                                if (_onlyExecuteAfterPaid) {
                                    Toast.show("收费后才可执行").then();
                                    return;
                                } else if (_hasAstUnExecuted) {
                                    Toast.show("请先完成皮试后才可执行").then();
                                    return;
                                }
                                ExecuteInvoicePageBloc.fromContext(this.context).requestExecute(chargeForm);
                            }}
                        />
                    );
                }}
            />
        );
    }

    renderTextWithErrorHintView(options: {
        key?: string;
        text?: string | number;
        style?: Style | Style[];
        textStyle?: Style | Style[];
    }): JSX.Element {
        const { text, style, textStyle } = options;
        return <TextWithErrorHint text={text} style={style} textStyle={textStyle} />;
    }

    renderItem(options: {
        formItem: ChargeFormItem;
        index: number;
        length?: number;
        showBottomLine?: boolean;
        hasGroupId?: boolean;
    }): JSX.Element {
        const { source, departmentId } = this.props;
        const { formItem, index, showBottomLine, hasGroupId } = options;
        const isFadeText = formItem?.status == ChargeFormItemStatus.refunded || formItem?.status == ChargeFormItemStatus.chargeBack; //已退单或者已退费，需要淡化文字
        const { pharmacyInfoConfig } = ExecuteInvoicePageBloc.fromContext(this.context).currentState;
        const defaultPharmacyNo = pharmacyInfoConfig?.getDefaultPharmacy({
            departmentId: departmentId,
            goodsInfo: { typeId: formItem.goodsInfo?.typeId },
        })?.no;

        let _astStatusName = "";
        let _astStatusColor = Colors.T2;
        switch (formItem.ast) {
            case ExecuteInfusionSkinTestStatus.continuedUse: {
                _astStatusName = "续用";
                _astStatusColor = Colors.theme2;
                break;
            }
            case ExecuteInfusionSkinTestStatus.noSkin: {
                _astStatusName = "免试";
                _astStatusColor = Colors.theme2;
                break;
            }
        }

        // 门诊-执行单
        if (source == ChargeInvoiceSource.registration || source == ChargeInvoiceSource.outpatient) {
            // 用法信息
            const textWithErrorHintInfo = [
                {
                    key: "usage",
                    text: formItem?.usageInfo?.usage,
                    style: { width: Sizes.dp60 },
                    textStyle: [{ textAlign: "left" }, isFadeText ? TextStyles.t14NT4 : {}],
                },
                {
                    key: "freq",
                    text: formItem?.usageInfo?.freq,
                    style: { width: Sizes.dp40 },
                    textStyle: [{ textAlign: "left" }, isFadeText ? TextStyles.t14NT4 : {}],
                },
                {
                    key: "dosageUnit",
                    text:
                        formItem?.usageInfo?.dosageUnit == "适量"
                            ? "适量"
                            : !!formItem?.usageInfo?.dosage && !!formItem.usageInfo?.dosageUnit
                            ? `${formItem?.usageInfo?.dosage}${formItem.usageInfo?.dosageUnit}/次`
                            : "--",
                    style: { width: Sizes.dp60 },
                    textStyle: [{ textAlign: "left" }, isFadeText ? TextStyles.t14NT4 : {}],
                },
                {
                    key: "days",
                    text: formItem?.usageInfo?.days ? `${formItem?.usageInfo?.days}天` : "--",
                    style: { width: Sizes.dp48, marginRight: Sizes.dp16 },
                    textStyle: [{ textAlign: "left" }, isFadeText ? TextStyles.t14NT4 : {}],
                },
            ];

            const hasGroupIdWidth = Sizes.dp26; // 有分组id时，需要预留的宽度

            return (
                <View
                    key={formItem.id}
                    style={[
                        showBottomLine ? ABCStyles.bottomLine : {},
                        {
                            marginHorizontal: Sizes.listHorizontalMargin,
                            paddingTop: index !== 0 ? Sizes.listHorizontalMargin : undefined,
                            paddingBottom: Sizes.listHorizontalMargin,
                        },
                    ]}
                >
                    <View style={ABCStyles.rowAlignCenter}>
                        <View style={{ width: hasGroupId ? hasGroupIdWidth : undefined }}>
                            {!!formItem.groupId && (
                                <View style={{ marginRight: !!formItem.groupId ? Sizes.dp10 : undefined }}>
                                    <Text style={[TextStyles.t16NM, { textAlign: "center" }]}>
                                        {ABCUtils.toCircledNum(formItem.groupId ?? 99)}
                                    </Text>
                                </View>
                            )}
                        </View>
                        <Text style={[TextStyles.t16NB, { lineHeight: Sizes.dp24, flexShrink: 1 }]} numberOfLines={1}>
                            {formItem.name ?? ""}
                        </Text>
                        <Text
                            style={[TextStyles.t14NT3.copyWith({ color: Colors.t3 }), { lineHeight: Sizes.dp24, marginLeft: Sizes.dp4 }]}
                            numberOfLines={1}
                        >
                            {formItem.goodsInfo?.packageSpec ?? ""}
                        </Text>
                        {pharmacyInfoConfig?.isOpenMultiplePharmacy && (
                            <PharmacyTagView
                                style={{ marginLeft: Sizes.dp4 }}
                                pharmacyNo={formItem.pharmacyNo}
                                defaultPharmacyNo={defaultPharmacyNo}
                            />
                        )}
                        <Spacer />
                        <SizedBox width={Sizes.dp12} />
                        {formItem.ast == 1 && <AstItemView key={formItem.id} formItem={formItem} />}
                    </View>

                    <View style={[ABCStyles.rowAlignCenter, { marginTop: Sizes.dp4 }]}>
                        {hasGroupId && <SizedBox width={hasGroupIdWidth} />}
                        {textWithErrorHintInfo?.map((item) => {
                            return this.renderTextWithErrorHintView({
                                key: item.key,
                                text: item.text,
                                style: item.style,
                                textStyle: item.textStyle,
                            });
                        })}
                        <Spacer />
                        {formItem.unitCount && (
                            <Text style={TextStyles.t14NT1.copyWith({ color: isFadeText ? Colors.T4 : Colors.T1 })}>
                                {`x${formItem.unitCount}${formItem?.unit ?? ""}`}
                            </Text>
                        )}
                    </View>

                    {(!!formItem?.ast ||
                        !!formItem?.astResult ||
                        !!formItem?.specialRequirement ||
                        (formItem?.usageInfo?.executedTotalCount ?? 0) > 0) && (
                        <View style={[ABCStyles.rowAlignCenter, { marginTop: Sizes.dp4 }]}>
                            {hasGroupId && <SizedBox width={hasGroupIdWidth} />}
                            {!!_astStatusName && (
                                <Text style={[TextStyles.t14NT1, { color: _astStatusColor }, { marginRight: Sizes.dp15 }]}>
                                    {_astStatusName}
                                </Text>
                            )}
                            <Text
                                style={[isFadeText ? TextStyles.t14NT4 : TextStyles.t14NT1, { lineHeight: Sizes.dp20, flexShrink: 1 }]}
                                numberOfLines={1}
                            >
                                {formItem?.specialRequirement ?? ""}
                            </Text>
                            <Spacer />
                            {!!formItem?.needExecutive && (
                                <Text style={[TextStyles.t14NT1, { marginLeft: Sizes.dp12, lineHeight: Sizes.dp20 }]} numberOfLines={1}>
                                    {`执行进度：${Math.round(formItem?.executedUnitCount ?? 0)}/${Math.round(
                                        formItem?.usageInfo?.executedTotalCount ?? 0
                                    )}次`}
                                </Text>
                            )}
                        </View>
                    )}
                </View>
            );
        } else {
            return (
                // 执行站开单
                <View key={formItem.id} style={{ marginHorizontal: Sizes.listHorizontalMargin, marginBottom: Sizes.listHorizontalMargin }}>
                    <View style={ABCStyles.rowAlignCenter}>
                        <Text
                            numberOfLines={1}
                            ellipsizeMode={"tail"}
                            style={[TextStyles.t16NB, { lineHeight: Sizes.dp24, flexShrink: 1 }]}
                        >
                            {`${formItem.groupId ? `${formItem.groupId.toString()}  ` : ""}${formItem.name ?? ""}`}
                        </Text>
                        <Spacer />
                        <SizedBox width={Sizes.dp12} />
                        {formItem.unitCount && <Text style={TextStyles.t14NT1}>{`x${formItem.unitCount}${formItem?.unit ?? ""}`}</Text>}
                    </View>
                    <View style={ABCStyles.rowAlignCenter}>
                        <Text style={[TextStyles.t14NT3.copyWith({ color: Colors.t3 }), { flexShrink: 1 }]} numberOfLines={1}>
                            {formItem.goodsInfo?.packageSpec ?? ""}
                        </Text>
                        {pharmacyInfoConfig?.isOpenMultiplePharmacy && (
                            <PharmacyTagView
                                style={{ marginLeft: Sizes.dp4 }}
                                pharmacyNo={formItem.pharmacyNo}
                                defaultPharmacyNo={defaultPharmacyNo}
                            />
                        )}
                    </View>
                    {formItem?.remark && (
                        <Text
                            style={[TextStyles.t14NT3.copyWith({ color: Colors.t3 }), { lineHeight: Sizes.dp20, flexShrink: 1 }]}
                            numberOfLines={1}
                        >
                            {formItem?.remark ?? ""}
                        </Text>
                    )}
                </View>
            );
        }
    }

    render() {
        const { chargeForm } = this.props;
        const views: JSX.Element[] = [];
        const size = chargeForm.chargeFormItems?.length ?? 0;
        chargeForm.chargeFormItems?.forEach((formItem, index) => {
            views.push(
                this.renderItem({
                    formItem: formItem,
                    index: index,
                    length: chargeForm.chargeFormItems?.length,
                    showBottomLine: index < size - 1,
                    hasGroupId: !!chargeForm.chargeFormItems?.find((item) => item.groupId),
                })
            );
        });
        return (
            <AbcBasePanel key={chargeForm.id} panelStyle={{ marginHorizontal: Sizes.dp8, marginTop: Sizes.dp18, paddingTop: Sizes.dp20 }}>
                {this.groupTitle()}
                <View style={{ backgroundColor: Colors.white, marginTop: Sizes.dp8 }}>{views}</View>
            </AbcBasePanel>
        );
    }
}
