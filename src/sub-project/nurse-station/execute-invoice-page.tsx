/**
 * create by deng<PERSON><PERSON>
 * desc:
 * create date 2020/6/9
 */
import React from "react";
import { BasePage, SizedBox, <PERSON>lBar, ToolBarButtonStyle1, IconFontView } from "../base-ui";
import { BlocBuilder } from "../bloc";
import {
    ChargeFormItemStatus,
    ChargeInvoiceSource,
    ChargeSourceFormType,
    ChargeStatus,
    ChargeFormStatus,
} from "../charge/data/charge-beans";
import { ExecuteInvoicePageBloc } from "./execute-invoice-page-bloc";
import { ScrollView, Text, View } from "@hippy/react";
import { ABCNetworkPageContentStatus, BaseBlocNetworkPage } from "../base-ui/base-page";
import { ChargeExecuteStatus } from "./data/bean";
import { ExternalMedicineGroupView, MedicalRecordCardView, ProjectCardView } from "./nurse-station-views";
import { WesternMedicineGroupView } from "./western-medicine-group-view";
import { InjectionMedicineGroupView } from "./injection-medicine-group-view";
import { ChineseMedicineGroupView } from "./chinese-medicine-group-view";
import { ABCStyles, Color, Colors, Sizes, TextStyles } from "../theme";
import { keyboardListener } from "../common-base-module/utils/keyboard-listener";
import { AssetImageView } from "../base-ui/views/asset-image-view";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { Toast } from "../base-ui/dialog/toast";
import { AbcPopMenu, MenuItem } from "../base-ui/views/pop-menu";
import { TreeDotView } from "../base-ui/iconfont/iconfont-view";
import { AbcButton } from "../base-ui/views/abc-button";
import { AbcBasePanel } from "../base-ui/abc-app-library";
import { AbcText } from "../base-ui/views/abc-text";
import { PatientInfoMethod } from "../base-business/data/patient-beans";
import { HistoryPermissionModuleType } from "../base-business/data/beans";
import { AbcBannerTips } from "../base-ui/abc-banner-tips/abc-banner-tips";
import { DeviceUtils } from "../base-ui/utils/device-utils";

interface ExecuteInvoicePageProps {
    orderId?: string;
}

enum ExecuteInvoiceMenuItemValue {
    push = 1, // 推送支付
    delete, // 删除执行单
    history, // 执行历史
}

export default class ExecuteInvoicePage extends BaseBlocNetworkPage<ExecuteInvoicePageProps, ExecuteInvoicePageBloc> {
    private inputFocused?: boolean;
    constructor(props: ExecuteInvoicePageProps) {
        super(props);
        this.bloc = new ExecuteInvoicePageBloc({ executeInvoiceId: props.orderId });
    }

    getAppBar(): JSX.Element {
        return <BlocBuilder bloc={this.bloc} condition={(/*preState, newState*/) => true} build={(/*state*/) => super.getAppBar()} />;
    }

    getAppBarTitle(): string {
        return "执行单";
    }

    getStatusBarColor(): Color {
        return Colors.panelBg;
    }

    getAppBarBgColor(): Color {
        return Colors.panelBg;
    }

    getAPPBarCustomTitle(): JSX.Element | undefined {
        const detailData = this.bloc.currentState.detailData;
        const statusName = detailData?.statusName ?? ""; // 执行单状态名
        let statusColor = "";
        const executeStatus = detailData?.executeStatus; // 执行状态
        const executeSheetStatus = detailData?.status as Number; // 执行单收费情况

        if (executeStatus == ChargeExecuteStatus.EXECUTION_ORDER_STATUS_UNEXECUTED) {
            statusColor = Colors.B2; // 待执行
        } else if (executeStatus == ChargeExecuteStatus.EXECUTION_ORDER_STATUS_EXECUTED) {
            statusColor = Colors.theme2; // 已执行
        } else if (executeSheetStatus == ChargeFormStatus.unCharged) {
            statusColor = Colors.Y2; // 未收费
        } else if (executeSheetStatus == ChargeFormStatus.refunded) {
            statusColor = Colors.R2; // 已退费
        } else {
            statusColor = Colors.G2;
        }

        return (
            <BlocBuilder
                bloc={this.bloc}
                condition={() => true}
                build={
                    (/*bloc*/) => (
                        <View style={ABCStyles.rowAlignCenter}>
                            <SizedBox width={Sizes.dp48} />
                            <Text style={TextStyles.t18NB}>{this.getAppBarTitle()}</Text>
                            {statusName != "" ? (
                                <Text style={[TextStyles.t14NG2, { width: Sizes.dp48, textAlign: "center", color: statusColor }]}>
                                    {statusName}
                                </Text>
                            ) : (
                                <SizedBox width={Sizes.dp48} />
                            )}
                        </View>
                    )
                }
            />
        );
    }

    getRightAppBarIcons(): JSX.Element[] {
        const { executeSheetCanDelete } = this.bloc.currentState;
        return [
            <View key={"all"} style={ABCStyles.rowAlignCenter}>
                <View key={"menu"} style={{ marginRight: Sizes.dp8 }}>
                    {this._renderInvoicePageMenuIcon()}
                </View>
                {executeSheetCanDelete && (
                    <AbcButton
                        style={{ height: Sizes.dp30, width: Sizes.dp52 }}
                        onClick={() => {
                            this.bloc.requestModifyInvoice();
                        }}
                        pressColor={Colors.mainColorPress}
                    >
                        <Text style={[TextStyles.t14MS2, ABCStyles.rowAlignCenter]} numberOfLines={1}>
                            {"修改"}
                        </Text>
                    </AbcButton>
                )}
            </View>,
        ];
    }

    componentDidMount(): void {
        super.componentDidMount();

        if (!this.bloc.currentState.showLoadingAnimation) return;

        this.setContentStatus(ABCNetworkPageContentStatus.loading, null);
        const stateObserver = this.bloc.state.subscribe((state) => {
            if (!this.bloc.currentState.showLoadingAnimation) return;
            let status = ABCNetworkPageContentStatus.show_data;
            let error: any;
            if (state.loading) {
                status = ABCNetworkPageContentStatus.loading;
            } else if (state.loadError) {
                status = ABCNetworkPageContentStatus.error;
                error = state.loadError;
            }

            this.setContentStatus(status, error);
        });

        this.addDisposable(stateObserver);

        keyboardListener.subscribe((e) => {
            this.inputFocused = e.visible;
            this.setState({});
        });
    }

    reloadData(): void {
        this.bloc.requestReload();
    }

    renderContent(): JSX.Element {
        if (!this.bloc.currentState.detailData) return <View />;
        const { hasExecuteItem, onlyExecuteAfterPaid, detailData } = this.bloc.currentState;

        const showToolbarView = detailData?.statusName !== "已执行";
        const isLockingOrder = detailData.isRefundLocking;
        const tips = detailData.lockCopy?.tips;
        const isOhos = DeviceUtils.isOhos();
        return (
            <View style={{ flex: 1, ...(isOhos ? {} : { backgroundColor: Colors.prescriptionBg }) }}>
                {isLockingOrder && <AbcBannerTips tips={tips} />}
                {this.bloc.currentState.isCross ? this._renderCrossTipView() : <View />}
                <ScrollView showsVerticalScrollIndicator={false} style={{ flex: 1 }}>
                    {this._renderExecutionOrderDetail()}
                </ScrollView>
                {!this.inputFocused && showToolbarView && (
                    <ToolBar>
                        {detailData?.statusName !== "已执行" && (
                            <ToolBarButtonStyle1
                                style={
                                    hasExecuteItem
                                        ? {}
                                        : {
                                              backgroundColor: Colors.bottomBtnDisable,
                                              borderColor: Colors.bottomBtnDisable,
                                              fontColor: Colors.T2,
                                          }
                                }
                                text={"批量执行"}
                                onClick={() => {
                                    if (!hasExecuteItem) return;
                                    for (const form of detailData?.chargeForms ?? []) {
                                        if (!onlyExecuteAfterPaid && form.status == ChargeFormItemStatus.unCharged) {
                                            Toast.show("收费后才可执行").then();
                                            return;
                                        }
                                    }
                                    this.bloc.requestBatchExecute();
                                }}
                            />
                        )}
                    </ToolBar>
                )}
            </View>
        );
    }

    private _createMenuItemList(): MenuItem<number>[] {
        const { isMeetpushPayment, detailData, isAccessible, executeSheetCanDelete } = this.bloc.currentState;
        const menuItems: MenuItem<number>[] = [];

        const isAnonymousPatients = detailData?.patient?.name;
        // 推送按钮显示：完成开单后，并且只推送支付执行站开具的执行单(type=6)，其余来源的执行单不能推送；部分收费的也不能,只有待收的才行
        // 如果当前为匿名患者，推送支付按钮不展示
        const isShowPushScanBtn =
            isMeetpushPayment && detailData?.status == ChargeStatus.unCharged && detailData?.type == 6 && !!isAnonymousPatients;
        if (isShowPushScanBtn) {
            menuItems.push(
                new MenuItem({
                    value: ExecuteInvoiceMenuItemValue.push,
                    icon: (
                        <IconFontView
                            name={"push"}
                            color={Colors.T1}
                            size={Sizes.dp18}
                            style={{ bottom: -Sizes.dp5, width: Sizes.dp22, height: Sizes.dp24 }}
                        />
                    ),
                    text: "推送支付",
                    textStyle: { fontSize: Sizes.dp14 },
                })
            );
        }

        if (executeSheetCanDelete) {
            menuItems.push(
                new MenuItem({
                    value: ExecuteInvoiceMenuItemValue.delete,
                    icon: <IconFontView name={"trash"} color={Colors.T1} size={Sizes.dp22} style={{ bottom: -Sizes.dp1 }} />,
                    text: "删除执行单",
                    textStyle: { fontSize: Sizes.dp14 },
                })
            );
        }

        if (!!isAccessible) {
            menuItems.push(
                new MenuItem({
                    value: ExecuteInvoiceMenuItemValue.history,
                    icon: <AssetImageView name={"execute_history"} style={{ width: Sizes.dp24, height: Sizes.dp24, bottom: -Sizes.dp1 }} />,
                    text: "执行历史",
                    textStyle: { fontSize: Sizes.dp14 },
                })
            );
        }

        return menuItems;
    }

    protected renderPatientBaseInfoCard(): JSX.Element {
        const state = this.bloc.currentState;

        const patient = state.detailData?.patient;
        const canSeePatientPhone = state.canSeePatientPhone;
        return (
            <AbcBasePanel
                panelStyle={{ marginTop: Sizes.dp18, marginHorizontal: Sizes.dp8, backgroundColor: Colors.white }}
                contentStyle={[ABCStyles.rowAlignCenter, { padding: Sizes.listHorizontalMargin }]}
            >
                <View style={{ flexShrink: 1 }}>
                    <AbcText
                        style={[
                            !!patient?.name ? TextStyles.t18MT1 : TextStyles.t18NT4.copyWith({ color: Colors.t4 }),
                            { lineHeight: Sizes.dp25 },
                        ]}
                        numberOfLines={1}
                    >
                        {!!patient?.name ? patient?.name : "匿名患者"}
                    </AbcText>
                </View>
                {!!patient?.isMember && (
                    <AssetImageView
                        name={"charge_invoice_patient_member"}
                        style={{ marginLeft: Sizes.dp4, width: Sizes.dp20, height: Sizes.dp20 }}
                    />
                )}
                {!!patient?.name && (
                    <View style={ABCStyles.rowAlignCenter}>
                        <AbcText
                            style={[
                                !!patient?.name ? TextStyles.t18NT1 : TextStyles.t18NT4.copyWith({ color: Colors.t4 }),
                                { lineHeight: Sizes.dp25, marginLeft: Sizes.dp8 },
                            ]}
                        >
                            {!!patient?.sex ? patient?.sex : "--"}
                        </AbcText>
                        <AbcText
                            style={[
                                !!patient?.name ? TextStyles.t18NT1 : TextStyles.t18NT4.copyWith({ color: Colors.t4 }),
                                { lineHeight: Sizes.dp25, marginLeft: Sizes.dp8 },
                            ]}
                        >
                            {!!patient?.age?.displayAgeIncludeMonth ? patient?.age?.displayAgeIncludeMonth : "--"}
                        </AbcText>
                        <AbcText style={[TextStyles.t16NT1, { lineHeight: Sizes.dp25, marginLeft: Sizes.dp8 }]}>
                            {PatientInfoMethod.canSeePatientMobile({
                                type: HistoryPermissionModuleType.execution,
                                canSeePatientMobileInExecution: canSeePatientPhone,
                            })
                                ? patient?.mobile ?? ""
                                : PatientInfoMethod.encryptThePhoneNumber(patient?.mobile)}
                        </AbcText>
                    </View>
                )}
            </AbcBasePanel>
        );
    }

    _renderExecutionOrderDetail(): JSX.Element[] {
        const detailData = this.bloc.currentState.detailData,
            views = [];
        const isLockingOrder = detailData?.isRefundLocking;

        views.push(this.renderPatientBaseInfoCard()); // 患者卡片

        // 诊断/开单人
        if (detailData?.diagnosis) {
            views.push(
                <MedicalRecordCardView
                    key={"MedicalRecordCard"}
                    // PC逻辑 -- 解决可能门诊和收费单同时打开，导致门诊和执行站诊断内容不一致
                    diagnosis={
                        (detailData.source === ChargeInvoiceSource.therapy
                            ? detailData?.diagnosis
                            : detailData?.medicalRecord?.diagnosis) || ""
                    }
                    doctorName={detailData?.chargeSheetSummary?.doctorName}
                    sellerName={detailData?.sellerName}
                    sellerDepartmentName={detailData?.departmentName ?? detailData?.sellerDepartmentName}
                />
            );
        } else {
            views.push(
                <MedicalRecordCardView
                    key={"MedicalRecordCard"}
                    diagnosisHix={"由收费处直接开单"}
                    doctorName={detailData?.chargeSheetSummary?.doctorName}
                    sellerName={detailData?.sellerName}
                    sellerDepartmentName={detailData?.departmentName ?? detailData?.sellerDepartmentName}
                />
            );
        }

        const chineseForms = [];
        const westernForms = [];
        const infusionForms = [];
        const goodsForms = [];
        const externalForms = [];
        for (const form of detailData?.chargeForms ?? []) {
            if (form.sourceFormType == ChargeSourceFormType.chinesePrescription) chineseForms.push(form);
            else if (form.isWesternPrescription) westernForms.push(form);
            else if (form.isInfusionPrescription) infusionForms.push(form);
            else if (form.isGoods || form.isMaterial) goodsForms.push(form);
            else if (form.isExternal) externalForms.push(form);
        }

        views.push(
            <ProjectCardView
                key={"ProjectCard"}
                chargeForms={detailData?.chargeForms?.filter((form) => !form.isExternal) ?? []}
                isLockingOrder={isLockingOrder}
            />
        ); // 诊疗项目

        if (externalForms.length) {
            views.push(
                <ExternalMedicineGroupView key={"ExternalPrescriptionGroup"} chargeForms={externalForms} isLockingOrder={isLockingOrder} />
            ); // 外治处方
        }
        views.push(
            <WesternMedicineGroupView
                key={"WesternMedicineGroup"}
                titlePrefix={"成药处方"}
                patientOrderDetailData={detailData!}
                chargeForms={westernForms}
                isLockingOrder={isLockingOrder}
            />
        );
        // 输注处方
        views.push(
            <InjectionMedicineGroupView key={"InjectionMedicineGroup"} patientOrderDetailData={detailData!} chargeForms={infusionForms} />
        );

        // 中药处方
        views.push(
            <ChineseMedicineGroupView key={"ChineseMedicineGroup"} patientOrderDetailData={detailData!} chargeForms={chineseForms} />
        );

        views.push(<SizedBox height={Sizes.dp18} />);

        return views;
    }

    _renderCrossTipView(): JSX.Element {
        return (
            <View
                style={[
                    ABCStyles.rowAlignCenter,
                    { height: Sizes.dp40, backgroundColor: Colors.Y4, paddingHorizontal: Sizes.dp16, marginBottom: Sizes.dp8 },
                ]}
            >
                <Text style={TextStyles.t12NY2}>{`开单门店：${this.bloc.currentState.detailData?.clinicName}`}</Text>
            </View>
        );
    }

    private _renderInvoicePageMenuIcon(): JSX.Element {
        const { isAccessible, detailData } = this.bloc.currentState;
        const menuItems: MenuItem<number>[] = this._createMenuItemList();
        if (!menuItems.length) return <View />;
        // 外层View是为了兼容鸿蒙系统，不能省略
        return (
            <View>
                <View
                    style={{ paddingHorizontal: Sizes.dp8 }}
                    collapsable={false}
                    onClick={async () => {
                        const select = await AbcPopMenu.show(
                            menuItems,
                            { x: Sizes.dp24, y: Sizes.dp42 },
                            { x: Sizes.dp29, y: Sizes.dp42 },
                            { fullGrid: true }
                        );

                        switch (select) {
                            case ExecuteInvoiceMenuItemValue.delete: {
                                this.bloc.requestDeleteInvoice();
                                break;
                            }
                            case ExecuteInvoiceMenuItemValue.push: {
                                this.bloc.requestPushToScan(detailData?.patientOrderId ?? "");
                                break;
                            }
                            case ExecuteInvoiceMenuItemValue.history: {
                                if (!isAccessible) return ABCNavigator.navigateToPage(<EmptyExecuteHistoryView />);
                                this.bloc.requestViewExecuteRecord();
                                break;
                            }
                        }
                    }}
                >
                    <TreeDotView color={Colors.T1} />
                </View>
            </View>
        );
    }
}

/**
 * 无查看历史记录权限页面
 */
class EmptyExecuteHistoryView extends BasePage {
    getAppBarTitle(): string {
        return "执行单";
    }

    renderContent(): JSX.Element {
        return (
            <View style={{ flex: 1, paddingTop: Sizes.dp100, alignItems: "center", backgroundColor: Colors.white }}>
                <AssetImageView style={{ width: Sizes.dp42, height: Sizes.dp42 }} name={"image_dlg_fail"} />
                <SizedBox height={Sizes.dp24} />
                <Text style={[TextStyles.t18MT1, { lineHeight: Sizes.dp28 }]}>{"未参与过执行，无查看权限"}</Text>
            </View>
        );
    }
}
