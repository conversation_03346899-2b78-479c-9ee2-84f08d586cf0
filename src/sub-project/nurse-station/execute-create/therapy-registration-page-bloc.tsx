/**
 * 成都字节星球科技公司
 * <AUTHOR>
 * @date 2020-03-20
 *
 * @description
 */

import React from "react";
import { Bloc, BlocEvent } from "../../bloc";
import { EventName } from "../../bloc/bloc";
import { Subject } from "rxjs";
import { userCenter } from "../../user-center";
import { switchMap } from "rxjs/operators";
import {
    NormalTherapyRegistrationItem,
    PatientOrderDataAgent,
    TherapyDoctorBasicInfo,
    TherapyRegistrationItem,
} from "../data/nurse-station-data";
import { ABCError } from "../../common-base-module/common-error";
import { BaseLoadingState } from "../../bloc/bloc-helper";
import { LoadingDialog } from "../../base-ui/dialog/loading-dialog";
import { errorSummary } from "../../common-base-module/utils";
import { showOptionsBottomSheet } from "../../base-ui/dialog/bottom_sheet";
import _ from "lodash";
import { showBottomSheetMenu } from "../../base-ui/dialog/bottom-sheet-menu";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { ExecuteInvoiceCreatePage } from "./execute-invoice-create-page";
import { PatientOrderSearchPage } from "../patient-order-search-page";
import { runFuncBeforeCheckExpired } from "../../views/clinic-edition";
import { TextStyles } from "../../theme";
import { DentistryAgent } from "../../registration/dentistry/data/dentistry-agent";
import { DentistryConfig, RegistrationType } from "../../registration/dentistry/data/bean";
import { RegistrationDataProvider } from "../../registration/data/registration";

export class State extends BaseLoadingState {
    date: Date = new Date();
    doctorName?: string;
    doctorId?: string;

    registrationList?: TherapyRegistrationItem[];
    normalRegistrationList?: NormalTherapyRegistrationItem[];
    doctors?: TherapyDoctorBasicInfo[];

    //门诊挂号、理疗预约的预约设置
    registrationConfig?: DentistryConfig;

    clone(): State {
        return Object.assign(new State(), this);
    }
}

export class TherapyRegistrationPageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<TherapyRegistrationPageBloc | undefined>(undefined);

    static fromContext(context: TherapyRegistrationPageBloc): TherapyRegistrationPageBloc {
        return context;
    }

    constructor() {
        super();

        this.dispatch(new _EventInit()).then();
    }

    private _innerState!: State;

    private _loadTrigger = new Subject<number>();

    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventUpdateSearchDate, this._mapEventUpdateSearchDate);
        map.set(_EventSwitchDoctor, this._mapEventSwitchDoctor);
        map.set(_EventViewItem, this._mapEventViewItem);

        return map;
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    public requestReload(): void {
        this._loadTrigger.next(0);
    }

    public requestUpdateSearchDate(newDate: Date): void {
        this.dispatch(new _EventUpdateSearchDate(newDate)).then();
    }

    // 切换理疗师
    public requestSwitchDoctor(): void {
        this.dispatch(new _EventSwitchDoctor());
    }

    // 点击预约项
    @runFuncBeforeCheckExpired()
    public requestViewItem(data: TherapyRegistrationItem): void {
        this.dispatch(new _EventViewItem(data));
    }

    private async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        // 获取治疗理疗预约配置
        if (userCenter.isAllowedRegUpgrade) {
            this.innerState.registrationConfig = await DentistryAgent.queryDentistryRegistrationConfig(
                RegistrationType.therapyAppointment,
                false
            ).catchIgnore();
        }

        this.innerState.doctorId = userCenter.employee?.id ?? "";
        this.innerState.doctorName = userCenter.employee?.name ?? "";
        this.addDisposable(this._loadTrigger);
        this._loadTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.loadError = undefined;
                    this.innerState.loading = true;
                    this.update();
                    if (userCenter.isAllowedRegUpgrade) {
                        return PatientOrderDataAgent.getTherapyRegistrations(this.innerState.doctorId!, this.innerState.date)
                            .then((rsp) => rsp.rows)
                            .catch((error) => new ABCError(error))
                            .toObservable();
                    }
                    return PatientOrderDataAgent.getNormalTherapyRegistrations(this.innerState.doctorId!, this.innerState.date)
                        .then((rsp) => rsp.rows)
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                this.innerState.loading = false;
                this.innerState.registrationList = [];
                this.innerState.normalRegistrationList = [];
                if (rsp instanceof ABCError) {
                    this.innerState.loadError = rsp.detailError;
                } else {
                    if (userCenter.isAllowedRegUpgrade) {
                        this.innerState.registrationList = rsp;
                    } else {
                        this.innerState.normalRegistrationList = rsp;
                    }
                }
                this.update();
            })
            .addToDisposableBag(this);

        this._loadTrigger.next(0);
    }

    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    private async *_mapEventUpdateSearchDate(event: _EventUpdateSearchDate): AsyncGenerator<State> {
        this.innerState.date = event.newDate;
        this._loadTrigger.next(0);
    }

    private async *_mapEventSwitchDoctor(/*event: _EventSwitchDoctor*/): AsyncGenerator<State> {
        if (this.innerState.doctors == undefined) {
            const loading = new LoadingDialog("正在获取医生列表...");
            loading.show(1000);
            const rsp = userCenter.isAllowedRegUpgrade
                ? ((await RegistrationDataProvider.getDoctorList({ registrationType: RegistrationType.therapyAppointment })
                      .then((rsp) => rsp?.[0].doctors)
                      .catch((error) => new ABCError(error))) as TherapyDoctorBasicInfo[])
                : await PatientOrderDataAgent.getTherapyDoctorsBasic().catch((error) => new ABCError(error));
            if (rsp instanceof ABCError) {
                await loading.fail("获取失败：" + errorSummary(rsp));
                return;
            }

            await loading.hide();

            this.innerState.doctors = rsp;
        }

        const doctors = this.innerState.doctors;
        const options = doctors.map((item) => item.doctorName!);
        const selectIndex = doctors.findIndex((item) => item.doctorId === this.innerState.doctorId);
        const selects = await showOptionsBottomSheet({
            title: "选择理疗师",
            options: options,
            initialSelectIndexes: new Set([selectIndex]),
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
            showCloseButton: true,
        });

        if (_.isEmpty(selects)) return;

        const selectDoctor = doctors[_.first(selects!)!];
        this.innerState.doctorId = selectDoctor.doctorId;
        this.innerState.doctorName = selectDoctor.doctorName;

        this._loadTrigger.next(0);
    }

    private async *_mapEventViewItem(event: _EventViewItem): AsyncGenerator<State> {
        // const signInStatus = event.data.chargeSheetQueryExceptionType;
        // if (signInStatus !== TherapyRegistrationSignStatus.notNeed && signInStatus !== TherapyRegistrationSignStatus.signed) return;

        const selectIndex = await showBottomSheetMenu({
            menuItems: ["开单", "查看已有执行单"],
        });
        if (selectIndex == undefined) return;
        if (selectIndex === 0) {
            ABCNavigator.navigateToPage(
                <ExecuteInvoiceCreatePage patient={event.data.patient} registrationDetail={event.data.registrationFormItem} />
            ).then();
        } else {
            ABCNavigator.navigateToPage(<PatientOrderSearchPage name={event.data.patient?.name} />).then();
        }
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}
class _EventUpdateSearchDate extends _Event {
    newDate: Date;
    constructor(newDate: Date) {
        super();
        this.newDate = newDate;
    }
}

class _EventSwitchDoctor extends _Event {}

class _EventViewItem extends _Event {
    data: TherapyRegistrationItem;
    constructor(data: TherapyRegistrationItem) {
        super();
        this.data = data;
    }
}
