/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2022/12/19
 * @Copyright 成都字节流科技有限公司© 2022
 */
import React from "react";
import { View, Text, ScrollView } from "@hippy/react";
import { BaseComponent } from "../../base-ui/base-component";
import { showBottomSheet } from "../../base-ui/dialog/bottom_sheet";
import { RegistrationFormItem } from "../../registration/data/bean";
import { BottomSheetHelper } from "../../base-ui/abc-app-library";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { IconFontView, SizedBox } from "../../base-ui";
import { SafeAreaBottomView } from "../../base-ui/safe_area_view";
import { pxToDp } from "../../base-ui/utils/ui-utils";
import { TimeUtils } from "../../common-base-module/utils";

interface ExecuteRegistrationDetailDialogProps {
    detail?: RegistrationFormItem;
}

export class ExecuteRegistrationDetailDialog extends BaseComponent<ExecuteRegistrationDetailDialogProps> {
    constructor(props: ExecuteRegistrationDetailDialogProps) {
        super(props);
    }

    static async show(detail?: RegistrationFormItem): Promise<void> {
        return showBottomSheet(<ExecuteRegistrationDetailDialog detail={detail} />);
    }

    render(): JSX.Element {
        const { detail } = this.props;
        return (
            <View style={[ABCStyles.panelTopStyle, { height: pxToDp(375) }]}>
                {BottomSheetHelper.createTitleBar("预约挂号信息")}
                <ScrollView contentContainerStyle={{ paddingHorizontal: Sizes.dp16, paddingTop: Sizes.dp16 }}>
                    <View>{this.renderRegItemView("doctor-2", `${detail?.displayDoctorDepartName} ${detail?.getRevisitStatusName}`)}</View>
                    <View>
                        {this.renderRegItemView(
                            "time_1",
                            `${TimeUtils.formatDate(detail?.reserveDate, "MM月dd日")} ${TimeUtils.getDayOfWeek(
                                detail?.reserveDate,
                                "周"
                            )} ${detail?.reserveStart}~${detail?.reserveEnd}`
                        )}
                    </View>
                    {detail?.registrationProductName?.length && (
                        <View>{this.renderRegItemView("project", detail?.registrationProductName)}</View>
                    )}
                    {detail?.visitSourceRemark?.length && <View>{this.renderRegItemView("remark", detail?.visitSourceRemark)}</View>}
                </ScrollView>
                <SafeAreaBottomView />
            </View>
        );
    }

    private renderRegItemView(icon: string, text = ""): JSX.Element {
        return (
            <View style={[ABCStyles.rowAlignCenter, { marginBottom: Sizes.dp16 }]}>
                <IconFontView name={icon} size={Sizes.dp14} color={Colors.T6} />
                <SizedBox width={Sizes.dp11} />
                <Text style={TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp22 })}>{text}</Text>
            </View>
        );
    }
}
