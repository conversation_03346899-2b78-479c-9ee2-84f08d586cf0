/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020/9/16
 *
 * @description 理疗预约面板
 *
 */
import React from "react";
import { BaseBlocPage, NetworkView } from "../../base-ui/base-page";
import { ListView, Text, View, Style } from "@hippy/react";
import { ABCStyles, ABCStyleSheet, Color, Colors, flattenStyles, Sizes, TextStyles } from "../../theme";
import { TimeUtils } from "../../common-base-module/utils";
import { DropdownArrowView } from "../../base-ui/iconfont/iconfont-view";
import { TherapyRegistrationPageBloc } from "./therapy-registration-page-bloc";
import { BlocHelper } from "../../bloc/bloc-helper";
import { TimePicker } from "../../base-ui/picker/time-picker";
import { NormalTherapyRegistrationItem, TherapyRegistrationItem, TherapyRegistrationSignStatus } from "../data/nurse-station-data";
import { PatientListItemStatusTextStyle } from "../../outpatient/views/patient-list-item-view";
import { AbcView } from "../../base-ui/views/abc-view";
import { IconFontView, Spacer } from "../../base-ui";
import { RegistrationStatusV2 } from "../../registration/data/bean";
import { userCenter } from "../../user-center";
import { AbcEmptyItemView } from "../../base-ui/views/empty-view";
import { showConfirmDialog } from "../../base-ui/dialog/dialog-builder";
import { AssetImageView } from "../../base-ui/views/asset-image-view";
import { BaseComponent } from "../../base-ui/base-component";
import { Patient } from "../../base-business/data/beans";
import _ from "lodash";

const styles = ABCStyleSheet.create({
    timeFilter: {
        marginTop: -Sizes.dp1,
        ...ABCStyles.rowAlignCenter,
        height: Sizes.dp48,
        justifyContent: "space-between",
        backgroundColor: Colors.panelBg,
    },
});

interface TherapyRegistrationPageProps {}

export class TherapyRegistrationPage extends BaseBlocPage<TherapyRegistrationPageProps, TherapyRegistrationPageBloc> {
    constructor(props: TherapyRegistrationPageProps) {
        super(props);
        this.bloc = new TherapyRegistrationPageBloc();
    }

    componentDidMount(): void {
        super.componentDidMount();
    }

    getAppBarTitle(): string {
        return "预约看板";
    }

    getStatusBarColor(): Color {
        return Colors.panelBg;
    }

    getAppBarBgColor(): Color {
        return Colors.panelBg;
    }

    renderContent(): JSX.Element | undefined {
        return (
            <View style={{ flex: 1 }}>
                {this._renderFilterView()}
                <_ListView />
            </View>
        );
    }

    private _renderFilterView(): JSX.Element {
        const { date, doctorName } = this.bloc.currentState;
        return (
            <View style={styles.timeFilter}>
                <AbcView
                    style={[ABCStyles.rowAlignCenter, { flex: 1, justifyContent: "center" }]}
                    onClick={this._onClickTimeFilter.bind(this)}
                >
                    <Text style={TextStyles.t14NT2.copyWith({ color: Colors.t2 })} numberOfLines={1}>
                        {TimeUtils.formatDatetimeAsRecent(date, { time: false })}
                    </Text>
                    <DropdownArrowView />
                </AbcView>
                <AbcView
                    style={[ABCStyles.rowAlignCenter, { flex: 1, justifyContent: "center" }]}
                    onClick={() => this.bloc.requestSwitchDoctor()}
                >
                    <Text style={TextStyles.t14NT2.copyWith({ color: Colors.t2 })} numberOfLines={1}>
                        {doctorName ?? ""}
                    </Text>
                    <DropdownArrowView />
                </AbcView>
            </View>
        );
    }

    private async _onClickTimeFilter(): Promise<void> {
        const newDate = await TimePicker.show(this.bloc.currentState.date);
        if (newDate != undefined) {
            this.bloc.requestUpdateSearchDate(newDate);
        }
    }
}

interface _ListViewProps {}

class _ListView extends NetworkView<_ListViewProps> {
    static contextType = TherapyRegistrationPageBloc.Context;

    constructor(props: _ListViewProps) {
        super(props);
    }

    componentDidMount(): void {
        super.componentDidMount();
        BlocHelper.connectLoadingStatus(TherapyRegistrationPageBloc.fromContext(this.context), this, (state) => {
            return userCenter.isAllowedRegUpgrade ? state.registrationList?.length == 0 : state.normalRegistrationList?.length == 0;
        });
    }
    reloadData() {
        TherapyRegistrationPageBloc.fromContext(this.context).requestReload();
    }

    emptyContent(): JSX.Element {
        return <AbcEmptyItemView tips={"暂无理疗预约记录"} />;
    }

    renderContent(): JSX.Element {
        const { registrationList = [], normalRegistrationList = [] } = TherapyRegistrationPageBloc.fromContext(this.context).currentState;
        const dataList = userCenter.isAllowedRegUpgrade ? registrationList : normalRegistrationList;
        return (
            <ListView
                style={{ backgroundColor: Colors.white, flex: 1 }}
                dataSource={dataList}
                numberOfRows={dataList.length}
                renderRow={this._renderRow.bind(this)}
                scrollEventThrottle={300}
            />
        );
    }

    private _renderRow(data: TherapyRegistrationItem | NormalTherapyRegistrationItem): JSX.Element {
        const { registrationConfig } = TherapyRegistrationPageBloc.fromContext(this.context).currentState;

        let statusTextStyle = PatientListItemStatusTextStyle.draft;
        let statusName,
            notCanEdit = false,
            orderNoStr: string | undefined,
            // reserveDate: Date | undefined,
            reserveStart: string | undefined,
            reserveEnd: string | undefined,
            executeActionsDisplay;
        if (data instanceof TherapyRegistrationItem) {
            orderNoStr = data?.registrationFormItem?.orderNoStr;
            statusName = data?.registrationFormItem?.statusName;
            // reserveDate = data?.registrationFormItem?.reserveDate;
            reserveStart = data?.registrationFormItem?.reserveStart;
            reserveEnd = data?.registrationFormItem?.reserveEnd;
            if (data?.registrationFormItem?.statusV2 == RegistrationStatusV2.waitingSignIn) {
                //待签(黄色)
                statusTextStyle = PatientListItemStatusTextStyle.Style4;
            }
            // else if (data?.registrationFormItem?.statusV2 == RegistrationStatusV2.reserved) {
            //     //已预约（蓝色）
            //     statusTextStyle = PatientListItemStatusTextStyle.draft;
            // }
            else if (data?.registrationFormItem?.statusV2 == RegistrationStatusV2.signed) {
                //已签（灰色）
                statusTextStyle = PatientListItemStatusTextStyle.visited;
            }

            //预约项目
            executeActionsDisplay = data.registrationFormItem?.registrationProducts?.map((item) => item.displayName).join("、");

            //待签状态下，禁用开单和查看
            notCanEdit = data?.registrationFormItem?.statusV2 == RegistrationStatusV2.waitingSignIn;
        } else if (data instanceof NormalTherapyRegistrationItem) {
            const { signInStatus } = data.therapyRegistration!;
            orderNoStr = data?.therapyRegistration?.displayOrderNo;
            // reserveDate = data.therapyRegistration?.reserveDate;
            reserveStart = data.therapyRegistration?.reserveStart;
            reserveEnd = data.therapyRegistration?.reserveEnd;
            switch (signInStatus) {
                case TherapyRegistrationSignStatus.waitSign:
                    statusName = "待签";
                    notCanEdit = true;
                    break;
                case TherapyRegistrationSignStatus.signed:
                    statusName = "已签";
                    statusTextStyle = PatientListItemStatusTextStyle.visited;
                    break;
                // case TherapyRegistrationSignStatus.notNeed:
                //     statusName = "已预约";
                //     statusTextStyle = PatientListItemStatusTextStyle.draft;
                //     break;
            }
        }

        // 已预约状态不展示
        const _statusName = statusName == "已预约" ? undefined : statusName;
        return (
            <TherapyRegistrationListItem
                style={{ backgroundColor: Colors.white }}
                onClick={() => {
                    if (notCanEdit) {
                        return showConfirmDialog("", "请患者签到后再进行开单/查单执行");
                    }
                    !notCanEdit && TherapyRegistrationPageBloc.fromContext(this.context).requestViewItem(data);
                }}
                patient={data.patient}
                displayProductName={!!executeActionsDisplay ? executeActionsDisplay : ""}
                orderNoStr={orderNoStr}
                // timeStr={`${TimeUtils.formatDatetimeAsRecent(reserveDate, { time: false })} ${reserveStart}~${reserveEnd}`}
                timeStr={`${reserveStart}~${reserveEnd}`}
                statusName={_statusName}
                statusTextStyle={statusTextStyle}
                needSignIn={registrationConfig?.isNeedSignIn}
                isFixedMode={registrationConfig?.isFixedMode}
                isFlexibleMode={registrationConfig?.isFlexibleMode}
            />
        );
    }
}

interface TherapyRegistrationListItemProps {
    patient?: Patient;
    displayProductName?: string; // 项目名称
    orderNoStr?: string; // 号数
    timeStr?: string; // 时间字符串
    statusName?: string; // 签到状态名称
    statusTextStyle?: PatientListItemStatusTextStyle;
    style?: Style | Style[];
    needSignIn?: boolean; // 是否需要签到
    isFixedMode?: boolean; // 是否号源模式
    isFlexibleMode?: boolean; // 是否灵活模式

    onClick?(): void;
}

class TherapyRegistrationListItem extends BaseComponent<TherapyRegistrationListItemProps> {
    renderPatientHeaderView(): JSX.Element {
        const { patient } = this.props;
        return (
            <View
                style={[
                    !!patient?.wxHeadImgUrl ? {} : Sizes.paddingLTRB(Sizes.dp10, Sizes.dp9, Sizes.dp10, Sizes.dp9),
                    { borderRadius: Sizes.dp2, borderWidth: 0.5, borderColor: Colors.dividerLineColor },
                ]}
            >
                {!!patient?.wxHeadImgUrl ? (
                    <AssetImageView
                        src={patient?.wxHeadImgUrl}
                        style={{ width: Sizes.dp32, height: Sizes.dp32, borderRadius: Sizes.dp2 }}
                    />
                ) : (
                    <IconFontView
                        size={Sizes.dp14}
                        name={"patient"}
                        color={patient?.sex == "男" || !patient?.name ? Colors.malePatientColor : Colors.femalePatientColor}
                    />
                )}
            </View>
        );
    }

    protected renderPatientNameView(): JSX.Element {
        const { patient } = this.props;
        if (!patient?.name) return <View />;

        return (
            <Text style={[TextStyles.t16NT1, { marginLeft: Sizes.dp8, flexShrink: 1 }]} numberOfLines={1}>
                {patient?.name ? patient?.name : "匿名患者"}
            </Text>
        );
    }

    renderTimeStr() {
        const { timeStr, isFixedMode, isFlexibleMode, orderNoStr } = this.props;
        let isFixedModeShow: undefined | false | boolean = isFixedMode && !!orderNoStr;
        let isFlexibleModeShow: undefined | false | boolean = isFlexibleMode && !!timeStr;
        if (isFixedMode == undefined && isFlexibleMode == undefined) {
            isFixedModeShow = true;
        }

        // 不是预约升级的用户，展示号数时间段
        if (!userCenter?.isAllowedRegUpgrade) {
            isFixedModeShow = true;
            isFlexibleModeShow = true;
        }

        return (
            <View style={ABCStyles.rowAlignCenter}>
                {isFixedModeShow && (
                    <Text style={[TextStyles.t14NB, { marginLeft: Sizes.dp8 }]} numberOfLines={1}>
                        {orderNoStr}
                    </Text>
                )}
                {isFlexibleModeShow && (
                    <Text style={[TextStyles.t14NB, { marginLeft: Sizes.dp8 }]} numberOfLines={1}>
                        {timeStr}
                    </Text>
                )}
            </View>
        );
    }

    renderStatusView(): JSX.Element {
        const { statusName, statusTextStyle } = this.props;
        let textStatusStyle = TextStyles.t14MT1;
        switch (statusTextStyle) {
            case PatientListItemStatusTextStyle.draft:
                textStatusStyle = textStatusStyle.copyWith({ color: Colors.B2 });
                break;
            case PatientListItemStatusTextStyle.waitVisit:
                textStatusStyle = textStatusStyle.copyWith({ color: Colors.G2 });
                break;
            case PatientListItemStatusTextStyle.visited:
                textStatusStyle = TextStyles.t14NT6.copyWith({ color: Colors.T6 });
                break;
            case PatientListItemStatusTextStyle.Style4:
                textStatusStyle = textStatusStyle.copyWith({ color: Colors.Y2 });
                break;
        }

        return !_.isEmpty(statusName) ? (
            <View style={{ marginLeft: Sizes.dp12 }}>
                <Text style={textStatusStyle}>{statusName}</Text>
            </View>
        ) : (
            <View />
        );
    }

    renderMemberView(): JSX.Element {
        const { patient } = this.props;
        if (!patient?.isMember) return <View />;
        return (
            <View style={{ marginLeft: Sizes.dp4 }}>
                <AssetImageView name={"charge_invoice_patient_member"} style={{ width: Sizes.dp16, height: Sizes.dp16 }} />
            </View>
        );
    }

    render() {
        const { displayProductName, style, onClick } = this.props;
        return (
            <AbcView
                style={[
                    ABCStyles.bottomLine,
                    ABCStyles.rowAlignCenter,
                    flattenStyles(style),
                    { marginLeft: Sizes.listHorizontalMargin, paddingVertical: Sizes.dp22 },
                ]}
                onClick={() => onClick?.()}
            >
                <View style={[ABCStyles.rowAlignCenter, { flexShrink: 1 }, flattenStyles(style)]}>
                    {this.renderPatientHeaderView()}
                    {this.renderPatientNameView()}
                    {this.renderMemberView()}
                </View>
                <Spacer />
                <View style={[ABCStyles.rowAlignCenter, { paddingRight: Sizes.listHorizontalMargin, marginLeft: Sizes.dp8 }]}>
                    {displayProductName && (
                        <Text style={[TextStyles.t14NT1, { flexShrink: 1 }]} numberOfLines={1}>
                            {displayProductName}
                        </Text>
                    )}
                    {this.renderTimeStr()}
                    {this.renderStatusView()}
                </View>
            </AbcView>
        );
    }
}
