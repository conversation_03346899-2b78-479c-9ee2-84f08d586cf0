import PathUtils from "../base-business/file/path-utils";
import FileUtils from "../common-base-module/file/file-utils";
import { LogUtils } from "../common-base-module/log";

/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/9/17
 *
 * @description
 */

export class ExecuteConst {
    static async getDraftDir(): Promise<string | null> {
        const dir = await PathUtils.getExecuteInvoiceDir();
        const ret = await FileUtils.createDir(`${dir}/draft`);

        if (!ret) {
            LogUtils.d("failed to create excute draft dir");
            return null;
        }

        return dir;
    }

    static async getDraftFile(draftId: string): Promise<string> {
        const draftDir = await this.getDraftDir();

        return `${draftDir}/${draftId}.dat`;
    }

    static defaultSex = "男";
}
