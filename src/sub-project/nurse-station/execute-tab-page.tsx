/**
 * create by deng<PERSON>e
 * desc: 执行列表页面
 * create date 2020/8/3
 */
import React from "react";
import { Text, View } from "@hippy/react";
import {
    CommonFilterId,
    FilterGroupId,
    FilterViewProps,
    SearchInput,
    departmentFilterId,
    departmentNotSpecifiedItem,
} from "../base-ui/searchBar/search-bar";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { FilterGroup, FilterItem, Filters } from "../base-ui/searchBar/search-bar-bean";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { PatientOrderSearchPage } from "./patient-order-search-page";
import { SizedBox } from "../base-ui";
import { of, Subject } from "rxjs";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import IconFontView from "../base-ui/iconfont/iconfont-view";
import { TherapyRegistrationPage } from "./execute-create/therapy-registration-page";
import { ExecuteInvoiceCreatePage } from "./execute-create/execute-invoice-create-page";
import { Range } from "../base-ui/utils/value-holder";
import { TimeUtils } from "../common-base-module/utils";
import ExecuteInvoicePage from "./execute-invoice-page";
import { runFuncBeforeCheckExpired } from "../views/clinic-edition";
import { AbcButton } from "../base-ui/views/abc-button";
import { AbcView } from "../base-ui/views/abc-view";
import { Badge } from "../base-ui/badge/badge";
import { StatFilterEnumID } from "../statistics/utils/statistics-utils";
import { UIUtils } from "../base-ui/utils";
import { showRightSheet } from "../base-ui/dialog/bottom_sheet";
import { StatisticsTimeFilterView } from "../statistics/views/statistics-time-filter-view";
import { FilterViewExpansion } from "../statistics/views/stat-filter-view";
import { userCenter } from "../user-center";
import { EmployeesSelectionItem } from "../statistics/data/statistics-bean";
import { EmployeeTypeEnum } from "../statistics/data/statistics-agent";
import { ClinicAgent, departmentInfo } from "../base-business/data/clinic-agent";
import { ABCError } from "../common-base-module/common-error";
import { StatisticsPersonnelFilterView } from "../statistics/views/stat-filter-employee-search-dialog";
import { switchMap } from "rxjs/operators";
import { PatientOrderDataAgent, PatientOrderSearchResult } from "./data/nurse-station-data";
import { OnlinePropertyConfigProvider } from "../data/online-property-config-provder";
import { AbcModuleTabPage, ModuleTabItem } from "../views/base-tab-page";
import { PatientOrderListView } from "./execute-list-view";
import { ExecuteTabType } from "./data/bean";
import { showBottomPanel } from "../base-ui/abc-app-library/panel";
import { SelectionDialogWithSearch } from "../base-ui/selection/selection-dialog-with-search";
import { AbcSet } from "../base-ui/utils/abc-set";
import { cloneDeep } from "lodash";
import { Toast } from "../base-ui/dialog/toast";

interface ExecuteTabPageProps {
    tabIndex?: number;
}

interface ExecuteTabPageStates {
    enableOpenSheet: boolean;
    canHistorySheet: boolean; // 查看历史执行单--仅允许查看自己开出的
}

export class ExecuteTabPage extends AbcModuleTabPage<ExecuteTabPageProps, ExecuteTabPageStates, PatientOrderListView> {
    tabsTimeFilterInfo: Map<ExecuteTabType, Filters> = new Map([
        [ExecuteTabType.allBill, ExecuteFilter({ canSelectEmployee: true, executeTabType: ExecuteTabType.allBill })],
        [ExecuteTabType.myBill, ExecuteFilter({ executeTabType: ExecuteTabType.myBill })],
        [ExecuteTabType.myExecute, ExecuteFilter({ executeTabType: ExecuteTabType.myExecute })],
    ]);
    tabsViewProps = {
        lazy: false,
        tabsStyle: {
            height: Sizes.dp44,
            justifyContent: "center",
            backgroundColor: Colors.panelBg,
            paddingHorizontal: Sizes.listHorizontalMargin,
        },
        tabStyle: {
            ...TextStyles.t16NT2.copyWith({ color: Colors.t2 }),
            marginRight: Sizes.dp28,
        },
    };

    constructor(props: ExecuteTabPageProps) {
        super(props);
        this.state = {
            enableOpenSheet: true,
            canHistorySheet: true,
        };
        this.currentTabIndex = this.props.tabIndex ?? ExecuteTabType.allBill;
    }

    get isDentistry(): boolean {
        return !!userCenter.clinic?.isDentistryClinic;
    }

    componentDidMount(): void {
        //获取执行站配置
        OnlinePropertyConfigProvider.instance.getNurseStationConfig().then((rsp) => {
            this.setState({ enableOpenSheet: !!rsp.enableOpenSheet });
        });
        ClinicAgent.getEmployeesMeConfig()
            .then((rsp) => {
                this.setState({
                    canHistorySheet: !!rsp.employeeDataPermission?.nurse?.historySheet,
                });
            })
            .catchIgnore();
    }

    @runFuncBeforeCheckExpired()
    createExecutePage(): void {
        ABCNavigator.navigateToPage(<ExecuteInvoiceCreatePage />);
    }

    _jumpRelativePage(rsp: PatientOrderSearchResult): void {
        const data = rsp?.result;
        if (data?.length == 1) {
            if (data[0]?.isLocalDraft) {
                ABCNavigator.navigateToPage(<ExecuteInvoiceCreatePage draftId={data?.[0]?.localDraftId} />).then();
            } else {
                // 是否可访问已执行单据详情
                ABCNavigator.navigateToPage(<ExecuteInvoicePage orderId={data?.[0]?.id} />).then();
            }
        } else {
            ABCNavigator.navigateToPage(<PatientOrderSearchPage name={rsp?.keyword} />).then();
        }
    }

    handleChangeTab(index: number): void {
        super.handleChangeTab(index);
        this.listViewRef?.reloadData();
    }

    protected renderLeftSuffix(): JSX.Element {
        return super.renderLeftSuffix(false);
    }

    protected renderRightSuffix(): JSX.Element {
        // 口腔诊所隐藏开单按钮
        if (!this.state.enableOpenSheet || this.isDentistry) return <View />;
        return (
            <AbcButton
                style={[
                    ABCStyles.rowAlignCenter,
                    { height: Sizes.dp30, width: Sizes.dp63, backgroundColor: Colors.mainColor, marginRight: Sizes.dp16 },
                ]}
                key={"submit"}
                onClick={() => {
                    this.createExecutePage();
                }}
                pressColor={Colors.mainColorPress}
            >
                <IconFontView name={"add"} size={Sizes.dp10} color={Colors.S2} style={{ marginRight: Sizes.dp4 }} />
                <Text style={[TextStyles.t14NS2, ABCStyles.rowAlignCenter]} numberOfLines={1}>
                    {"开单"}
                </Text>
            </AbcButton>
        );
    }

    protected createModuleTabList(): ModuleTabItem[] {
        // 如果权限设置的是「仅允许查看自己开出的」，并且当前登录的账户不是医助，那么执行单列表不展示「我的开单」这个tab(但管理员不受这个限制)
        const notShowAll = this.state.canHistorySheet && !userCenter.clinic?.isDoctorAssist && !userCenter.clinic?.isAdministrator;
        const list = [
            {
                id: ExecuteTabType.allBill,
                title: "全部",
                show: true,
            },
            {
                id: ExecuteTabType.myBill,
                title: "我的开单",
                show: !notShowAll,
            },
            {
                id: ExecuteTabType.myExecute,
                title: "我的执行",
                show: true,
            },
        ];

        return list
            ?.filter((t) => t.show)
            .map((moduleList) => ({
                ...moduleList,
                contentRender: (item) => (
                    <>
                        {this._renderSearchFilterView(item.id)}
                        <PatientOrderListView
                            tabType={item.id}
                            ref={(ref) => {
                                if (ref) this.listViewRefs.set(item.id, ref);
                            }}
                        />
                    </>
                ),
            }));
    }

    private _renderSearchFilterView(tabType: ExecuteTabType): JSX.Element {
        const canShowTherapyAppointmentPanel = this.listViewRef?.bloc.currentState.canShowTherapyAppointmentPanel;
        const therapyRegistrationCount = this.listViewRef?.bloc.currentState.therapyRegistrationCount;
        const height = Sizes.dp48;
        // 如果权限设置的是「仅允许查看自己开出的」，并且当前登录的账户不是医助，那么执行单列表「全部」这个tab下面不显示开单人筛选条件(但管理员不受这个限制)
        const notShowAll = this.state.canHistorySheet && !userCenter.clinic?.isDoctorAssist && !userCenter.clinic?.isAdministrator;
        if (tabType == ExecuteTabType.allBill) {
            const oldFilter = this.tabsTimeFilterInfo.get(tabType);
            const newFilter = ExecuteFilter({ canSelectEmployee: !notShowAll, executeTabType: tabType });
            if (oldFilter) {
                // 只更新必要属性，其余保留，用 fillSelectAttrs 保留用户选择
                newFilter.fillSelectAttrs(oldFilter, true);
            }
            this.tabsTimeFilterInfo.set(tabType, newFilter);
        }

        return (
            <ExecuteFilterView
                filters={JsonMapper.deserialize(Filters, this.tabsTimeFilterInfo.get(tabType))}
                canShowTherapyAppointmentPanel={canShowTherapyAppointmentPanel}
                therapyRegistrationCount={therapyRegistrationCount}
                height={Sizes.dp12 + height}
                showClear={true}
                width={UIUtils.getScreenWidth()}
                onChange={(filter: Filters) => {
                    this.tabsTimeFilterInfo.get(tabType)?.fillSelectAttrs(filter, true);
                    this.listViewRef?.bloc.requestChangeFilter(filter);
                    this.listViewRef?.reloadData(); // 这里解决全部开单筛选事件后的刷新问题
                    this.setState({});
                }}
                isShowScanIcon={true}
                qrScanGenerator={PatientOrderDataAgent.getPatientOrderList}
                qrScanRsp={(rsp: any) => this._jumpRelativePage(rsp)}
            />
        );
    }
}

// 筛选项目
const ExecuteFilter = (options?: { canSelectEmployee?: boolean; executeTabType?: ExecuteTabType }) => {
    const list = [
        {
            id: FilterGroupId.time,
            name: "时间",
            filters: [
                {
                    title: "今天",
                    id: CommonFilterId.timeToday,
                    exclusive: true,
                    select: options?.executeTabType != ExecuteTabType.myExecute,
                    isDefault: options?.executeTabType != ExecuteTabType.myExecute,
                    timeRange: new Range<Date>(TimeUtils.getTodayStart(), TimeUtils.getTodayStart()),
                },
                {
                    title: "昨天",
                    id: CommonFilterId.timeYesterday,
                    exclusive: true,
                    timeRange: new Range<Date>(TimeUtils.getYesterdayStart(), TimeUtils.getYesterdayStart()),
                },
                {
                    title: "本周",
                    id: CommonFilterId.timeThisWeek,
                    exclusive: true,
                    timeRange: new Range<Date>(TimeUtils.getThisWeekFirstDay(), TimeUtils.getTodayEnd()),
                },
                {
                    title: "本月",
                    id: CommonFilterId.timeThisMonth,
                    exclusive: true,
                    select: options?.executeTabType == ExecuteTabType.myExecute,
                    isDefault: options?.executeTabType == ExecuteTabType.myExecute,
                    timeRange: new Range<Date>(TimeUtils.getThisMonthFirstDay(), TimeUtils.getTodayEnd()),
                },
                {
                    title: "选择时间",
                    defaultTitle: "选择时间",
                    id: CommonFilterId.asyncFilter,
                    exclusive: true,
                    stepTime: {
                        months: 3,
                    },
                },
            ],
        },
    ];
    if (!!options?.canSelectEmployee) {
        list.push({
            id: FilterGroupId.statEmployee,
            name: "开单人",
            filters: [
                {
                    // @ts-ignore
                    id: StatFilterEnumID.achievementEmployeeID,
                    exclusive: true,
                    select: true,
                    isDefault: true,
                    info: { id: "", name: "", namePy: "", namePyFirst: "" },
                },
            ],
        });
    }
    list.push({
        id: FilterGroupId.billingDepartment,
        name: "开单科室",
        filters: [
            {
                // @ts-ignore
                id: departmentFilterId.billingDepartmentId,
                exclusive: true,
                select: true,
                isDefault: true,
                info: [],
            },
        ],
    });
    return Filters.createFilters(list);
};
interface FilterViewExpansionProps extends FilterViewProps {
    departmentList: departmentInfo[];
}
class ExecuteFilterViewExpansion<P extends FilterViewExpansionProps> extends FilterViewExpansion<P> {
    constructor(props: P) {
        super(props);
    }

    async _handleEmployeeCheck(item: FilterItem, group: FilterGroup): Promise<void> {
        const employeeList = await ClinicAgent.getClinicEmployeesSimpleInfo().catch((error) => new ABCError(error));
        if (employeeList instanceof ABCError) return;
        employeeList.sort((last) => (last.id == userCenter.employee?.id ? -1 : 1));

        const select = await StatisticsPersonnelFilterView.show({
            selectEmployeeId: item.info?.id ?? "",
            selectEmployeeType: EmployeeTypeEnum.SELLER,
            employeesSelectionData: employeeList,
        });
        if (select) {
            item.info = select;
            this._handleCheckItem(item, group, false, true);
        }
    }
    async _handleCheckItem(item: FilterItem, group: FilterGroup, disable: boolean, employeeCheck = false): Promise<void> {
        this._copyFilterViewHandleCheckItem(item, group, disable);

        //选中time的其他项时清空range
        const timeRange = this.filters?.getFilterItemById(CommonFilterId.asyncFilter);
        const _timeRange = this._checkFilterTemp?.getFilterItemById(CommonFilterId.asyncFilter);
        if (!_timeRange?.select) {
            timeRange!.timeRange = undefined;
            this.forceUpdate();
        }

        if (!employeeCheck && group.id == FilterGroupId.statEmployee && item.id == StatFilterEnumID.achievementEmployeeID) {
            group.filters?.map((_item) => {
                _item.info = undefined;
                this.setState({});
            });
        }
    }

    /**
     * 选择科室
     * @private
     */
    async _handleDepartmentCheck(item: FilterItem, group: FilterGroup): Promise<void> {
        const selectDepartmentList = item.info;
        const tempList: AbcSet<departmentInfo> =
            new AbcSet(cloneDeep(selectDepartmentList), (item) => item.id ?? "") ?? new AbcSet<departmentInfo>();
        const { departmentList } = this.props;

        const newDepartmentList = [...(departmentList || []), departmentNotSpecifiedItem];
        const searchListByKeyword = (list: departmentInfo[], searchText?: string): departmentInfo[] => {
            const keywords = (searchText ?? "").toLowerCase();
            if (!keywords) {
                return list;
            }
            return list.filter((item: { name: string }) => {
                return item.name?.toLowerCase()?.indexOf(keywords) > -1;
            });
        };

        const currentSelectedItem = (item?: departmentInfo | undefined) => {
            if (!item) return;
            const { id, name } = departmentNotSpecifiedItem;
            if (
                tempList?.size &&
                ((item.id === id && item.name === name && !tempList?.has(departmentNotSpecifiedItem)) ||
                    (item.id !== id && item.name !== name && tempList?.has(departmentNotSpecifiedItem)))
            ) {
                Toast.show("暂不支持同时选择科室和未指定科室选项");
                return;
            }
            if (tempList?.has(item)) {
                tempList.delete(item);
            } else {
                tempList?.add(item);
            }
            return tempList;
        };

        const onConfirmSelectDepartment = (data: departmentInfo[] | undefined) => {
            if (!data) return;

            item.info = data;
            this._handleCheckItem(item, group, false);
        };

        await showBottomPanel(
            <SelectionDialogWithSearch
                list={newDepartmentList}
                searchHint="选择科室"
                selectionModel="multiple"
                filterList={(value) => searchListByKeyword(newDepartmentList, value)}
                displayOption={{ title: (data) => data["name"] ?? "" }}
                onSelectItem={(item) => currentSelectedItem(item)}
                onConfirm={(selectedItems) => {
                    onConfirmSelectDepartment(selectedItems);
                    ABCNavigator.pop();
                }}
                selectItem={tempList}
            />,
            {
                topMaskHeight: Sizes.dp160,
            }
        );
    }
}

/**
 *  执行站导航栏（搜索执行单、筛选时间开单人、查看预约看板）
 * @private
 */
export interface ExecuteTimeFilterViewProps {
    filters?: Filters;

    defaultFilterInfo?: Filters;

    height?: number;

    canShowTherapyAppointmentPanel?: boolean; // 显示理疗预约按钮 开启了理疗预约或存在有效预约（可能预约后，诊所又关闭了理疗预约，也显示出来）
    therapyRegistrationCount?: number;

    achievementEmployeesSelectionData?: EmployeesSelectionItem[]; // 开单人列表
    selectEmployees?: EmployeesSelectionItem;
    selectEmployeesType?: EmployeeTypeEnum;
    isShowScanIcon?: boolean; // 是否显示扫码Icon

    onChange?(arg1?: Filters): void;
    qrScanGenerator?: Function; //扫码查询的接口
    qrScanRsp?: (rsp?: any) => void;
}

interface AbcSearchBarWithFilterStates {
    activeFilterView: boolean; // 筛选栏Icon状态
}
class ExecuteFilterView<P extends ExecuteTimeFilterViewProps = {}> extends StatisticsTimeFilterView<P, AbcSearchBarWithFilterStates> {
    private _qrScanSearchTrigger: Subject<string> = new Subject<string>();
    constructor(props: P) {
        super(props);
        this.state = {
            activeFilterView: false,
        };
    }

    componentDidMount(): void {
        this._qrScanSearchTrigger
            .pipe(
                switchMap((code) => {
                    if (!code) return of(null);
                    return this.props.qrScanGenerator?.({ keyword: code }).toObservable();
                })
            )
            .subscribe((rsp) => {
                this.props.qrScanRsp?.(rsp);
            })
            .addToDisposableBag(this);
    }

    // 防止连续点击的标志
    private _isFilterViewProcessing = false;

    protected async _showFilterView(): Promise<void> {
        // 如果正在处理筛选视图，则忽略此次点击
        if (this._isFilterViewProcessing) {
            return;
        }
        try {
            // 设置正在处理标志
            this._isFilterViewProcessing = true;
            const employeeList = await ClinicAgent.getClinicEmployeesSimpleInfo().catch((error) => new ABCError(error));
            const departmentList = await ClinicAgent.getClinicDepartmentsOutpatient().catch((error) => new ABCError(error));
            if (employeeList instanceof ABCError) return;
            if (departmentList instanceof ABCError) {
                this._isFilterViewProcessing = false;
                return;
            }
            employeeList.sort((last) => (last.id == userCenter.employee?.id ? -1 : 1));

            const { height = Sizes.dp48, filters } = this.props;
            this.setState({ activeFilterView: true });

            const select: Filters = await showRightSheet(
                <ExecuteFilterViewExpansion
                    filters={JsonMapper.deserialize(Filters, filters)}
                    height={Sizes.dp44 + height}
                    showClear={true}
                    width={UIUtils.getScreenWidth()}
                    achievementEmployeesSelectionData={employeeList}
                    departmentList={departmentList}
                />
            );
            this.setState({ activeFilterView: false });

            if (!select) return;
            this.defaultFilterItem = select;
            this.props.onChange?.(this.defaultFilterItem);
            this.forceUpdate();
        } finally {
            // 延迟300毫秒后重置标志，防止短时间内的连续点击
            setTimeout(() => {
                this._isFilterViewProcessing = false;
            }, 300);
        }
    }

    // 理疗师预约看板
    renderAppointmentBoardView(): JSX.Element {
        const { therapyRegistrationCount, canShowTherapyAppointmentPanel } = this.props;
        if (!canShowTherapyAppointmentPanel) return <View />;
        return (
            <AbcView
                style={{ paddingLeft: Sizes.dp12, paddingRight: Sizes.dp16, justifyContent: "center" }}
                onClick={() => ABCNavigator.navigateToPage(<TherapyRegistrationPage />)}
            >
                <Badge value={(therapyRegistrationCount ?? 0) <= 0 ? undefined : therapyRegistrationCount} maxValue={99}>
                    <IconFontView name={"calendar"} size={Sizes.dp18} color={Colors.T1} />
                </Badge>
            </AbcView>
        );
    }

    public render(): JSX.Element {
        return (
            <View style={[ABCStyles.rowAlignCenter, { height: Sizes.dp59, backgroundColor: Colors.panelBg }]}>
                <View
                    style={{ flex: 1 }}
                    collapsable={false}
                    onLayout={(layoutInfo) => {
                        //@ts-ignore
                        this.viewHeight = layoutInfo.layout.height;
                    }}
                >
                    <View style={{ flexDirection: "row", marginVertical: Sizes.dp8 }}>
                        <SizedBox width={Sizes.dp16} />
                        <SearchInput
                            inputStyle={{ backgroundColor: Colors.white }}
                            searchContainerStyle={{ backgroundColor: Colors.white }}
                            placeholder={"搜索执行单"}
                            editable={false}
                            showClear={true}
                            onChange={() => ABCNavigator.navigateToPage(<PatientOrderSearchPage />).then()}
                            rightIconStyle={{ paddingRight: Sizes.dp8 }}
                            isShowScanIcon={this.props?.isShowScanIcon}
                            onTriggerScan={(code) => {
                                this._qrScanSearchTrigger.next(code);
                            }}
                            scanIconStyle={{ paddingRight: Sizes.dp8 }}
                        />
                        <SizedBox width={Sizes.dp4} />
                        <AbcView
                            style={{ paddingLeft: Sizes.dp16, paddingRight: Sizes.dp12, justifyContent: "center" }}
                            onClick={() => this._showFilterView()}
                        >
                            <IconFontView
                                name={"filter"}
                                size={Sizes.dp16}
                                color={this.state.activeFilterView ? Colors.mainColor : Colors.T1}
                            />
                        </AbcView>
                        {this.renderAppointmentBoardView()}
                    </View>
                </View>
            </View>
        );
    }
}
