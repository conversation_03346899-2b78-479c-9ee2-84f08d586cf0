#!/usr/bin/env python
# -*- coding: UTF-8 -*-

import sys
import json
import os
import shutil
import hashlib
import json
import time
import importlib

outputDir = 'dist'
timestamp = time.strftime('%Y%m%d_%H%M%S', time.localtime(time.time()))

sourceMapPassword = 'r5Y!xqT4aq%TK#yh'


buildNum = str(int(time.time()/60))
print('buildNum = ' + str(buildNum))

with open("./buildNum.txt", "w") as f:
    f.write(buildNum);


class PluginInfo:
    def __init__(self):
        self.name = ''
        self.version = ''
        self.md5 = ''
        self.name = ''
        self.host_min_version = ''
        self.host_max_version = ''
        self.url = ''


class PackageInfo:
    def __init__(self):
        self.hostMinVersion = ''
        self.hostMaxVersion = ''
        self.version = ''
        self.name = ''

    def loadFromJson(self, packageJson):
        self.hostMinVersion = packageJson['hostMinVersion']
        self.hostMaxVersion = packageJson['hostMaxVersion']
        self.version = packageJson['version']
        self.name = packageJson['name']


def _loadPackageInfo():
    with open("./package.json", "r") as load_f:
        packageJson = json.load(load_f)
        packageInfo = PackageInfo()
        packageInfo.loadFromJson(packageJson)
        return packageInfo


def _prepareEnv():
    initDirsCmd = 'rm -rf {0}'.format(outputDir)
    os.system(initDirsCmd)


def _build():
    buildVendorCmd = 'npm run hippy:vendor'
    if os.system(buildVendorCmd) != 0:
        print('build hippy:vendor失败')
        return False

    buildCmd = 'npm run hippy:build'
    if os.system(buildCmd) != 0:
        print('build hippy:build失败')
        return False



    os.system('mkdir dist/source_map')
    os.system('mkdir dist/source_map/android')
    os.system('mkdir dist/source_map/ios')
    os.system('mkdir dist/source_map/ios-9.0')
    os.system('mv dist/android/index.android.js.map dist/source_map/android')
    os.system('mv dist/ios/index.ios.js.map dist/source_map/ios')
    os.system('mv dist/ios-9.0/index.ios.js.map dist/source_map/ios-9.0')

    return True


def _fileMD5(file):
    f = open(file, 'rb')
    md5_obj = hashlib.md5()
    while True:
        d = f.read(8096)
        if not d:
            break
        md5_obj.update(d)
    hash_code = md5_obj.hexdigest()
    f.close()
    md5 = str(hash_code).lower()
    return md5


def _writeConfFile(file, packageInfo):
    with open(file, "w") as f:
        jsonConf = {}
        jsonConf['hostMinVersion'] = packageInfo.hostMinVersion
        jsonConf['hostMaxVersion'] = packageInfo.hostMaxVersion
        jsonConf['name'] = packageInfo.name
        jsonConf['version'] = packageInfo.version
        jsonConf['timestamp'] = timestamp
        json.dump(jsonConf, f)


def _writeZipInfo(infoFile, zipFile, packageInfo):
    pluginInfo = {}
    pluginInfo['name'] = packageInfo.name
    pluginInfo['version'] = packageInfo.version
    pluginInfo['md5'] = _fileMD5(zipFile)
    pluginInfo['hostMinVersion'] = packageInfo.hostMinVersion
    pluginInfo['hostMaxVersion'] = packageInfo.hostMaxVersion
    pluginInfo['url'] = "https://cis-static-common.oss-cn-shanghai.aliyuncs.com/apks/plugins/hippy_test/" + os.path.basename(zipFile)


    with open(infoFile, "w") as f:
        f.write(json.dumps(pluginInfo, sort_keys=True, indent=4, separators=(',', ': ')))

    with open(infoFile + ".for_curl", "w") as f:
        f.write(json.dumps(pluginInfo, sort_keys=True, indent=4, separators=(',', ': ')))

    pluginInfo['url'] = "https://cis-static-common.oss-cn-shanghai.aliyuncs.com/apks/plugins/hippy_release/" + os.path.basename(zipFile)
    with open(infoFile + ".release_for_curl", "w") as f:
        f.write(json.dumps(pluginInfo, sort_keys=True, indent=4, separators=(',', ': ')))


def _generateZipPlugins(packageInfo):
    _writeConfFile('dist/android/conf.json', packageInfo)
    _writeConfFile('dist/ios/conf.json', packageInfo)
    _writeConfFile('dist/ios-9.0/conf.json', packageInfo)
    cmd = 'cd dist/android && zip -q -r -o  ../android.zip .'
    if os.system(cmd) != 0:
        print('生成zip包失败')
        return False

    cmd = 'cd dist/ios && zip -q -r -o  ../ios.zip .'
    if os.system(cmd) != 0:
        print('生成zip包失败')
        return False


    cmd = 'cd dist/ios-9.0 && zip -q -r -o  ../ios-9.0.zip .'
    if os.system(cmd) != 0:
        print('生成zip包失败')
        return False

    zipNameSuffix = "{0}_{1}_{2}.zip".format(packageInfo.name, packageInfo.version, timestamp)

    newAndroidZipName = outputDir + '/android_{0}'.format(zipNameSuffix)
    mvAndroidZipCmd = 'mv ' + outputDir + '/android.zip  ' + newAndroidZipName
    print("mvAndroidZipCmd = " + mvAndroidZipCmd)
    os.system(mvAndroidZipCmd)

    _writeZipInfo("./dist/android_zip_info.txt", newAndroidZipName, packageInfo)

    newIOSZipName = outputDir + '/ios_{0}'.format(zipNameSuffix)
    mvIOSZipCmd = 'mv ' + outputDir + '/ios.zip  ' + newIOSZipName
    os.system(mvIOSZipCmd)

    _writeZipInfo("./dist/ios_zip_info.txt", newIOSZipName, packageInfo)


    newIOSZipName = outputDir + '/ios_9.0_{0}'.format(zipNameSuffix)
    mvIOSZipCmd = 'mv ' + outputDir + '/ios-9.0.zip  ' + newIOSZipName
    os.system(mvIOSZipCmd)

    _writeZipInfo("./dist/ios_9.0_zip_info.txt", newIOSZipName, packageInfo)

    return True


def _generateSourceMapZip(packageInfo):
    cmd = 'cd dist/source_map/android && zip -q -r -o  --password ' + sourceMapPassword + ' '+ packageInfo.version + '.zip index.android.js.map'
    os.system(cmd)

    cmd = 'cd dist/source_map/ios && zip -q -r -o  --password ' + sourceMapPassword + ' '+ packageInfo.version + '.zip index.ios.js.map'
    os.system(cmd)

    cmd = 'cd dist/source_map/ios-9.0 && zip -q -r -o  --password ' + sourceMapPassword + ' '+ packageInfo.version + '.zip index.ios.js.map'
    os.system(cmd)

    return True

def _generateEmbedsBundle(packageInfo):
    cmd = 'cd dist && mkdir for_embeded && mkdir for_embeded/ios && mkdir for_embeded/ios-9.0 && mkdir for_embeded/android'
    if os.system(cmd) != 0:
        return False

    cmd = 'cd dist && cp -rf ios  for_embeded/ios/' + packageInfo.name
    if os.system(cmd) != 0:
        return False

    cmd = 'cd dist && cp -rf ios-9.0  for_embeded/ios-9.0/' + packageInfo.name
    if os.system(cmd) != 0:
        return False

    cmd = 'cd dist && cp -rf android  for_embeded/android/' + packageInfo.name
    if os.system(cmd) != 0:
        return False



packageInfo = _loadPackageInfo()
packageInfo.version += '.' + buildNum

print('version = ' + packageInfo.version)


def main():
    importlib.reload(sys)
#     sys.setdefaultencoding('utf8')
    _prepareEnv()
    _build()
    _generateZipPlugins(packageInfo)
    _generateSourceMapZip(packageInfo)
    _generateEmbedsBundle(packageInfo)



    cmd = "osascript -e 'display dialog \"打包完成\" buttons \"Yes\" with icon note' && open dist"
    os.system(cmd)

if __name__ == '__main__':
    main()
