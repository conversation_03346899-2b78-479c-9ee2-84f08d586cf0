/*
 * <PERSON><PERSON> is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import React from 'react';

interface ListViewItemProps {
  [props: string]: any;
  type?: number | string | undefined;
  key?: string;
  itemViewType?: string;
  sticky?: boolean;
  style?: HippyTypes.Style;
  onLayout?: (evt: any) => void;
}

function ListViewItem(props: ListViewItemProps) {
  return (
    // @ts-ignore
    <li nativeName="ListViewItem" {...props} />
  );
}

export default ListViewItem;
export {
  ListViewItemProps,
};
