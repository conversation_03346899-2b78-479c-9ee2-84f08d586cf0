/*
 * <PERSON><PERSON> is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2017-2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

interface Event {
  type: string;
  bubbles: boolean;
  currentTarget: Element | null;
  target: Element | null;
}

class Event implements Event {
  /**
   * constructor
   * @param eventName - handler name, e.g. onClick
   * @param currentTarget - currentTarget is the node which the handler bind to
   * @param target - target is the node which triggered the real event
   */
  public constructor(eventName: string, currentTarget: Element | null, target: Element | null) {
    this.type = eventName;
    this.bubbles = true;
    // currentTarget is the node which the handler bind to
    this.currentTarget = currentTarget;
    // target is the node which triggered the real event
    this.target = target;
  }

  public stopPropagation() {
    this.bubbles = false;
  }

  public preventDefault() {
    // noop
  }
}

export default Event;
