# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [2.13.7](https://github.com/Tencent/Hippy/compare/2.13.6...2.13.7) (2022-04-27)


### Bug Fixes

* **debug-server:** update vulerable pacakage ([bd4653f](https://github.com/Tencent/Hippy/commit/bd4653f04aa9ea13f7cf96b2554a75c4cad2fd1e))





## [2.13.6](https://github.com/Tencent/Hippy/compare/2.13.5...2.13.6) (2022-04-18)

**Note:** Version bump only for package @hippy/debug-server





## [2.13.5](https://github.com/Tencent/Hippy/compare/2.13.4...2.13.5) (2022-04-01)

**Note:** Version bump only for package @hippy/debug-server





## [2.13.4](https://github.com/Tencent/Hippy/compare/2.13.3...2.13.4) (2022-03-29)


### Bug Fixes

* **vue:** update vulerable packages ([3e15f5c](https://github.com/Tencent/Hippy/commit/3e15f5c26ff9b7d875791a3cac4520ba22d99524))


### Features

* **vue-demo, react-demo:** remote debug, load remote bundle ([1b12953](https://github.com/Tencent/Hippy/commit/1b129538d92fe14119b1c29418ae52def03951b5))





## [2.13.3](https://github.com/Tencent/Hippy/compare/2.13.2...2.13.3) (2022-03-09)

**Note:** Version bump only for package @hippy/debug-server





## [2.13.2](https://github.com/Tencent/Hippy/compare/2.13.1...2.13.2) (2022-03-08)

**Note:** Version bump only for package @hippy/debug-server





## [2.13.1](https://github.com/Tencent/Hippy/compare/2.13.0...2.13.1) (2022-02-27)

**Note:** Version bump only for package @hippy/debug-server





# [2.13.0](https://github.com/Tencent/Hippy/compare/2.12.2...2.13.0) (2022-02-09)


### Bug Fixes

* **js:** update ts version and ts declaration issues ([2df21c5](https://github.com/Tencent/Hippy/commit/2df21c55aef2e4369175abfc8bd40d861481a847))





## [2.12.2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.12.1...2.12.2) (2022-01-26)

**Note:** Version bump only for package @hippy/debug-server





## [2.12.1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.12.0...2.12.1) (2022-01-18)

**Note:** Version bump only for package @hippy/debug-server





# [2.12.0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.11.6...2.12.0) (2022-01-04)


### Bug Fixes

* **debug-server:** fix webpack-dev-server compile callback ([900772d](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/commit/900772df46d90866e8e60fe57186ad09b318e917))
* **debug-server:** webpack dev callback ([70f20a6](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/commit/70f20a678390f51d8c86f357bf9214d5cf8a9384))


### Features

* **debug-server:** add cli API ([b872c34](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/commit/b872c343cd5c4bcb731328c43b9abfeee0e0b5f0))
* **vue, react, debug-server, vue-loader, vue-css-loader:** support HMR ([ff9f763](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/commit/ff9f763a4578d41a4ff657a577ced7f3675ba8e3))





## [2.11.6](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.11.5...2.11.6) (2021-12-23)

**Note:** Version bump only for package @hippy/debug-server





## [2.11.5](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.11.4...2.11.5) (2021-12-15)

**Note:** Version bump only for package @hippy/debug-server





## [2.11.4](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.11.3...2.11.4) (2021-12-10)

**Note:** Version bump only for package @hippy/debug-server





## [2.11.3](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.11.2...2.11.3) (2021-12-08)

**Note:** Version bump only for package @hippy/debug-server





## [2.11.2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.11.1...2.11.2) (2021-12-06)

**Note:** Version bump only for package @hippy/debug-server





## [2.11.1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.11.0...2.11.1) (2021-11-24)

**Note:** Version bump only for package @hippy/debug-server





# [2.11.0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.10.3...2.11.0) (2021-11-18)


### Features

* **vue:** add vue jsi ([368874d](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/commit/368874dda6d3ef6ba7c65d693b0722132edb6aee))





## [2.10.3](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.10.2...2.10.3) (2021-11-16)

**Note:** Version bump only for package @hippy/debug-server





## [2.10.2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.10.1...2.10.2) (2021-11-02)

**Note:** Version bump only for package @hippy/debug-server





## [2.10.1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.10.0...2.10.1) (2021-10-27)

**Note:** Version bump only for package @hippy/debug-server





# [2.10.0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.9.2...2.10.0) (2021-10-12)

**Note:** Version bump only for package @hippy/debug-server





## [2.9.2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.9.1...2.9.2) (2021-10-12)


### Bug Fixes

* **hippy-debug-server:** change child_process to cross-spawn ([870ab07](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/commit/870ab07d3592eb1865e40cfca617b6b338a4f711))





## [2.9.1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.9.0...2.9.1) (2021-09-24)


### Bug Fixes

* **css-loader:** collapsable transfer to boolean ([b5b2e12](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/commit/b5b2e12c8e52b62aac4bc2be0eaa443cc2f50a63))





# [2.9.0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.8.4...2.9.0) (2021-09-10)


### Features

* **debug-server:** change chrome dev protocal for Elements tab ([da50de2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/commit/da50de25ff00705604373c0e482879405dc283ee))
* **react,vue,android:** add live reload ([22582f4](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/commit/22582f4179d484ae4b7ff088511d42656307e1de))





## [2.8.4](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.8.3...2.8.4) (2021-08-13)

**Note:** Version bump only for package @hippy/debug-server





## [2.8.3](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.8.2...2.8.3) (2021-08-12)

**Note:** Version bump only for package @hippy/debug-server





## [2.8.2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.8.1...2.8.2) (2021-08-09)

**Note:** Version bump only for package @hippy/debug-server





## [2.8.1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.8.0...2.8.1) (2021-08-09)

**Note:** Version bump only for package @hippy/debug-server





# [2.8.0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.7.6...2.8.0) (2021-08-04)

**Note:** Version bump only for package @hippy/debug-server





## [2.7.6](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.7.5...2.7.6) (2021-07-22)

**Note:** Version bump only for package @hippy/debug-server





## [2.7.5](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.7.4...2.7.5) (2021-07-16)

**Note:** Version bump only for package @hippy/debug-server





## [2.7.4](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.7.3...2.7.4) (2021-07-08)

**Note:** Version bump only for package @hippy/debug-server





## [2.7.3](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.7.2...2.7.3) (2021-07-08)

**Note:** Version bump only for package @hippy/debug-server





## [2.7.2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.7.1...2.7.2) (2021-06-18)


### Features

* **hippy-debug-server:** update websocket version ([ab0fdfe](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/commit/ab0fdfee364faf669cd1a6a86d08ca5dd64ce824))
* **hippy-react-web:** update swiper ([2c93933](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/commit/2c939332d95f737cb57a49187324e66d57b89b48))





## [2.7.1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.7.0...2.7.1) (2021-06-10)

**Note:** Version bump only for package @hippy/debug-server





# [2.7.0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.6.4...2.7.0) (2021-06-07)

**Note:** Version bump only for package @hippy/debug-server





## [2.6.4](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.6.3...2.6.4) (2021-05-19)

**Note:** Version bump only for package @hippy/debug-server





## [2.6.3](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.6.2...2.6.3) (2021-05-17)

**Note:** Version bump only for package @hippy/debug-server





## [2.6.2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.6.1...2.6.2) (2021-04-26)

**Note:** Version bump only for package @hippy/debug-server





## [2.6.1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.6.0...2.6.1) (2021-04-22)

**Note:** Version bump only for package @hippy/debug-server





# [2.6.0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.5.5...2.6.0) (2021-04-19)


### Features

* **hippy-debug-server:** extend MIME ([4f1fe7d](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/commit/4f1fe7d6f4a1476b9cd3ccf95412d2e36e54a5a2))





## [2.5.5](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.5.4...2.5.5) (2021-03-26)

**Note:** Version bump only for package @hippy/debug-server





## [2.5.4](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.5.3...2.5.4) (2021-03-17)

**Note:** Version bump only for package @hippy/debug-server





## [2.1.2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.1.1...2.1.2) (2020-11-23)


### Bug Fixes

* **hippy-debug-server:** fixed the way the server read from file ([#405](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/issues/405)) ([fa16ee8](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/commit/fa16ee85a31cfbd05c91e33a8b3f4029a15936ac))
* **hippy-debug-server:** response 404 when file not found ([#410](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/issues/410)) ([8b161a8](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/commit/8b161a883d8506015cb7c0f0c41cc4fc2022a89e))


### Features

* **hippy-debug-server:** modify debug server to support multiple files ([#411](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/issues/411)) ([508ec9f](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/commit/508ec9f2f11070b1616cf155a330fb538d9c23ff))





## [2.1.1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.1.0...2.1.1) (2020-11-11)

**Note:** Version bump only for package @hippy/debug-server





# [2.1.0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.0.3...2.1.0) (2020-10-29)

**Note:** Version bump only for package @hippy/debug-server




## [2.0.3](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.0.2...2.0.3) (2020-04-23)

**Note:** Version bump only for package @hippy/debug-server





## [2.0.2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-debug-server/compare/2.0.1...2.0.2) (2020-03-18)

**Note:** Version bump only for package @hippy/debug-server

## 2.0.1 (2020-01-22)

### Change

* Rename to @hippy/debug-server.

## 2.0.0 (2019-12-18)

### Change

* Extract from hippy-cli and make it public.
