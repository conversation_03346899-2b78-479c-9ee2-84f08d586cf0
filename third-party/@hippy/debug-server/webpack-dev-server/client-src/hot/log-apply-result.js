/*
	MIT License http://www.opensource.org/licenses/mit-license.php
	Author <PERSON> @sokra
*/
module.exports = function (updatedModules, renewedModules) {
  const unacceptedModules = updatedModules.filter(moduleId => renewedModules && renewedModules.indexOf(moduleId) < 0);
  const log = require('./log');

  if (unacceptedModules.length > 0) {
    log(
      'warning',
      '[HMR] The following modules couldn\'t be hot updated: (They would need a full reload!)',
    );
    unacceptedModules.forEach((moduleId) => {
      log('warning', `[HMR]  - ${moduleId}`);
    });
  }

  if (!renewedModules || renewedModules.length === 0) {
    log('info', '[HMR] Nothing hot updated.');
  } else {
    log('info', '[HMR] Updated modules:');
    renewedModules.forEach((moduleId) => {
      if (typeof moduleId === 'string' && moduleId.indexOf('!') !== -1) {
        const parts = moduleId.split('!');
        log.groupCollapsed('info', `[HMR]  - ${parts.pop()}`);
        log('info', `[HMR]  - ${moduleId}`);
        log.groupEnd('info');
      } else {
        log('info', `[HMR]  - ${moduleId}`);
      }
    });
    const numberIds = renewedModules.every(moduleId => typeof moduleId === 'number');
    if (numberIds) {
      log('info', '[HMR] Consider using the NamedModulesPlugin for module names.');
    }
  }
};
