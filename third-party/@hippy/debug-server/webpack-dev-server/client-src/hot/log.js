let logLevel = 'info';

function dummy() {}

function shouldLog(level) {
  const shouldLog =    (logLevel === 'info' && level === 'info')
    || (['info', 'warning'].indexOf(logLevel) >= 0 && level === 'warning')
    || (['info', 'warning', 'error'].indexOf(logLevel) >= 0 && level === 'error');
  return shouldLog;
}

function logGroup(logFn) {
  return function (level, msg) {
    if (shouldLog(level)) {
      logFn(msg);
    }
  };
}

module.exports = function (level, msg) {
  if (shouldLog(level)) {
    if (level === 'info') {
      console.log(msg);
    } else if (level === 'warning') {
      console.warn(msg);
    } else if (level === 'error') {
      console.error(msg);
    }
  }
};

const group = console.group || dummy;
const groupCollapsed = console.groupCollapsed || dummy;
const groupEnd = console.groupEnd || dummy;

module.exports.group = logGroup(group);

module.exports.groupCollapsed = logGroup(groupCollapsed);

module.exports.groupEnd = logGroup(groupEnd);

module.exports.setLogLevel = function (level) {
  logLevel = level;
};

module.exports.formatError = function (err) {
  const { message } = err;
  const { stack } = err;
  if (!stack) {
    return message;
  } if (stack.indexOf(message) < 0) {
    return `${message}\n${stack}`;
  }
  return stack;
};
