# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [2.13.7](https://github.com/Tencent/Hippy/compare/2.13.6...2.13.7) (2022-04-27)


### Features

* **react,vue:** add position absolute for modal default style ([7bbed56](https://github.com/Tencent/Hippy/commit/7bbed5684e309cde7fe229f81ad746b1873467da))





## [2.13.6](https://github.com/Tencent/Hippy/compare/2.13.5...2.13.6) (2022-04-18)

**Note:** Version bump only for package @hippy/react





## [2.13.5](https://github.com/Tencent/Hippy/compare/2.13.4...2.13.5) (2022-04-01)

**Note:** Version bump only for package @hippy/react





## [2.13.4](https://github.com/Tencent/Hippy/compare/2.13.3...2.13.4) (2022-03-29)


### Bug Fixes

* **react,vue:** add TextInput color parser ([f9f0e2b](https://github.com/Tencent/Hippy/commit/f9f0e2b9637efc1fa82db293bbeeb3124df0eb61))





## [2.13.3](https://github.com/Tencent/Hippy/compare/2.13.2...2.13.3) (2022-03-09)

**Note:** Version bump only for package @hippy/react





## [2.13.2](https://github.com/Tencent/Hippy/compare/2.13.1...2.13.2) (2022-03-08)

**Note:** Version bump only for package @hippy/react





## [2.13.1](https://github.com/Tencent/Hippy/compare/2.13.0...2.13.1) (2022-02-27)


### Features

* **android:** view adds touch ripple feature ([#989](https://github.com/Tencent/Hippy/issues/989)) ([5518ffb](https://github.com/Tencent/Hippy/commit/5518ffb68a3348697d9a5586486c43a6ef376793))





# [2.13.0](https://github.com/Tencent/Hippy/compare/2.12.2...2.13.0) (2022-02-09)


### Bug Fixes

* **js:** update ts version and ts declaration issues ([2df21c5](https://github.com/Tencent/Hippy/commit/2df21c55aef2e4369175abfc8bd40d861481a847))


### Features

* **react,vue:** perf startBatch ([5bce8e9](https://github.com/Tencent/Hippy/commit/5bce8e9dedf1b5fd495216792f5f4b7f422671b5))





## [2.12.2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.12.1...2.12.2) (2022-01-26)


### Bug Fixes

* **hippy-react:** fixed network-info wrong stored listener ([e3926d9](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/e3926d9582e0a15eea15ac721e620aeac20fa9cc))





## [2.12.1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.12.0...2.12.1) (2022-01-18)

**Note:** Version bump only for package @hippy/react





# [2.12.0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.11.6...2.12.0) (2022-01-04)


### Features

* **hippy-react:** add bind_event boolean value ([bc32a94](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/bc32a948acce814e6f34c8a34f811744a5080ad0))
* **react:** add commitEffects hook to change batch update synchronously ([10da99c](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/10da99c6963e6c26b4d1a1b241508e33ee983211))
* **vue, react, debug-server, vue-loader, vue-css-loader:** support HMR ([ff9f763](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/ff9f763a4578d41a4ff657a577ced7f3675ba8e3))





## [2.11.6](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.11.5...2.11.6) (2021-12-23)

**Note:** Version bump only for package @hippy/react





## [2.11.5](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.11.4...2.11.5) (2021-12-15)


### Features

* **hippy-react:** change event capture handle ([fb73c63](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/fb73c639824be97625ae45fcb67a9671f1570ad1))
* **hippy-react:** compatible for react18 ([1b300ea](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/1b300ea9b35d11c9493709fa2d0cb46d331f0b71))
* **react,vue:** add caret color parser ([68ef4a5](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/68ef4a526dd72fda2ce184848d12e37e06c862e3))





## [2.11.4](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.11.3...2.11.4) (2021-12-10)

**Note:** Version bump only for package @hippy/react





## [2.11.3](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.11.2...2.11.3) (2021-12-08)


### Bug Fixes

* **hippy-react:** don't update node on event handler changed ([0be2968](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/0be2968656b6d103492f23c6ee2176baee97b2dc))





## [2.11.2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.11.1...2.11.2) (2021-12-06)


### Bug Fixes

* **hippy-react:** fixed cached fiberNode event handler problem ([1ed9a77](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/1ed9a771284096b9001ee181c805d7e84fa9b424))
* **react,vue:** fixed timeRemaining judge ([8dd993f](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/8dd993f16d83be509019b2d890ed6a9d3095462e))


### Features

* **hippy-react:** add system event instance ([bc0e6ac](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/bc0e6ac52638adeac84fcf181da74c9fd7890729))
* **react:** support event capture phase ([6eba75a](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/6eba75acb2599ddf89fb0dcb25f1f554fd7a7408))
* **react,vue:** add js node cache to improve event performance ([b72e42c](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/b72e42ca1419891019f937942d45c9a26f9c92e2))
* **react,vue:** perf node traversing process ([8102057](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/8102057db4743989fc996ec7455226f08b58c81b))





## [2.11.1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.11.0...2.11.1) (2021-11-24)

**Note:** Version bump only for package @hippy/react





# [2.11.0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.10.3...2.11.0) (2021-11-18)


### Bug Fixes

* **hippy-react:** fixed getting appear event name for anonymous function ([9c180a7](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/9c180a711e92784ef262311993862b2d7fac78af))


### Features

* **react,vue:** improve managing node performance ([8b35ba6](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/8b35ba6bdbe4bfaf2f3ac866ed05ebfed2df3d8e))
* **vue:** add vue jsi ([368874d](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/368874dda6d3ef6ba7c65d693b0722132edb6aee))





## [2.10.3](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.10.2...2.10.3) (2021-11-16)


### Bug Fixes

* **hippy-react:** fixed insertBefore moveChild condition ([b2d71eb](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/b2d71ebee6bec5813da930e4f907d445cc1282ee))





## [2.10.2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.10.1...2.10.2) (2021-11-02)

**Note:** Version bump only for package @hippy/react





## [2.10.1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.10.0...2.10.1) (2021-10-27)


### Bug Fixes

* **hippy-react:** change dev condition judge ([268a6e9](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/268a6e90eb8c926f17776a26e7f554221134f9cd))


### Features

* **hippy-react:** add global bubbles config ([56edf20](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/56edf204214b56cbf1098097c34cc84d20dff069))





# [2.10.0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.9.2...2.10.0) (2021-10-12)


### Features

* **core:** seperate console & ConsoleModule ([dd9d80a](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/dd9d80a4e5bdf9048bf61f31866c59266e1aeaf6))
* **hippy-react:** update react 17 ([df6bccb](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/df6bccba5d6e74fcb88e343e77ff6425e46442f4))
* **hippy-vue,hippy-react:** add text shadow ([a891690](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/a8916904cca88f09bdee55511dce670bf09571fa))





## [2.9.2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.9.1...2.9.2) (2021-10-12)

**Note:** Version bump only for package @hippy/react





## [2.9.1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.9.0...2.9.1) (2021-09-24)


### Features

* **hippy-react:** add unhideInstance api in hostconfig ([39da68a](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/39da68a1212fc1a979886d84599a42c1fb6849f3))
* **hippy-react:** compatible for react 17 ([a3c21d6](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/a3c21d6f79b40475aa8bae7840ffa10a2bd9aa3a))





# [2.9.0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.8.4...2.9.0) (2021-09-10)


### Bug Fixes

* **hippy-react:** add callUIFunction default options ([24c3d9c](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/24c3d9c7732c558251d99eb77725b03a3c951373))


### Features

* **animation:** animation cubic-bezier timingFunction ([#785](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/issues/785)) ([044e8b2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/044e8b2dec9d86a0c5e391d99794953b73c11bcf))
* **react,vue,ios,android:** add waterfall component for hippy ([#933](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/issues/933)) ([909cf79](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/909cf793af5fb70f54f001a2ddb1c43702ca2352)), closes [#1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/issues/1)





## [2.8.4](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.8.3...2.8.4) (2021-08-13)


### Features

* **hippy-react:** add nativeNode attributes info for debugging ([#923](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/issues/923)) ([6af97f7](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/6af97f72872024b17e65f2c81aff66788bbd7e93))





## [2.8.3](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.8.2...2.8.3) (2021-08-12)


### Bug Fixes

* **react:** fixed linearGradient update not work ([96f7451](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/96f74515a8cbb5b6f43682d6e15e9744bca1455a))





## [2.8.2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.8.1...2.8.2) (2021-08-09)

**Note:** Version bump only for package @hippy/react





## [2.8.1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.8.0...2.8.1) (2021-08-09)


### Bug Fixes

* **react,vue:** fix rgb format for linear-gradient parser ([1ae803c](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/1ae803c9aa7eb77601d7f995b73c451e0a6971bc))


### Features

* **react:** add local img ([3d901a8](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/3d901a8f7478a04bc7af16106e7e3bf5cb2a2752))





# [2.8.0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.7.6...2.8.0) (2021-08-04)


### Bug Fixes

* **commit:** fix husky commit issue ([397e717](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/397e717d73fcf96b5a8602e208e855fe4ff4af58))
* **react:** fixed custom style for scrollview not work ([6e19169](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/6e1916939be607bcf87dc8dd491a3f59a242c884))
* **react:** fixed focusable component child displayName ([ce69b77](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/ce69b7789fd88c2a92e70d7eaa9875880f53407f))


### Features

* **android,react,vue:** support RTL layout ([#893](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/issues/893)) ([149ec04](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/149ec0458e82676d16c0728a6feba486bfb2aace))
* **hippy-react,hippy-vue:** add linear-gradient ([02b5b82](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/02b5b8256995a5fa6a70fcfd0f67ed1c383569ae))





## [2.7.6](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.7.5...2.7.6) (2021-07-22)

**Note:** Version bump only for package @hippy/react





## [2.7.5](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.7.4...2.7.5) (2021-07-16)

**Note:** Version bump only for package @hippy/react





## [2.7.4](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.7.3...2.7.4) (2021-07-08)

**Note:** Version bump only for package @hippy/react





## [2.7.3](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.7.2...2.7.3) (2021-07-08)

**Note:** Version bump only for package @hippy/react





## [2.7.2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.7.1...2.7.2) (2021-06-18)

**Note:** Version bump only for package @hippy/react





## [2.7.1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.7.0...2.7.1) (2021-06-10)

**Note:** Version bump only for package @hippy/react





# [2.7.0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.6.4...2.7.0) (2021-06-07)


### Bug Fixes

* **hippy-vue,hippy-react:** add task polyfill for batch render ([7cdf026](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/7cdf026e345a187202a0f7ae91171c2804cb4562))


### Features

* **hippy-vue:** add some native modules ([e960c01](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/e960c01d98aa09db5ea443b14c16e427d7023fcd))





## [2.6.4](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.6.3...2.6.4) (2021-05-19)

**Note:** Version bump only for package @hippy/react





## [2.6.3](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.6.2...2.6.3) (2021-05-17)


### Bug Fixes

* **hippy-react-demo:** perf tabhost demo ([ea33076](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/ea330765b61b7b7a7b70e225d3affd8a779a6ce6))





## [2.6.2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.6.1...2.6.2) (2021-04-26)

**Note:** Version bump only for package @hippy/react





## [2.6.1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.6.0...2.6.1) (2021-04-22)


### Features

* **hippy-react,hippy-vue:** support to load local img ([4331fd5](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/4331fd5c3a0ae0ae6700973e3399d520cf3d1d00))





# [2.6.0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.5.5...2.6.0) (2021-04-19)


### Features

* **hippy-react:** perf pullHeader example ([3d01ae4](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/3d01ae40dc24fdd0e4941d18a543e438dc766ac3))
* **hippy-react,hippy-vue:** support color animation ([6c191a0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/6c191a08e203f45e8dd28e8e2e2f492bee20de8d))





## [2.5.5](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.5.4...2.5.5) (2021-03-26)

**Note:** Version bump only for package @hippy/react





## [2.5.4](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.5.3...2.5.4) (2021-03-17)

**Note:** Version bump only for package @hippy/react





## [2.5.3](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.5.2...2.5.3) (2021-03-10)

**Note:** Version bump only for package @hippy/react





## [2.5.2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.5.1...2.5.2) (2021-03-08)

**Note:** Version bump only for package @hippy/react





## [2.5.1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.5.0...2.5.1) (2021-03-02)

**Note:** Version bump only for package @hippy/react





# [2.5.0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.4.0...2.5.0) (2021-02-25)

**Note:** Version bump only for package @hippy/react





# [2.4.0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.3.4...2.4.0) (2021-02-25)


### Bug Fixes

* **hippy-vue:** fix vue webpack dll problem ([bbdc95c](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/bbdc95cd602d8d545a2958cd1b877905300b1c4d))





## [2.3.4](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.3.3...2.3.4) (2021-01-25)

**Note:** Version bump only for package @hippy/react





## [2.3.3](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.3.2...2.3.3) (2021-01-20)

**Note:** Version bump only for package @hippy/react





## [2.3.2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.3.1...2.3.2) (2021-01-18)


### Bug Fixes

* **hippy-react:** fix appendChild error in react16 by key diff ([233e40c](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/233e40c48511eee5745f189a6b9b58df1604a377))





## [2.3.1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.3.0...2.3.1) (2021-01-13)


### Features

* **hippy-react:** added Text displayName ([f591206](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/f5912066a56d3ebed8a5ec0cd96e8bedc46d792f))





# [2.3.0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.2.2...2.3.0) (2021-01-11)


### Bug Fixes

* **hippy-vue,hippy-react:** fixed listview appear & disappear event ([86c02fd](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/86c02fde0e47ff8862e92a4892c768da78a63674))


### Features

* **hippy-react:** forward Text ref ([5595dc1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/5595dc14d430c0289692358b47de3975f40945c2))
* **hippy-vue,hippy-react:** added willAppear & willDisappear event ([d0eb0f1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/d0eb0f1aac4a13886fed6682ac08e2c082bd8448))





## [2.2.2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.2.1...2.2.2) (2021-01-04)

**Note:** Version bump only for package @hippy/react





## [2.2.1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.2.0...2.2.1) (2020-12-28)

**Note:** Version bump only for package @hippy/react





# [2.2.0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.1.6...2.2.0) (2020-12-25)

**Note:** Version bump only for package @hippy/react





## [2.1.6](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.1.5...2.1.6) (2020-12-23)


### Bug Fixes

* **hippy-react:** fixed hairlineWidth NaN in ios ([82faee1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/82faee1f6693dce534ad97e18f4a42d9af1d2d9d))
* **hippy-vue,hippy-react:** compatible loadMore and endReached event ([#429](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/issues/429)) ([d992cbe](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/d992cbefbd9a0f76bee70bf604df7d377a08e97c))


### Features

* **hippy-react:** added hippy-react boxShadow attr and demo ([#458](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/issues/458)) ([6fd6a34](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/6fd6a342f7c0b7b6aa742eeee5c585e9e5a1d31b))





## [2.1.5](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.1.4...2.1.5) (2020-12-10)


### Bug Fixes

* **hippy-react:** continue finding nodeId if stringref's stateNode is a ([#442](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/issues/442)) ([3860d3f](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/3860d3ff3c36299b1f973dedbede83bcf94fa9ad))





## [2.1.4](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.1.3...2.1.4) (2020-12-03)


### Bug Fixes

* **hippy-react:** fix pullHeader and pullFooter ([#420](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/issues/420)) ([abfc574](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/abfc57401951acca4fb3fea72456784efcd4e926))


### Features

* **hippy-vue,hippy-react:** added setNativeProps on element ([#430](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/issues/430)) ([d1f7e21](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/d1f7e216b5fef46ace0cf50803ad2940b429a0d6))
* **hippy-vue,hippy-react:** perf setNativeProps ([5cd1291](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/5cd12910262ad3bb15d07c2dc974a829958a2b86))





## [2.1.2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.1.1...2.1.2) (2020-11-23)


### Features

* **hippy-vue, hippy-react:** changeTryConvertNumberCompatibility ([714faaf](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/714faaf11988659b450a3276342597b7ed095a17))





## [2.1.1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.1.0...2.1.1) (2020-11-11)


**Note:** Version bump only for package @hippy/react



# [2.1.0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.0.3...2.1.0) (2020-10-29)


### Bug Fixes

* **hippy-react:** fix hippy-react animationSet destroy problem ([#382](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/issues/382)) ([3c66ca6](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/3c66ca676d8f4fa3bc852492d24e533c617b252d))
* **hippy-react:** removed unncessary Object.values() ([8a68d44](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/8a68d44d7c7bd439b2be0badc542e9224685c76f))
* **hippy-react:** restore the ListView type props be number ([#367](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/issues/367)) ([231ec5a](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/231ec5a37b41eb778b649e079ec5d6bbe712fb8f)), closes [/github.com/Tencent/Hippy/commit/9de74e331b797c2137b1d0e3d08cd0dde0ee821a#diff-ccaf44058906717491bd079958ea5684a93acaa5d726e22cb34c0a6c82c79](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/issues/diff-ccaf44058906717491bd079958ea5684a93acaa5d726e22cb34c0a6c82c79)
* **hippy-vue:** fix hippy-vue transform multi-animation not working ([84bd58b](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/84bd58be840ea3f5ddd9d387e92b5a084387e9d1))


### Features

* **hippy-react:** add new method measureInAppWindow ([e25bb67](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/e25bb676942b89fbb57601d7c4ac2c9ce8ec175f))
* **hippy-react:** added PullHeader and PullFooter components support ([2fcdee9](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/2fcdee9b3ef290f40a25321c978a0c232299b06a))




## [2.0.3](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.0.2...2.0.3) (2020-04-23)


### Bug Fixes

* **hippy-react:** drop Object.entries() for lower iOS compatible ([d76b074](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/d76b074b7ed2536422be6052c56165be83b341c2))


### Features

* **hippy-react:** merge createNode operation ([#200](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/issues/200)) ([04d77a0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/04d77a074c5d43cbf4bfa0cc40c513167314addc))
* **hippy-react-web:** added default export for hippy-react web ([62cbdb0](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/62cbdb0cb7d65c989439e1d7ffb0a5fa1143eddd))





## [2.0.2](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/compare/2.0.1...2.0.2) (2020-03-18)


### Bug Fixes

* **hippy-react:** callUIFunction supports passing  as targetNode ([f7c8391](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/f7c83911622140db9f3f5ac9eba44aefe44cd4ce))
* **hippy-react:** change the NODE_ENV to 'development' ([2585bc5](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/2585bc5f3f28816c7ff1f4bdf210011508e7d2e8))
* **hippy-react:** text component text repeated rendering ([96e278d](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/96e278d33c3bd18cdec6c839cc5454c5c3479224))
* **hippy-react:** text nest ([da5ca3b](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/da5ca3b45cad28659bf0c6bacc90a1a64658d906))
* **hippy-react:** text-input style ([a9fa8d1](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/a9fa8d1e896c5d5ee62c8ef09d6b32de85124618))
* **hippy-react:** ui operation merge ([9b4f77d](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/9b4f77dfa54a0747efccec3beee6db170c3848cd))


### Features

* **vue-native-components:** added stateChanged event handler to swiper ([71760cc](https://github.com/Tencent/Hippy/tree/master/packages/hippy-react/commit/71760cccf15a819c644efaa1e084a96fcc4e856e))


## 2.0.1 (2020-01-22)

### Change

* Rename to @hippy/react.

## 2.0.0 (2019-12-18)

### Change

* Removed the npm namespace prefix '@tencent', restore to version 2.0.0, and make it public.

## 2.0.1 (2019-12-11)

### Added

* Added `silent` option for disable the framework log.

### Fixed

* Fixed nesting `<Text>` rendering issue.

## 2.0.0 (2019-11-21)

### Changed

* Nothing different with beta.11 but released.

## 2.0.0-beta.11 (2019-11-14)

### Changed

* Changed deleteNode with children to childNode only.

### Fixed

* Fixed array and object mixed style
* Fixed style determine issue

## 2.0.0-beta.10 (2019-11-07)

### Fixed

* Fixed parse color issue

## 2.0.0-beta.9 (2019-11-07)

### Fixed

* Fixed `<Text>` nesting issue.

## 2.0.0-beta.8 (2019-11-06)

### Fixed

* Fixed `<Text>` component meets null in children array issue.

## 2.0.0-beta.7 (2019-11-05)

### Changed

* Nothing changed, but tnpm crashes.

## 2.0.0-beta.6 (2019-10-25)

### Fixed

* Restored onPageScrollStateChanged event handler for ViewPager.

## 2.0.0-beta.5 (2019-10-25)

### Fixed

* Fixed Text component meets number children warning.

## 2.0.0-beta.3 (2019-10-22)

### Added

* Added type definition of Typescript

### Fixed

* Fixed compatible issues with QB

## 1.1.9 (2019-09-16)

### Added

* Passthrough the props to Modal directly.

## 1.1.8 (2019-08-23)

### Added

* Add IOS ViewPager onPageScrollStateChanged - thx @victoryin

## 2.0.0-beta.1 (2019-07-18)

### Changed

* Brand new hippy-core architecture.

## 1.1.7 (2019-07-16)

### Changed

* Restored ScrollView scrollTo method, and added scrollToWithDuration method

## 1.1.6 (2019-07-15)

### Added

* Added onRowLayout event handler to ListView.

### Fixed

* Fixed ScrollView scrollTo method compatible issue.

## 1.1.4 (2019-07-10)

### Added

* Added setValue method to TextInput

### Changed

* Changed ScrollView's scrollTo method animated option to duration.

### Fixed

* Fixed keyboardHeight to dp for Android.

## 1.1.3 (2019-07-01)

### Fixed

* Fixed animation meets undefined issue - thx @calvinma.

## 1.1.2 (2019-06-24)

### Fixed

* Fixed Text style meets null issue.

## 1.1.1 (2019-06-19)

### Changed

* Changed default Text color to black - #000.

### Fixed

* Fixed mobx compatibility - Thx @daringuo and @calvinma

## 1.1.0 (2019-06-13)

### Added

* Added default color style to Text
* Added Clipboard module

### Changed

* Dropped web adapter

### Fixed

* Fixed redux 5.x compatible -- Thanks @calvinma

## 1.1.0-beta.1 (2019-05-13)

### Added

* Added default text color to style.

### Changed

* Removed web adapter
