interface NodeMeta {
    skipAddToDom?: boolean;
    component: {
        name?: string;
        skipAddToDom?: boolean;
    };
}
declare class ViewNode {
    nodeId: number;
    meta: NodeMeta;
    index: number;
    childNodes: ViewNode[];
    parentNode: ViewNode | null;
    private mounted;
    constructor();
    toString(): string;
    get isMounted(): boolean;
    set isMounted(isMounted: boolean);
    insertBefore(childNode: ViewNode, referenceNode: ViewNode): void;
    moveChild(childNode: ViewNode, referenceNode: ViewNode): void | ViewNode;
    appendChild(childNode: ViewNode): void;
    removeChild(childNode: ViewNode): void;
    /**
     * Find a specific target with condition
     */
    findChild(condition: Function): ViewNode | null;
    /**
     * Traverse the children and execute callback
     * @param callback - callback function
     * @param newIndex - index to be updated
     */
    traverseChildren(callback: Function, newIndex?: number | undefined): void;
}
export default ViewNode;
