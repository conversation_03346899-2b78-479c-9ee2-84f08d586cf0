import ViewNode from './view-node';
interface Attributes {
    [key: string]: string | number | boolean | undefined;
}
interface NativePropsStyle {
    [key: string]: string | object | number | HippyTypes.Transform;
}
declare class ElementNode extends ViewNode {
    tagName: string;
    id: string;
    style: HippyTypes.Style;
    attributes: Attributes;
    constructor(tagName: string);
    get nativeName(): string | undefined;
    toString(): string;
    hasAttribute(key: string): boolean;
    getAttribute(key: string): string | number | boolean | undefined;
    setStyleAttribute(value: any): void;
    setAttribute(key: string, value: any): void;
    removeAttribute(key: string): void;
    setStyle(property: string, value: string | number | HippyTypes.Transform, isBatchUpdate?: boolean): void;
    /**
     * set native style props
     */
    setNativeProps(nativeProps: NativePropsStyle): void;
    setText(text: string | undefined): void | null;
}
export default ElementNode;
