import ViewNode from '../dom/view-node';
import Element from '../dom/element-node';
/**
 * endBatch - end batch update
 * @param {boolean} isHookUsed - whether used commitEffects hook
 */
declare function endBatch(isHookUsed?: boolean): void;
/**
 * Render Element to native
 */
declare function renderToNative(rootViewId: number, targetNode: Element): HippyTypes.NativeNode | null;
/**
 * Render Element with children to native
 * @param {number} rootViewId - rootView id
 * @param {ViewNode} node - current node
 * @param {number} [atIndex] - current node index
 * @param {Function} [callback] - function called on each traversing process
 * @returns {HippyTypes.NativeNode[]}
 */
declare function renderToNativeWithChildren(rootViewId: number, node: ViewNode, atIndex?: number, callback?: Function): HippyTypes.NativeNode[];
declare function insertChild(parentNode: ViewNode, childNode: ViewNode, atIndex?: number): void;
declare function removeChild(parentNode: ViewNode, childNode: ViewNode | null, index: number): void;
declare function updateChild(parentNode: Element): void;
declare function updateWithChildren(parentNode: ViewNode): void;
export { endBatch, renderToNative, renderToNativeWithChildren, insertChild, removeChild, updateChild, updateWithChildren, };
