import Document from '../dom/document-node';
import Element from '../dom/element-node';
import { Type, Props, UpdatePayload } from '../types';
declare function appendChild(parent: Element, child: Element): void;
declare function appendChildToContainer(container: any, child: Element): void;
declare function appendInitialChild(parent: Element, child: Element): void;
declare function commitMount(): void;
declare function commitTextUpdate(): void;
declare function commitUpdate(instance: any, updatePayload: any, type: string, oldProps: Props, newProps: Props, workInProgress: any): void;
export declare function commitMutationEffectsBegin(): void;
export declare function commitMutationEffectsComplete(): void;
declare function prepareUpdate(instance: Element, type: Type, oldProps: Props, newProps: Props): UpdatePayload;
declare function createContainerChildSet(): void;
declare function createInstance(type: Type, newProps: Props, rootContainerInstance: Document, currentHostContext: object, workInProgress: any): Element;
declare function createTextInstance(newText: string, rootContainerInstance: Document, hostContext: object, workInProgress: any): Element;
declare function finalizeInitialChildren(): boolean;
declare function finalizeContainerChildren(): void;
declare function getPublicInstance(instance: Element): Element;
declare function insertBefore(parent: Element, child: Element, beforeChild: Element): void;
declare function prepareForCommit(): null;
declare function replaceContainerChildren(): void;
declare function removeChild(parent: Element, child: Element): void;
declare function removeChildFromContainer(parent: Element, child: Element): void;
declare function resetAfterCommit(): void;
declare function resetTextContent(): void;
declare function getRootHostContext(): {};
declare function getChildHostContext(): {};
export declare function getCurrentEventPriority(): number;
declare function shouldDeprioritizeSubtree(): boolean;
declare function shouldSetTextContent(type: Type, nextProps: Props): boolean;
declare function hideInstance(instance: Element): void;
declare function hideTextInstance(): void;
declare function unhideInstance(instance: Element, props: Props): void;
declare function clearContainer(): void;
declare function unhideTextInstance(): void;
declare function getFundamentalComponentInstance(): void;
declare function mountFundamentalComponent(): void;
declare function shouldUpdateFundamentalComponent(): void;
declare function updateFundamentalComponent(): void;
declare function unmountFundamentalComponent(): void;
declare function getInstanceFromNode(): void;
declare function isOpaqueHydratingObject(): boolean;
declare function makeOpaqueHydratingObject(): string;
declare function makeClientId(): string;
declare function makeClientIdInDEV(): string;
declare function beforeActiveInstanceBlur(): void;
declare function afterActiveInstanceBlur(): void;
declare function preparePortalMount(): void;
declare function detachDeletedInstance(): void;
export declare const scheduleTimeout: typeof setTimeout;
export declare const cancelTimeout: typeof clearTimeout;
export declare const noTimeout = -1;
export { afterActiveInstanceBlur, appendChild, appendChildToContainer, appendInitialChild, beforeActiveInstanceBlur, commitMount, commitTextUpdate, commitUpdate, clearContainer, createContainerChildSet, createInstance, createTextInstance, detachDeletedInstance, finalizeContainerChildren, finalizeInitialChildren, getChildHostContext, getPublicInstance, getInstanceFromNode, getFundamentalComponentInstance, getRootHostContext, hideInstance, hideTextInstance, insertBefore, isOpaqueHydratingObject, makeClientId, makeClientIdInDEV, makeOpaqueHydratingObject, mountFundamentalComponent, prepareForCommit, preparePortalMount, prepareUpdate, replaceContainerChildren, removeChild, removeChildFromContainer, resetAfterCommit, resetTextContent, unmountFundamentalComponent, updateFundamentalComponent, unhideTextInstance, unhideInstance, shouldDeprioritizeSubtree, shouldUpdateFundamentalComponent, shouldSetTextContent, };
