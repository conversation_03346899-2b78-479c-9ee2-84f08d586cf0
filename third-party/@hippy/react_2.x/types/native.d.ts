import * as Clipboard from './modules/clipboard';
import * as <PERSON><PERSON> from './modules/cookie-module';
import * as ImageLoader from './modules/image-loader-module';
import * as NetworkInfo from './modules/network-info';
import * as UIManager from './modules/ui-manager-module';
import BackAndroid from './modules/back-android';
declare const flushSync: <A, R>(fn: (a: A) => R, a: A) => R;
declare const addEventListener: (eventName: string, listener: () => void) => void, removeEventListener: (eventName: string, listener?: (() => void) | undefined) => void, dispatchEvent: (eventName: string, ...args: any[]) => void, AsyncStorage: HippyTypes.AsyncStorage, Bridge: HippyTypes.Bridge, Device: {
    cancelVibrate: () => void;
    vibrate: (pattern: number, repeatTimes?: number | undefined) => void;
    platform: {
        Localization: {
            country: string;
            language: string;
            direction: number;
        } | undefined;
        OS: HippyTypes.Platform;
        APILevel?: number | undefined;
    };
    screen: HippyTypes.Sizes;
    window: HippyTypes.Sizes;
}, HippyRegister: {
    regist: (appName: string, entryFunc: (...args: any[]) => void) => void;
};
export { addEventListener, removeEventListener, dispatchEvent, AsyncStorage, BackAndroid, Bridge, Clipboard, Cookie, Device, HippyRegister, ImageLoader, NetworkInfo, UIManager, flushSync, };
