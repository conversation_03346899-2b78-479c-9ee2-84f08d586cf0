import EventEmitterRevoker from './emitter-revoker';
import HippyEventListener from './listener';
interface EventListeners {
    [eventName: string]: HippyEventListener;
}
declare class HippyEventEmitter {
    hippyEventListeners: EventListeners;
    constructor(sharedListeners?: EventListeners);
    sharedListeners(): EventListeners;
    addListener(event: string | undefined, callback: (data?: any) => void, context?: any): EventEmitterRevoker;
    removeAllListeners(event: string | undefined): void;
    emit(event: string | undefined, param: any): boolean;
    listenerSize(event: string | undefined): number;
}
export default HippyEventEmitter;
