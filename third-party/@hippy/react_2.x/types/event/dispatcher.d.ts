declare type EventParam = string[] | number[];
interface NativeEvent {
    id: number;
    name: string;
}
declare function receiveUIComponentEvent(nativeEvent: any[]): void;
declare function receiveNativeGesture(nativeEvent: NativeEvent): void;
declare function getHippyEventHub(eventName: any): any;
declare function registerNativeEventHub(eventName: any): any;
declare function unregisterNativeEventHub(eventName: any): void;
declare function receiveNativeEvent(nativeEvent: EventParam): void;
declare const EventDispatcher: {
    registerNativeEventHub: typeof registerNativeEventHub;
    getHippyEventHub: typeof getHippyEventHub;
    unregisterNativeEventHub: typeof unregisterNativeEventHub;
    receiveNativeEvent: typeof receiveNativeEvent;
    receiveNativeGesture: typeof receiveNativeGesture;
    receiveUIComponentEvent: typeof receiveUIComponentEvent;
};
export default EventDispatcher;
