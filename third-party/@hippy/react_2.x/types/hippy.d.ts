import { FunctionComponent, ComponentClass } from 'react';
import * as Native from './native';
interface HippyReactConfig {
    /**
     * Hippy app name, it's will register to `__GLOBAL__.appRegister` object,
     * waiting the native load instance event for start the app.
     */
    appName: string;
    /**
     * Entry component of Hippy app.
     */
    entryPage: string | FunctionComponent<any> | ComponentClass<any, any>;
    /**
     * Disable trace output
     */
    silent?: boolean;
    /**
     * enable global bubbles
     */
    bubbles?: boolean;
    /**
     * The callback after rendering.
     */
    callback?: () => void | undefined | null;
}
interface HippyReact {
    config: HippyReactConfig;
    rootContainer: any;
    regist: () => void;
}
declare class HippyReact implements HippyReact {
    static version: string;
    static get Native(): typeof Native;
    /**
     * Create new Hippy instance
     *
     * @param {Object} config - Hippy config.
     * @param {string} config.appName - The name of Hippy app.
     * @param {HippyReactConfig.entryPage} config.entryPage - The Entry page of Hippy app.
     * @param {function} config.callback - The callback after rendering.
     */
    constructor(config: HippyReactConfig);
    /**
     * Start hippy app execution.
     */
    start(): void;
    /**
     * Native rendering callback
     * @param {Object} superProps - The props passed by native start the app.
     */
    private render;
}
export default HippyReact;
