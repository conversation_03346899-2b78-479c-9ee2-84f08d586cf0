/**
 * Trace running information
 */
declare function trace(...context: any[]): void;
/**
 * Warning information output
 */
declare function warn(...context: any[]): void;
/**
 * Convert unicode string to normal string
 * @param {string} text - The unicode string input
 */
declare function unicodeToChar(text: string): string;
/**
 * ensure capture event name
 * @param {any} eventName
 */
declare function isCaptureEvent(eventName: any): boolean;
/**
 * Try to convert something to number
 *
 * @param {any} input - The input try to convert number
 */
declare function tryConvertNumber(input: any): any;
/**
 * Determine input is function.
 *
 * @param {any} input - The input will determine is function.
 * @returns {boolean}
 */
declare function isFunction(input: any): boolean;
/**
 * Determine a string is number.
 * @param {string} input - the input will determine is number.
 * @returns {boolean}
 */
declare function isNumber(input: string): boolean;
/**
 * Make trace be silent.
 * @param {boolean} silentArg - The silent flag for log
 */
declare function setSilent(silentArg: boolean): void;
/**
 * set bubbles config, default is false
 * @param bubbles
 */
declare function setBubbles(bubbles?: boolean): void;
/**
 * get bubbles config
 * @returns boolean
 */
declare function isGlobalBubble(): boolean;
/**
 * Convert Image url to specific type
 * @param url - image path
 */
declare function convertImgUrl(url: string): string;
/**
 * isHostComponent - judge current tag is hostComponent type
 * @param {number} tag
 */
declare function isHostComponent(tag: number): boolean;
export { trace, warn, unicodeToChar, tryConvertNumber, isCaptureEvent, isFunction, isNumber, setSilent, setBubbles, isGlobalBubble, convertImgUrl, isHostComponent, };
