import { Fiber } from 'react-reconciler';
import ElementNode from '../dom/element-node';
declare type RootContainer = any;
declare function setRootContainer(rootId: number, root: RootContainer): void;
declare function getRootContainer(): RootContainer;
declare function getRootViewId(): number;
declare function findNodeByCondition(condition: (node: Fiber) => boolean): null | Fiber;
declare function findNodeById(nodeId: number): Fiber | null;
/**
 * preCacheFiberNode - cache FiberNode
 * @param {Fiber} targetNode
 * @param {number} nodeId
 */
declare function preCacheFiberNode(targetNode: Fiber, nodeId: number): void;
/**
 * unCacheFiberNode - delete Fiber Node from cache
 * @param {number} nodeId
 */
declare function unCacheFiberNode(nodeId: number): void;
/**
 * getElementFromFiber - get ElementNode by Fiber
 * @param {number} fiberNode
 */
declare function getElementFromFiber(fiberNode: Fiber): any;
/**
 * getFiberNodeFromId - get FiberNode by nodeId
 * @param {number} nodeId
 */
declare function getFiberNodeFromId(nodeId: number): any;
/**
 * unCacheFiberNodeOnIdle - recursively delete FiberNode cache on idle
 * @param {ElementNode|number} node
 */
declare function unCacheFiberNodeOnIdle(node: ElementNode | number): void;
/**
 * recursivelyUnCacheFiberNode - delete ViewNode cache recursively
 * @param {ElementNode|number} node
 */
declare function recursivelyUnCacheFiberNode(node: ElementNode | number): void;
/**
 * requestIdleCallback polyfill
 * @param {Function} cb
 * @param {{timeout: number}} [options]
 */
declare function requestIdleCallback(cb: IdleRequestCallback, options?: {
    timeout: number;
}): ReturnType<typeof setTimeout> | number;
/**
 * cancelIdleCallback polyfill
 * @param {ReturnType<typeof requestIdleCallback>} id
 */
declare function cancelIdleCallback(id: ReturnType<typeof requestIdleCallback>): void;
interface EventNamesMap {
    [propName: string]: string[];
}
declare const NATIVE_EVENT = 1;
declare const eventNamesMap: EventNamesMap;
export { NATIVE_EVENT, eventNamesMap, requestIdleCallback, cancelIdleCallback, setRootContainer, getRootContainer, getRootViewId, findNodeByCondition, findNodeById, preCacheFiberNode, unCacheFiberNode, getFiberNodeFromId, getElementFromFiber, unCacheFiberNodeOnIdle, recursivelyUnCacheFiberNode, };
