import './global';
import { HippyEventEmitter, HippyEventListener } from './event';
import { colorParse } from './color';
import HippyReact from './hippy';
import AppRegistry from './adapters/app-registry';
import Animated from './adapters/animated';
import Easing from './adapters/easing';
import Animation from './modules/animation';
import AnimationSet from './modules/animation-set';
import View from './components/view';
import Text from './components/text';
import Image from './components/image';
import ListView from './components/list-view';
import ListViewItem from './components/list-view-item';
import RefreshWrapper from './components/refresh-wrapper';
import Navigator from './components/navigator';
import ViewPager from './components/view-pager';
import TextInput from './components/text-input';
import ScrollView from './components/scroll-view';
import Modal from './components/modal';
import Focusable from './components/focusable';
import WebView from './components/web-view';
import WebSocket from './modules/websocket';
import PullHeader from './components/pull-header';
import PullFooter from './components/pull-footer';
import WaterfallView from './components/waterfall-view';
import * as Native from './native';
import * as StyleSheet from './modules/stylesheet';
import { ListViewProps } from './components/list-view';
import { LayoutableProps, ClickableProps, TouchableProps, HTMLAttributesExtension } from "./types";
import Style = HippyTypes.Style;
import TouchEvent = HippyTypes.TouchEvent;
import LayoutEvent = HippyTypes.LayoutEvent;

declare const AsyncStorage: HippyTypes.AsyncStorage, BackAndroid: {
    exitApp(): void;
    addListener(handler: () => void): import("./modules/back-android").BackAndroidRevoker;
    removeListener(handler: () => void): void;
    initEventListener(): void;
}, Clipboard: typeof Native.Clipboard, NetworkModule: typeof Native.Cookie, HippyRegister: {
    regist: (appName: string, entryFunc: (...args: any[]) => void) => void;
}, ImageLoaderModule: typeof Native.ImageLoader, NetInfo: typeof Native.NetworkInfo, UIManagerModule: typeof Native.UIManager, flushSync: <A, R>(fn: (a: A) => R, a: A) => R;
declare const callNative: (moduleName: string, methodName: string, ...args: any[]) => void, callNativeWithPromise: <T>(moduleName: string, methodName: string, ...args: any[]) => Promise<T>, callNativeWithCallbackId: (moduleName: string, methodName: string, ...args: any[]) => number, removeNativeCallback: (callbackId: number) => void;
declare const TimerModule: null;
declare const ConsoleModule: HippyTypes.ConsoleModule;
declare const Platform: {
    Localization: {
        country: string;
        language: string;
        direction: number;
    } | undefined;
    OS: HippyTypes.Platform;
    APILevel?: number | undefined;
};
declare const Hippy: typeof HippyReact;
declare const RNfqb: typeof HippyReact;
declare const ImageBackground: typeof Image;
declare const RNfqbRegister: {
    regist: (appName: string, entryFunc: (...args: any[]) => void) => void;
};
declare const RNfqbEventEmitter: typeof HippyEventEmitter;
declare const RNfqbEventListener: typeof HippyEventListener;
declare const Dimensions: {
    get(name: 'window' | 'screen'): HippyTypes.Sizes;
};
declare const PixelRatio: {
    get(): number;
};
export { flushSync, colorParse, callNative, callNativeWithPromise, callNativeWithCallbackId, removeNativeCallback, RNfqbRegister, RNfqbEventEmitter, RNfqbEventListener, HippyRegister, HippyEventEmitter, HippyEventListener, AsyncStorage, AppRegistry, Animated, Easing, UIManagerModule, StyleSheet, Dimensions, PixelRatio, TimerModule, NetworkModule, NetInfo, Clipboard, ConsoleModule, ImageLoaderModule, Platform, BackAndroid, Animation, AnimationSet, Hippy, RNfqb, View, Text, Image, ListView, ListViewItem, RefreshWrapper, Navigator, ViewPager, TextInput, ScrollView, Modal, Focusable, WebView, ImageBackground, WebSocket, PullHeader, PullFooter, WaterfallView, ListViewProps, LayoutableProps, ClickableProps, TouchableProps, HippyTypes, Style, TouchEvent, LayoutEvent, HTMLAttributesExtension, };
export default HippyReact;
