/**
 * The WebSocket API is an advanced technology that makes it possible to open a two-way
 * interactive communication session between the user's browser and a server. With this API,
 * you can send messages to a server and receive event-driven responses without having to
 * poll the server for a reply.
 */
declare class WebSocket implements HippyTypes.WebSocket {
    protocol: string;
    url: string;
    readyState: number;
    webSocketCallbackId: number;
    webSocketId: number | undefined;
    readonly webSocketCallbacks: {
        onOpen?: (...args: any[]) => void;
        onClose?: (...args: any[]) => void;
        onError?: (...args: any[]) => void;
        onMessage?: (...args: any[]) => void;
    };
    /**
     * Returns a newly created WebSocket object.
     *
     * @param {string} url - The URL to which to connect; this should be the URL to which the
     *                       WebSocket server will respond.
     * @param {string | string[]} [protocols] - Either a single protocol string or an array
     *                                          of protocol strings. These strings are used to
     *                                          indicate sub-protocols, so that a single server
     *                                          can implement multiple WebSocket sub-protocols
     *                                          (for example, you might want one server to be able
     *                                          to handle different types of interactions depending
     *                                          on the specified protocol).
     *                                          If you don't specify a protocol string, an empty
     *                                          string is assumed.
     * @param {Object} extrasHeaders - Http headers will append to connection.
     */
    constructor(url: any, protocols: string[] | string, extrasHeaders: {
        [key: string]: string;
    });
    /**
     * Closes the WebSocket connection or connection attempt, if any.
     * If the connection is already CLOSED, this method does nothing.
     *
     * @param {number} [code] - A numeric value indicating the status code explaining
     *                          why the connection is being closed. If this parameter
     *                          is not specified, a default value of 1005 is assumed.
     *                          See the list of status codes of CloseEvent for permitted values.
     * @param {string} [reason] - A human-readable string explaining why the connection
     *                            is closing. This string must be no longer than 123 bytes
     *                            of UTF-8 text (not characters).
     */
    close(code: number, reason: string): void;
    /**
     * Enqueues the specified data to be transmitted to the server over the WebSocket connection.
     *
     * @param {string} data - The data to send to the server. Hippy supports string type only.
     */
    send(data: string | undefined): void;
    /**
     * Set an EventHandler that is called when the WebSocket connection's readyState changes to OPEN;
     */
    set onopen(callback: () => void);
    /**
     * Set an EventHandler that is called when the WebSocket connection's readyState
     * changes to CLOSED.
     */
    set onclose(callback: () => void);
    /**
     * Set an EventHandler that is called when a message is received from the server.
     */
    set onerror(callback: () => void);
    /**
     * Set an event handler property is a function which gets called when an error
     * occurs on the WebSocket.
     */
    set onmessage(callback: (data: any) => void);
    /**
     * WebSocket events handler from Native.
     *
     * @param {Object} param - Native response.
     */
    private onWebSocketEvent;
}
export default WebSocket;
