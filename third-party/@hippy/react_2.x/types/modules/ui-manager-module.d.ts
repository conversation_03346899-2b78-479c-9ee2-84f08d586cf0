import { Fiber } from 'react-reconciler';
import { findNodeById } from '../utils/node';
import Element from '../dom/element-node';
declare const createNode: (rootViewId: number, queue: HippyTypes.NativeNode[]) => void, updateNode: (rootViewId: number, queue: HippyTypes.NativeNode[]) => void, deleteNode: (rootViewId: number, queue: HippyTypes.NativeNode[]) => void, flushBatch: (rootViewId: number, queue: HippyTypes.NativeNode[]) => void, startBatch: () => void, endBatch: () => void, sendRenderError: (err: Error) => void;
declare const getNodeById: typeof findNodeById;
/**
 * Get the nodeId from FiberNode ref.
 *
 * @param {Fiber} ref - ref instance.
 */
declare function getElementFromFiberRef(ref: Fiber | Element): any;
/**
 * Get the nodeId number by ref
 * Most use in the module access components.
 *
 * @param {string | Fiber | Fiber} ref - ref instance, reference to the class is recommend
 */
declare function getNodeIdByRef(ref: string | Fiber | Element): number;
/**
 * Component access UI functions
 *
 * @param {ViewNode} ref - Element ref that have nodeId.
 * @param {string} funcName - function name.
 * @param {Array} options - function options.
 */
declare function callUIFunction(ref: Element | Fiber, funcName: string, ...options: any[]): void;
/**
 * Get the ref position and size in the visible window.
 * > For the position and size in the layout, use onLayout event.
 * P.S. iOS can only obtains the layout of rootView container,
 * so measureInAppWindow method is recommended
 *
 * @deprecated
 * @param {Fiber | Element} ref - ref that need to measure.
 * @param {Function} callback
 */
declare function measureInWindow(ref: Fiber, callback?: (layout: HippyTypes.LayoutEvent | string) => void): Promise<unknown>;
/**
 * Get the ref position and size in the App visible window.
 * > For the position and size in the layout, use onLayout event.
 *
 * @param {Fiber | Element} ref - ref that need to measure.
 * @param {Function} callback
 */
declare function measureInAppWindow(ref: Fiber, callback?: (layout: HippyTypes.LayoutEvent | string) => void): Promise<unknown>;
export { createNode, updateNode, deleteNode, flushBatch, startBatch, endBatch, sendRenderError, getNodeById, getNodeIdByRef, getElementFromFiberRef, callUIFunction, measureInWindow, measureInAppWindow, };
