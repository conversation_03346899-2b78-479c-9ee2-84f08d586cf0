import Animation from '../modules/animation';
import View from '../components/view';
import * as TextComp from '../components/text';
import Image from '../components/image';
interface TimingConfig {
    toValue: number;
    duration: number;
    easing?: 'linear' | 'ease' | 'in' | 'ease-in' | 'out' | 'ease-out' | 'inOut' | 'ease-in-out' | 'cubic-bezier';
}
declare class Animated {
    static View: typeof View;
    static Text: typeof TextComp;
    static Image: typeof Image;
    static Value(val: any): any;
    static timing(value: number, config: TimingConfig): Animation;
    Value: typeof Animated.Value;
}
export default Animated;
