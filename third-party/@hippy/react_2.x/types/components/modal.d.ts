import React from 'react';
declare type ModalOrientation = 'portrait' | 'portrait-upside-down' | 'landscape' | 'landscape-left' | 'landscape-right';
interface ModalProps {
    /**
     * Show or hide
     *
     * Default false
     */
    visible: boolean;
    /**
     * Primary key
     * > iOS only
     */
    primaryKey: string;
    /**
     * Background is transparent or not
     * Default: true
     */
    transparent?: boolean;
    /**
     * Enable animation to popup or hide
     *
     * Default: true
     *
     * > Deprecated, use animationType to instance of
     */
    animated?: boolean;
    /**
     * Be text color in statusbar dark theme.
     * Default: false
     */
    darkStatusBarText?: boolean;
    /**
     * Make the Modal content be under of statusbar.
     * > Android Only
     *
     * Default: false
     */
    immersionStatusBar?: boolean;
    /**
     * Hide statusbar texts when Mo<PERSON> is showing
     *
     * Default: false
     */
    autoHideStatusBar?: boolean;
    /**
     * The animation effect when toggle
     *
     * Default: 'slide'
     */
    animationType?: 'none' | 'slide' | 'fade' | 'slide_fade';
    /**
     * Modal supports orientations
     */
    supportedOrientations?: ModalOrientation[];
    style?: HippyTypes.Style;
    /**
     * Trigger when hardware button pressed
     * > Android Only
     */
    onRequestClose?: () => void;
    /**
     * Trigger when the Modal will show
     */
    onShow?: () => void;
    /**
     * Trigger when the Modal will hide
     */
    onDismiss?: () => void;
    /**
     * Trigger when the device orientation changed.
     */
    onOrientationChange?: () => void;
}
/**
 * The Modal component is a basic way to present content above an enclosing view.
 * @noInheritDoc
 */
declare class Modal extends React.Component<ModalProps, {}> {
    private static defaultProps;
    private eventSubscription;
    /**
     * @ignore
     */
    constructor(props: ModalProps);
    /**
     * @ignore
     */
    componentDidMount(): void;
    /**
     * @ignore
     */
    componentWillUnmount(): void;
    /**
     * @ignore
     */
    render(): JSX.Element | null;
}
export default Modal;
