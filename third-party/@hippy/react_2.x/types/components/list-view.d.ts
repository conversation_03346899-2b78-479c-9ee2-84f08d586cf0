import React from 'react';
declare type DataItem = any;
export interface ListViewProps {
    /**
     * Overrides the text that's read by the screen reader when the user interacts with the element.
     * By default, the label is constructed by traversing all the children and accumulating
     * all the Text nodes separated by space.
     */
    accessibilityLabel?: string;
    /**
     * Render specific number of rows of data.
     * Set equal to dataSource.length in most case.
     */
    numberOfRows: number;
    /**
     * Data source
     */
    dataSource: DataItem[];
    /**
     * Specfic how many data will render at first screen.
     */
    initialListSize?: number;
    /**
     * Scroll to offset after `ListView` with data rendered.
     */
    initialContentOffset?: number;
    /**
     * This controls how often the scroll event will be fired while scrolling
     * (as a time interval in ms). A lower number yields better accuracy for code
     * that is tracking the scroll position, but can lead to scroll performance
     * problems due to the volume of information being send over the bridge.
     * You will not notice a difference between values set between 1-16 as the JS run loop
     * is synced to the screen refresh rate. If you do not need precise scroll position tracking,
     * set this value higher to limit the information being sent across the bridge.
     *
     * The default value is zero, which results in the scroll event being sent only once
     * each time the view is scrolled.
     */
    scrollEventThrottle: number;
    /**
     * When `true`, shows a horizon scroll indicator.
     * The default value is `true`.
     */
    showScrollIndicator?: boolean;
    /**
     * Passing the data and returns the row component.
     *
     * @param {Object} data - Data for row rendering
     * @param {null} unknown - seems null.
     * @param {number} index - Index Of data.
     * @returns {React.Component}
     */
    renderRow?: (data: DataItem, unknown?: any, // FIXME: What's the argument meaning?
    index?: number) => React.ReactElement;
    renderPullHeader?: () => React.ReactElement;
    renderPullFooter?: () => React.ReactElement;
    /**
     * Each row have different type, it will be using at render recycle.
     *
     * @param {number} index - Index Of data.
     * @returns {string}
     */
    getRowType?: (index: number) => number;
    /**
     * Returns the style for specific index of row.
     *
     * @param {number} index - Index Of data.
     * @returns {Object}
     */
    getRowStyle?: (index: number) => HippyTypes.Style;
    /**
     * Specfic the key of row, for better data diff
     * More info: https://reactjs.org/docs/lists-and-keys.html
     *
     * @param {number} index - Index Of data.
     * @returns {string}
     */
    getRowKey?: (index: number) => string;
    /**
     * Is the row should sticky after scrolling up.
     * @param {number} index - Index Of data.
     * @returns {boolean}
     */
    rowShouldSticky?: (index: number) => boolean;
    style?: HippyTypes.Style;
    /**
     *  Called when the `ListView` is scrolling to bottom.
     */
    onEndReached?: () => void;
    /**
     * the same with onEndReached
     */
    onLoadMore?: () => void;
    /**
     *  Called when the row first layouting or layout changed.
     *
     * @param {Object} evt - Layout event data
     * @param {number} evt.x - The position X of component
     * @param {number} evt.y - The position Y of component
     * @param {number} evt.width - The width of component
     * @param {number} evt.height - The height of component
     * @param {number} index - Index of data.
     */
    onRowLayout?: (evt: HippyTypes.LayoutEvent, index: number) => void;
    /**
     * Called when the momentum scroll starts (scroll which occurs as the ListView starts gliding).
     */
    onMomentumScrollBegin?: () => void;
    /**
     * Called when the momentum scroll ends (scroll which occurs as the ListView glides to a stop).
     */
    onMomentumScrollEnd?: () => void;
    /**
     * Fires at most once per frame during scrolling.
     * The frequency of the events can be controlled using the `scrollEventThrottle` prop.
     *
     * @param {Object} evt - Scroll event data.
     * @param {number} evt.contentOffset.x - Offset X of scrolling.
     * @param {number} evt.contentOffset.y - Offset Y of scrolling.
     */
    onScroll?: (evt: {
        contentOffset: {
            x: number;
            y: number;
        };
    }) => void;
    /**
     * Called when the user begins to drag the scroll view.
     */
    onScrollBeginDrag?: () => void;
    /**
     * Called when the user stops dragging the scroll view and it either stops or begins to glide.
     */
    onScrollEndDrag?: () => void;
    /**
     * android expose ability flag
     */
    exposureEventEnabled?: boolean;
    /**
     * Called when user pulls the ListView down
     */
    onHeaderPulling?: () => void;
    /**
     * Called when user release the pulling ListView
     */
    onHeaderReleased?: () => void;
    /**
     * Called when user swipe up ListView to get more data on reaching the footer
     */
    onFooterPulling?: () => void;
    /**
     * Called when user release the getting-more-data ListView
     */
    onFooterReleased?: () => void;
    /**
     * Called when a whole new list item appears
     */
    onAppear?: (index: number) => void;
    /**
     * Called when a whole list item disappears
     */
    onDisappear?: (index: number) => void;
    /**
     * Called when a new list item will appear(1 px)
     */
    onWillAppear?: (index: number) => void;
    /**
     * Called when a new list item will disappear(1 px)
     */
    onWillDisappear?: (index: number) => void;
}
interface ListViewState {
    initialListReady: boolean;
}
/**
 * Recyclable list for better performance, and lower memory usage.
 * @noInheritDoc
 */
declare class ListView extends React.Component<ListViewProps, ListViewState> {
    private static defaultProps;
    /**
     * change key
     */
    private static convertName;
    private instance;
    private pullHeader;
    private pullFooter;
    constructor(props: ListViewProps);
    componentDidMount(): void;
    /**
     * Scrolls to a given index of item, either immediately, with a smooth animation.
     *
     * @param {number} xIndex - Scroll to horizon index X.
     * @param {number} yIndex - Scroll To vertical index Y.
     * @param {boolean} animated - With smooth animation.By default is true.
     */
    scrollToIndex(xIndex: number | undefined, yIndex: number | undefined, animated: boolean | undefined): void;
    /**
     * Scrolls to a given x, y offset, either immediately, with a smooth animation.
     *
     * @param {number} xOffset - Scroll to horizon offset X.
     * @param {number} yOffset - Scroll To vertical offset Y.
     * @param {boolean} animated - With smooth animation.By default is true.
     */
    scrollToContentOffset(xOffset: number | undefined, yOffset: number | undefined, animated: boolean | undefined): void;
    /**
     * Expand the PullHeaderView and display the content
     */
    expandPullHeader(): void;
    /**
     * Collapse the PullHeaderView and hide the content
     */
    collapsePullHeader(options: object): void;
    /**
     * Expand the PullFooterView and display the content
     */
    expandPullFooter(): void;
    /**
     * Collapse the PullView and hide the content
     */
    collapsePullFooter(): void;
    render(): JSX.Element;
    private handleInitialListReady;
    private getPullHeader;
    private getPullFooter;
    private handleRowProps;
}
export default ListView;
