import React from 'react';
declare type DataItem = any;
interface WaterfallViewProps {
    numberOfColumns: number;
    numberOfItems: number;
    contentInset?: {
        top?: number;
        left?: number;
        bottom?: number;
        right?: number;
    };
    columnSpacing: number;
    interItemSpacing: number;
    preloadItemNumber?: number;
    style?: HippyTypes.Style;
    containPullHeader?: boolean;
    containPullFooter?: boolean;
    initialContentOffset?: number;
    containBannerView?: boolean;
    renderBanner?: () => React.ReactElement;
    /**
     * Passing the data and returns the row component.
     *
     * @param {Object} data - Data for row rendering
     * @returns {(React.Component | undefined)}
     */
    renderItem?: (data: DataItem) => React.ReactElement;
    renderPullHeader?: () => React.ReactElement;
    renderPullFooter?: () => React.ReactElement;
    /**
     * Each row have different type, it will be using at render recycle.
     *
     * @param {number} index - Index Of data.
     * @returns {string}
     */
    getItemType?: (index: number) => number;
    /**
     * Returns the style for specific index of row.
     *
     * @param {number} index - Index Of data.
     * @returns {Object}
     */
    getItemStyle?: (index: number) => HippyTypes.Style;
    /**
     * Specific the key of row, for better data diff
     * More info: https://reactjs.org/docs/lists-and-keys.html
     *
     * @param {number} index - Index Of data.
     * @returns {string}
     */
    getItemKey?: (index: number) => string;
    onEndReached?: () => void;
    /**
     *  Called when the row first layout or layout changed.
     *
     * @param {Object} evt - Layout event data
     * @param {number} evt.x - The position X of component
     * @param {number} evt.y - The position Y of component
     * @param {number} evt.width - The width of component
     * @param {number} evt.height - The height of component
     * @param {number} index - Index of data.
     */
    onItemLayout?: (evt: HippyTypes.LayoutEvent, index: number) => void;
    /**
     * Called when user scrolls WaterfallView
     *
     * @param {Object} evt - onScroll event
     * @param {number} evt.startEdgePos - Scrolled offset of List top edge
     * @param {number} evt.endEdgePos - Scrolled offset of List end edge
     * @param {number} evt.firstVisibleRowIndex - Index of the first list item at current visible screen
     * @param {number} evt.lastVisibleRowIndex - Index of the last list item at current visible screen
     * @param {Object[]} evt.visibleRowFrames - Frame info of current screen visible items
     * @param {number} evt.visibleRowFrames[].x - Current item's horizontal offset relative to ListView
     * @param {number} evt.visibleRowFrames[].y - Current item's vertical offset relative to ListView
     * @param {number} evt.visibleRowFrames[].width - Current item's width
     * @param {number} evt.visibleRowFrames[].height - Current item's height
     */
    onScroll?: (evt: {
        startEdgePos: number;
        endEdgePos: number;
        firstVisibleRowIndex: number;
        lastVisibleRowIndex: number;
        visibleRowFrames: Object;
    }) => void;
    onHeaderPulling?: () => void;
    onHeaderReleased?: () => void;
    onFooterPulling?: () => void;
    onFooterReleased?: () => void;
    onExposureReport?: () => void;
    onInitialListReady?: () => void;
}
/**
 * Recyclable list for better performance, and lower memory usage.
 * @noInheritDoc
 */
declare class WaterfallView extends React.Component<WaterfallViewProps> {
    private instance;
    private pullHeader;
    private pullFooter;
    /**
     * @constructor
     */
    constructor(props: WaterfallViewProps);
    /**
     * Scrolls to a given index of item, either immediately, with a smooth animation.
     *
     * @param {Object} scrollToIndex params
     * @param {number} scrollToIndex.index - Scroll to specific index.
     * @param {boolean} scrollToIndex.animated - With smooth animation. By default is true.
     */
    scrollToIndex({ index, animated }: {
        index?: number | undefined;
        animated?: boolean | undefined;
    }): void;
    /**
     * Scrolls to a given x, y offset, either immediately, with a smooth animation.
     *
     * @param {Object} scrollToContentOffset params
     * @param {number} scrollToContentOffset.xOffset - Scroll to horizon offset X.
     * @param {number} scrollToContentOffset.yOffset - Scroll To vertical offset Y.
     * @param {boolean} scrollToContentOffset.animated - With smooth animation. By default is true.
     */
    scrollToContentOffset({ xOffset, yOffset, animated, }: {
        xOffset?: number | undefined;
        yOffset?: number | undefined;
        animated?: boolean | undefined;
    }): void;
    expandPullHeader(): void;
    collapsePullHeader(options: object): void;
    expandPullFooter(): void;
    collapsePullFooter(): void;
    render(): JSX.Element;
    /**
     * @ignore
     */
    componentDidMount(): void;
    private handleRowProps;
    /**
     *
     * @param renderPullHeader - PullHeader View
     * @param onHeaderPulling - Called when header is pulled
     * @param onHeaderReleased - Called when header is released
     * @private
     */
    private getPullHeader;
    /**
     *
     * @param renderPullFooter - PullHeader View
     * @param onFooterPulling - Called when footer is pulled
     * @param onFooterReleased - Called when footer is released
     * @private
     */
    private getPullFooter;
    private handleInitialListReady;
}
export default WaterfallView;
