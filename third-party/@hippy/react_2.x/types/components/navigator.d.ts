import React from 'react';
interface Route {
    routeName: string;
    component?: string | React.FunctionComponent<any> | React.ComponentClass<any, any>;
    initProps?: any;
    animated?: boolean;
}
interface NavigatorProps {
    /**
     * Initial page option, the option object should contains.
     *
     * * {string} routeName - Router name
     * * {React.Component} component - Initial react component
     * * {Object} initProps - Initial props for initial react component
     * * {boolean} animated - Use animation effect to switch to new page
     */
    initialRoute: Route;
}
/**
 * Simply router component for switch in multiple Hippy page.
 * @noInheritDoc
 */
declare class Navigator extends React.Component<NavigatorProps, {}> {
    private stack;
    private instance;
    private routeList;
    private backListener?;
    /**
     * @ignore
     */
    constructor(props: NavigatorProps);
    /**
     * @ignore
     */
    componentWillMount(): void;
    /**
     * @ignore
     */
    componentDidMount(): void;
    /**
     * @ignore
     */
    componentWillUnmount(): void;
    getCurrentPage(): any;
    handleAndroidBack(): void;
    /**
     * Push into a new page/component.
     *
     * @param {Object} route - New router
     */
    push(route: Route): void;
    /**
     * Return back to previous page.
     */
    pop(option: {
        animated: boolean;
    }): void;
    /**
     * Clear history stack
     */
    clear(): void;
    /**
     * @ignore
     */
    render(): JSX.Element;
}
export default Navigator;
