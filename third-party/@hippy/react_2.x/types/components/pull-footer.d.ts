import React from 'react';
import { LayoutableProps } from '../types';
interface PullFooterProps extends LayoutableProps {
    /**
     * Keep content displaying after onFooterReleased trigged.
     */
    sticky?: boolean;
    /**
     * Trigger when release the finger after pulling distance larger than the content height
     */
    onFooterReleased?: () => void;
    /**
     * Trigger when pulling
     *
     * @param {Object} evt - Event data
     * @param {number} evt.contentOffset - Dragging distance
     */
    onFooterPulling?: (evt: HippyTypes.PullingEvent) => void;
}
declare class PullFooter extends React.Component<PullFooterProps, {}> {
    private static defaultProps;
    private instance;
    /**
     * Expand the PullView and display the content
     */
    expandPullFooter(): void;
    /**
     * Collapse the PullView and hide the content
     */
    collapsePullFooter(): void;
    render(): JSX.Element;
}
export default PullFooter;
