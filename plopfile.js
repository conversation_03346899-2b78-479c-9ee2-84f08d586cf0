module.exports = function (plop) {
    plop.setGenerator("abc-component", {
        description: "快速生成 abc-xxx 组件目录结构",
        prompts: [
            {
                type: "input",
                name: "name",
                message: "请输入组件名称（如 collapse 或 index）：",
                validate: (v) => (!!v ? true : "名称不能为空"),
            },
        ],
        actions: function (data) {
            const folder = `abc-${data.name}`;
            const basePath = `packages/abc-mobile-ui/src/components/${folder}`;

            return [
                {
                    type: "add",
                    path: `${basePath}/${data.name}.tsx`,
                    templateFile: "plop-templates/component.tsx.hbs",
                },
                {
                    type: "add",
                    path: `${basePath}/index.ts`,
                    templateFile: "plop-templates/index.ts.hbs",
                },
                {
                    type: "add",
                    path: `${basePath}/demo/index.tsx`,
                    templateFile: "plop-templates/demo-index.tsx.hbs",
                },
            ];
        },
    });
};
