#!/bin/bash
npm run tsc
if [ $? != 0 ]; then
    exit
fi

git checkout only_for_integration && git pull --rebase
if [ $? != 0 ]; then
    exit
fi

git checkout master && git pull --rebase
if [ $? != 0 ]; then
    exit
fi

git checkout develop && git pull --rebase
if [ $? != 0 ]; then
    exit
fi

git checkout rc && git pull --rebase
if [ $? != 0 ]; then
    exit
fi

git checkout gray && git pull --rebase

if [ $? != 0 ]; then
    exit
fi



