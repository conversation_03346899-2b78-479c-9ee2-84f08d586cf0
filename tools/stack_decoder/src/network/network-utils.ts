import * as fs from "fs";

type AnyType = any;
export type Session = any; //electron.Session

class NetworkUtils {
    public static downloadFile(url: string, filePath: string, body?: AnyType): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            let http;
            if (url.startsWith("https")) {
                http = require("https");
            } else {
                http = require("http");
            }

            const headers: { [key: string]: any } = {};

            if (body) {
                const contentLength = headers["Content-Length"];
                if (typeof body === "string" && (contentLength === undefined || contentLength === null))
                    headers["Content-Length"] = Buffer.byteLength(body, "utf8");
            }

            const file = fs.createWriteStream(filePath);
            const req = http
                .request(url, (res: any) => {
                    if (res.statusCode !== 200) {
                        reject(`下载失败 code:${res.statusCode}`);
                    } else {
                        res.pipe(file);
                    }
                    res.on("end", () => {
                        console.log(`end res.statusCode = ${res.statusCode}`);
                        file.close();
                        resolve();
                    });
                })
                .on("error", (e: any) => {
                    reject(e);
                });

            if (body) req.write(body);

            req.end();
        });
    }
}

export { NetworkUtils };
