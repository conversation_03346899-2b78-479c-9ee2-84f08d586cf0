import e from "express";
import * as path from "path";
import { routes } from "./routes/routes";

const app = e();
console.log(`path.join(__dirname, "/../../views") = ${path.resolve(__dirname, "../../src/views")}`);
app.set("views", path.resolve(__dirname, "../../src/views"));
app.set("view engine", "ejs");
app.use(e.static(path.join(__dirname, "public")));
app.use(e.json());
app.use(routes);

app.get("/", function (req, res) {
    res.render("index");
});

app.post("/api/decode", function (req, res) {
    res.send("");
});

const PORT = 8089;
app.listen(PORT, function () {
    const { exec } = require("child_process");
    const url = `http://127.0.0.1:${PORT}`;
    console.log(url);

    exec(`open ${url}`);
});

// Handle 404 - Keep this as a last route
app.use(function (req, res /*, next*/) {
    res.status(404);
    res.send("404: File Not Found");
});

app.use("/", function (req, res) {
    res.render("index");
});
