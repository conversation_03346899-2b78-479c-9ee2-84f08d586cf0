/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2021/3/18
 *
 * @description
 */
import { Router } from "express";
import { NetworkUtils } from "../network/network-utils";

import path from "path";

const apiRouter = Router();

interface ParserReq {
    env?: string;
    os?: string;
    version?: string;
    exception?: string;
}

interface ParseRsp {
    stack?: string;
    errorMsg?: string;
}

const cacheDir = path.resolve(__dirname, "../../../dist/cache");
apiRouter.post("/parse", async function (req, res) {
    const { env, os, version, exception } = req.body as ParserReq;
    console.log(`req.body = ${JSON.stringify(req.body)}`);
    let rsp: ParseRsp;
    do {
        if (!env?.length) {
            rsp = { errorMsg: "未选择环境" + JSON.stringify(req.body) };
            break;
        }
        if (!os?.length) {
            rsp = { errorMsg: `示选择系统` };
            break;
        }

        if (!version?.length) {
            rsp = {
                errorMsg: `未指定hippy 版本号`,
            };
            break;
        }
        if (!exception?.length) {
            rsp = {
                errorMsg: `未指定要解析的异常内容`,
            };
        }

        const downloadUrl = `https://cis-static-common.oss-cn-shanghai.aliyuncs.com/apks/abcyun-clinic-app/hippy-jsbundle-source-map/${env}/${os}/${version}.zip`;
        const fs = require("fs");
        const tmpCacheDir = path.join(cacheDir, `${env}/${os}/${version}`);
        if (!fs.existsSync(tmpCacheDir)) {
            fs.mkdirSync(tmpCacheDir, { recursive: true });
        }

        const tmpDownloadFile = `${tmpCacheDir}/${version}.zip`;
        if (!fs.existsSync(tmpDownloadFile)) {
            try {
                await NetworkUtils.downloadFile(downloadUrl, tmpDownloadFile);
                //延时1s,刚下载的文件立即查看，无法查到
                await new Promise((resolve) => {
                    setTimeout(() => {
                        resolve();
                    }, 1000);
                });
            } catch (e) {
                if (fs.existsSync(tmpDownloadFile)) {
                    //由于运行时报fs.rmdirSync不存在,改为使用fs.rm 方法
                    fs.unlinkSync(tmpDownloadFile);
                }

                rsp = {
                    errorMsg: `下载文件失败：${e instanceof Error ? e.message : e}, url = ${downloadUrl}, path = ${tmpDownloadFile}`,
                };
                break;
            }
        }

        const sourceMapFile = `${tmpCacheDir}/index.${os === "android" ? "android" : "ios"}.js.map`;
        if (!fs.existsSync(sourceMapFile)) {
            try {
                await new Promise((resolve, reject) => {
                    const spawn = require("child_process").spawn;
                    const chilldProcess = spawn("unzip", ["-P", "r5Y!xqT4aq%TK#yh", "-d", tmpCacheDir, tmpDownloadFile]);
                    chilldProcess.on("error", (err: any) => {
                        console.log(`unzip error code = ${err}`);
                        reject(err);
                    });
                    chilldProcess.on("message", (message: any) => {
                        console.log(`unzip message = ${message}`);
                    });
                    chilldProcess.on("close", (code: number) => {
                        if (code == 0) {
                            resolve();
                        }
                    });
                });
            } catch (e) {
                rsp = { errorMsg: `解压失败: ${e}path = ${tmpDownloadFile}, downloadUrl= ${downloadUrl}` };
                break;
            }

            if (!fs.existsSync(sourceMapFile)) {
                rsp = {
                    errorMsg: `解压失败：解压后未找到文件${sourceMapFile}`,
                };
                break;
            }
        }

        const sourceMap = require("source-map");
        const SourceMapConsumer = sourceMap.SourceMapConsumer;
        const Stacktracey = require("stacktracey");

        const sourceMapFileContent = fs.readFileSync(sourceMapFile); // sourcemap文件内容

        const tracey = new Stacktracey(exception?.replace(/\\n/g, "\n")); // 解析错误信息
        const sourceMapContent = JSON.parse(sourceMapFileContent);
        const consumer = await new SourceMapConsumer(sourceMapContent);

        const stacks: string[] = [];
        for (const frame of tracey.items) {
            // 这里的frame就是stack中的一行所解析出来的内容
            // originalPosition不仅仅是行列信息，还有错误发生的文件 originalPosition.source
            const originalPosition = consumer.originalPositionFor({
                line: frame.line,
                column: frame.column,
            });

            const stack = `${originalPosition.source}:line=${originalPosition.line}, column:${originalPosition.column}`;
            // 错误所对应的源码
            console.log(stack);
            stacks.push(stack);
        }
        rsp = { stack: `${stacks.join("\n")}, \npath= ${sourceMapFile},\ndownloadUrl= ${downloadUrl}` };
    } while (0);

    res.send(rsp);
});

export { apiRouter };
