/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2021/3/18
 *
 * @description
 */

function elementWithId<T extends HTMLElement>(id: string): T | null {
    return document.getElementById(id) as T;
}

function fetchAsJson(req: string | { path: string; method: "GET" | "POST"; body: any }): Promise<any> {
    let path: string;
    let method = "GET";
    let body: any;
    if (typeof req === "string") {
        path = req;
    } else {
        path = req.path;
        method = req.method;
        body = req.body;
    }

    return new Promise(function (resolve, reject) {
        const xhr = new XMLHttpRequest();
        xhr.onreadystatechange = function () {
            if (this.readyState === 4) {
                if (this.status === 200) {
                    resolve(JSON.parse(this.responseText));
                } else {
                    reject({
                        status: this.status,
                        responseText: this.responseText,
                    });
                }
            }
        };
        xhr.open(method, path, true);
        xhr.setRequestHeader("Content-Type", "application/json;charset=UTF-8");
        const bodyStr = body !== undefined && body !== null ? JSON.stringify(body) : undefined;
        xhr.send(bodyStr);
    });
}

interface ParserReq {
    env: string;
    os: string;
    version: string;
    exception: string;
}

interface ParseRsp {
    stack?: string;
    errorMsg?: string;
}

const KSearchHistoryKey = "search_history_key";
async function submit(): Promise<void> {
    const env = elementWithId<HTMLSelectElement>("env")?.value ?? "prod";
    const os = elementWithId<HTMLSelectElement>("os")?.value ?? "android";
    let version = elementWithId<HTMLInputElement>("hippy_version")?.value ?? "";
    version = version.trim();
    let exception = elementWithId<HTMLTextAreaElement>("exception")?.value ?? "";
    exception = exception.trim();
    if (!version.length) {
        alert("请输入Hippy版本号");
        return;
    }
    if (!exception.length) {
        alert("请输入要解析的异常内容");
        return;
    }

    const req: ParserReq = { env, os, version, exception };
    localStorage.setItem(KSearchHistoryKey, JSON.stringify(req));
    const json = await fetchAsJson({ method: "POST", path: `api/parse`, body: req });

    const consoleDiv = elementWithId<HTMLDivElement>("console");
    if (json.stack) consoleDiv!.innerHTML = `<pre>${json.stack}</pre>`;
    else consoleDiv!.innerHTML = `<pre>${JSON.stringify(json)}</pre>`;
    console.log(`result = ${json}`);
}

window.addEventListener("load", () => {
    const historyStr = localStorage.getItem(KSearchHistoryKey);
    if (!historyStr) {
        return;
    }

    const env = elementWithId<HTMLSelectElement>("env");
    const os = elementWithId<HTMLSelectElement>("os");
    const version = elementWithId<HTMLInputElement>("hippy_version");
    const exception = elementWithId<HTMLTextAreaElement>("exception");

    const history: ParserReq = JSON.parse(historyStr);
    env!.value = history.env;
    os!.value = history.os;
    version!.value = history.version;
    exception!.value = history.exception;
});
