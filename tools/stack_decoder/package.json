{"name": "stack_decoder", "version": "1.0.0", "private": true, "main": "dist/src/main.js", "author": "", "license": "", "description": "ABC诊所管家移动APP-异常解析", "repository": "", "scripts": {"start": "tsc --build tsconfig.json && node dist/src/main.js", "dev": "ts-node-dev --respawn --pretty --transpile-only src/main.ts"}, "keywords": ["Hippy", "React"], "dependencies": {"@types/express": "^4.17.11", "express": "^4.17.1", "lodash": "^4.17.15", "reflect-metadata": "^0.1.13"}, "devDependencies": {"ejs": "^3.1.3", "source-map": "^0.7.3", "stacktracey": "^2.0.18", "style-loader": "^1.0.2", "ts-loader": "^6.2.1", "ts-node-dev": "^1.1.6", "typescript": "^3.8.3"}}