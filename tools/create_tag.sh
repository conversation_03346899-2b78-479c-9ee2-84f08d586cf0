#!/bin/sh
proxyName="abcyun-app"

if [ -z "$1" ]; then
  echo "\t缺少类型参数, t|p|g|v"
  echo "\te.g: createTag.sh t"
  exit -1
fi

if [ $1 == 'g' ]; then
  git checkout gray
fi

if [ $1 == 'v' ]; then
  git checkout master
fi

#计算当前第几周
weekOfYear=`date +%W`
year=`date +%Y`
#weekOfYear=`expr $weekOfYear + 1`
weekOfYear=`printf "%02d\n" $weekOfYear`

tagPrefix="$proxyName-${1}${year}.${weekOfYear}"
#a258496705dc76670c6489c5c76bdd147a06d308    refs/tags/abcyun-app-t2.39.0
lastTagNum=`git ls-remote --tags | grep $tagPrefix | grep -v "\^" | tail -n 1 | awk -F "/" '{print $NF}' | awk -F "." '{print $NF}'`


if [ -z "$lastTagNum" ]; then
    lastTagNum="0"
fi
tagNum=`expr $lastTagNum + 1`

# 补全两位
tagNum=`printf "%02d\n" $tagNum`


# 拼接Tag名
tagName="${tagPrefix}.${tagNum}"
echo "tagName = $tagName"

#生成tag并push
git tag -a ${tagName} -m "${tagName}"
git push origin ${tagName}
