# coding:utf-8
import re
import json
import os
import urllib.request
import ssl
import subprocess
import sys
import argparse
import time
from typing import Optional, Dict

# 创建未经验证的 SSL 上下文（用于处理自签名证书）
ssl._create_default_https_context = ssl._create_unverified_context

def _run_and_capture(cmd, stdin_text: str = ""):
    """Helper: run a command and return combined stdout+stderr (never raises)."""
    try:
        proc = subprocess.run(
            cmd,
            input=stdin_text,
            text=True,
            capture_output=True,
            check=False,
        )
        return (proc.stdout or "") + "\n" + (proc.stderr or "")
    except Exception as e:
        return f"[exec error] {e}"


def _try_parse_url_from_text(text: str) -> str:
    # 精确中文文案
    m = re.search(r"上传成功，地址为：\s*(https?://[^\s;]+\.js)", text)
    if m:
        return m.group(1)
    # 兜底从任何文本中提取第一个 .js 链接
    m2 = re.search(r"(https?://[^\s;]+\.js)", text)
    if m2:
        return m2.group(1)
    return ""


def _try_parse_url_from_clipboard() -> str:
    # macOS: pbpaste
    if sys.platform == "darwin":
        try:
            clip = subprocess.check_output(["pbpaste"], text=True)
            return _try_parse_url_from_text(clip)
        except Exception:
            return ""
    return ""


def get_latest_iconpark_url(selection: str = "iconpark_mobile", timeout_sec: int = 600, poll_interval: float = 1.0) -> str:
    """
    仅通过 abc-if 交互流程获取最新地址：
    - 启动 `abc-if update`（继承当前 TTY），请在出现菜单时选择指定项目（默认 iconpark_mobile），并完成上传。
    - abc-if 成功后会将地址复制到剪贴板。
    - 本方法轮询剪贴板，直到解析出 .js 地址或超时。
    返回解析到的 URL（.js 结尾）。若超时或失败返回空字符串。
    """
    try:
        # 清空剪贴板，避免误读上一次的 URL
        if sys.platform == "darwin":
            try:
                subprocess.run(["pbcopy"], input="", text=True, check=False)
            except Exception:
                pass
        print("即将启动 abc-if 交互流程，请在出现的界面/终端中：", flush=True)
        print(f"1) 选择项目：{selection}", flush=True)
        print("2) 等待上传完成；abc-if 将把 JS 地址复制到剪贴板。", flush=True)
        print("3) 本脚本会自动检测剪贴板中的地址并继续执行。", flush=True)

        # 继承父进程的 stdio，让用户完整交互
        proc = subprocess.Popen(["abc-if", "update"], stdin=None, stdout=None, stderr=None)

        start = time.time()
        last_log = 0
        while True:
            try:
                # 超时控制
                elapsed = time.time() - start
                if elapsed > timeout_sec:
                    try:
                        proc.poll()
                        if proc.returncode is None:
                            proc.terminate()
                    except Exception:
                        pass
                    print("等待剪贴板出现最新地址超时，请重试或先单独运行 `abc-if update` 完成上传后再执行脚本。", flush=True)
                    return ""

                # 每隔一定时间提示一次仍在等待（60 秒一次）
                if int(elapsed) // 60 > last_log:
                    last_log = int(elapsed) // 60
                    print("... 正在等待 abc-if 上传完成并复制地址到剪贴板 ...", flush=True)

                # 轮询剪贴板
                clip_url = _try_parse_url_from_clipboard()
                if clip_url:
                    print(f"已从剪贴板解析 URL：{clip_url}", flush=True)
                    # 捕获到 URL 后尽快结束 abc-if，避免其后续提示打乱日志顺序
                    try:
                        proc.poll()
                        if proc.returncode is None:
                            try:
                                proc.terminate()
                            except Exception:
                                pass
                            # 给一点时间退出
                            try:
                                proc.wait(timeout=2)
                            except Exception:
                                pass
                    except Exception:
                        pass
                    return clip_url

                # 若子进程已退出但剪贴板没有目标地址，再尝试解析终端输出失败提示
                proc.poll()
                if proc.returncode is not None:
                    # 进程已结束，但仍未拿到 URL
                    print("abc-if 进程已结束，但未在剪贴板检测到 JS 地址。", flush=True)
                    return ""

                time.sleep(poll_interval)
            except KeyboardInterrupt:
                # 用户中断，终止 abc-if 并返回空字符串
                try:
                    proc.poll()
                    if proc.returncode is None:
                        proc.terminate()
                except Exception:
                    pass
                print("\033[93m已取消等待（按下 Ctrl+C）。\033[0m", flush=True)
                return ""
    except Exception as e:
        print(f"执行 abc-if update 失败：{e}")
        return ""

def download_iconfont(project: str = "iconpark_mobile", url_override: Optional[str] = None):
    # 从URL下载最新的iconfont内容并保存到文件
    # 若通过参数传入 URL，则直接使用该 URL；pnpm run iconfont -- --url "URL"

    # 否则通过 abc-if 自动获取最新地址

    if url_override:
        url = url_override.strip()
    else:
        url = get_latest_iconpark_url(project)
    if not url:
        print("未能自动获取最新的 iconpark 地址，请手动执行 `abc-if update` 选择 iconpark_mobile 后重试。")
        exit(1)
    try:
        print(f"获取到的最新地址：{url}")
        print("正在下载iconfont内容...")
        # 使用 urllib 下载内容
        with urllib.request.urlopen(url) as response:
            content = response.read().decode('utf-8')

            # 获取当前脚本所在目录
            script_dir = os.path.dirname(os.path.abspath(__file__))
            output_file = os.path.join(script_dir, "iconfont.js")

            # 保存下载的内容到文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(content)
        print(f"成功下载并保存到: {output_file}")
        return content
    except Exception as e:
        # 红色提示错误信息
        print(f"\033[91m下载失败: {e}\033[0m")
        return None


def main():
    parser = argparse.ArgumentParser(description="更新 IconPark 并生成 icons.ts")
    parser.add_argument("--project", default="iconpark_mobile", help="abc-if update 选择的项目名称，默认 iconpark_mobile")
    parser.add_argument("--url", dest="url", default=None, help="直接指定已上传的 .js 地址，指定后将跳过 abc-if 自动获取")
    # 兼容 pnpm 在传参时注入的分隔符 "--"
    argv = [a for a in sys.argv[1:] if a != "--"]
    args = parser.parse_args(argv)

    # 下载最新的iconfont内容
    content = download_iconfont(project=args.project, url_override=args.url)
    if content is None:
        # 如果下载失败，尝试读取本地文件
        input_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "iconfont.js")
        try:
            with open(input_file, "r", encoding='utf-8') as f:
                content = f.read()
            print(f"\033[92m使用本地文件: {input_file}\033[0m")
        except Exception as e:
            print(f"读取本地文件失败: {e}")
            exit(1)

    # 优先尝试 IconPark 新格式：window.__iconpark__ = JSON.parse("{...}")
    symbol_dict: Dict[str, str] = {}
    parsed = False
    try:
        m_jsonparse = re.search(r'JSON\.parse\("([\s\S]+?)"\)', content)
        if m_jsonparse:
            # 第一次 json.loads 反转义，得到纯 JSON 文本
            json_text = json.loads('"' + m_jsonparse.group(1).replace('\\', '\\\\').replace('"', '\\"') + '"')
            # 上面处理太复杂，改用更稳妥的方式：直接对捕获内容执行 bytes 解转义
    except Exception:
        pass

    if not parsed:
        try:
            # 简化且更可靠的双阶段解码：先把捕获内容当成 JSON 字符串解析一次
            m_jsonparse2 = re.search(r'JSON\.parse\("([\s\S]+?)"\)', content)
            if m_jsonparse2:
                raw_inner = m_jsonparse2.group(1)
                # 还原转义：包一层引号交给 json.loads 处理转义序列
                inner_json_text = json.loads(f'"{raw_inner}"')
                obj = json.loads(inner_json_text)
                # 期望结构：{ iconId: { viewBox, fill, content: "<g>...</g>" }, ... }
                for k, v in obj.items():
                    body = v.get('content', '') if isinstance(v, dict) else ''
                    if body:
                        symbol_dict[k] = body
                parsed = len(symbol_dict) > 0
        except Exception as e:
            print(f"解析 IconPark JSON 格式失败：{e}")

    if not parsed:
        # 回退到旧格式：从字符串中提取 <svg> 与 <symbol>
        match = re.search(r"'(<svg>.*?</svg>)'", content, re.DOTALL)
        if not match:
            print("未找到可解析的图标数据（既非 IconPark JSON，也非内嵌 <svg>）。")
            exit(1)

        svg_content = match.group(1)

        # 提取所有 <symbol>
        symbol_pattern = re.compile(r'<symbol\s+id="([^"]+)"[^>]*>(.*?)</symbol>', re.DOTALL)
        symbols = symbol_pattern.findall(svg_content)

        # 构建 JSON 对象
        for symbol_id, symbol_body in symbols:
            symbol_dict[symbol_id] = symbol_body

    # 写入 JSON 文件
    with open("./src/sub-project/base-ui/iconfont/icons.ts", "w") as f:
        f.write("/* eslint-disable */ \n const icons:{ [key: string]: string } = " + json.dumps(symbol_dict, indent=4, ensure_ascii=False) + "\n export default icons")

    # 同步写入 abc-mobile-ui 组件库 icons.ts
    with open("./packages/abc-mobile-ui/src/components/abc-iconfont/icons.ts", "w") as f:
        f.write("/* eslint-disable */ \n const icons:{ [key: string]: string } = " + json.dumps(symbol_dict, indent=4, ensure_ascii=False) + "\n export default icons")

    print("\033[92m已生成 icons.ts，并同步到 abc-mobile-ui icons.ts\033[0m")


if __name__ == "__main__":
    main()
