#!/bin/sh
# usage: ./merge.sh package_examination_support statics_achivement
# 本脚本用于将多个feature分支合并到一个临时分支上，方便给测试
# 创建的分支，需要手动删除

echo $@

if [ $# == 0 ]; then
    echo "未指定合并的分支名"
    exit -1;
fi

finalFetchName=""
for feature in $@
do
    git fetch origin feature/$feature
    if [ $? != 0 ]; then
        echo "更新分支 feature/$feature 失败"
        exit -1
    fi
    
    if [ -z "$finalFetchName" ]; then
        finalFetchName="$feature"
    else
        finalFetchName="${finalFetchName}__$feature"
    fi
done

git fetch origin develop
if [ $? != 0 ]; then
    echo "更新分支 develop分支失败"
    exit -1
fi

finalFetchName=${finalFetchName}__merged
echo "准备创建分支 = $finalFetchName"

# 检测是否已经存在同名分支，存在，则删除
existBranch=`git branch | grep $finalFetchName`
if [ ! -z "$existBranch" ]; then
    git branch -D $finalFetchName
fi


git checkout -b $finalFetchName origin/develop


for feature in $@
do
    git merge origin/feature/$feature --no-edit
    if [ $? != 0 ]; then
        echo "合并分支 origin/feature/$feature 失败"
        exit -1
    fi
done

echo "分支合并完成: $finalFetchName"
