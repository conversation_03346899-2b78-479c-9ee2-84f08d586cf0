const path = require('path');
const webpack = require('webpack');
const CaseSensitivePathsPlugin = require('case-sensitive-paths-webpack-plugin');

const platform = 'global';

module.exports = {
    mode: 'production',
    bail: true,
    entry: {
        vendor: [path.resolve(__dirname, './vendor.js')],
    },
    output: {
        filename: `[name].${platform}.js`,
        path: path.resolve(`./dist/${platform}/`),
        globalObject: '(0, eval)("this")',
        library: 'hippyReactBase',
    },
    optimization: {
        minimize: false,
        moduleIds: 'named',
    },
    plugins: [
        new webpack.DefinePlugin({
            'process.env.NODE_ENV': JSON.stringify('production'),
            __PLATFORM__: null,
        }),
        new CaseSensitivePathsPlugin(),
        new webpack.DllPlugin({
            context: path.resolve(__dirname, '..'),
            path: path.resolve(__dirname, `../dist/${platform}/[name]-manifest.json`),
            name: 'hippyReactBase',
            format: true,
            entryOnly: false,
        }),
    ],
    module: {
        rules: [
            {
                test: /\.tsx?$/,
                // exclude: /node_modules/,
                use: [
                    {
                        loader: "babel-loader",
                        options: {
                            "presets": [
                                '@babel/preset-typescript',
                                '@babel/preset-react',
                                '@babel/preset-env',
                                [
                                    '@babel/preset-env',
                                    {
                                        targets: {
                                            ios: 9,
                                        },
                                    },
                                ],
                            ],
                            "plugins": [
                                ["@babel/plugin-proposal-decorators", {"legacy": true}],
                                "@babel/plugin-proposal-optional-chaining",
                                "@babel/plugin-proposal-nullish-coalescing-operator",
                                "@babel/proposal-class-properties",
                                "@babel/plugin-proposal-object-rest-spread",
                            ]
                        },

                    },
                    'unicode-loader',
                ]
            },
        ],
    },
    resolve: {
        extensions: ['.js', '.jsx', '.json'],
        modules: [path.resolve(__dirname, '../node_modules')],
        alias: {
            "@hippy/react": path.resolve(__dirname, "../node_modules/@hippy/react"),
            "@app/abc-mobile-ui": path.resolve(__dirname, "../packages/abc-mobile-ui/dist"),
            "@app/theme": path.resolve(__dirname, "../packages/theme/dist"),
            "@app/utils": path.resolve(__dirname, "../packages/utils/dist"),
        },
    },
};
