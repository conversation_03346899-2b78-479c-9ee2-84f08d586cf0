const Tool = require("abc-fed-build-tool");
const path = require("path");
const fs = require("fs");
const buildEnv = process.env.BUILD_ENV;

const ossType = process.env.OSS_TYPE || "ali";
const strategy = {
    "ali": function(env) {
        return Tool.OSS.getOSSInfo(env, `pc`);
    },
    "tencent": function(env) {
        return Tool.OSS.getCOSInfo(env, `pc`);
    }
};

const sourceMapEnv = ['dev', 'test', 'pre', 'gray', 'prod']
const bucketInfo = strategy[ossType](process.env.BUILD_ENV || "dev");

let ossHippyPrefix = "";
let ossPrefix ="";
if (buildEnv === "dev" || buildEnv === "test") {
    ossHippyPrefix = "hippy_test";
    ossPrefix = "test";
} else if (buildEnv === "gray") {
    ossHippyPrefix = "hippy_gray";
    ossPrefix = "gray";
} else if (buildEnv === "prod") {
    ossHippyPrefix = "hippy_release";
    ossPrefix = "release";
} else if (buildEnv === "pre") {
    ossHippyPrefix = "hippy_pre";
    ossPrefix = "pre";
}


const { bucket, region, secretId, secretKey } = bucketInfo;

const outputPath = path.resolve(`./dist/`);
console.log("更新zip文件信息")
fs.readdirSync(outputPath).filter(file => file.endsWith(".zip")).forEach(file => {
    const zipInfoFileName = path.resolve(outputPath, `${file.match(/^(.*?)_abcyun-hippy(.*?)\.zip$/)[1]}_zip_info.txt`);
    // 读取zip文件信息
    const zipInfo = fs.readFileSync(zipInfoFileName, 'utf-8');
    // 读取为json
    const zipInfoJson = JSON.parse(zipInfo);
    // 修改url
    const { url } = Tool.OSS.getOSSInfo(buildEnv, `abc-app`);
    zipInfoJson.url = `https:${url}${file}`;
    // 重新写入文件
    fs.writeFileSync(zipInfoFileName, JSON.stringify(zipInfoJson, null, 2));
});

module.exports = {
    mode: 'production',
    bail: true,
    entry: {},
    output: {},
    plugins: [
        new Tool.AbcWebpackOSSPlugin({
            prefix: 'abc-app',
            Bucket: bucket,
            Region: region,
            SecretId: secretId,
            SecretKey: secretKey,
            ossType: ossType,
            map: sourceMapEnv.includes(process.env.BUILD_ENV),
            buildTag: process.env.BUILD_TAG || 'latest',
            buildEnv: process.env.BUILD_ENV || 'dev',
            local: true,
        })
    ]
};
