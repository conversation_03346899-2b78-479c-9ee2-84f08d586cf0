const fs = require('fs');
const path = require('path');
const webpack = require('webpack');
const HippyDynamicImportPlugin = require('@hippy/hippy-dynamic-import-plugin');
// const ReactRefreshWebpackPlugin = require('@hippy/hippy-react-refresh-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const pkg = require('../package.json');
const CopyPlugin = require('copy-webpack-plugin');

module.exports = {
    mode: 'development',
    devtool: 'eval-source-map',
    watch: true,
    watchOptions: {
        aggregateTimeout: 1500,
    },
    devServer: {
        remote: {
            protocol: 'http',
            host: '0.0.0.0',
            port: 38989,
        },
        reactDevtools: false,
        multiple: true,
        hot: true,
        liveReload: true,
        client: {
            overlay: false,
        },
        devMiddleware: {
            writeToDisk: true,
        },
    },
    entry: {
        index: ['@hippy/rejection-tracking-polyfill', 'regenerator-runtime', path.resolve(pkg.main)],
    },
    output: {
        filename: 'index.bundle',
        strictModuleExceptionHandling: true,
        path: path.resolve('./dist/dev/'),
        globalObject: '(0, eval)("this")',
    },
    plugins: [
        new webpack.DefinePlugin({
            'process.env': {
                NODE_ENV: JSON.stringify('development'),
                HOST: JSON.stringify(process.env.DEV_HOST || '0.0.0.0'),
                PORT: JSON.stringify(process.env.DEV_PORT || 38989),
            },
            __PLATFORM__: null,
        }),
        new HippyDynamicImportPlugin(),
        // use SourceMapDevToolPlugin can generate sourcemap file while setting devtool to false
        // new webpack.SourceMapDevToolPlugin({
        //   test: /\.(js|jsbundle|css|bundle)($|\?)/i,
        //   filename: '[file].map',
        // }),
        // new ReactRefreshWebpackPlugin({
        //     overlay: false,
        // }),
        new CleanWebpackPlugin({
            cleanStaleWebpackAssets: false,
            cleanOnceBeforeBuildPatterns: ['**/*', '!assets/**']
        }),
        new CopyPlugin([
            { from: 'src/assets', to: './assets/' },
        ]),
    ],
    module: {
        rules: [
            {
                test: /\.tsx?$/,
                // exclude: /node_modules/,
                use: [
                    {
                        loader: "babel-loader",
                        options: {
                            "presets": [
                                '@babel/preset-typescript',
                                '@babel/preset-react',
                                [
                                    '@babel/preset-env',
                                    {
                                        targets: {
                                            ios: 9,
                                        },
                                    },
                                ],
                            ],
                            "plugins": [
                                ["@babel/plugin-proposal-decorators", { "legacy": true }],
                                "@babel/proposal-class-properties",
                                "@babel/plugin-proposal-optional-chaining",
                                "@babel/plugin-proposal-nullish-coalescing-operator",
                            ],
                        },

                    },
                    'unicode-loader',
                ]
            },
            {
                test: /\.(jsx?)$/,
                use: [
                    {
                        loader: 'babel-loader',
                        options: {
                            presets: [
                                '@babel/preset-react',
                                [
                                    '@babel/preset-env',
                                    {
                                        targets: {
                                            ios: 9,
                                        },
                                    },
                                ],
                            ],
                            plugins: [
                                '@babel/plugin-proposal-class-properties',
                            ],
                        },
                    },
                    'unicode-loader',
                ],
            },
            {
                test: /\.(png|jpe?g|gif|jpg)$/i,
                use: [{
                    loader: 'url-loader',
                    options: {
                        limit: true,
                        // TODO local path not supported on defaultSource/backgroundImage
                        // limit: 8192,
                        // fallback: 'file-loader',
                        // name: '[name].[ext]',
                        // outputPath: 'assets/',
                    },
                }],
            },
            {
                test: /\.json$/,
                loader: 'json-loader',
            },

        ],
    },
    resolve: {
        extensions: ['.js', '.jsx', '.json', '.ts', '.tsx'],
        modules: [path.resolve(__dirname, '../node_modules')],
        alias: {
            "@app/abc-mobile-ui": path.resolve(__dirname, "../packages/abc-mobile-ui/dist"),
            "@app/theme": path.resolve(__dirname, "../packages/theme/dist"),
            "@app/utils": path.resolve(__dirname, "../packages/utils/dist"),
        },
    },
};
