<!DOCTYPE html>

<html lang="zh-CN">
<head>
  <meta charset="utf-8">
  <title><%= htmlWebpackPlugin.options.title || 'Hippy'%></title>
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <link rel="icon" href="<%= htmlWebpackPlugin.options.favouriteIcon%>">
  <style>
    body {
      margin: 0 auto;
    }

    body::-webkit-scrollbar {
      display: none;
    }

    p {
      margin: 0;
    }

    ul {
      margin: 0;
      padding: 0;
    }

    #root {
      min-height: 100vh;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
    }

    #root::-webkit-scrollbar {
      display: none;
    }
  </style>

  <script>
    window.__HIPPYNATIVEGLOBAL__ = {
      Platform: {
        OS: "web"
      },
      Dimensions: {
        window: {
          scale: window.devicePixelRatio || 1,
          height: window.innerHeight,
          width: window.innerWidth
        }
      }
    }

    window.hippyCallNatives = function () {
    }

  </script>
</head>
<body>
  <div id="root"></div>
</body>
</html>
