const path = require('path');
const webpack = require('webpack');
const CaseSensitivePathsPlugin = require('case-sensitive-paths-webpack-plugin');
const HippyDynamicImportPlugin = require('@hippy/hippy-dynamic-import-plugin');
const pkg = require('../package.json');
const platform = 'global';
// eslint-disable-next-line import/no-dynamic-require
const manifest = require(path.resolve(__dirname, `../dist/${platform}/vendor-manifest.json`));
const CopyPlugin = require('copy-webpack-plugin');

module.exports = {
    mode: 'production',
    bail: true,
    devtool: 'source-map',
    entry: {
        index: ['regenerator-runtime', path.resolve(pkg.main)],
    },
    output: {
        filename: `[name].${platform}.js`,
        path: path.resolve(`./dist/${platform}/`),
        globalObject: '(0, eval)("this")',
    },
    plugins: [
        new webpack.DefinePlugin({
            'process.env.NODE_ENV': JSON.stringify('production'),
            __PLATFORM__: null,
        }),
        new CaseSensitivePathsPlugin(),
        new webpack.DllReferencePlugin({
            context: process.cwd(),
            manifest,
        }),
        new HippyDynamicImportPlugin(),
        new CopyPlugin([
            {from: 'src/assets', to: './assets/'},
        ]),
    ],
    optimization: {
        minimize: false,
        moduleIds: 'named',
    },
    module: {
        rules: [
            {
                test: /\.tsx?$/,
                // exclude: /node_modules/,
                use: [
                    {
                        loader: "babel-loader",
                        options: {
                            "presets": [
                                '@babel/preset-typescript',
                                '@babel/preset-react',
                                [
                                    '@babel/preset-env',
                                    {
                                        targets: {
                                            ios: 9,
                                        },
                                    },
                                ],
                            ],
                            "plugins": [
                                ["@babel/plugin-proposal-decorators", {"legacy": true}],
                                "@babel/plugin-proposal-optional-chaining",
                                "@babel/plugin-proposal-nullish-coalescing-operator",
                                "@babel/proposal-class-properties",
                                "@babel/plugin-proposal-object-rest-spread",
                            ]
                        },

                    },
                    'unicode-loader',
                ]
            },
            {
                test: /\.(png|jpg|gif)$/,
                use: [{
                    loader: 'file-loader',
                    options: {
                        name: '[name].[ext]',
                        outputPath: 'assets/',
                    },
                }],
            },
        ],
    },
    resolve: {
        extensions: ['.js', '.jsx', '.json', '.ts', '.tsx'],
        modules: [path.resolve(__dirname, '../node_modules')],
        alias: {
            "@hippy/react": path.resolve(__dirname, "../node_modules/@hippy/react"),
            "@app/abc-mobile-ui": path.resolve(__dirname, "../packages/abc-mobile-ui/dist"),
            "@app/theme": path.resolve(__dirname, "../packages/theme/dist"),
            "@app/utils": path.resolve(__dirname, "../packages/utils/dist"),
        },
    },
};
