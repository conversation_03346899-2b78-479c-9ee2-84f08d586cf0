# -*- coding: utf-8 -*-
from __future__ import print_function

import ast
import os
import json
import sys
import requests

reload(sys)
sys.setdefaultencoding('utf8')

# 获取环境变量
buildEnv = os.environ.get('BUILD_ENV', '')
tagName = os.environ.get('TAG_NAME', '')
buildZone = os.environ.get('BUILD_ZONE', 'primary')
# 根据 BUILD_ZONE 的值决定调用次数
if buildZone == "primary":
    zone_calls = ["0"]
elif buildZone == "standby":
    zone_calls = ["1"]
elif buildZone == "primary,standby":
    zone_calls = ["0", "1"]
elif buildZone == "standby,primary":
    zone_calls = ["1", "0"]
else:  # buildZone 为空字符串的情况
    zone_calls = ["0"]

deployRegion = os.environ.get('DEPLOY_REGION', "")

if deployRegion == "default":
    deployRegion = ""

# 处理逗号分隔的部署区域
if deployRegion and "," in deployRegion:
    deployRegions = [region.strip() for region in deployRegion.split(",")]
else:
    deployRegions = [deployRegion] if deployRegion else ["region1", "region2"]

appName = 'ABC_HIPPY'  # 与 _uploadPayload 中的 app_name 保持一致


def _get_api_host():
    """
    根据构建环境获取API主机地址
    
    Returns:
        str: API主机地址
    """
    if buildEnv == "dev":
        return "dev.rpc.abczs.cn"
    elif buildEnv == "test":
        return "test.rpc.abczs.cn"
    elif buildEnv in ("prod", "gray", "pre"):
        return "pre.rpc.abczs.cn"
    else:
        raise ValueError("Unsupported build environment: {}".format(buildEnv))


def _get_deployment_record(app_name, env, tag):
    """
    从部署平台获取部署记录
    
    Args:
        app_name: 应用名称
        env: 环境
        tag: 构建标签
        
    Returns:
        dict: 部署记录数据
    """
    try:
        host = _get_api_host()

        # 构建 API URL
        api_path = "/rpc/low-code/deployment/deploy-platform/record/{0}/{1}/{2}".format(
            app_name, env, tag
        )
        api_url = "http://{0}{1}".format(host, api_path)

        print("正在获取部署记录: {0}".format(api_url))

        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'ABC-CI-Deploy-Tool/1.0'
        }

        response = requests.get(api_url, headers=headers, timeout=30)

        if response.status_code == 200:
            data = response.json()
            record = data.get('data', {})
            print("成功获取部署记录")
            return record
        else:
            print("获取部署记录失败，状态码: {0}".format(response.status_code))
            print("响应内容: {0}".format(response.text))

    except requests.exceptions.RequestException as e:
        print("网络请求异常: {0}".format(str(e)))
    except Exception as e:
        print("获取部署记录时发生异常: {0}".format(str(e)))

    return None


def _update_upgrade_item(record, platform, gray_flag):
    """
    更新升级项
    
    Args:
        record: 部署记录数据
        platform: 平台 (android, ios, ios_9.0, ohos)
        gray_flag: 灰度标志 (0: 正式, 1: 灰度, 2: 预发)
    """
    try:
        if not record:
            print("错误: 部署记录为空")
            return False

        extra_info = record.get("extraInfo", {})
        platforms = json.loads(extra_info)
        platformInfo = platforms.get("platforms").get(platform)

        # 根据环境确定 API 主机地址
        if buildEnv == "dev":
            host = "dev.rpc.abczs.cn"
        elif buildEnv == "test":
            host = "test.rpc.abczs.cn"
        elif buildEnv in ["prod", "gray", "pre"]:
            host = "pre.rpc.abczs.cn"
        else:
            raise ValueError("Unsupported build environment: {0}".format(buildEnv))

        # 构建 API URL
        api_url = "http://{0}/rpc/mobile/plugin/updatePlugin".format(host)

        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'ABC-CI-Deploy-Tool/1.0'
        }

        all_success = True
        for zone in zone_calls:
            for region in deployRegions:
                zip_info = platformInfo.copy()
                zip_info['grayFlag'] = gray_flag
                zip_info['zone'] = zone
                zip_info["region"] = region

                print("url:{0} ,zip_info: {1}".format(api_url, json.dumps(zip_info, indent=2, ensure_ascii=False)))
                response = requests.put(api_url, data=json.dumps(zip_info), headers=headers, timeout=30)

                if response.status_code == 200:
                    print("成功更新 {0} 的升级项，zone: {1}, region: {2}".format(platform, zone, region))
                else:
                    print(
                        "更新 {0} 升级项失败，状态码: {1}，zone: {2}, region: {3}".format(platform, response.status_code,
                                                                                        zone, region))
                    print("响应内容: {0}".format(response.text))
                    all_success = False

        return all_success

    except Exception as e:
        print("更新 {0} 升级项时发生异常: {1}".format(platform, str(e)))
        return False


def update_upgrade_info():
    """
    更新所有平台的升级信息
    """
    # 1. 获取部署记录
    record = _get_deployment_record(appName, buildEnv, tagName)
    if not record:
        print("错误: 无法获取部署记录，终止更新升级信息")
        return False

    # 2. 根据环境更新升级项
    success = True

    if buildEnv in ("dev", "test"):
        # 开发/测试环境更新所有灰度标志
        platforms = ["android", "ios", "ios_9.0", "ohos"]
        gray_flags = ["0", "1", "2"]
        for platform in platforms:
            for gray_flag in gray_flags:
                if not _update_upgrade_item(record, platform, gray_flag):
                    success = False
    elif buildEnv == "gray":
        # 灰度环境只更新灰度标志为1
        platforms = ["android", "ios", "ios_9.0", "ohos"]
        for platform in platforms:
            if not _update_upgrade_item(record, platform, "1"):
                success = False
    elif buildEnv == "prod":
        # 生产环境只更新灰度标志为0
        platforms = ["android", "ios", "ios_9.0", "ohos"]
        for platform in platforms:
            if not _update_upgrade_item(record, platform, "0"):
                success = False
    elif buildEnv == "pre":
        # 预发环境只更新灰度标志为2
        platforms = ["android", "ios", "ios_9.0", "ohos"]
        for platform in platforms:
            if not _update_upgrade_item(record, platform, "2"):
                success = False

    return success


def update_status(app_name, env, tag, status):
    """
    更新部署记录状态
    
    Args:
        app_name: 应用名称
        env: 环境
        tag: 构建标签
        status: 状态值 (1: 已构建, 2: 发布成功, 3: 发布失败)
        
    Returns:
        bool: 是否更新成功
    """
    try:
        host = _get_api_host()
        api_path = "/rpc/low-code/deployment/deploy-platform/record/{0}/{1}/{2}".format(
            app_name, env, tag
        )
        api_url = "http://{0}{1}".format(host, api_path)

        print("正在更新部署记录状态: {0}".format(api_url))

        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'ABC-CI-Deploy-Tool/1.0'
        }

        # 构建请求体
        payload = {
            'status': status
        }

        print("api_url: {0}".format(api_url))
        print("payload: {0}".format(json.dumps(payload, indent=2, ensure_ascii=False)))
        response = requests.put(api_url, headers=headers, json=payload, timeout=30)

        if response.status_code == 200:
            print("成功更新部署记录状态")
            return True
        else:
            print("更新部署记录状态失败，状态码: {0}".format(response.status_code))
            print("响应内容: {0}".format(response.text))
            return False

    except requests.exceptions.RequestException as e:
        print("网络请求异常: {0}".format(str(e)))
    except Exception as e:
        print("更新部署记录状态时发生异常: {0}".format(str(e)))

    return False


# 主函数
if __name__ == "__main__":
    print("开始更新升级信息...")
    if update_upgrade_info():
        print("升级信息更新完成")
        update_status(appName, buildEnv, tagName, 2)
        sys.exit(0)
    else:
        print("升级信息更新失败")
        update_status(appName, buildEnv, tagName, 3)
        sys.exit(1)
