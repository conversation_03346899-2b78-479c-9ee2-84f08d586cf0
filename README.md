# abcyun_clinic_app_hippy

ABC诊所管家移动端（Hippy + React）

## 项目简介
本项目基于 Hippy + React 技术栈，采用 monorepo 管理，支持多端组件开发与预览，适用于诊所管家业务场景。

## 主要命令

- `pnpm install`         安装依赖
- `pnpm run dev`         启动所有包的开发模式（监听编译，支持类型提示）
- `pnpm run build`       构建所有包

## 包结构说明

- `packages/abc-mobile-ui`  —— 移动端 UI 组件库
- `packages/theme`          —— 主题与样式包
- `packages/utils`          —— 工具函数与常量
- `packages/shared`         —— 共享模块

## 开发说明

- 推荐使用 Node.js 18.x 及以上版本
- 使用 pnpm 管理依赖，确保 workspace 软链正常
- 所有包的类型声明自动生成，路径别名已配置

## 常见问题

- **TS2307: Cannot find module '@app/utils'**
  - 检查 tsconfig 路径别名配置是否同步到各子包
  - 确保依赖已安装且软链无误
  - 重启 IDE/TS 服务

- **pnpm 版本或 Node 版本不兼容**
  - 请升级 Node.js 至 18.x 及以上
  - 升级 pnpm 至最新 LTS

---
如需更多帮助，请联系项目维护者。


> 🚀 本项目采用 TurboRepo + pnpm monorepo 架构，支持统一一键开发、构建、热更新。

---

## 🆕 推荐统一命令（TurboRepo + pnpm）

### 安装依赖

```bash
pnpm install
```

### 一键开发/热更新（全局 watch，所有包并发）

```bash
pnpm run dev
# 或
pnpm turbo run dev
```

> 说明：TurboRepo 1.10+ 官方推荐所有包的 dev/watch 任务并发启动，无需依赖链（dependsOn），所有包的 dev 会自动并发启动并监听自身源码变更，依赖包 dist 变更会被下游 watch 到，实现全局热更新。

### 快速创建文件夹
```bash
pnpm exec plop 组件名(`不需要带abc前缀`), 生成文件结构如下:
```
abc-collapse
├─ collapse.tsx      
├─ index.ts
└─ demo/
└─ index.tsx

### 一键全量构建

```bash
pnpm run build
# 或
pnpm turbo run build
```

### 一键清理产物

```bash
pnpm -r run clean
```

---

## 传统业务命令

### 编译

```bash
$ pnpm i -D
```

### 开发调试

```bash
$ pnpm run hippy:dev
$ pnpm run hippy:debug
```

### 打包

```bash
$ pnpm run hippy:vendor
```

### 提测
进入 tools目录下运行./create_tag.sh, 将生成的 tagName交给测试
e.g:打测试tag
```bash
$ ./create_tag.sh t

output:
tagName = abcyun-app-d2.39.23
Enumerating objects: 1, done.
Counting objects: 100% (1/1), done.
Writing objects: 100% (1/1), 162 bytes | 162.00 KiB/s, done.
Total 1 (delta 0), reused 0 (delta 0)
To https://e.coding.net/abc-clinic/abcyun_clinic_app_hippy.git
 * [new tag]           abcyun-app-d2.39.23 -> abcyun-app-d2.39.23
```
# AirTest脚本工程
作为hippy工程的子工程，需要进行工程初始化和独立提交
```bash
git submodule init 
git submodule update src/modules/AbcUITestByAirtest
```